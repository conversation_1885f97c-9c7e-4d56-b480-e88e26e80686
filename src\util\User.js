var logined = false;
var userData = null;
var name = '';
var email = '';
var userId = null;
var outletId = null;
var merchantId = null;
var refreshToken = null;
var refreshMainScreen = null;
var checkAsyncStorage = null;
var refreshCurrentAction = null;
var queueStatus = 0;
var reservationStatus = 0;

export function setQueueStatus(param) {
  queueStatus = param;
}

export function setReservationStatus(param) {
  reservationStatus = param;
}

export function getReservationStatus(param) {
  return reservationStatus;
}

export function islogined() {
  return logined;
}

export function setlogin(param) {
  logined = param;
}

export function getRefreshCurrentAction() {
  return refreshCurrentAction();
}

export function setRefreshCurrentAction(param) {
  refreshCurrentAction = param;
}

export function setRefreshMainScreen(func) {
  refreshMainScreen = func;
}

export function getRefreshMainScreen() {
  return refreshMainScreen();
}

export function setCheckAsyncStorage(func) {
  checkAsyncStorage = func;
}

export function getCheckAsyncStorage() {
  return checkAsyncStorage();
}

export function setName(param) {
  name = param;
}

export function getName() {
  return name;
}

export function setUserData(param) {
  userData = param;
}

export function getUserData() {
  return userData;
}

export function getUserId() {
  return userId;
}

export function setUserId(param) {
  userId = param;
}

export function getOutletId() {
  return outletId;
}


export function setUserEmail(param) {

  email = param;

}

export function getEmail() {
  return email;
 
}

export function setOutletId(param) {
  outletId = param;
}

export function setRefreshToken(param) {
  refreshToken = param;
}

export function getRefreshToken() {
  return refreshToken;
}

export function getMerchantId() {
  return merchantId;
}

export function setMerchantId(param) {
  merchantId = param;
}

export function initFromJSON(jsonData) {
  if (!jsonData) {
    return;
  }
  setUserData(jsonData);
  setName(userData.name);
  setRefreshToken(userData.refreshToken);
  setUserId(userData.firebaseUid);
  setMerchantId(userData.merchantId);
  setOutletId(userData.outletId);
  setlogin(true);
  setUserEmail(userData.email);
}
