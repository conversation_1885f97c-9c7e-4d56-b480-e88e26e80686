import React, {
  Component,
  useEffect,
  useReducer,
  useState,
  useCallback,
} from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  TouchableOpacity,
  TextInput,
  Dimensions,
  FlatList,
  Modal,
  PermissionsAndroid,
  Platform,
  ActivityIndicator,
  useWindowDimensions,
} from "react-native";
import Colors from "../constant/Colors";
import SideBar from "./SideBar";
import * as User from "../util/User";
import Icon from "react-native-vector-icons/Feather";
import Icon1 from "react-native-vector-icons/FontAwesome";
import Icon2 from "react-native-vector-icons/EvilIcons";
import Icon3 from "react-native-vector-icons/Foundation";
import Icon4 from "react-native-vector-icons/FontAwesome5";
import DropDownPicker from "react-native-dropdown-picker";
// import { ceil } from 'react-native-reanimated';
// import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from "react-native-modal-datetime-picker";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import moment from "moment";
import Close from "react-native-vector-icons/AntDesign";
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Styles from "../constant/Styles";
// import DocumentPicker from 'react-native-document-picker';
import Ionicons from "react-native-vector-icons/Ionicons";
// import RNFetchBlob from 'rn-fetch-blob';
// import {isTablet} from '../util/common';
import "react-native-get-random-values";
import { customAlphabet } from "nanoid";
import { CommonStore } from "../store/commonStore";
import { MerchantStore } from "../store/merchantStore";
import { UserStore } from "../store/userStore";
import {
  STOCK_TRANSFER_STATUS,
  STOCK_TRANSFER_STATUS_PARSED,
  EMAIL_REPORT_TYPE,
  PURCHASE_ORDER_STATUS,
  EXPAND_TAB_TYPE,
} from "../constant/common";
import { convertArrayToCSV, generateEmailReport } from "../util/common";
import { sliceUnicodeStringV2WithDots } from "../util/common";
// import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
// import RNPickerSelect from 'react-native-picker-select';
// import { useKeyboard } from '../hooks';
import AntDesign from "react-native-vector-icons/AntDesign";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import XLSX from "xlsx";
import { Row } from "react-native-table-component";
import { v4 as uuidv4 } from "uuid";
import { ReactComponent as GCalendar } from "../assets/svg/GCalendar.svg";
import { ReactComponent as GCalendarGrey } from "../assets/svg/GCalendarGrey.svg";
import { OutletStore } from "../store/outletStore";
import { useFocusEffect } from "@react-navigation/native";
import Select from "react-select";

import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import { CSVLink } from "react-csv";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "../constant/datePicker.css";
import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";
import APILocal from '../util/apiLocalReplacers';
// import UserIdleWrapper from '../components/userIdleWrapper';
import Ionicon from "react-native-vector-icons/Ionicons";

// const RNFS = require('react-native-fs');

const alphabet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
const nanoid = customAlphabet(alphabet, 12);

const StockReturnProductScreen = (props) => {
  //port til may 11 2023 changes
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  // const [isMounted, setIsMounted] = useState(true);

  // useFocusEffect(
  //   useCallback(() => {
  //     setIsMounted(true);
  //     return () => {
  //       setIsMounted(false);
  //     };
  //   }, [])
  // );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const [stockTransfer, setStockTransfer] = useState(true);
  const [addPurchase, setAddPurchase] = useState(false);
  const [editPurchase, setEditPurchase] = useState(false);
  const [addStockTransfer, setAddStockTransfer] = useState(false);
  const [stockList, setStockList] = useState([]);
  const [stockTransferList, setStockTransferList] = useState([]);
  const [stockTakeList, setStockTakeList] = useState([]);
  const [orderList, setOrderList] = useState([]);
  const [itemsToOrder, setItemsToOrder] = useState([{}, {}, {}]);
  const [itemsToOrder2, setItemsToOrder2] = useState([{}, {}, {}]);
  const [addStockTransferList, setAddStockTransferList] = useState([
    {},
    {},
    {},
  ]);
  const [addCountedStockTakeList, setAddCountedStockTakeList] = useState([
    {},
    {},
    {},
  ]);
  const [addUnCountedStockTakeList, setAddUnCountedStockTakeList] = useState([
    {},
    {},
    {},
  ]);
  const [productList, setProductList] = useState([]);
  const [isSelected, setIsSelected] = useState(false);
  const [isSelected2, setIsSelected2] = useState(false);
  const [isSelected3, setIsSelected3] = useState(true);
  const [isSelected4, setIsSelected4] = useState(false);
  const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
  const [date, setDate] = useState(Date.now());
  const [date1, setDate1] = useState(Date.now());
  const [createdDate, setCreatedDate] = useState(Date.now());
  const [visible, setVisible] = useState(false);
  const [Email, setEmail] = useState("");
  const [modal, setModal] = useState(false);
  // const [// outletId, set// outletId] = useState(1);
  const [outletId, setOutletId] = useState(User.getOutletId());
  const [search, setSearch] = useState("");
  const [search2, setSearch2] = useState("");
  const [search3, setSearch3] = useState("");
  const [ideal, setIdeal] = useState("");
  const [minimum, setMinimum] = useState("");
  const [itemId, setItemId] = useState("");
  const [choose, setChoose] = useState(null);

  const [loading, setLoading] = useState(false);

  //////////////////////////////////////////////////////////////////////

  const [poId, setPoId] = useState("");
  const [editMode, setEditMode] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState(false);
  const [exportEmail, setExportEmail] = useState("");
  const [exportModal, setExportModal] = useState(false);
  const [importModal, setImportModal] = useState(false);

  const [isLoadingExcel, setIsLoadingExcel] = useState(false);
  const [isLoadingCsv, setIsLoadingCsv] = useState(false);

  const [poStatus, setPoStatus] = useState(STOCK_TRANSFER_STATUS.CREATED);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState("");

  const [selectedSourceOutletIdPrev, setSelectedSourceOutletIdPrev] =
    useState("");
  const [selectedSourceOutletId, setSelectedSourceOutletId] = useState("");

  const [outletSupplyItemDropdownList, setOutletSupplyItemDropdownList] =
    useState([]);

  const [poItems, setPoItems] = useState([
    {
      outletSupplyItemId: "",
      name: "",
      sku: "",
      unit: "",
      skuMerchant: "",
      quantity: 0,
      transferQuantity: 0,
      balance: 0,
      price: 0,
      totalPrice: 0,

      supplyItem: null,
    },
  ]);

  const [subtotal, setSubtotal] = useState(0);
  const [taxTotal, setTaxTotal] = useState(0);
  const [discountTotal, setDiscountTotal] = useState(0);
  const [finalTotal, setFinalTotal] = useState(0);

  const [outletItems, setOutletItems] = useState([]);

  // const [supplyItems, setSupplyItems] = useState([]);

  //////////////////////////////////////////////////////////////////////////////

  const [supplierDropdownList, setSupplierDropdownList] = useState([]);

  const [selectedSupplierId, setSelectedSupplierId] = useState("ALL");

  const suppliersProduct = CommonStore.useState((s) => s.suppliersProduct);

  //////////////////////////////////////////////////////////////////////////////

  const outletItemsRaw = OutletStore.useState((s) => s.outletItems);
  const allOutletsItems = OutletStore.useState((s) => s.allOutletsItems);

  const supplyItems = CommonStore.useState((s) => s.supplyItems);
  const supplyItemsSkuDict = CommonStore.useState((s) => s.supplyItemsSkuDict);

  const allOutletsSupplyItemsSkuDict = CommonStore.useState(
    (s) => s.allOutletsSupplyItemsSkuDict
  );
  const allOutletsSupplyItems = CommonStore.useState(
    (s) => s.allOutletsSupplyItems
  );
  const allOutletsSupplyItemsDict = CommonStore.useState(
    (s) => s.allOutletsSupplyItemsDict
  );

  const outletSupplyItemsDict = CommonStore.useState(
    (s) => s.outletSupplyItemsDict
  );
  const outletSupplyItemsSkuDict = CommonStore.useState(
    (s) => s.outletSupplyItemsSkuDict
  );

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const stockReturnsProduct = CommonStore.useState(
    (s) => s.stockReturnsProduct
  );

  const userName = UserStore.useState((s) => s.name);
  const userId = UserStore.useState((s) => s.firebaseUid);
  const merchantName = MerchantStore.useState((s) => s.name);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const dropDownRef = React.useRef();
  const dropDownRef1 = React.useRef();

  const selectedStockReturnEdit = CommonStore.useState(
    (s) => s.selectedStockReturnEdit
  );
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView
  );

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const [rev_date, setRev_date] = useState(moment().subtract(6, "days").startOf("day"));
  const [rev_date1, setRev_date1] = useState(moment().endOf(Date.now()).endOf("day"));

  useEffect(() => {
    if (currOutletId !== '' &&
      allOutlets.length > 0 &&
      stockReturnsProduct.length > 0) {
      var stockReturnsProductTemp = [];
      for (var i = 0; i < stockReturnsProduct.length; i++) {
        if ((moment(rev_date).isSameOrBefore(stockReturnsProduct[i].createdAt)) &&
          (moment(rev_date1).isAfter(stockReturnsProduct[i].createdAt))
        ) {
          stockReturnsProductTemp.push(stockReturnsProduct[i]);
        }
      }
      stockReturnsProductTemp.sort((a, b) => b.orderDate - a.orderDate)
      setStockList(stockReturnsProductTemp);
    }
  }, [currOutletId, rev_date, rev_date1, stockReturnsProduct])

  useEffect(() => {
    if (selectedStockReturnEdit) {
      // insert info

      console.log('oho', selectedStockReturnEdit)
      setEditMode(false);

      setPoId(selectedStockReturnEdit.stId);
      setPoStatus(selectedStockReturnEdit.status);
      setSelectedSourceOutletId(selectedStockReturnEdit.sourceOutletId);
      setSelectedTargetOutletId(selectedStockReturnEdit.targetOutletId);
      setSelectedTargetOutletId(selectedStockReturnEdit.supplierId);
      setDate(selectedStockReturnEdit.estimatedArrivalDate);
      setCreatedDate(selectedStockReturnEdit.createdAt);
      setSelectedSupplierId(selectedStockReturnEdit.supplierId);

      if (selectedStockReturnEdit.stItems) {
        setPoItems(selectedStockReturnEdit.stItems);

        // delay the state update
        setTimeout(() => {
          setPoItems(selectedStockReturnEdit.stItems);
        }, 100);
      }
    } else {
      // designed to always mounted, thus need clear manually...

      setEditMode(false);

      if (stockReturnsProduct.length > 0) {
        // setPoId(`ST${moment().format('MMM').toUpperCase() + moment().format('YY') + (stockTransfers.length + 1).toString().padStart(4, '0')}`);
        setPoId(
          `SR${(stockReturnsProduct.length + 1).toString().padStart(4, "0")}`
        );
      }
      setPoStatus(STOCK_TRANSFER_STATUS.CREATED);
      if(allOutlets && allOutlets[0] && allOutlets[0].uniqueId){
        setSelectedSourceOutletId(allOutlets[0].uniqueId);
        setSelectedTargetOutletId(allOutlets[0].uniqueId);
      }
      suppliersProduct.length > 0 &&
        setSelectedSupplierId(suppliersProduct[0].uniqueId);
      setDate(Date.now());

      if (outletItems.length > 0) {
        setPoItems([
          {
            outletSupplyItemId: outletItems[0].uniqueId,
            name: outletItems[0].name,
            sku: outletItems[0].sku,
            unit: "",
            skuMerchant: outletItems[0].skuMerchant,
            quantity: outletItems[0].stockCount || 0,
            transferQuantity: 0,
            balance: 0,
            price: outletItems[0].price,
            totalPrice: 0,

            supplyItem: outletItems[0],
          },
        ]);
      }

      // if (outletSupplyItems.length > 0 && Object.keys(allOutletsSupplyItemsDict).length > 0) {
      // if (outletSupplyItems.length > 0) {
      //   setPoItems([
      //     {
      //       outletSupplyItemId: outletSupplyItems[0].uniqueId,
      //       name: outletSupplyItems[0].name,
      //       sku: outletSupplyItems[0].sku,
      //       quantity: outletSupplyItems[0].quantity,
      //       transferQuantity: 0,
      //       price: outletSupplyItems[0].price,
      //       totalPrice: 0,
      //     }
      //   ]);
      // }
      // else {
      //   setPoItems([
      //     {
      //       outletSupplyItemId: '',
      //       name: '',
      //       sku: '',
      //       quantity: 0,
      //       transferQuantity: 0,
      //       price: 0,
      //       totalPrice: 0,
      //     }
      //   ]);
      // }
    }
  }, [selectedStockReturnEdit, addStockTransfer]);

  useEffect(() => {
    setSupplierDropdownList(
      suppliersProduct.map((supplier) => ({
        label: supplier.name,
        value: supplier.uniqueId,
      }))
    );

    if (selectedSupplierId === "" && suppliersProduct.length > 0) {
      setSelectedSupplierId(suppliersProduct[0].uniqueId);
    }
    // console.log('1');
  }, [suppliersProduct]);

  useEffect(() => {
    if (selectedStockReturnEdit === null && stockReturnsProduct.length > 0) {
      // setPoId(`ST${moment().format('MMM').toUpperCase() + moment().format('YY') + (stockTransfers.length + 1).toString().padStart(4, '0')}`);
      setPoId(
        `ST${(stockReturnsProduct.length + 1).toString().padStart(4, "0")}`
      );
    }
  }, [stockReturnsProduct]);

  useEffect(() => {
    if (outletItems.length > 0) {
      // setPoItems([
      //   {
      //     outletSupplyItemId: outletSupplyItems[0].uniqueId,
      //     name: outletSupplyItems[0].name,
      //     sku: outletSupplyItems[0].sku,
      //     skuMerchant: outletSupplyItems[0].skuMerchant,
      //     quantity: outletSupplyItems[0].quantity,
      //     transferQuantity: outletSupplyItems[0].transferQuantity,
      //     balance: outletSupplyItems[0].balance,
      //     price: outletSupplyItems[0].price,
      //     totalPrice: 0,
      //     supplyItem: supplyItems[0],
      //   }
      // ]);
      // delay the state updating
      // setTimeout(() => {
      //   setPoItems([
      //     {
      //       outletSupplyItemId: outletSupplyItems[0].uniqueId,
      //       name: outletSupplyItems[0].name,
      //       sku: outletSupplyItems[0].sku,
      //       skuMerchant: outletSupplyItems[0].skuMerchant,
      //       quantity: outletSupplyItems[0].quantity,
      //       transferQuantity: outletSupplyItems[0].transferQuantity,
      //       balance: outletSupplyItems[0].balance,
      //       price: outletSupplyItems[0].price,
      //       totalPrice: 0,
      //       supplyItem: supplyItems[0],
      //     }
      //   ]);
      // }, 500);
    } else {
      setPoItems([
        {
          outletSupplyItemId: "",
          name: "",
          sku: "",
          unit: "",
          skuMerchant: "",
          quantity: 0,
          transferQuantity: 0,
          price: 0,
          totalPrice: 0,

          supplyItem: null,
        },
      ]);
    }
  }, [selectedSourceOutletId]);

  useEffect(() => {
    var outletItemsTemp = allOutletsItems.filter((outletSupplyItem) => {
      if (
        outletSupplyItem.outletId === selectedSourceOutletId &&
        (outletSupplyItem.supplierId === selectedSupplierId ||
          selectedSupplierId === "ALL")
        // outletSupplyItem.quantity > 0
      ) {
        return true;
      } else {
        return false;
      }
    });

    setOutletItems(outletItemsTemp);

    if (outletItemsTemp.length > 0) {
      setPoItems([
        {
          outletSupplyItemId: outletItemsTemp[0].uniqueId,
          name: outletItemsTemp[0].name,
          sku: outletItemsTemp[0].sku,
          unit: "",
          skuMerchant: outletItemsTemp[0].skuMerchant,
          quantity: outletItemsTemp[0].stockCount || 0,
          transferQuantity: 0,
          balance: 0,
          price: outletItemsTemp[0].price,
          totalPrice: 0,

          supplyItem: outletItemsTemp[0],
        },
      ]);
    }
  }, [
    // allOutletsSupplyItems,
    selectedSourceOutletId,
    selectedSupplierId,

    allOutletsItems,
    selectedSupplierId,
  ]);

  useEffect(() => {
    const outletDropdownListTemp = allOutlets.map((outlet) => ({
      label: outlet.name,
      value: outlet.uniqueId,
    }));

    setTargetOutletDropdownList(outletDropdownListTemp);

    if (selectedTargetOutletId === "" && allOutlets.length > 0) {
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
      setSelectedSourceOutletId(allOutlets[0].uniqueId);
    }
  }, [allOutlets]);

  useEffect(() => {
    setOutletSupplyItemDropdownList(
      outletItems.map((outletSupplyItem) => {
        // if (selectedSupplierId === supplyItem.supplierId) {
        //   return { label: supplyItem.name, value: supplyItem.uniqueId };
        // }

        return {
          label: outletSupplyItem.name,
          value: outletSupplyItem.uniqueId,
        };
      })
    );

    if (
      outletItems.length > 0 &&
      poItems.length === 1 &&
      poItems[0].outletSupplyItemId === ""
    ) {
      setPoItems([
        {
          outletSupplyItemId: outletItems[0].uniqueId,
          name: outletItems[0].name,
          sku: outletItems[0].sku,
          unit: "",
          skuMerchant: outletItems[0].skuMerchant,
          quantity: outletItems[0].stockCount || 0,
          transferQuantity: 0,
          balance: 0,
          price: outletItems[0].price,
          totalPrice: 0,

          supplyItem: outletItems[0],
        },
      ]);
    } else if (
      poItems[0].outletSupplyItemId !== ""
      // &&
      // Object.keys(allOutletsSupplyItemsDict).length > 0
    ) {
      if (
        selectedSourceOutletIdPrev.length > 0 &&
        selectedSourceOutletIdPrev !== selectedSourceOutletId
      ) {
        // reset current outlet supply items

        setPoItems([
          {
            outletSupplyItemId: outletItems[0].uniqueId,
            name: outletItems[0].name,
            unit: "",
            sku: outletItems[0].sku,
            skuMerchant: outletItems[0].skuMerchant,
            quantity: outletItems[0].stockCount || 0,
            transferQuantity: 0,
            balance: 0,
            price: outletItems[0].price,
            totalPrice: 0,

            supplyItem: outletItems[0],
          },
        ]);

        // disabled first, outletSupplyItems might slow to retrieve
        // setSelectedSourceOutletIdPrev(selectedSourceOutletId);
      } else {
        var poItemsTemp = [...poItems];

        for (var i = 0; i < poItemsTemp.length; i++) {
          poItemsTemp[i] = {
            ...poItemsTemp[i],
            // quantity: allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId] ? allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId].quantity : 0, // check if the supply item sku for this outlet existed | might changed in real time
            // price: allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId] ? allOutletsSupplyItemsDict[poItemsTemp[i].outletSupplyItemId].price : 0, // might changed in real time
            quantity: allOutletsItems.find(
              (item) => item.uniqueId === poItemsTemp[i].outletSupplyItemId
            )
              ? allOutletsItems.find(
                (item) => item.uniqueId === poItemsTemp[i].outletSupplyItemId
              ).stockCount || 0
              : 0,
            price: allOutletsItems.find(
              (item) => item.uniqueId === poItemsTemp[i].outletSupplyItemId
            )
              ? allOutletsItems.find(
                (item) => item.uniqueId === poItemsTemp[i].outletSupplyItemId
              ).price || 0
              : 0,
          };
        }

        setPoItems(poItemsTemp);
      }
    }
  }, [
    // outletSupplyItems,
    // allOutletsSupplyItemsDict,
    // supplyItems,
    outletItems,
    allOutletsItems,
    selectedSourceOutletIdPrev,
  ]);

  useEffect(() => {
    // console.log('balance');
    // console.log(poItems.reduce((accum, poItem) => accum + poItem.balance, 0));
    setSubtotal(poItems.reduce((accum, poItem) => accum + poItem.balance, 0));
  }, [poItems]);

  useEffect(() => {
    // console.log('subtotal');
    // console.log(poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0));
    setSubtotal(
      poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0)
    );
  }, [poItems]);

  // useEffect(() => {
  //   // console.log('taxTotal');
  //   // console.log(subtotal * selectedSupplier.taxRate);
  //   setTaxTotal(subtotal * selectedSupplier.taxRate);
  // }, [subtotal]);

  useEffect(() => {
    // console.log('finalTotal');
    // console.log((subtotal - discountTotal) + taxTotal);
    setFinalTotal(subtotal - discountTotal + taxTotal);
  }, [subtotal, discountTotal, taxTotal]);

  useEffect(() => {
    requestStoragePermission();

    setPoId(nanoid());
  }, []);

  //////////////////////////////////////////////////////////////////////

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false
  // });

  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus
  );

  // const [outletDropdownList, setOutletDropdownList] = useState([]);
  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  const [openSS, setOpenSS] = useState(false)
  const [openDS, setOpenDS] = useState(false)
  const [openP, setOpenP] = useState(false)

  const [openList, setOpenList] = useState([]);

  // var outletNames = [];

  // for (var i = 0; i < allOutlets.length; i++) {
  //   for (var j = 0; j < selectedOutletList.length; j++) {
  //     if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
  //       outletNames.push(allOutlets[i].name);
  //       break;
  //     }
  //   }
  // }

  // useEffect(() => {
  //   setOutletDropdownList(
  //     allOutlets.map((item) => {
  //       return { label: item.name, value: item.uniqueId };
  //     })
  //   );
  // }, [allOutlets]);

  var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));

  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.outletSelectDropdownView = () => {
  //       return (
  //         <View
  //           style={{
  //             flexDirection: "row",
  //             alignItems: "center",
  //             borderRadius: 8,
  //             width: 200,
  //             backgroundColor: "white",
  //           }}
  //         >
  //           {currOutletId.length > 0 &&
  //             allOutlets.find((item) => item.uniqueId === currOutletId) ? (
  //             <MultiSelect
  //               clearable={false}
  //               singleSelect={true}
  //               defaultValue={currOutletId}
  //               placeholder={"Choose Outlet"}
  //               onChange={(value) => {
  //                 if (value) {
  //                   // if choose the same option again, value = ''
  //                   MerchantStore.update((s) => {
  //                     s.currOutletId = value;
  //                     s.currOutlet =
  //                       allOutlets.find(
  //                         (outlet) => outlet.uniqueId === value
  //                       ) || {};
  //                   });
  //                 }

  //                 CommonStore.update((s) => {
  //                   s.shiftClosedModal = false;
  //                 });
  //               }}
  //               options={targetOutletDropdownListTemp}
  //               className="msl-varsHEADER"
  //             />
  //           ) : (
  //             <ActivityIndicator size={"small"} color={Colors.whiteColor} />
  //           )}

  //           {/* <Select

  //             placeholder={"Choose Outlet"}
  //             onChange={(items) => {
  //               setSelectedOutletList(items);
  //             }}
  //             options={outletDropdownList}
  //             isMulti
  //           /> */}
  //         </View>
  //       );
  //     };
  //   });
  // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
        style={{
          width: 124,
          height: 26,
        }}
        resizeMode="contain"
        source={require('../assets/image/logo.png')}
      /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Stock Return
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo Manager")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
            style={{
              width: windowHeight * 0.05,
            height: windowHeight * 0.05,
              alignSelf: 'center',
            }}
            source={require('../assets/image/profile-pic.jpg')}
          /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const requestStoragePermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: "KooDoo Merchant Storage Permission",
          message: "KooDoo Merchant App needs access to your storage ",
          buttonNegative: "Cancel",
          buttonPositive: "OK",
        }
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        // console.log("Storage permission granted");
      } else {
        // console.log("Storage permission denied");
      }
    } catch (err) {
      console.warn(err);
    }
  };

  const convertDataToExcelFormat = () => {
    var excelData = [];

    for (var i = 0; i < stockList.length; i++) {
      for (var j = 0; j < stockList[i].stItems.length; j++) {
        var excelRow = {
          'Transfer Product': stockList[i].stItems[j].name,
          Instock: stockList[i].stItems[j].quantity ? stockList[i].stItems[j].quantity.toFixed(0) : '0',
          'Transfer Quantity': stockList[i].stItems[j].transferQuantity ? stockList[i].stItems[j].transferQuantity.toFixed(0) : '0',
          Balance: stockList[i].stItems[j].balance ? stockList[i].stItems[j].balance.toFixed(0) : '0',

          // 'Transfer Product': stockList[i].stItems.map(item => item.name).join(','),
          // 'Instock': stockList[i].stItems.map(item => item.quantity || 0).join(','),
          // 'Transfer Quantity': stockList[i].stItems.map(item => `${item.transferQuantity}`).join(','),
          // 'Balance': stockList[i].stItems.map(item => item.balance).join(','),

          'Stock Transfer ID': stockList[i].stId,
          'Created Date': moment(stockList[i].orderDate).format('DD/MM/YYYY'),
          'From Outlet': stockList[i].sourceOutletName,
          'To Supplier': stockList[i].supplierName,
          'Status': stockList[i].status,
        };

        excelData.push(excelRow);
      }
    }

    return excelData;
  };
  const handleExportExcel = () => {
    var wb = XLSX.utils.book_new(),
      ws = XLSX.utils.json_to_sheet(convertDataToExcelFormat());

    XLSX.utils.book_append_sheet(wb, ws, "StockTransfer");
    XLSX.writeFile(wb, "KooDoo_Stock_Retrun_Product.xlsx");
  };

  const renderStockTransferItem = ({ item }) => (
    <TouchableOpacity
      style={{
        backgroundColor: "#ffffff",
        flexDirection: "row",
        paddingVertical: 20,
        paddingHorizontal: 15,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: "#c4c4c4",
        alignItems: "center",
        borderBottomLeftRadius: 5,
        borderBottomRightRadius: 5,
      }}
      onPress={() => {
        CommonStore.update((s) => {
          s.selectedStockReturnEdit = item;
        });

        setStockTransfer(false);
        setAddStockTransfer(true);
      }}
    >
      <Text
        style={{
          fontSize: switchMerchant ? 10 : 14,
          fontFamily: "NunitoSans-Regular",
          width: "20%",
          color: Colors.primaryColor,
          marginRight: 4,
        }}
      >
        ST{item.stId}
      </Text>
      <Text
        style={{
          fontSize: switchMerchant ? 10 : 14,
          fontFamily: "NunitoSans-Regular",
          width: "15%",
          marginHorizontal: 4,
        }}
      >
        {moment(item.createdAt).format("DD MMM YYYY")}
      </Text>
      <Text
        style={{
          fontSize: switchMerchant ? 10 : 14,
          fontFamily: "NunitoSans-Regular",
          width: "21%",
          marginHorizontal: 4,
        }}
      >
        {item.sourceOutletName}
      </Text>
      <Text
        style={{
          fontSize: switchMerchant ? 10 : 14,
          fontFamily: "NunitoSans-Regular",
          width: "21%",
          marginHorizontal: 4,
        }}
      >
        {item.supplierName}
      </Text>
      <View
        style={{
          width: "20%",
          marginLeft: 4,
        }}
      >
        <View
          style={{
            width: switchMerchant ? 90 : 130,
            alignItems: "center",
            borderRadius: 10,
            padding: 10,
            backgroundColor:
              item.status == 0
                ? "#dedede"
                : item.status == 1
                  ? Colors.secondaryColor
                  : Colors.primaryColor,
          }}
        >
          <Text
            style={{
              color:
                item.status == 0
                  ? Colors.blackColor
                  : item.status == 1
                    ? Colors.blackColor
                    : Colors.whiteColor,
              fontSize: switchMerchant ? 10 : 14,
              fontFamily: "NunitoSans-Regular",
            }}
          >
            {/* {item.status == 0 ? "Created" : item.status == 1 ? "Shipped" : "Completed"} */}
            {STOCK_TRANSFER_STATUS_PARSED[item.status]}
            {/* {selectedStockReturnEdit.status} */}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderAddStock = ({ item, index }) => {
    return (
      <View
        style={{
          backgroundColor: "#ffffff",
          flexDirection: "row",
          paddingVertical: 20,
          paddingHorizontal: 10,
          //paddingBottom: 100,
          borderBottomWidth: StyleSheet.hairlineWidth,
          borderBottomColor: "#c4c4c4",
          alignItems: "baseline",
          height: 160,
        }}
      >
        <View style={{ width: "29%" }}>
          <View
            style={{
              width: "85%",
              height: 45,
              borderColor: "#E5E5E5",
              //borderWidth: 1,
              //borderRadius: 5,
              backgroundColor: "white",
              //alignItems: 'center',
              // justifyContent: "flex-start",
              //paddingLeft: 10,
              shadowOpacity: 0,
              shadowColor: "#000",
              shadowOffset: {
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 2,
            }}
          >
            {console.log('poItems[index].outletSupplyItemId', poItems[index].outletSupplyItemId)}
            {outletSupplyItemDropdownList.find(
              (dropdownItem) =>
                dropdownItem.value === poItems[index].outletSupplyItemId
            ) ? (
              <DropDownPicker
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: 250,
                  height: 40,
                  borderRadius: 10,
                  borderWidth: 1,
                  borderColor: "#E5E5E5",
                  flexDirection: "row",
                }}
                dropDownContainerStyle={{
                  width: 250,
                  height: 100,
                  backgroundColor: Colors.fieldtBgColor,
                  borderColor: "#E5E5E5",
                }}
                labelStyle={{
                  marginLeft: 5,
                  flexDirection: "row",
                }}
                textStyle={{
                  fontSize: 14,
                  fontFamily: 'NunitoSans-Regular',

                  marginLeft: 5,
                  paddingVertical: 10,
                  flexDirection: "row",

                  flex: 1, 
                  overflow: 'hidden', 
                  textOverflow: 'ellipsis', 
                  whiteSpace: 'nowrap',
                }}
                selectedItemContainerStyle={{
                  flexDirection: "row",
                }}

                showArrowIcon={true}
                ArrowDownIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-down-outline"
                  />
                )}
                ArrowUpIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-up-outline"
                  />
                )}
                listMode="SCROLLVIEW"
                scrollViewProps={{
                  nestedScrollEnabled: true,
                }}
                showTickIcon={true}
                TickIconComponent={({ press }) => (
                  <Ionicon
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    color={
                      press ? Colors.fieldtBgColor : Colors.primaryColor
                    }
                    name={'md-checkbox'}
                    size={25}
                  />
                )}
                dropDownDirection="BOTTOM"
                items={outletSupplyItemDropdownList}
                value={poItems[index].name}
                placeholder="Outlet Supply Items"
                placeholderStyle={{
                  color: Colors.fieldtTxtColor,
                  // marginTop: 15,
                }}
                onChangeItem={(value) => {
                  if (value) {
                    const supplyItem = allOutletsItems.find(
                      (findItem) => findItem.uniqueId === value.value
                    );

                    setPoItems(
                      poItems.map((poItem, i) =>
                        i === index
                          ? {
                            ...poItem,
                            outletSupplyItemId: value,
                            name: supplyItem.name,
                            sku: supplyItem.sku,
                            unit: "",
                            quantity: supplyItem.stockCount || 0,
                            transferQuantity: 0,
                            price: supplyItem.price,
                            totalPrice: 0,

                            supplyItem: supplyItem,
                          }
                          : poItem
                      )
                    );
                  }
                }}
                searchable={true}
                open={openList[index]}
                setOpen={(value) => {
                  setOpenList((prevOpenList) => {
                    const newOpenList = [...prevOpenList];
                    newOpenList[index] = value;
                    return newOpenList;
                  });
                }}
              />
            ) : (
              // <Text style={{ width: '100%', color: "#949494", marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
              //   {poItems[index].name || '-'}
              //   {/* {poItems[index].skuMerchant || poItems[index].sku ? poItems[index].sku : poItems[index].supplyItem.skuMerchant || poItems[index].supplyItem.skuMerchant ? poItems[index].supplyItem.skuMerchant : poItems[index].sku } */}
              // </Text>
              <></>
            )}
          </View>
        </View>

        <Text
          style={{
            width: "14%",
            color: "#949494",
            marginLeft: 10,
            fontFamily: "NunitoSans-Regular",
            fontSize: switchMerchant ? 10 : 14,
          }}
        >
          {poItems[index].skuMerchant || "-"}
          {/* {poItems[index].skuMerchant || poItems[index].sku ? poItems[index].sku : poItems[index].supplyItem.skuMerchant || poItems[index].supplyItem.skuMerchant ? poItems[index].supplyItem.skuMerchant : poItems[index].sku } */}
        </Text>

        <Text
          style={{
            width: "9%",
            color: "#949494",
            fontFamily: "NunitoSans-Regular",
            fontSize: switchMerchant ? 10 : 14,
          }}
        >
          {poItems[index].quantity}
        </Text>

        <View style={{ width: "11%" }}>
          {!selectedStockReturnEdit ? (
            // || (selectedStockReturnEdit && selectedStockReturnEdit.status !== STOCK_TRANSFER_STATUS.COMPLETED)
            <TextInput
              editable={true}
              underlineColorAndroid={Colors.fieldtBgColor}
              style={{
                backgroundColor: Colors.fieldtBgColor,
                width: switchMerchant ? 55 : 80,
                height: 35,
                borderRadius: 5,
                padding: 5,
                borderWidth: 1,
                borderColor: "#E5E5E5",
                paddingLeft: 10,
                fontFamily: "NunitoSans-Regular",
                fontSize: switchMerchant ? 10 : 14,
              }}
              placeholder={"0"}
              placeholderStyle={{
                fontFamily: "NunitoSans-Regular",
                fontSize: switchMerchant ? 10 : 14,
              }}
              placeholderTextColor={Platform.select({ ios: "#a9a9a9" })}
              keyboardType={"decimal-pad"}
              onChangeText={(text) => {
                // setState({ itemName: text });
                setPoItems(
                  poItems.map((poItem, i) =>
                    i === index
                      ? {
                        ...poItem,
                        transferQuantity:
                          text.length > 0
                            ? parseInt(text) >= poItem.quantity
                              ? window.confirm("Quantity is more than Stock")
                              : parseInt(text)
                            : 0, // will cause bugs, some outlet item usage consume 0.01, 0.05, etc
                        //balance: poItem.quantity - parseInt(text),
                        balance:
                          parseInt(text) < poItem.quantity
                            ? poItem.quantity - parseInt(text)
                            : 0,
                        totalPrice: parseInt(text) * poItem.price,
                      }
                      : poItem
                  )
                );
              }}
              value={
                poItems[index].transferQuantity
                  ? poItems[index].transferQuantity.toFixed(0)
                  : "0"
              }
            />
          ) : (
            <TextInput
              editable={false}
              underlineColorAndroid={Colors.fieldtBgColor}
              style={{
                backgroundColor: Colors.fieldtBgColor,
                width: switchMerchant ? 55 : 80,
                height: 35,
                borderRadius: 5,
                padding: 5,
                borderWidth: 1,
                borderColor: "#E5E5E5",
                paddingLeft: 10,
                fontFamily: "NunitoSans-Regular",
                fontSize: switchMerchant ? 10 : 14,
              }}
              placeholder={"0"}
              placeholderStyle={{
                fontFamily: "NunitoSans-Regular",
                fontSize: switchMerchant ? 10 : 14,
              }}
              placeholderTextColor={Platform.select({ ios: "#a9a9a9" })}
              keyboardType={"decimal-pad"}
              onChangeText={(text) => {
                // setState({ itemName: text });
                setPoItems(
                  poItems.map((poItem, i) =>
                    i === index
                      ? {
                        ...poItem,
                        transferQuantity:
                          text.length > 0
                            ? parseInt(text) >= poItem.quantity
                              ? poItem.quantity
                              : parseInt(text)
                            : 0, // will cause bugs, some outlet item usage consume 0.01, 0.05, etc
                        balance: poItem.quantity - parseInt(text),
                        totalPrice: parseInt(text) * poItem.price,
                      }
                      : poItem
                  )
                );
              }}
              value={poItems[index].transferQuantity.toFixed(0)}
            />
          )}
        </View>

        <Text
          style={{
            width: "13%",
            color: "#949494",
            fontFamily: "NunitoSans-Regular",
            fontSize: switchMerchant ? 10 : 14,
          }}
        >
          {/* {allOutletsSupplyItemsDict[poItems[index].outletSupplyItemId] ? allOutletsSupplyItemsDict[poItems[index].outletSupplyItemId].quantity : 0} */}
          {/* {outletSupplyItemsSkuDict[poItems[index].sku] ? outletSupplyItemsSkuDict[poItems[index].sku].quantity : 0} */}
          {allOutletsItems.find(
            (item) => item.uniqueId === poItems[index].outletSupplyItemId
          )
            ? allOutletsItems.find(
              (item) => item.uniqueId === poItems[index].outletSupplyItemId
            ).stockCount || 0
            : 0}
        </Text>
        <Text
          style={{
            width: "10%",
            color: "#949494",
            fontFamily: "NunitoSans-Regular",
            fontSize: switchMerchant ? 10 : 14,
          }}
        >
          {poItems[index].price}
        </Text>
        <Text
          style={{
            width: "11%",
            color: "#949494",
            fontFamily: "NunitoSans-Regular",
            fontSize: switchMerchant ? 10 : 14,
          }}
        >
          {poItems[index].totalPrice}
        </Text>

        <TouchableOpacity
          style={{ width: "3%" }}
          onPress={() => {
            setPoItems([
              ...poItems.slice(0, index),
              ...poItems.slice(index + 1),
            ]);
          }}
        >
          <Icon
            name="trash-2"
            size={switchMerchant ? 15 : 20}
            color="#eb3446"
          />
        </TouchableOpacity>
      </View>
    );
  };

  const createStockTransfer = (
    stockTransferStatus = STOCK_TRANSFER_STATUS.CREATED
  ) => {
    // if (poItems.find(poItem => poItem.outletSupplyItemId)){
    //   for(var i = 0; i < poItems.length; i++){
    //     for(var j = 0; j < poItems.length; j++){
    //       if(poItems[i].outletSupplyItemId === poItems[j].outletSupplyItemId) {
    //         window.confirm(
    //           'Error',
    //           'Same Supply Item.',
    //           [
    //             {
    //               text: "OK", onPress: () => {
    //               }
    //             }
    //           ],
    //         );
    //         return;
    //       }
    //     }
    //   }
    // }

    if (selectedStockReturnEdit === null) {
      var body = {
        stId: poId,
        stItems: poItems.map(item => ({
          ...item,
          transferQuantity: item.transferQuantity ? item.transferQuantity : 0,
        })),
        tax: +parseFloat(taxTotal).toFixed(2),
        discount: +parseFloat(discountTotal).toFixed(2),
        totalPrice: +parseFloat(subtotal).toFixed(2),
        finalTotal: +parseFloat(finalTotal).toFixed(2),
        estimatedArrivalDate: moment(date).valueOf(),

        // status: STOCK_TRANSFER_STATUS.CREATED,
        status: stockTransferStatus,

        sourceOutletId: selectedSourceOutletId,
        targetOutletId: selectedTargetOutletId,
        sourceOutletName: allOutlets.find(
          (outlet) => outlet.uniqueId === selectedSourceOutletId
        ).name,
        targetOutletName: "",

        supplierId: selectedSupplierId,
        supplierName:
          selectedSupplierId === "ALL"
            ? "All"
            : suppliersProduct.find(
              (supplier) => supplier.uniqueId === selectedSupplierId
            ).name,

        merchantId: merchantId,
        remarks: "",

        staffName: userName,
        staffId: userId,
      };

      // console.log(body);

      APILocal.createStockReturnProduct({ body: body }).then((result) => {
        // ApiClient.POST(API.createStockReturnProduct, body).then((result) => {
        // console.log("Result", result)
        if (result && result.status === "success") {
          if (window.confirm("Stock return has been created")) {
            setAddStockTransfer(false);
            setStockTransfer(true);
          };
        } else {
          window.confirm(
            "Error",
            "Failed to create stock return",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
        }
      });
    } else {
      var body = {
        stId: poId,
        stItems: poItems.map(item => ({
          ...item,
          transferQuantity: item.transferQuantity ? item.transferQuantity : 0,
        })),
        tax: +parseFloat(taxTotal).toFixed(2),
        discount: +parseFloat(discountTotal).toFixed(2),
        totalPrice: +parseFloat(subtotal).toFixed(2),
        finalTotal: +parseFloat(finalTotal).toFixed(2),
        estimatedArrivalDate: moment(date).valueOf(),

        // status: STOCK_TRANSFER_STATUS.CREATED,
        status: stockTransferStatus,

        sourceOutletId: selectedSourceOutletId,
        targetOutletId: selectedTargetOutletId,
        sourceOutletName: allOutlets.find(
          (outlet) => outlet.uniqueId === selectedSourceOutletId
        ).name,
        targetOutletName: "",

        supplierId: selectedSupplierId,
        supplierName:
          selectedSupplierId === "ALL"
            ? "All"
            : suppliersProduct.find(
              (supplier) => supplier.uniqueId === selectedSupplierId
            ).name,

        merchantId: merchantId,
        remarks: "",

        staffName: userName,
        staffId: userId,

        uniqueId: selectedStockReturnEdit.uniqueId,
      };

      // console.log(body);

      APILocal.updateStockReturnProduct({ body: body }).then((result) => {
        // ApiClient.POST(API.updateStockReturnProduct, body).then((result) => {
        // console.log("Result", result)
        if (result && result.status === "success") {
          window.confirm(
            "Success",
            "Stock return has been updated",
            [
              {
                text: "OK",
                onPress: () => {
                  // setState({ addStockTransfer: false, stockTransfer: true })
                  setAddStockTransfer(false);
                  setStockTransfer(true);
                },
              },
            ],
            { cancelable: false }
          );
        } else {
          window.confirm(
            "Error",
            "Failed to update stock return",
            [{ text: "OK", onPress: () => { } }],
            { cancelable: false }
          );
        }
      });
    }
  };

  return (
    // <UserIdleWrapper disabled={!isMounted}>
    <View style={[styles.container]}>
      <View style={[styles.sidebar]}>
        <SideBar
          navigation={props.navigation}
          selectedTab={3}
          expandInventory={true}
        />
      </View>
      <View style={{ height: windowHeight, flex: 9 }}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{}}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}
        >
          <ScrollView horizontal={true} showsHorizontalScrollIndicator={true}>
            <View 
            //style={styles.content}
            >
              <Modal
                supportedOrientations={["portrait", "landscape"]}
                style={
                  {
                    // flex: 1
                  }
                }
                visible={exportModal}
                transparent={true}
                animationType={"fade"}
              >
                <View
                  style={{
                    flex: 1,
                    backgroundColor: Colors.modalBgColor,
                    alignItems: "center",
                    justifyContent: "center",
                    top: 0,
                  }}
                >
                  <View
                    style={{
                      height: windowWidth * 0.08,
                      width: windowWidth * 0.18,
                      backgroundColor: Colors.whiteColor,
                      borderRadius: 12,
                      padding: windowWidth * 0.03,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <TouchableOpacity
                      disabled={isLoading}
                      style={{
                        position: "absolute",
                        right: windowWidth * 0.015,
                        top: windowWidth * 0.01,

                        elevation: 1000,
                        zIndex: 1000,
                      }}
                      onPress={() => {
                        setExportModal(false);
                      }}
                    >
                      <AntDesign
                        name="closecircle"
                        size={switchMerchant ? 15 : 25}
                        color={Colors.fieldtTxtColor}
                      />
                    </TouchableOpacity>
                    <View
                      style={{
                        alignItems: "center",
                        top: "20%",
                        position: "absolute",
                      }}
                    >
                      <Text
                        style={{
                          fontFamily: "NunitoSans-Bold",
                          textAlign: "center",
                          fontSize: switchMerchant ? 18 : 24,
                        }}
                      >
                        Download Report
                      </Text>
                    </View>
                    <View style={{ top: switchMerchant ? "14%" : "10%" }}>
                      {/* <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold' }}>Email Address:</Text>
                  <TextInput
                    underlineColorAndroid={Colors.fieldtBgColor}
                    style={{
                      backgroundColor: Colors.fieldtBgColor,
                      width: switchMerchant ? 240 : 370,
                      height: switchMerchant ? 35 : 50,
                      borderRadius: 5,
                      padding: 5,
                      marginVertical: 5,
                      borderWidth: 1,
                      borderColor: '#E5E5E5',
                      paddingLeft: 10,
                      fontSize: switchMerchant ? 10 : 16
                    }}
                    autoCapitalize='none'
                    placeholderStyle={{ padding: 5 }}
                    placeholder="Enter your email"
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    onChangeText={(text) => {
                      setExportEmail(text);
                    }}
                    value={exportEmail}
                  />
                  <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', marginTop: 15 }}>Send As:</Text> */}

                      <View
                        style={{
                          alignItems: "center",
                          justifyContent: "center",
                          //top: '10%',
                          flexDirection: "row",
                          marginTop: 30,
                        }}
                      >
                        <TouchableOpacity
                          disabled={isLoading}
                          style={{
                            justifyContent: "center",
                            flexDirection: "row",
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: "#4E9F7D",
                            borderRadius: 5,
                            width: switchMerchant ? 100 : 100,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: "center",
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginRight: 15,
                          }}
                          onPress={() => {
                            // if (exportEmail.length > 0) {
                            //   CommonStore.update(s => {
                            //     s.isLoading = true;
                            //   });
                            //   setIsLoadingExcel(true);
                            // const excelData = convertDataToExcelFormat();

                            //   generateEmailReport(
                            //     EMAIL_REPORT_TYPE.EXCEL,
                            //     excelData,
                            //     'KooDoo Stock Return Report',
                            //     'KooDoo Stock Return Report.xlsx',
                            //     `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                            //     exportEmail,
                            //     'KooDoo Stock Return Report',
                            //     'KooDoo Stock Return Report',
                            //     () => {
                            //       CommonStore.update(s => {
                            //         s.isLoading = false;
                            //       });
                            //       setIsLoadingExcel(false);

                            //      window.confirm('Success', 'Report will be sent to the email address shortly');

                            //       setExportModal(false);
                            //     },
                            //   );
                            // }
                            // else {
                            //   window.confirm('Info', 'Invalid email address');
                            // }
                            if (convertDataToExcelFormat() && convertDataToExcelFormat().length > 0) {
                              handleExportExcel();
                            }
                            else {
                              alert('Info, Empty data to export.');

                              CommonStore.update(s => {
                                s.isLoading = false;
                              });
                              setIsLoadingExcel(false);
                            }
                          }}
                        >
                          {isLoadingExcel ? (
                            <ActivityIndicator
                              size={"small"}
                              color={Colors.whiteColor}
                            />
                          ) : (
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: "NunitoSans-Bold",
                              }}
                            >
                              EXCEL
                            </Text>
                          )}
                        </TouchableOpacity>

                        <CSVLink
                          style={{
                            justifyContent: "center",
                            alignContent: "center",
                            alignItems: "center",
                            display: "inline-block",
                            flexDirection: "row",
                            textDecoration: "none",
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: "#4E9F7D",
                            borderRadius: 5,
                            width: switchMerchant ? 100 : 100,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: "center",
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          }}
                          data={convertDataToExcelFormat()}
                          filename="KooDoo_Stock_Retrun_Product.csv"
                        >
                          <View
                            style={{
                              width: "100%",
                              height: "100%",
                              alignContent: "center",
                              alignItems: "center",
                              alignSelf: "center",
                              justifyContent: "center",
                            }}
                          >
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: "NunitoSans-Bold",
                              }}
                            >
                              CSV
                            </Text>
                          </View>
                        </CSVLink>
                      </View>
                    </View>
                  </View>
                </View>
              </Modal>

              {stockTransfer ? (
                // <ScrollView
                //   scrollEnabled={switchMerchant}
                //   horizontal={true}
                //   contentContainerStyle={{ paddingRight: switchMerchant ? '7%' : 0, }}
                // >
                <ScrollView
                  nestedScrollEnabled={true}
                  contentContainerStyle={{
                    paddingBottom: switchMerchant ? "20%" : "5%",
                  }}
                  //style={{ backgroundColor: 'red' }}
                  scrollEnabled={switchMerchant}
                >
                  <View
                    style={{
                      height: windowHeight * 0.73,
                      alignItems: "center",
                      marginLeft: windowWidth * 0.02,
                      // width: windowWidth * 0.87,
                    }}
                  >
                    <View
                      style={{ width: windowWidth * 0.87 }}
                    >
                      <View
                        style={{
                          flexDirection: "row",
                          justifyContent: "space-between",
                        }}
                      >
                        <View
                          style={{ alignItems: "center", flexDirection: "row" }}
                        >
                          <Text
                            style={{
                              //fontSize: 30,
                              fontSize: switchMerchant ? 20 : 26,
                              fontFamily: "NunitoSans-Bold",
                            }}
                          >
                            {stockList.length} Stock Return
                          </Text>
                        </View>

                        <View style={{ flexDirection: "row" }}>
                          <View style={{ flexDirection: "row" }}>
                            {/* {isTablet() && ( */}
                            <View style={{ alignItem: "center" }}>
                              <TouchableOpacity
                                style={{
                                  justifyContent: "center",
                                  flexDirection: "row",
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  backgroundColor: "#4E9F7D",
                                  borderRadius: 5,
                                  //width: 160,
                                  paddingHorizontal: 10,
                                  height: switchMerchant ? 35 : 40,
                                  alignItems: "center",
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                }}
                                onPress={() => {
                                  setExportModal(true);
                                }}
                              >
                                <View
                                  style={{
                                    flexDirection: "row",
                                    alignItems: "center",
                                  }}
                                >
                                  <Icon
                                    name="download"
                                    size={switchMerchant ? 10 : 20}
                                    color={Colors.whiteColor}
                                  />
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      marginLeft: 5,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: "NunitoSans-Bold",
                                    }}
                                  >
                                    DOWNLOAD
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                            {/* )} */}
                          </View>

                          <View style={{ flexDirection: "row" }}>
                            {/* {isTablet() && ( */}
                            <View
                              style={{
                                alignSelf: "flex-start",
                              }}
                            >
                              <TouchableOpacity
                                style={{
                                  justifyContent: "center",
                                  flexDirection: "row",
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  backgroundColor: "#4E9F7D",
                                  borderRadius: 5,
                                  //width: 160,
                                  paddingHorizontal: switchMerchant ? 5 : 10,
                                  height: switchMerchant ? 35 : 40,
                                  alignItems: "center",
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                  marginHorizontal: switchMerchant ? 10 : 15,
                                }}
                                onPress={() => {
                                  CommonStore.update((s) => {
                                    s.selectedStockReturnEdit = null;
                                  });

                                  setStockTransfer(false);
                                  setAddStockTransfer(true);
                                }}
                              >
                                <View
                                  style={{
                                    flexDirection: "row",
                                    alignItems: "center",
                                  }}
                                >
                                  <AntDesign
                                    name="pluscircle"
                                    size={switchMerchant ? 10 : 20}
                                    color={Colors.whiteColor}
                                  />
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      marginLeft: 5,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: "NunitoSans-Bold",
                                    }}
                                  >
                                    STOCK RETURN
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                            {/* )} */}
                          </View>

                          <View style={{ flexDirection: "row" }}>
                            <View
                              style={[
                                {
                                  height: 40,
                                },
                              ]}
                            >
                              <View
                                style={{
                                  width:
                                    windowWidth <= 1024
                                      ? 160
                                      : 250,
                                  height: switchMerchant ? 35 : 40,
                                  backgroundColor: "white",
                                  borderRadius: 5,
                                  // marginLeft: '53%',
                                  flexDirection: "row",
                                  alignContent: "center",
                                  alignItems: "center",
                                  alignSelf: "flex-end",
                                  shadowColor: "#000",
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 3,
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                }}
                              >
                                <Icon
                                  name="search"
                                  size={switchMerchant ? 13 : 18}
                                  color={Colors.primaryColor}
                                  style={{ marginLeft: 15 }}
                                />
                                <TextInput
                                  editable={!loading}
                                  underlineColorAndroid={Colors.whiteColor}
                                  style={{
                                    width:
                                      windowWidth <= 1024
                                        ? 140
                                        : 220,
                                    fontSize: switchMerchant ? 10 : 15,
                                    fontFamily: "NunitoSans-Regular",
                                    paddingLeft: 5,
                                    height: 45,
                                  }}
                                  clearButtonMode="while-editing"
                                  placeholder="Search"
                                  placeholderTextColor={Platform.select({
                                    ios: "#a9a9a9",
                                  })}
                                  onChangeText={(text) => {
                                    // setSearch(text.trim());
                                    // setList1(false);
                                    // setSearchList(true);
                                    setSearch(text);
                                  }}
                                  value={search}
                                />
                              </View>
                            </View>
                          </View>
                        </View>
                      </View>
                      <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                        <View
                          style={[
                            {
                              //marginRight: Platform.OS === 'ios' ? 0 : 10,
                              // paddingLeft: 15,
                              paddingHorizontal: 15,
                              flexDirection: "row",
                              alignItems: "center",
                              borderRadius: 10,
                              paddingVertical: 10,
                              justifyContent: "center",
                              backgroundColor: Colors.whiteColor,
                              shadowOpacity: 0,
                              shadowColor: "#000",
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              marginTop: 15,
                            },
                          ]}
                        >
                          <View
                            style={{ alignSelf: "center", marginRight: 5 }}
                            onPress={() => {
                              setState({
                                pickerMode: "date",
                                showDateTimePicker: true,
                              });
                            }}
                          >
                            <GCalendar
                              width={switchMerchant ? 15 : 20}
                              height={switchMerchant ? 15 : 20}
                            />
                          </View>

                          <DatePicker
                            selected={moment(rev_date).toDate()}
                            onChange={(date) => {
                              setRev_date(moment(date).startOf("day"));
                              // CommonStore.update(s => {
                              //   s.historyStartDate = moment(date).startOf('day');
                              // });
                            }}
                            maxDate={moment(rev_date1).toDate()}
                          />

                          <Text
                            style={
                              switchMerchant
                                ? { fontSize: 10, fontFamily: "NunitoSans-Regular", marginHorizontal: 10 }
                                : { fontFamily: "NunitoSans-Regular", marginHorizontal: 10 }
                            }
                          >
                            -
                          </Text>

                          <DatePicker
                            selected={moment(rev_date1).toDate()}
                            onChange={(date) => {
                              setRev_date1(moment(date).endOf("day"));
                              // CommonStore.update(s => {
                              //   s.historyEndDate = moment(date).endOf('day');
                              // });
                            }}
                            minDate={moment(rev_date).toDate()}
                          />
                        </View>
                      </View>
                      <View
                        style={{
                          backgroundColor: Colors.whiteColor,
                          width: windowWidth * 0.87,
                          height: windowHeight * 0.72,
                          marginTop: 10,
                          marginHorizontal: 30,
                          marginBottom: 30,
                          alignSelf: "center",
                          borderRadius: 5,
                          shadowOpacity: 0,
                          shadowColor: "#000",
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,

                          zIndex: -5,
                        }}
                      >
                        <View
                          style={{
                            borderTopLeftRadius: 10,
                            borderTopRightRadius: 10,
                            backgroundColor: "#ffffff",
                            flexDirection: "row",
                            paddingVertical: 20,
                            paddingHorizontal: 15,
                            //marginTop: 10,
                            borderBottomWidth: StyleSheet.hairlineWidth,
                          }}
                        >
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 14,
                              width: "20%",
                              alignSelf: "center",
                              fontFamily: "NunitoSans-Bold",
                              marginRight: 4,
                            }}
                          >
                            ID
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 14,
                              width: "15%",
                              alignSelf: "center",
                              fontFamily: "NunitoSans-Bold",
                              marginHorizontal: 4,
                            }}
                          >
                            Created Date
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 14,
                              width: "21%",
                              alignSelf: "center",
                              fontFamily: "NunitoSans-Bold",
                              marginHorizontal: 4,
                            }}
                          >
                            From Outlet
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 14,
                              width: "21%",
                              alignSelf: "center",
                              fontFamily: "NunitoSans-Bold",
                              marginHorizontal: 4,
                            }}
                          >
                            To Supplier
                          </Text>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 10 : 14,
                              width: "20%",
                              alignSelf: "center",
                              fontFamily: "NunitoSans-Bold",
                              marginLeft: 4,
                            }}
                          >
                            Status
                          </Text>
                        </View>

                        <FlatList
                          nestedScrollEnabled={true}
                          showsVerticalScrollIndicator={false}
                          data={stockList.filter((item) => {
                            if (search !== "") {
                              const searchLowerCase = search.toLowerCase();

                              return (
                                item.sourceOutletName
                                  .toLowerCase()
                                  .includes(searchLowerCase) ||
                                item.supplierName
                                  .toLowerCase()
                                  .includes(searchLowerCase)
                              );
                            } else {
                              return true;
                            }
                          })}
                          extraData={stockList.filter((item) => {
                            if (search !== "") {
                              const searchLowerCase = search.toLowerCase();

                              return (
                                item.sourceOutletName
                                  .toLowerCase()
                                  .includes(searchLowerCase) ||
                                item.supplierName
                                  .toLowerCase()
                                  .includes(searchLowerCase)
                              );
                            } else {
                              return true;
                            }
                          })}
                          renderItem={renderStockTransferItem}
                          keyExtractor={(item, index) => String(index)}
                        />
                      </View>
                    </View>
                  </View>
                </ScrollView>
              ) : // </ScrollView>

                null}

              {addStockTransfer ? (
                <View
                  style={
                    {
                      /* height: Dimensions.get("window").height */
                    }
                  }
                >
                  <View
                    style={
                      {
                        /* paddingBottom: windowHeight * 0.3,
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.32,
                      shadowRadius: 3.22,
                      elevation: 1, */
                      }
                    }
                    contentContainerStyle={
                      {
                        // top: Platform.OS === 'ios' ? -keyboardHeight * 0.3 : 0,
                      }
                    }
                  >
                    {/* <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginHorizontal: 0, }}> */}

                    <View
                      style={{
                        width: switchMerchant
                          ? null
                          : windowWidth * 0.886,
                        alignSelf: switchMerchant ? null : "center",
                        marginBottom: switchMerchant ? 5 : 0,
                        marginTop: Platform.OS === "ios" ? 0 : -5,
                        alignItems: "baseline",
                      }}
                    >
                      <TouchableOpacity
                        style={{
                          marginBottom: switchMerchant ? 0 : 10,
                          flexDirection: "row",
                          alignItems: "center",
                          marginTop: switchMerchant ? 0 : 10,
                        }}
                        onPress={() => {
                          CommonStore.update((s) => {
                            s.selectedStockReturnEdit = null;
                          });
                          setStockTransfer(true);
                          setAddStockTransfer(false);
                        }}
                      >
                        <Icon
                          name="chevron-left"
                          size={switchMerchant ? 20 : 30}
                          color={Colors.primaryColor}
                        />
                        <Text
                          style={{
                            fontFamily: "NunitoSans-Bold",
                            fontSize: switchMerchant ? 14 : 17,
                            color: Colors.primaryColor,
                            marginBottom: Platform.OS === "android" ? 2 : 0,
                          }}
                        >
                          Back
                        </Text>
                      </TouchableOpacity>
                    </View>

                    <View
                      style={{
                        backgroundColor: Colors.whiteColor,
                        width: windowWidth * 0.87,
                        height: windowHeight * 0.72,
                        //marginTop: 10,
                        marginHorizontal: 30,
                        marginLeft: switchMerchant ? 31 : 29,
                        marginBottom: 30,
                        alignSelf: "center",
                        //borderRadius: 5,
                        shadowOpacity: 0,
                        shadowColor: "#000",
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                        borderRadius: 5,
                      }}
                    >
                      <ScrollView
                        showsVerticalScrollIndicator={false}
                        style={
                          {
                            // height: Dimensions.get('window').height * 0.75,
                            // backgroundColor: Colors.whiteColor,
                            // borderWidth: 1,
                            // borderColor: '#E5E5E5',
                            // borderRadius: 10,
                            //  shadowOffset: {
                            //   width: 0,
                            //   height: 2,
                            //   },
                            //   shadowOpacity: 0.42,
                            //   shadowRadius: 3.22,
                            //   elevation: 2,
                          }
                        }
                      >
                        <View style={{}}>
                          <View
                            style={{
                              position: "absolute",
                              alignSelf: "flex-end",
                              marginTop: 30,
                              zIndex: 10000,
                            }}
                          >
                            {selectedStockReturnEdit &&
                              selectedStockReturnEdit.status ===
                              STOCK_TRANSFER_STATUS.CREATED ? (
                              // &&
                              // currOutletId === selectedStockReturnEdit.targetOutletId
                              <>
                                <View style={{}}>
                                  <TouchableOpacity
                                    style={{
                                      justifyContent: "center",
                                      flexDirection: "row",
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      backgroundColor: "#4E9F7D",
                                      borderRadius: 5,
                                      width: switchMerchant ? 160 : 230,
                                      paddingHorizontal: 10,
                                      height: switchMerchant ? 35 : 40,
                                      alignItems: "center",
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                      zIndex: -1,
                                      marginRight: 20,
                                      marginBottom: 10,
                                    }}
                                    onPress={() => {
                                      createStockTransfer(
                                        STOCK_TRANSFER_STATUS.COMPLETED
                                      );
                                    }}
                                    disabled={
                                      selectedStockReturnEdit ? false : true
                                    }
                                  >
                                    <Text
                                      style={{
                                        color: Colors.whiteColor,
                                        //marginLeft: 5,
                                        fontSize: switchMerchant ? 10 : 16,
                                        fontFamily: "NunitoSans-Bold",
                                      }}
                                    >
                                      {"MARK COMPLETED"}
                                    </Text>
                                  </TouchableOpacity>
                                </View>
                              </>
                            ) : (
                              <></>
                            )}

                            {selectedStockReturnEdit &&
                              selectedStockReturnEdit.status ===
                              STOCK_TRANSFER_STATUS.COMPLETED ? (
                              <></>
                            ) : (
                              <View style={{}}>
                                <TouchableOpacity
                                  style={{
                                    justifyContent: "center",
                                    flexDirection: "row",
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    backgroundColor: "#4E9F7D",
                                    borderRadius: 5,
                                    width:
                                      selectedStockReturnEdit &&
                                        selectedStockReturnEdit.status ===
                                        STOCK_TRANSFER_STATUS.CREATED &&
                                        currOutletId ===
                                        selectedStockReturnEdit.targetOutletId &&
                                        !switchMerchant
                                        ? 230
                                        : selectedStockReturnEdit &&
                                          selectedStockReturnEdit.status ===
                                          STOCK_TRANSFER_STATUS.CREATED &&
                                          currOutletId ===
                                          selectedStockReturnEdit.targetOutletId &&
                                          switchMerchant
                                          ? 160
                                          : 120,
                                    paddingHorizontal: 10,
                                    height: switchMerchant ? 35 : 40,
                                    alignItems: "center",
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -1,
                                    marginRight: 20,
                                  }}
                                  onPress={() => {
                                    createStockTransfer();
                                  }}
                                >
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      //marginLeft: 5,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: "NunitoSans-Bold",
                                    }}
                                  >
                                    SAVE
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            )}
                          </View>

                          <View
                            style={{
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                              }}
                            >
                              <Text
                                style={{
                                  alignSelf: "center",
                                  marginTop: 30,
                                  fontSize: switchMerchant ? 20 : 40,
                                  fontWeight: "600",
                                }}
                              >
                                Stock Return
                              </Text>
                            </View>
                            <Text
                              style={{
                                alignSelf: "center",
                                fontSize: switchMerchant ? 10 : 16,
                                color: "#adadad",
                              }}
                            >
                              Fill In The Stock Return Information
                            </Text>
                          </View>

                          <View
                            style={{
                              flexDirection: "row",
                              marginTop: 55,
                              justifyContent: "space-between",
                              width: "80%",
                              alignSelf: "center",
                            }}
                          >
                            <View
                              style={{
                                flexDirection: "row",
                                width: "45%",
                                alignItems: "center",
                              }}
                            >
                              <Text
                                style={{
                                  width: "42%",
                                  fontFamily: "NunitoSans-Bold",
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontWeight: "400",
                                }}
                              >
                                ID
                              </Text>
                              <View
                                style={{
                                  width: "58%",
                                  justifyContent: "space-between",
                                  flexDirection: "row",
                                  alignItems: "center",
                                }}
                              >
                                <View
                                  style={{
                                    justifyContent: "center",
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    borderRadius: 5,
                                    height: 35,
                                    width: "90%",
                                    backgroundColor: Colors.fieldtBgColor,
                                    paddingHorizontal: 10,
                                  }}
                                >
                                  {editMode ? (
                                    <TextInput
                                      //editable={false}
                                      style={{
                                        // backgroundColor: Colors.fieldtBgColor,
                                        // borderRadius: 5,
                                        // padding: 5,
                                        // borderWidth: 1,
                                        // borderColor: '#E5E5E5',
                                        //paddingLeft:10,
                                        fontFamily: "NunitoSans-Regular",
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}
                                      placeholder="ID (Max Length 12)"
                                      placeholderStyle={{
                                        fontFamily: "NunitoSans-Regular",
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}
                                      placeholderTextColor={Platform.select({
                                        ios: "#a9a9a9",
                                      })}
                                      onChangeText={(text) => {
                                        setPoId(text);
                                      }}
                                      maxLength={12}
                                    //value={`ST${poId}`}
                                    />
                                  ) : (
                                    <Text
                                      style={{
                                        fontSize: 15,
                                        fontFamily: "NunitoSans-Regular",
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}
                                    >
                                      {`${poId}`}
                                    </Text>
                                  )}
                                </View>
                                {selectedStockReturnEdit ? (
                                  <></>
                                ) : (
                                  <TouchableOpacity
                                    style={{ marginLeft: 5 }}
                                    onPress={() => {
                                      setEditMode(!editMode);
                                    }}
                                  >
                                    {/* <MaterialCommunityIcons name="pencil" size={20} color={Colors.primaryColor} /> */}
                                    <Icon
                                      name="edit"
                                      size={switchMerchant ? 15 : 20}
                                      color={Colors.primaryColor}
                                    />
                                  </TouchableOpacity>
                                )}
                              </View>
                            </View>

                            <View
                              style={{
                                flexDirection: "row",
                                width: "50%",
                                alignItems: "center",
                              }}
                            >
                              <Text
                                style={{
                                  width: "38%",
                                  fontFamily: "NunitoSans-Bold",
                                  fontSize: switchMerchant ? 10 : 14,
                                  fontWeight: "400",
                                }}
                              >
                                Source Store
                              </Text>

                              <View style={{ width: "62%" }}>
                                {targetOutletDropdownList.find(
                                  (item) =>
                                    item.value === selectedSourceOutletId
                                ) ? (
                                  <DropDownPicker
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width: 210,
                                      height: 40,
                                      borderRadius: 10,
                                      borderWidth: 1,
                                      borderColor: "#E5E5E5",
                                      flexDirection: "row",
                                    }}
                                    dropDownContainerStyle={{
                                      width: 210,
                                      backgroundColor: Colors.fieldtBgColor,
                                      borderColor: "#E5E5E5",
                                    }}
                                    labelStyle={{
                                      marginLeft: 5,
                                      flexDirection: "row",
                                    }}
                                    textStyle={{
                                      fontSize: 14,
                                      fontFamily: 'NunitoSans-Regular',

                                      marginLeft: 5,
                                      paddingVertical: 10,
                                      flexDirection: "row",

                                      flex: 1, 
                                      overflow: 'hidden', 
                                      textOverflow: 'ellipsis', 
                                      whiteSpace: 'nowrap',
                                    }}
                                    selectedItemContainerStyle={{
                                      flexDirection: "row",
                                    }}

                                    showArrowIcon={true}
                                    ArrowDownIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-down-outline"
                                      />
                                    )}
                                    ArrowUpIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-up-outline"
                                      />
                                    )}

                                    showTickIcon={true}
                                    TickIconComponent={({ press }) => (
                                      <Ionicon
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        color={
                                          press ? Colors.fieldtBgColor : Colors.primaryColor
                                        }
                                        name={'md-checkbox'}
                                        size={25}
                                      />
                                    )}
                                    dropDownDirection="BOTTOM"
                                    items={targetOutletDropdownList}
                                    value={selectedSourceOutletId}
                                    placeholder={"Select a store"}
                                    placeholderStyle={{
                                      color: Colors.fieldtTxtColor,
                                      // marginTop: 15,
                                    }}
                                    onSelectItem={(item) => {
                                      setSelectedSourceOutletIdPrev(
                                        selectedSourceOutletId
                                      );
                                      setSelectedSourceOutletId(item.value);
                                    }}
                                    open={openSS}
                                    setOpen={setOpenSS}
                                  />
                                ) : (
                                  <></>
                                )}
                              </View>
                              {/* <View style={{ width: '62%' }}>
                          <View style={{ width: switchMerchant ? 170 : 220, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 10 }}>
                            <Text>
                              {selectedSourceOutletId}
                            </Text>
                          </View>
                        </View> */}
                            </View>
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: "row",
                            marginTop: 10,
                            justifyContent: "space-between",
                            alignSelf: "center",
                            marginTop: 50,
                            zIndex: -1,
                            width: "80%",
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              width: "45%",
                              alignItems: "center",
                            }}
                          >
                            {/* <Text style={{ fontSize: 16, width: '40%', textAlign: 'left' }}>Current status</Text>
                        <View style={{ paddingHorizontal: 18, paddingVertical: 10, alignItems: "center", backgroundColor: '#838387', borderRadius: 10, width: '40%' }}>
                          <Text style={{ color: Colors.whiteColor }}>{PURCHASE_ORDER_STATUS_PARSED[poStatus]}</Text>
                        </View> */}

                            <Text
                              style={{
                                fontFamily: "NunitoSans-Bold",
                                fontSize: switchMerchant ? 10 : 14,
                                width: "42%",
                                fontWeight: "400",
                              }}
                            >
                              Created Date
                            </Text>
                            <View
                              style={{ width: "58%", alignItems: "baseline" }}
                            >
                              <View
                                style={{
                                  paddingHorizontal: 10,
                                  borderColor: "#E5E5E5",
                                  borderWidth: 1,
                                  borderRadius: 5,
                                  width: "90.5%",
                                  height: 35,
                                  justifyContent: "space-between",
                                  backgroundColor: Colors.fieldtBgColor,
                                  flexDirection: "row",
                                  alignItems: "center",
                                }}
                              >
                                <GCalendarGrey
                                  width={switchMerchant ? 15 : 20}
                                  height={switchMerchant ? 15 : 20}
                                  style={{ marginRight: 5 }}
                                />
                                <Text
                                  style={{
                                    marginRight: "18%",
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                >
                                  {selectedStockReturnEdit
                                    ? moment(createdDate).format("DD MMM YYYY")
                                    : moment(date1).format("DD MMM YYYY")}
                                </Text>
                              </View>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: "row",
                              width: "50%",
                              alignItems: "center",
                            }}
                          >
                            <Text
                              style={{
                                fontFamily: "NunitoSans-Bold",
                                fontSize: switchMerchant ? 10 : 14,
                                width: "38%",
                                fontWeight: "400",
                              }}
                            >
                              Destination Supplier
                            </Text>
                            <View style={{ width: "62%" }}>
                              {supplierDropdownList.find(
                                (item) => item.value === selectedSupplierId
                              ) || selectedSupplierId === "ALL" ? (
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    width: 210,
                                    height: 120,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",

                                    flex: 1, 
                                    overflow: 'hidden', 
                                    textOverflow: 'ellipsis', 
                                    whiteSpace: 'nowrap',
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  dropDownDirection="BOTTOM"
                                  // disabled={selectedStockTransferEdit ? true : false}
                                  items={[
                                    { label: "All", value: "ALL" },
                                  ].concat(supplierDropdownList)}
                                  value={selectedSupplierId}
                                  // placeholder={"Select a Store"}
                                  // placeholderStyle={{
                                  //   color: Colors.fieldtTxtColor,
                                  //   // marginTop: 15,
                                  // }}
                                  onSelectItem={(item) => {
                                    if (item) {
                                      setSelectedSupplierId(item.value);
                                    }
                                  }}
                                  open={openDS}
                                  setOpen={setOpenDS}
                                />
                              ) : (
                                <></>
                              )}
                            </View>
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "space-between",
                            alignSelf: "center",
                            marginTop: 50,
                            marginBottom: 40,
                            zIndex: 10,
                            width: "80%",
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              width: "45%",
                              alignItems: "center",
                            }}
                          >
                            <Text
                              style={{
                                fontFamily: "NunitoSans-Bold",
                                fontSize: switchMerchant ? 10 : 14,
                                width: "42%",
                                fontWeight: "400",
                              }}
                            >
                              Shipped Date
                            </Text>
                            {/* <Text style={{ color: '#adadad', marginLeft: 80, fontSize: 16, }}>8/10/2020</Text> */}
                            <View
                              style={{
                                paddingHorizontal: 0,
                                flexDirection: "row",
                                alignItems: "center",
                                width: "57%",
                              }}
                            >
                              <View
                                style={{
                                  // width: 140,
                                  width: "90.5%",
                                  height: 35,
                                  backgroundColor: Colors.fieldtBgColor,
                                  paddingHorizontal: 10,
                                  flexDirection: "row",
                                  alignItems: "center",
                                  justifyContent: "space-between",
                                  borderRadius: 5,
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                }}
                              >
                                <View style={{ marginRight: 5 }}>
                                  {poStatus ===
                                    PURCHASE_ORDER_STATUS.COMPLETED ? (
                                    <GCalendarGrey
                                      width={switchMerchant ? 15 : 20}
                                      height={switchMerchant ? 15 : 20}
                                    />
                                  ) : (
                                    <GCalendar
                                      width={switchMerchant ? 15 : 20}
                                      height={switchMerchant ? 15 : 20}
                                    />
                                  )}
                                </View>
                                <View style={{ paddingRight: "15%" }}>
                                  <DatePicker
                                    disabled={
                                      poStatus ===
                                      PURCHASE_ORDER_STATUS.COMPLETED
                                    }
                                    selected={moment(date).toDate()}
                                    onChange={(date) => {
                                      setDate(moment(date));
                                    }}
                                    minDate={moment().toDate()}
                                    className="background"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>
                        </View>
                        <View
                          style={{
                            borderWidth: 1,
                            borderColor: "#E5E5E5",
                            zIndex: -5,
                          }}
                        />

                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "center",
                            marginBottom: 10,
                            zIndex: -5,
                          }}
                        >
                          <Text
                            style={{
                              alignSelf: "center",
                              marginTop: 30,
                              fontSize: switchMerchant ? 15 : 20,
                              fontWeight: "bold",
                            }}
                          >
                            Stock to Return
                          </Text>
                        </View>

                        <View
                          style={{
                            backgroundColor: "#ffffff",
                            flexDirection: "row",
                            paddingVertical: 20,
                            paddingHorizontal: 10,
                            marginTop: 10,
                            borderBottomWidth: StyleSheet.hairlineWidth,
                            zIndex: -5,
                          }}
                        >
                          <Text
                            style={{
                              width: "29%",
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: "NunitoSans-Bold",
                              fontWeight: "500",
                            }}
                          >
                            Product Name
                          </Text>
                          <Text
                            style={{
                              width: "14%",
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: "NunitoSans-Bold",
                              fontWeight: "500",
                              marginLeft: 10,
                            }}
                          >
                            SKU
                          </Text>
                          <Text
                            style={{
                              width: "9%",
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: "NunitoSans-Bold",
                              fontWeight: "500",
                            }}
                          >
                            {/* In Stock */}
                            From Stock
                          </Text>
                          <Text
                            style={{
                              width: "11%",
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: "NunitoSans-Bold",
                              fontWeight: "500",
                            }}
                          >
                            Transfer Qty
                          </Text>
                          <Text
                            style={{
                              width: "13%",
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: "NunitoSans-Bold",
                              fontWeight: "500",
                            }}
                          >
                            Balance Stock
                          </Text>
                          <Text
                            style={{
                              width: "10%",
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: "NunitoSans-Bold",
                              fontWeight: "500",
                            }}
                          >
                            Cost (RM)
                          </Text>
                          <Text
                            style={{
                              width: "12%",
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: "NunitoSans-Bold",
                              fontWeight: "500",
                            }}
                          >
                            Subtotal (RM)
                          </Text>
                        </View>
                        {outletItems.length > 0 ? (
                          <FlatList
                            nestedScrollEnabled={true}
                            showsVerticalScrollIndicator={false}
                            data={poItems}
                            extraData={poItems}
                            renderItem={renderAddStock}
                            keyExtractor={(item, index) => String(index)}
                            style={{ zIndex: -5 }}
                          />
                        ) : (
                          <View
                            style={{
                              alignItems: "center",
                              marginVertical: 20,
                              marginTop: 50,
                              zIndex: -5,
                            }}
                          >
                            <Text
                              style={{
                                color: Colors.descriptionColor,
                                fontFamily: "NunitoSans-Regular",
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                            >
                              No supply items In current store
                            </Text>
                          </View>
                        )}

                        {outletItems.length > 0 ? (
                          <View
                            style={{
                              flexDirection: "row",
                              marginBottom: 20,
                              justifyContent: "space-between",
                            }}
                          >
                            {!selectedStockReturnEdit ? (
                              <TouchableOpacity
                                style={styles.submitText2}
                                onPress={() => {
                                  if (outletItems.length > 0) {
                                    setPoItems([
                                      ...poItems,
                                      {
                                        // supplyItemId: '',
                                        // name: '',
                                        // sku: '',
                                        // quantity: '',
                                        // insertQuantity: 0,
                                        outletSupplyItemId:
                                          outletItems[0].uniqueId,
                                        name: outletItems[0].name,
                                        sku: outletItems[0].sku,
                                        unit: "",
                                        skuMerchant: outletItems[0].skuMerchant,
                                        quantity:
                                          outletItems[0].stockCount || 0,
                                        transferQuantity: 0,
                                        balance: 0,
                                        price: outletItems[0].price,
                                        totalPrice: 0,

                                        supplyItem: outletItems[0],
                                      },
                                    ]);
                                  } else {
                                    window.confirm(
                                      "Error. No supplier items added"
                                    );
                                  }
                                }}
                              >
                                <View
                                  style={{
                                    flexDirection: "row",
                                    alignItems: "center",
                                    marginTop: 5,
                                    marginLeft: 0,
                                    zIndex: -5,
                                  }}
                                >
                                  <Icon1
                                    name="plus-circle"
                                    size={switchMerchant ? 15 : 20}
                                    color={Colors.primaryColor}
                                  />
                                  <Text
                                    style={{
                                      marginLeft: 10,
                                      color: Colors.primaryColor,
                                      marginBottom:
                                        Platform.OS === "ios" ? 0 : 1,
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}
                                  >
                                    Add Product Slot
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            ) : (
                              <></>
                            )}

                            <View
                              style={{
                                alignItems: "flex-end",
                                marginTop: 5,
                                marginRight: 20,
                              }}
                            >
                              <Text
                                style={{
                                  fontWeight: "bold",
                                  fontSize: switchMerchant ? 10 : 16,
                                }}
                              >
                                {poItems.totalPrice}
                              </Text>
                            </View>
                          </View>
                        ) : null}
                        <View style={{ height: 50 }}></View>
                      </ScrollView>
                    </View>
                  </View>
                </View>
              ) : null}
            </View>
          </ScrollView>
        </ScrollView>
      </View>
    </View>
    // </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: "row",
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: "row",
    alignItems: "center",
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: useWindowDimensions.width * Styles.sideBarWidth,
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 16,
    width: useWindowDimensions.width * (1 - Styles.sideBarWidth),
    alignItems: "center",
  },
  submitText: {
    height:
      Platform.OS == "ios"
        ? Dimensions.get("screen").height * 0.06
        : Dimensions.get("screen").height * 0.05,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: "row",
    color: "#4cd964",
    textAlign: "center",
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  submitText1: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: "row",
    color: "#4cd964",
    textAlign: "center",
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    alignSelf: "flex-end",
    marginRight: 20,
    // marginTop: 15,
    height: 40,
    width: 220,
  },
  submitText2: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: "row",
    color: "#4cd964",
    textAlign: "center",
    alignSelf: "flex-end",
    marginRight: 20,
    marginTop: 15,
  },
  /* textInput: {
    width: 300,
    height: '10%',
    padding: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 20,
  }, */

  textInput: {
    width: 200,
    height: 40,
    // padding:5,
    backgroundColor: Colors.whiteColor,
    borderRadius: 0,
    marginRight: "35%",
    flexDirection: "row",
    alignContent: "center",
    alignItems: "flex-end",

    shadowOffset:
      Platform.OS == "ios"
        ? {
          width: 0,
          height: 0,
        }
        : {
          width: 0,
          height: 7,
        },
    shadowOpacity: Platform.OS == "ios" ? 0 : 0.43,
    shadowRadius: Platform.OS == "ios" ? 0 : 0.51,
    elevation: 15,
  },
  searchIcon: {
    backgroundColor: Colors.whiteColor,
    height: 40,
    padding: 10,
    shadowOffset:
      Platform.OS == "ios"
        ? {
          width: 0,
          height: 0,
        }
        : {
          width: 0,
          height: 7,
        },
    shadowOpacity: Platform.OS == "ios" ? 0 : 0.43,
    shadowRadius: Platform.OS == "ios" ? 0 : 9.51,

    elevation: 15,
  },
  textInput1: {
    width: 160,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
  },
  textInput2: {
    width: 100,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
    textAlign: "center",
  },
  confirmBox: {
    width: 450,
    height: 450,
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: 80,
    width: 900,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: "center",
  },
  headerLeftStyle: {
    width: Dimensions.get("screen").width * 0.17,
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: "center",
    justifyContent: "center",
  },
  modalView: {
    height: Dimensions.get("screen").width * 0.2,
    width: Dimensions.get("screen").width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get("screen").width * 0.03,
    alignItems: "center",
    justifyContent: "center",
  },
  modalView1: {
    //height: Dimensions.get('screen').width * 0.2,
    width: Dimensions.get("screen").width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get("screen").width * 0.03,
    alignItems: "center",
    justifyContent: "center",
  },
  modalViewImport: {
    height: Dimensions.get("screen").width * 0.6,
    width: Dimensions.get("screen").width * 0.6,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get("screen").width * 0.03,
    padding: 20,
    paddingTop: 25,
    //paddingBottom: 30,
  },
  closeButton: {
    position: "absolute",
    right: Dimensions.get("screen").width * 0.02,
    top: Dimensions.get("screen").width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: "center",
    top: "20%",
    position: "absolute",
  },
  modalBody: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  modalTitleText: {
    fontFamily: "NunitoSans-Bold",
    textAlign: "center",
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 25,
    width: "20%",
  },
  modalSaveButton: {
    width: Dimensions.get("screen").width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
});
export default StockReturnProductScreen;
