import React, { useState, useRef } from 'react';
import { View, Text, Modal, Button, TouchableOpacity, FlatList, Alert, TextInput, StyleSheet, KeyboardAvoidingView } from 'react-native';
import Colors from '../../constant/Colors';
import ApiClient from '../../util/ApiClient';
import * as User from '../../util/User';
import API from '../../constant/API';
import AIcon from 'react-native-vector-icons/AntDesign'
import AntDesign from 'react-native-vector-icons/AntDesign';
import Styles from '../../constant/Styles';

const OrderModal = ({ tableId, close }) => {

  const [orderModalVisible, setOrderModalVisible] = useState(false);
  const [orderMenu, setOrderMenu] = useState([]);
  const [orderItems, setOrderItems] = React.useState([]);
  const [remarks, setRemarks] = useState('');

  // // const remarksTextRef = useRef(null);
  // // const [remarksTextRef, setRemarksTextRef] = useState();

  // const onRemarksChange = (value) => {
  //   // console.log('onchange');

  //   setRemarks(value);
  //   // window.remarks = value;

  //   // if (remarksTextRef && remarksTextRef.current) {
  //   //   // console.log('remarksTextRef.current.value');
  //   //   // console.log(remarksTextRef.current.value);
  //   //   remarksTextRef.current.value = `Remarks: ${value}`;
  //   // }
  // };

  React.useEffect(() => {
    getMenuItem();
  }, []);

  function getMenuItem() {
    ApiClient.GET(API.merchantMenu + User.getOutletId()).then(result => {
      setOrderMenu(result);
    })
  }

  function createOrder() {
    const body = {
      items: orderItems,
      tableId: tableId,
      outletId: User.getOutletId(),
      type: "Dine In",
      paymentMethod: "",
      voucherId: "",
      voucherType: "",
      customTable: "",
      sessionId: 0,
      collectionTime: new Date(),
      // remarks: null,
      remarks: global.remarks ? global.remarks : '',
    }
    ApiClient.POST(API.createOrder, body).then(result => {
      //// console.log(result)
      if (result.success === true) {
        Alert.alert("Success", "Order Done!")
        setOrderModalVisible(false)
        close();
      }
    }).catch(err => console.log(err))
  }

  function editOrder(action, itemId) {
    let orderList = orderItems.slice()
    let item = orderList.filter(a => a.itemId == itemId)

    if (action == "+") {
      if (item.length > 0) {
        let newQty = item[0].quantity + 1
        item[0].quantity = newQty
      } else {
        const newItem = {
          itemId: itemId,
          quantity: 1,
          options: [],
          remarks: remarks
        }
        orderList.push(newItem)
      }

      setOrderItems(orderList)
    } else {
      if (item.length > 0) {
        if (item[0]?.quantity > 0) {
          let newQty = item[0].quantity - 1
          item[0].quantity = newQty
        }
      }

      setOrderItems(orderList)
    }
  }

  function getOrderQty(itemId) {
    let orderItem = orderItems.filter(a => a.itemId == itemId)

    if (orderItem.length > 0) {
      return orderItem[0].quantity
    }

    return 0
  }

  function getBasketItemCount() {
    let itemCount = orderItems.reduce((a, b) => a + (b.quantity || 0), 0)

    return itemCount
  }

  function onChangeOrderRemark(text) {
    setRemarks(text);
  };

  function MenuItemList(item) {

    //const [remarks, setRemarks] =  useState(); 
    // const onRemarksChange = (value) => {
    //   setRemarks(value);
    //global.remarks = item.remarks;}


    const renderMenuItem = (item) => {
      return (

        <View style={{ flex: 1, justifyContent: 'space-between', marginBottom: 20, flexDirection: 'row' }}>
          <View style={{ flex: 2, justifyContent: 'center', marginLeft: 12 }}>
            <Text style={{ fontSize: 22 }}>{item.item.name}</Text>
            {/* {console.log("item.item.name",item.item.name)}
            {console.log("item.item",item.item)} */}
            <Text style={{ marginBottom: 2, fontSize: 18, }}>Enter Remarks and Add Ons:</Text>

            <View>
              <TextInput
                placeholder='e.g. Less Salty, Add Rice'
                onChangeText={onChangeOrderRemark}
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: 244,
                  height: 34,
                  borderRadius: 5,
                  padding: 5,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  paddingLeft:10,
                }}
              /* onChangeText={onRemarksChange}
              value={remarks} */
              />
              {/*  <Text
                style={{ marginBottom: 16 }}>
               {console.log("remarks")}
                {console.log(remarks)} 
              Remarks: {item.remarks}
              </Text>*/}
            </View>
          </View>

          <View style={{ flex: 1, flexDirection: 'row', justifyContent: 'flex-end', marginRight: 12, alignItems: 'center' }}>
            <TouchableOpacity
              style={{
                height: 30,
                width: 30,
                backgroundColor: Colors.primaryColor,
                alignItems: 'center',
                justifyContent: 'center',
                borderTopLeftRadius: 25,
                borderBottomLeftRadius: 25
              }}
              // onPress={() => editOrder("-", item.item.id, item.item.remarks)}
              onPress={() => editOrder("-", item.item.id)}
            >
              <Text style={{ color: 'white' }}>-</Text>
            </TouchableOpacity>

            <View
              style={{
                width: 20,
                backgroundColor: 'white',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <Text style={{ fontSize: 20, fontWeight: 'bold' }}>{getOrderQty(item.item.id)}</Text>
            </View>

            <TouchableOpacity
              style={{
                height: 30,
                width: 30,
                backgroundColor: Colors.primaryColor,
                alignItems: 'center',
                justifyContent: 'center',
                borderTopRightRadius: 25,
                borderBottomRightRadius: 25
              }}
              // onPress={() => editOrder("+", item.item.id, item.item.remarks)}
              onPress={() => editOrder("+", item.item.id)}
            >
              <Text style={{ color: 'white' }}>+</Text>
            </TouchableOpacity>

          </View>

        </View>

      )
    }

    return (
      <FlatList
        data={item.item.items}
        renderItem={renderMenuItem}
        keyExtractor={item => `${item.id}`}
      />
    )
  }

  function categoryList() {
    const renderItem = (item) => {

      return (
        <View style={{ flex: 1, marginBottom: 16 }}>
          <Text style={{ marginBottom: 8, fontSize: 30, fontWeight: 'bold', textAlign: 'center' }}>{item.item.category}</Text>
          {MenuItemList(item)}
        </View>
      )
    }

    return (
      <FlatList
        data={orderMenu}
        renderItem={renderItem}
        keyExtractor={item => `${item.category}`}
        ListFooterComponent={orderButton}
      />
    )
  }

  function orderButton() {
    // const [remarks, setRemarks] = useState('');

    // const onRemarksChange = (value) => {
    //   setRemarks(value);
    //   global.remarks = remarks;
    // };

    return (
      <View>
        {/* <Text style={{ marginBottom: 2, fontSize: 24, fontWeight: 'bold', textAlign: 'center' }}>Remarks and Add Ons</Text>
        <View style={styles.container}>
          <Text>Enter Remarks and Add Ons:</Text>
          {/* <KeyboardAvoidingView behavior="padding"><TextInput
            placeholder='e.g. Less Salty'
            style={styles.input}
            onChangeText={(value) => setRemarks(value)} />

            <Text style={{ marginBottom: 16 }}>Remarks: {remarks}</Text>
          </KeyboardAvoidingView> 
          <View>
            <TextInput
              placeholder='e.g. Less Salty, Add Rice'
              style={styles.input}
              onChangeText={onRemarksChange}
              value={remarks}
            />
            <Text
              //ref={newRef => setRemarksTextRef(newRef)} 
              style={{ marginBottom: 16 }}>
              {console.log("remarks")}
              {console.log(remarks)}
              Remarks: {remarks}
              {/* {`Remarks: ${window.remarks}`}
            </Text>
          </View>
        </View> */}

        <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
          <Text style={{ fontSize: 22 }}>
            Total Items:
          </Text>
          <Text style={{ fontSize: 22, fontWeight: 'bold' }}>
            {getBasketItemCount()}
          </Text>
        </View>

        <TouchableOpacity
          onPress={createOrder}
        >
          <View style={{ flex: 1, marginTop: 30, marginBottom: 12, justifyContent: 'center', alignItems: 'center' }}>
            <View style={{ width: 200, height: 50, backgroundColor: Colors.primaryColor, justifyContent: 'center', alignItems: 'center', borderRadius: 12 }}>
              <Text style={{ color: "white", fontSize: 30, fontWeight: 'bold' }}>
                Order
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    )
  }

  return (
    <View style={{ flex: 1, justifyContent: 'center', }}>
      <Modal
        supportedOrientations={['landscape', 'portrait']}
        visible={orderModalVisible}
        transparent={true}
        animationType={"slide"}
      >
        <View style={{ flex: 1, backgroundColor: "#FFF", borderRadius: 12, }}>
          <TouchableOpacity
            style={{ alignSelf: "flex-end", margin: 12 }}
            onPress={() => setOrderModalVisible(!orderModalVisible)}>
            {/* <AIcon name="closecircle" size={25} color={Colors.fieldtTxtColor} /> */}
            <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
          </TouchableOpacity>
          {categoryList()}
        </View>
      </Modal>
      <View style={{ flexWrap: "wrap", alignSelf: "flex-end" }}>
        <TouchableOpacity
          onPress={() => {
            setOrderModalVisible(!orderModalVisible)
          }}
          style={{
            backgroundColor: Colors.primaryColor,
            width: '50%',
            justifyContent: 'center',
            alignItems: 'center',
            alignContent: 'center',
            borderRadius: 2,
            height: 55,
          }}>
          <Text
            style={{
              fontSize: 24, color: Colors.whiteColor, fontFamily: 'NunitoSans-Regular',
              paddingHorizontal: 20,
            }}>
            ORDER
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
  },
  input: {
    borderWidth: 1,
    borderColor: 'black',
    padding: 8,
    margin: 10,
    width: 200,

  },
});
export default OrderModal;