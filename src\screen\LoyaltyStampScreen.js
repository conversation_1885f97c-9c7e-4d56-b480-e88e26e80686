import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useCallback,
} from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  TouchableOpacity,
  Modal,
  Dimensions,
  TextInput,
  FlatList,
  KeyboardAvoidingView,
  ActivityIndicator,
  useWindowD<PERSON><PERSON><PERSON>,
  Picker,
} from "react-native";
import Colors from "../constant/Colors";
import Styles from "../constant/Styles";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import SideBar from "./SideBar";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as User from "../util/User";
import Icon from "react-native-vector-icons/Ionicons";
import AIcon from "react-native-vector-icons/AntDesign";
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
// import DropDownPicker from 'react-native-dropdown-picker';
import Select from "react-select";
import Multiselect from "multiselect-react-dropdown";
import Feather from "react-native-vector-icons/Feather";
import { ReactComponent as GCalendar } from "../assets/svg/GCalendar.svg";
import EvilIcons from "react-native-vector-icons/EvilIcons";
import moment from "moment";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "../constant/datePicker.css";
import TimeKeeper from "react-timekeeper";
import { ReactComponent as Clock } from "../assets/svg/Clock.svg";
// import {isTablet} from 'react-native-device-detection';
import {
  MERCHANT_VOUCHER_CODE_FORMAT,
  MERCHANT_VOUCHER_TYPE,
  SEGMENT_TYPE,
  EXPAND_TAB_TYPE,
} from "../constant/common";
import { CommonStore } from "../store/commonStore";
import { UserStore } from "../store/userStore";
import { MerchantStore } from "../store/merchantStore";
import { OutletStore } from "../store/outletStore";
// import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
// import RNPickerSelect from 'react-native-picker-select';
// import {useKeyboard} from '../hooks';
// import {get} from 'react-native/Libraries/Utilities/PixelRatio';
import AsyncImage from "../components/asyncImage";
import Entypo from "react-native-vector-icons/Entypo";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import {
  PROMOTION_TYPE,
  PROMOTION_TYPE_VARIATION,
  PROMOTION_TYPE_VARIATION_DROPDOWN_LIST,
  CRM_SEGMENT_DROPDOWN_LIST,
} from "../constant/promotions";
import AntDesign from "react-native-vector-icons/AntDesign";
import Icon1 from "react-native-vector-icons/Feather";
import Icon2 from "react-native-vector-icons/FontAwesome";
import Ionicon from "react-native-vector-icons/Ionicons";
import { Platform } from "react-native";
import { useFocusEffect } from "@react-navigation/native";
// import UserIdleWrapper from '../components/userIdleWrapper';
import { useLinkTo, useRoute } from "@react-navigation/native";
import { redeemOutletItemMultipleByMerchant } from "../util/apiLocalReplacers";
import { sliceUnicodeStringV2WithDots } from "../util/common";
import MultiSelect from "react-multiple-select-dropdown-lite";

import DropDownPicker from "react-native-dropdown-picker";
import "../constant/styles.css";

/////////////////////////////////////////////////////////////////////////////////////

const ADD_LOYALTY_STAMP_SCREEN = {
  LOYALTY_STAMP_LIST: "ADD_LOYALTY_STAMP_SCREEN.LOYALTY_STAMP_LIST",
  ADD_LOYALTY_STAMP: "ADD_LOYALTY_STAMP_SCREEN.ADD_LOYALTY_STAMP",
};

const LoyaltyStampScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////
  const { height: windowHeight, width: windowWidth } = useWindowDimensions();
  // const linkTo = useLinkTo();

  // const [keyboardHeight] = useKeyboard();
  const [switchMerchant, setSwitchMerchant] = useState(false);
  const [expandThreeDots, setExpandThreeDots] = useState({}); //Use to expand the view when three dots are tapped
  const [threeDotsTapped, setThreeDotsTapped] = useState(false); //when the three dots are tapped will become (true)

  const userName = UserStore.useState((s) => s.name);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const merchantName = MerchantStore.useState((s) => s.name);
  const merchantLogo = MerchantStore.useState((s) => s.logo);
  const pointsRedeemPackages = OutletStore.useState(
    (s) => s.pointsRedeemPackages
  );
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const outletItemsUnsorted = OutletStore.useState((s) => s.outletItems);
  const outletCategoriesUnsorted = OutletStore.useState(
    (s) => s.outletCategories
  );
  const isLoading = CommonStore.useState((s) => s.isLoading);
  const allOutletsCategoriesUnique = OutletStore.useState(
    (s) => s.allOutletsCategoriesUnique
  );

  const allOutletsItemsSkuDict = OutletStore.useState(
    (s) => s.allOutletsItemsSkuDict
  );

  /////////ADD LOYALTY STAMP SCREEN/ LOYALTY STAMP LIST////////////////////////
  const [outletItems, setOutletItems] = useState([]);
  const [variationItemsDropdownList, setVariationItemsDropdownList] = useState(
    []
  );

  const [addLoyaltyStamp, setAddLoyaltyStamp] = useState(
    ADD_LOYALTY_STAMP_SCREEN.LOYALTY_STAMP_LIST
  );
  const [stampItemsModal, setStampItemsModal] = useState(false);
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [rev_PickDate, setRev_PickDate] = useState(moment(Date.now()));
  const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
  const [rev_PickTime, setRev_PickTime] = useState(moment(Date.now()));

  ///////////////////////////////////////////////////////////////////////////

  const [clockTimePickerStart, setClockTimePickerStart] = useState(false);
  const [clockTimePickerEnd, setClockTimePickerEnd] = useState(false);

  ///////////////////////////////////////////////////////////////////////////

  const [stampName, setStampName] = useState("");
  const [pickDate, setPickDate] = useState("");
  const [pickTime, setPickTime] = useState("");
  const [startDate, setStartDate] = useState(
    moment().startOf("month").startOf("day")
  );
  const [endDate, setEndDate] = useState(moment().endOf("month").endOf("day"));
  const [startTime, setStartTime] = useState(
    moment(Date.now()).format("hh:mm A")
  );
  const [endTime, setEndTime] = useState(moment(Date.now()).format("hh:mm A"));
  const [terms, setTerms] = useState("");

  // const [stmpItems, setStmpItems] = useState([]);
  const [targetUsers, setTargetUsers] = useState([]);
  const [totalNumberOfStamp, setTotalNumberOfStamp] = useState("0");
  const [numberOfStamp, setNumberOfStamp] = useState("0");
  const [ordinals, setOrdinals] = useState("");
  const [quantityGet, setQuantityGet] = useState("0");
  const [selectedVariationItems, setSelectedVariationItems] = useState([
    {
      noOfStamp: "",
      quantity: 0,
      productName: "",
    },
  ]);

  const [selectedVariation, setSelectedVariation] = useState(
    PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
  );
  const [crmSegmentsDropdownList, setCrmSegmentsDropdownList] = useState([]);
  const crmSegments = OutletStore.useState((s) => s.crmSegments);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView
  );
  const [loading, setLoading] = useState(false);

  const [pId, setPId] = useState("");
  // const [pItems, setPItems] = useState([
  //   {
  //     pItemId: '',
  //     outletItemSku: '',
  //     variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
  //     isActive: true,
  //     noOfStamp: '0',
  //     quantity: '0',
  //     // productName: '',
  //     tc: '',

  //     itemName: '',
  //     itemDescription: '',
  //     itemImage: '',
  //   }
  // ]);

  const [lsId, setLsId] = useState("");

  const [lsItemsBuy, setLsItemsBuy] = useState([
    {
      lsItemId: "",
      outletItemSku: "",
      variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
      isActive: true,
      noOfStamp: "1",
      quantity: "1",
      // productName: '',
      tc: "",

      itemName: "",
      itemDescription: "",
      itemImage: "",
    },
  ]);

  const [lsItems, setLsItems] = useState([
    {
      lsItemId: "",
      outletItemSku: "",
      variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
      isActive: true,
      noOfStamp: "1",
      quantity: "1",
      // productName: '',
      // tc: '',

      itemName: "",
      itemDescription: "",
      itemImage: "",
    },
  ]);

  const selectedLoyaltyStampEdit = CommonStore.useState(
    (s) => s.selectedLoyaltyStampEdit
  );

  const loyaltyStamps = OutletStore.useState((s) => s.loyaltyStamps);

  const [variationItemsProducts, setVariationItemsProducts] = useState([]);
  const [variationItemsCategories, setVariationItemsCategories] = useState([]);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  /////////////////////////////////////////////////////////////////////////////

  useEffect(() => {
    // setVariationItemsProducts(outletItems.map(item => ({ label: item.name, value: item.uniqueId })));
    // setVariationItemsCategories(allOutletsCategoriesUnique.map(item => ({ label: item.name, value: item.uniqueId })));

    // setVariationItemsProducts(outletItems.map(item => ({ label: item.name, value: item.uniqueId, name: item.name, description: item.description, image: item.image })));
    setVariationItemsProducts(
      outletItems.map((item) => ({
        label: item.name,
        value: item.sku,
        name: item.name,
        description: item.description,
        image: item.image,
      }))
    );
    setVariationItemsCategories(
      allOutletsCategoriesUnique.map((item) => ({
        label: item.name,
        value: item.uniqueId,
        name: item.name,
        description: "",
        image: "",
      }))
    );
  }, [
    outletItems,
    // outletCategories,
    allOutletsCategoriesUnique,

    selectedLoyaltyStampEdit,
  ]);

  // useEffect(() => {
  //   setVariationItemsDropdownList(outletItems.map(item => ({ label: item.name, value: item.sku, name: item.name, description: item.description, image: item.image })));

  // }, [
  //   outletItems,
  // ]);

  ////////////////////////////////////////////////////////

  useEffect(() => {
    setCrmSegmentsDropdownList(
      crmSegments.map((segment) => ({
        label: segment.name,
        value: segment.uniqueId,
      }))
    );
  }, [crmSegments]);

  useEffect(() => {
    var outletItemsTemp = [...outletItemsUnsorted];

    outletItemsTemp.sort((a, b) => a.name.localeCompare(b.name));

    setOutletItems(outletItemsTemp);
  }, [outletItemsUnsorted]);

  useEffect(() => {
    if (variationItemsProducts.length > 0) {
      if (
        lsItems.length > 0 &&
        !lsItems[0].lsItemId &&
        !lsItems[0].outletItemSku
      ) {
        setLsItems([
          {
            lsItemId: "",
            outletItemSku: variationItemsProducts[0].value,
            variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
            isActive: true,
            noOfStamp: "1",
            quantity: "1",

            itemName: variationItemsProducts[0].name,
            itemDescription: variationItemsProducts[0].description,
            itemImage: variationItemsProducts[0].image,
          },
        ]);
      }

      if (
        lsItemsBuy.length > 0 &&
        !lsItemsBuy[0].lsItemId &&
        !lsItemsBuy[0].outletItemSku
      ) {
        setLsItemsBuy([
          {
            lsItemId: "",
            outletItemSku: variationItemsProducts[0].value,
            variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
            isActive: true,
            noOfStamp: "1",
            quantity: "1",

            itemName: variationItemsProducts[0].name,
            itemDescription: variationItemsProducts[0].description,
            itemImage: variationItemsProducts[0].image,
          },
        ]);
      }
    }
  }, [
    variationItemsProducts,
    // lsItems,
  ]);

  useEffect(() => {
    // console.log('================================');
    // console.log('selectedLoyaltyStampEdit');
    // console.log(selectedLoyaltyStampEdit);

    if (selectedLoyaltyStampEdit) {
      // insert info

      setLsId(selectedLoyaltyStampEdit.uniqueId);
      setLsItems(
        selectedLoyaltyStampEdit.lsItems.map((item) => ({
          lsItemId: item.lsItemId,
          outletItemSku: item.outletItemSku,
          variation: item.variation,
          isActive: item.isActive,
          noOfStamp: item.noOfStamp.toString(),
          quantity: item.quantity.toString(),

          itemName: item.itemName,
          itemDescription: item.itemDescription,
          itemImage: item.itemImage,
        }))
      );
      setLsItemsBuy(
        selectedLoyaltyStampEdit.lsItemsBuy.map((item) => ({
          lsItemId: item.lsItemId,
          outletItemSku: item.outletItemSku,
          variation: item.variation,
          isActive: item.isActive,
          noOfStamp: item.noOfStamp.toString(),
          quantity: item.quantity.toString(),

          itemName: item.itemName,
          itemDescription: item.itemDescription,
          itemImage: item.itemImage,
        }))
      );
      // setBdOptions(selectedLoyaltyStampEdit.bdOptions);
      // setDocketPrice(selectedLoyaltyStampEdit.price.toFixed(2));

      setStampName(selectedLoyaltyStampEdit.name);
      // setSelectedTargetSegmentGroupList([CRM_SEGMENT_DROPDOWN_LIST[0].value]);
      setTotalNumberOfStamp(
        selectedLoyaltyStampEdit.totalNumberOfStamp.toString()
      );
      setStartDate(selectedLoyaltyStampEdit.startDate);
      setEndDate(selectedLoyaltyStampEdit.endDate);
      setStartTime(selectedLoyaltyStampEdit.startTime);
      setEndTime(selectedLoyaltyStampEdit.endTime);
      setTerms(selectedLoyaltyStampEdit.terms);

      setTargetUsers(selectedLoyaltyStampEdit.targetSegments || []);

      // setPurchaseDate(selectedLoyaltyStampEdit.purchaseDate ? selectedLoyaltyStampEdit.purchaseDate : moment().valueOf());
      // setPurchaseEndDate(selectedLoyaltyStampEdit.purchaseEndDate ? selectedLoyaltyStampEdit.purchaseEndDate : moment().valueOf());

      // setPurchaseTime(selectedLoyaltyStampEdit.purchaseTime ? selectedLoyaltyStampEdit.purchaseTime : moment().valueOf());
      // setPurchaseEndTime(selectedLoyaltyStampEdit.purchaseEndTime ? selectedLoyaltyStampEdit.purchaseEndTime : moment().valueOf());

      // setRedeemDate(selectedLoyaltyStampEdit.redeemDate ? selectedLoyaltyStampEdit.redeemDate : moment().valueOf());
      // setRedeemEndDate(selectedLoyaltyStampEdit.redeemEndDate ? selectedLoyaltyStampEdit.redeemEndDate : moment().valueOf());

      // setRedeemTime(selectedLoyaltyStampEdit.redeemTime ? selectedLoyaltyStampEdit.redeemTime : moment().valueOf());
      // setRedeemEndTime(selectedLoyaltyStampEdit.redeemEndTime ? selectedLoyaltyStampEdit.redeemEndTime : moment().valueOf());

      // setExpiredDate(selectedLoyaltyStampEdit.expiredDate ? selectedLoyaltyStampEdit.expiredDate : moment().valueOf());
      // setExpiredTime(selectedLoyaltyStampEdit.expiredTime ? selectedLoyaltyStampEdit.expiredTime : moment().valueOf());

      // setTermsConditions(selectedLoyaltyStampEdit.tnc);
      // setSelectedOutletList(selectedLoyaltyStampEdit.bdOutlets);

      // setImage(selectedLoyaltyStampEdit.image ? selectedLoyaltyStampEdit.image : '');
      // setIsImageChanged(false);

      // setChargesOfExtensionDateFormatType(selectedLoyaltyStampEdit.chargesOfExtensionDateFormatType ? selectedLoyaltyStampEdit.chargesOfExtensionDateFormatType : DATE_FORMAT_DROPDOWN_LIST[0].value);

      // setRedemptionQuantityLimitValue(selectedLoyaltyStampEdit.redemptionQuantityLimitValue ? selectedLoyaltyStampEdit.redemptionQuantityLimitValue.toFixed(2) : '0.00');
      // setRedemptionQuantityLimitDateFormatValue(selectedLoyaltyStampEdit.redemptionQuantityLimitDateFormatValue ? selectedLoyaltyStampEdit.redemptionQuantityLimitDateFormatValue.toFixed(2) : '0.00');
      // setRedemptionQuantityLimitDateFormatType(selectedLoyaltyStampEdit.redemptionQuantityLimitDateFormatType ? selectedLoyaltyStampEdit.redemptionQuantityLimitDateFormatType : DATE_FORMAT_DROPDOWN_LIST[0].value);
    } else {
      // designed to always mounted, thus need clear manually...

      setLsId("");
      if (variationItemsProducts.length > 0) {
        setLsItems([
          {
            lsItemId: "",
            outletItemSku:
              variationItemsProducts.length > 0 ? variationItemsProducts[0].value : "",
            variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
            isActive: true,
            noOfStamp: "1",
            quantity: "1",

            itemName: variationItemsProducts[0].name,
            itemDescription: variationItemsProducts[0].description,
            itemImage: variationItemsProducts[0].image,
          },
        ]);

        setLsItemsBuy([
          {
            lsItemId: "",
            outletItemSku:
              variationItemsProducts.length > 0 ? variationItemsProducts[0].value : "",
            variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
            isActive: true,
            noOfStamp: "1",
            quantity: "1",

            itemName: variationItemsProducts[0].name,
            itemDescription: variationItemsProducts[0].description,
            itemImage: variationItemsProducts[0].image,
          },
        ]);
      }

      setStampName("");
      setTotalNumberOfStamp("0");
      setStartDate(moment().valueOf());
      setEndDate(moment().valueOf());
      setStartTime(moment().valueOf());
      setEndTime(moment().valueOf());
      setTerms("");
      setTargetUsers([
        {
          label: "",
          value: "",
        },
      ]);

      // setBdOptions([
      //   {
      //     optionsId: '',
      //     isActive: true,
      //     duration: 0,
      //     price: 0,
      //     unit: 'day',
      //   }
      // ]);
      // setDocketPrice('0.00');

      // setStartDate(moment());
      // setEndDate(moment());

      // setPurchaseDate(moment());
      // setPurchaseEndDate(moment());

      // setPurchaseTime(moment());
      // setPurchaseEndTime(moment());

      // setExpiredDate(moment());
      // setExpiredTime(moment());

      // setTermsConditions('');
      // setSelectedOutletList([]);

      // setImage('');
      // setIsImageChanged(false);

      // setChargesOfExtensionDateFormatType(DATE_FORMAT_DROPDOWN_LIST[0].value);

      // setRedemptionQuantityLimitValue('0.00');
      // setRedemptionQuantityLimitDateFormatValue('0.00');
      // setRedemptionQuantityLimitDateFormatType(DATE_FORMAT_DROPDOWN_LIST[0].value);
    }
  }, [selectedLoyaltyStampEdit]);

  //////////////////////////////////////////////////////////////////////////////////////////////////////////

  //To remove unwanted sidebar
  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus
  );

  // const [outletDropdownList, setOutletDropdownList] = useState([]);
  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  const [openTS, setOpenTS] = useState(false);
  const [openList, setOpenList] = useState([]);
  const [openList2, setOpenList2] = useState([]);
  const [openList3, setOpenList3] = useState([]);
  const [openList4, setOpenList4] = useState([]);

  // var outletNames = [];

  // for (var i = 0; i < allOutlets.length; i++) {
  //   for (var j = 0; j < selectedOutletList.length; j++) {
  //     if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
  //       outletNames.push(allOutlets[i].name);
  //       break;
  //     }
  //   }
  // }

  // useEffect(() => {
  //   setOutletDropdownList(
  //     allOutlets.map((item) => {
  //       return { label: item.name, value: item.uniqueId };
  //     })
  //   );
  // }, [allOutlets]);

  var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));

  const [openO, setOpenO] = useState(false);

  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.outletSelectDropdownView = () => {
  //       return (
  //         <View
  //           style={{
  //             flexDirection: "row",
  //             alignItems: "center",
  //             borderRadius: 8,
  //             width: 200,
  //             backgroundColor: "white",
  //           }}
  //         >
  //           {currOutletId.length > 0 &&
  //             allOutlets.find((item) => item.uniqueId === currOutletId) ? (
  //               <DropDownPicker
  //               style={{
  //                 backgroundColor: Colors.fieldtBgColor,
  //                 width: 200,
  //                 height: 40,
  //                 borderRadius: 10,
  //                 borderWidth: 1,
  //                 borderColor: "#E5E5E5",
  //                 flexDirection: "row",
  //               }}
  //               dropDownContainerStyle={{
  //                 width: 200,
  //                 backgroundColor: Colors.fieldtBgColor,
  //                 borderColor: "#E5E5E5",
  //               }}
  //               labelStyle={{
  //                 marginLeft: 5,
  //                 flexDirection: "row",
  //               }}
  //               textStyle={{
  //                 fontSize: 14,
  //                 fontFamily: 'NunitoSans-Regular',

  //                 marginLeft: 5,
  //                 paddingVertical: 10,
  //                 flexDirection: "row",
  //               }}
  //               selectedItemContainerStyle={{
  //                 flexDirection: "row",
  //               }}

  //               showArrowIcon={true}
  //               ArrowDownIconComponent={({ style }) => (
  //                 <Ionicon
  //                   size={25}
  //                   color={Colors.fieldtTxtColor}
  //                   style={{ paddingHorizontal: 5, marginTop: 5 }}
  //                   name="chevron-down-outline"
  //                 />
  //               )}
  //               ArrowUpIconComponent={({ style }) => (
  //                 <Ionicon
  //                   size={25}
  //                   color={Colors.fieldtTxtColor}
  //                   style={{ paddingHorizontal: 5, marginTop: 5 }}
  //                   name="chevron-up-outline"
  //                 />
  //               )}

  //               showTickIcon={true}
  //               TickIconComponent={({ press }) => (
  //                 <Ionicon
  //                   style={{ paddingHorizontal: 5, marginTop: 5 }}
  //                   color={
  //                     press ? Colors.fieldtBgColor : Colors.primaryColor
  //                   }
  //                   name={'md-checkbox'}
  //                   size={25}
  //                 />
  //               )}
  //               placeholderStyle={{
  //                 color: Colors.fieldtTxtColor,
  //                 // marginTop: 15,
  //               }}
  //               dropDownDirection="BOTTOM"
  //               placeholder="Choose Outlet"
  //               items={targetOutletDropdownListTemp}
  //               value={currOutletId}
  //               onSelectItem={(item) => {
  //                 if (item) { // if choose the same option again, value = ''
  //                   MerchantStore.update((s) => {
  //                     s.currOutletId = item.value;
  //                     s.currOutlet =
  //                       allOutlets.find(
  //                         (outlet) => outlet.uniqueId === item.value
  //                       ) || {};
  //                   });
  //                 }

  //                 CommonStore.update((s) => {
  //                   s.shiftClosedModal = false;
  //                 });
  //               }}
  //               open={openO}
  //               setOpen={setOpenO}
  //             />
  //           ) : (
  //             <ActivityIndicator size={"small"} color={Colors.whiteColor} />
  //           )}
  //           {/* <Select

  //             placeholder={"Choose Outlet"}
  //             onChange={(items) => {
  //               setSelectedOutletList(items);
  //             }}
  //             options={outletDropdownList}
  //             isMulti
  //           /> */}
  //         </View>
  //       );
  //     };
  //   });
  // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

  //////////////////////////////////////////////////
  //Header
  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Loyalty Stamp
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo Manager")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
              style={{
                width: windowHeight * 0.05,
              height: windowHeight * 0.05,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });
  //////////////////////////////////////////////////////////////////

  const renderLoyaltyStamp = ({ item, index }) => {
    return (
      <TouchableOpacity
        style={{
          flexDirection: "row",
          borderBottomWidth: StyleSheet.hairlineWidth,
          borderBottomColor: "#c4c4c4",
          padding: 10,
          paddingVertical: 20,
        }}
        onPress={() => {
          CommonStore.update((s) => {
            s.selectedLoyaltyStampEdit = item;
          });

          setAddLoyaltyStamp(ADD_LOYALTY_STAMP_SCREEN.ADD_LOYALTY_STAMP);
        }}
      >
        <Text
          style={{
            fontSize: switchMerchant ? 10 : 14,
            fontWeight: "500",
            fontFamily: "NunitoSans-Regular",
            color: Colors.primaryColor,
            width: "26%",
            marginLeft: 5,
          }}
        >
          {/* {stampName} */}
          {item.name}
        </Text>
        <Text
          style={{
            fontSize: switchMerchant ? 10 : 14,
            fontWeight: "500",
            fontFamily: "NunitoSans-Regular",
            width: "18%",
            marginHorizontal: 5,
          }}
        >
          {/* {totalNumberOfStamp} */}
          {item.totalNumberOfStamp}
        </Text>
        <View style={{ width: "26%", marginHorizontal: 5 }}>
          {/* {item.outletNameList.length > 0 ?
        <View>
         {(item.outletNameList.map(( outletNameList ) => {
           return(
         <View>
           <Text style={{ fontSize: 14, fontWeight: '500', fontFamily: 'NunitoSans-Regular',  }}>
             {outletNameList}
           </Text>
         </View>
           )
        }))}
        </View>
        : null } */}
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontWeight: "500",
              fontFamily: "NunitoSans-Regular",
            }}
          >
            {item.outletNameList.join("\n")}
          </Text>
        </View>
        <View style={{ width: "21%", marginLeft: 5 }}>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontWeight: "500",
              fontFamily: "NunitoSans-Regular",
            }}
          >
            {moment(item.startDate).format("DD/MM/YYYY")} to{" "}
            {moment(item.endDate).format("DD/MM/YYYY")}
            {/* 21/7/2021 to 21/8/2021 */}
          </Text>
          <Text
            style={{
              fontSize: switchMerchant ? 10 : 14,
              fontWeight: "500",
              fontFamily: "NunitoSans-Regular",
              color: Colors.fieldtTxtColor,
            }}
          >
            {moment(item.startTime).format("hh:mmA")} to{" "}
            {moment(item.endTime).format("hh:mmA")}
            {/* 8:00 a.m. to 9:00 p.m. */}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const addProduct = () => {
    if (outletItemsUnsorted.length > 0) {
      // setSelectedVariationItems([
      //   ...selectedVariationItems,
      //   {
      //     noOfStamp: selectedVariationItems[0].number,
      //     quantity: selectedVariationItems[0].quantity,
      //     productName: selectedVariationItems[0].pName,
      //   }
      // ]);

      setLsItemsBuy([
        ...lsItemsBuy,
        {
          lsItemId: "",
          outletItemSku:
            variationItemsProducts.length > 0
              ? variationItemsProducts[0].value
              : "",
          variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
          isActive: true,
          noOfStamp: "1",
          quantity: "1",

          itemName: variationItemsProducts[0].name,
          itemDescription: variationItemsProducts[0].description,
          itemImage: variationItemsProducts[0].image,
        },
      ]);
    } else {
      window.confirm("Error", "Unable to add new product");
    }
  };

  const addStamp = () => {
    if (outletItemsUnsorted.length > 0) {
      // setSelectedVariationItems([
      //   ...selectedVariationItems,
      //   {
      //     noOfStamp: selectedVariationItems[0].number,
      //     quantity: selectedVariationItems[0].quantity,
      //     productName: selectedVariationItems[0].pName,
      //   }
      // ]);

      setLsItems([
        ...lsItems,
        {
          lsItemId: "",
          outletItemSku:
            variationItemsProducts.length > 0
              ? variationItemsProducts[0].value
              : "",
          variation: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
          isActive: true,
          noOfStamp: "1",
          quantity: "1",

          itemName: variationItemsProducts[0].name,
          itemDescription: variationItemsProducts[0].description,
          itemImage: variationItemsProducts[0].image,
        },
      ]);
    } else {
      window.confirm("Error", "Unable to add new stamp item");
    }
  };

  const renderProductEligible = () => {
    return (
      <>
        {lsItemsBuy.map((item, index) => {
          return (
            <View
              style={{
                position: "relative",
                //backgroundColor: '#ffffff',
                flexDirection: "row",
                paddingVertical: 10,
                marginLeft: switchMerchant ? "3.5%" : 0,

                // borderBottomWidth: StyleSheet.hairlineWidth,
                // borderBottomColor: '#c4c4c4',
                alignItems: "center",
                width: "100%",
                zIndex: 10001 + lsItemsBuy.length - index,
              }}
            >
              <View style={{ flexDirection: "column", width: "90%" }}>
                <View
                  style={{
                    flexDirection: "row",
                    position: "relative",
                    alignItems: "center",
                    paddingHorizontal: switchMerchant ? 0 : 20,
                  }}
                >
                  <View
                    style={{
                      width: switchMerchant ? "3%" : "5%",
                      marginHorizontal: 5,
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: "500",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                    >
                      Buy
                    </Text>
                  </View>

                  <View
                    style={{
                      flexDirection: "row",
                      width: switchMerchant ? "7%" : "7%",
                      marginHorizontal: 5,
                    }}
                  >
                    {/* <Text style={{ fontWeight: '500', height: 28, paddingTop: 5, marginRight: 2, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}>
            {numberOfStamp}10
          </Text>
          <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, alignSelf: 'flex-start' }}>
            {ordinals}th
          </Text> */}
                    <TextInput
                      placeholder="No."
                      placeholderTextColor={Platform.select({ ios: "#a9a9a9" })}
                      placeholderStyle={{
                        color: "grey",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: 14,
                      }}
                      style={[
                        {
                          backgroundColor: Colors.fieldtBgColor,
                          width: 50,
                          height: 40,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: "#E5E5E5",
                          paddingLeft: 10,
                          fontFamily: "NunitoSans-Regular",
                          fontSize: 14,
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            width: "100%",
                            height: 35,
                          }
                          : {},
                      ]}
                      //   onChangeText={(text) => {
                      //     setNumberOfStamp(text)
                      //   }}
                      //   defaultValue={numberOfStamp ? numberOfStamp : 'No.'}
                      // //value={numberOfStamp}
                      keyboardType={"decimal-pad"}
                      onChangeText={(text) => {
                        // setSelectedVariationItems(selectedVariationItems.map((stampItem, i) => (i === index ? {
                        //   ...stampItem,
                        //   noOfStamp: text,

                        // } : stampItem)))
                        setLsItemsBuy(
                          lsItemsBuy.map((lsItem, i) =>
                            i === index
                              ? {
                                ...lsItem,
                                // noOfStamp: text,
                                quantity: text,
                              }
                              : lsItem
                          )
                        );
                      }}
                      defaultValue={lsItemsBuy[index].quantity}
                      value={lsItemsBuy[index].quantity}
                    />
                  </View>

                  <View
                    style={{
                      width: switchMerchant ? "3%" : "3%",
                      marginHorizontal: 5,
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: "500",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                    >
                      of
                    </Text>
                  </View>

                  <View
                    style={{
                      width: switchMerchant ? "23%" : "23%",
                      marginHorizontal: 5,
                    }}
                  >
                    <View
                      style={{
                        zIndex: 10001 + lsItemsBuy.length - index,
                      }}
                    >
                      <View
                        style={{
                          flexDirection: "row",
                          //paddingBottom: 10,
                          //marginBottom: 15,
                          alignItems: "center",
                        }}
                      >
                        <View
                          style={{
                            //width: '40%', marginHorizontal: 5,
                            height: switchMerchant ? 35 : 40,
                            justifyContent: "center",
                            //alignItems: 'flex-end',
                            alignSelf: "flex-end",
                            width: switchMerchant
                              ? "100%"
                              : windowWidth < 1750
                                ? 200
                                : "100%",
                          }}
                        >
                          <DropDownPicker
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              // width: 210,
                              height: 40,
                              borderRadius: 10,
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              flexDirection: "row",
                            }}
                            dropDownContainerStyle={{
                              // width: 210,
                              backgroundColor: Colors.fieldtBgColor,
                              borderColor: "#E5E5E5",
                            }}
                            labelStyle={{
                              marginLeft: 5,
                              flexDirection: "row",
                            }}
                            textStyle={{
                              fontSize: 14,
                              fontFamily: 'NunitoSans-Regular',

                              marginLeft: 5,
                              paddingVertical: 10,
                              flexDirection: "row",
                            }}
                            selectedItemContainerStyle={{
                              flexDirection: "row",
                            }}

                            showArrowIcon={true}
                            ArrowDownIconComponent={({ style }) => (
                              <Ionicon
                                size={25}
                                color={Colors.fieldtTxtColor}
                                style={{ paddingHorizontal: 5, marginTop: 5 }}
                                name="chevron-down-outline"
                              />
                            )}
                            ArrowUpIconComponent={({ style }) => (
                              <Ionicon
                                size={25}
                                color={Colors.fieldtTxtColor}
                                style={{ paddingHorizontal: 5, marginTop: 5 }}
                                name="chevron-up-outline"
                              />
                            )}

                            showTickIcon={true}
                            TickIconComponent={({ press }) => (
                              <Ionicon
                                style={{ paddingHorizontal: 5, marginTop: 5 }}
                                color={
                                  press ? Colors.fieldtBgColor : Colors.primaryColor
                                }
                                name={'md-checkbox'}
                                size={25}
                              />
                            )}
                            placeholderStyle={{
                              color: Colors.fieldtTxtColor,
                              // marginTop: 15,
                            }}
                            dropDownDirection="BOTTOM"
                            // placeholder="Choose Outlet"
                            // multipleText={`${selectedOutletList.length} outlet(s) selected`}
                            // multipleText={'%d outlet(s) selected'}
                            items={PROMOTION_TYPE_VARIATION_DROPDOWN_LIST}
                            value={lsItemsBuy[index].variation}
                            // multiple={true}
                            open={openList[index]}
                            setOpen={(value) => {
                              setOpenList((prevOpenList) => {
                                const newOpenList = [...prevOpenList];
                                newOpenList[index] = value;
                                return newOpenList;
                              });
                            }}
                            onSelectItem={(item) => {
                              if (item) {
                                if (lsItemsBuy[index].variation !== item) {
                                  setLsItemsBuy(
                                    lsItemsBuy.map((lsItem, i) =>
                                      i === index
                                        ? {
                                          ...lsItem,
                                          variation: item.value,

                                          outletItemSku:
                                            item ===
                                              PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                                              ? variationItemsProducts[0]
                                                .value
                                              : variationItemsCategories[0]
                                                .value,
                                          itemName:
                                            item ===
                                              PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                                              ? variationItemsProducts[0].name
                                              : variationItemsCategories[0]
                                                .name,
                                          itemDescription:
                                            item ===
                                              PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                                              ? variationItemsProducts[0]
                                                .description
                                              : variationItemsCategories[0]
                                                .description,
                                          itemImage:
                                            item ===
                                              PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                                              ? variationItemsProducts[0]
                                                .image
                                              : variationItemsCategories[0]
                                                .image,
                                        }
                                        : lsItem
                                    )
                                  );
                                } else {
                                  setLsItemsBuy(
                                    lsItemsBuy.map((lsItem, i) =>
                                      i === index
                                        ? {
                                          ...lsItem,
                                          variation: item.value,
                                        }
                                        : lsItem
                                    )
                                  );
                                }
                              }
                            }}
                          />
                        </View>
                      </View>
                    </View>
                  </View>

                  <View
                    style={{
                      width: switchMerchant ? "23%" : "23%",
                      marginHorizontal: 5,
                    }}
                  >
                    <View
                      style={{
                        zIndex: 10001 + lsItemsBuy.length - index,
                      }}
                    >
                      <View
                        style={{
                          flexDirection: "row",
                          //paddingBottom: 10,
                          //marginBottom: 15,
                          alignItems: "center",
                        }}
                      >
                        <View
                          style={{
                            //width: '40%', marginHorizontal: 5,
                            height: switchMerchant ? 35 : 40,
                            justifyContent: "center",
                            //alignItems: 'flex-end',
                            alignSelf: "flex-end",
                            width: switchMerchant
                              ? "100%"
                              : windowWidth < 1750
                                ? 120
                                : "100%",
                          }}
                        >
                          {
                            // (variationItemsDropdownList.find(variationItem => variationItem.value === item.outletItemSku) || item.outletItemSku === '')
                            (
                              lsItemsBuy[index].variation ===
                                PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                                ? variationItemsCategories.length > 0 &&
                                variationItemsCategories.find(
                                  (item) => item.value
                                ) === lsItemsBuy[index].outletItemSku
                                : variationItemsProducts.length > 0 &&
                                variationItemsProducts.find(
                                  (item) => item.value
                                ) === lsItemsBuy[index].outletItemSku
                            ) ? (
                              <DropDownPicker
                                style={{
                                  backgroundColor: Colors.fieldtBgColor,
                                  // width: 210,
                                  height: 40,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  flexDirection: "row",
                                }}
                                dropDownContainerStyle={{
                                  // width: 210,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderColor: "#E5E5E5",
                                }}
                                labelStyle={{
                                  marginLeft: 5,
                                  flexDirection: "row",
                                }}
                                textStyle={{
                                  fontSize: 14,
                                  fontFamily: 'NunitoSans-Regular',

                                  marginLeft: 5,
                                  paddingVertical: 10,
                                  flexDirection: "row",
                                }}
                                selectedItemContainerStyle={{
                                  flexDirection: "row",
                                }}

                                showArrowIcon={true}
                                ArrowDownIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-down-outline"
                                  />
                                )}
                                ArrowUpIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-up-outline"
                                  />
                                )}

                                showTickIcon={true}
                                TickIconComponent={({ press }) => (
                                  <Ionicon
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    color={
                                      press ? Colors.fieldtBgColor : Colors.primaryColor
                                    }
                                    name={'md-checkbox'}
                                    size={25}
                                  />
                                )}
                                placeholderStyle={{
                                  color: Colors.fieldtTxtColor,
                                  // marginTop: 15,
                                }}
                                dropDownDirection="BOTTOM"
                                // placeholder="Choose Outlet"
                                // multipleText={`${selectedOutletList.length} outlet(s) selected`}
                                // multipleText={'%d outlet(s) selected'}
                                items={
                                  lsItemsBuy[index].variation ===
                                    PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                                    ? variationItemsCategories
                                    : variationItemsProducts
                                }
                                value={lsItemsBuy[index].outletItemSku}
                                // multiple={true}
                                open={openList2[index]}
                                setOpen={(value) => {
                                  setOpenList2((prevOpenList) => {
                                    const newOpenList = [...prevOpenList];
                                    newOpenList[index] = value;
                                    return newOpenList;
                                  });
                                }}
                                onSelectItem={(item) => {
                                  if (item) {
                                    setLsItemsBuy(
                                      lsItemsBuy.map((lsItem, i) =>
                                        i === index
                                          ? {
                                            ...lsItem,
                                            outletItemSku: item.value,
                                            itemImage: item.image,
                                            itemName: item.name,
                                            itemDescription: item.description,
                                          }
                                          : lsItem
                                      )
                                    );
                                  }
                                }}
                              />
                            ) : (
                              <DropDownPicker
                                style={{
                                  backgroundColor: Colors.fieldtBgColor,
                                  // width: 210,
                                  height: 40,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  flexDirection: "row",
                                }}
                                dropDownContainerStyle={{
                                  // width: 210,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderColor: "#E5E5E5",
                                }}
                                labelStyle={{
                                  marginLeft: 5,
                                  flexDirection: "row",
                                }}
                                textStyle={{
                                  fontSize: 14,
                                  fontFamily: 'NunitoSans-Regular',

                                  marginLeft: 5,
                                  paddingVertical: 10,
                                  flexDirection: "row",
                                }}
                                selectedItemContainerStyle={{
                                  flexDirection: "row",
                                }}

                                showArrowIcon={true}
                                ArrowDownIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-down-outline"
                                  />
                                )}
                                ArrowUpIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-up-outline"
                                  />
                                )}

                                showTickIcon={true}
                                TickIconComponent={({ press }) => (
                                  <Ionicon
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    color={
                                      press ? Colors.fieldtBgColor : Colors.primaryColor
                                    }
                                    name={'md-checkbox'}
                                    size={25}
                                  />
                                )}
                                placeholderStyle={{
                                  color: Colors.fieldtTxtColor,
                                  // marginTop: 15,
                                }}
                                dropDownDirection="BOTTOM"
                                // placeholder="Choose Outlet"
                                // multipleText={`${selectedOutletList.length} outlet(s) selected`}
                                // multipleText={'%d outlet(s) selected'}
                                items={
                                  lsItemsBuy[index].variation ===
                                    PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                                    ? variationItemsCategories
                                    : variationItemsProducts
                                }
                                value={lsItemsBuy[index].outletItemSku}
                                // multiple={true}
                                open={openList2[index]}
                                setOpen={(value) => {
                                  setOpenList2((prevOpenList) => {
                                    const newOpenList = [...prevOpenList];
                                    newOpenList[index] = value;
                                    return newOpenList;
                                  });
                                }}
                                onSelectItem={(item) => {
                                  if (item) {
                                    setLsItemsBuy(
                                      lsItemsBuy.map((lsItem, i) =>
                                        i === index
                                          ? {
                                            ...lsItem,
                                            outletItemSku: item.value,
                                            itemImage: item.image,
                                            itemName: item.name,
                                            itemDescription: item.description,
                                          }
                                          : lsItem
                                      )
                                    );
                                  }
                                }}
                              />
                            )
                          }
                        </View>
                      </View>
                    </View>
                  </View>

                  <View
                    style={{
                      width: switchMerchant ? "4%" : "7%",
                      marginLeft: 3,
                      marginRight: 5,
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: "500",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                    >
                      to get
                    </Text>
                  </View>

                  <View
                    style={{
                      width: switchMerchant ? "7%" : "7%",
                      marginRight: 5,
                      marginLeft: 7,
                    }}
                  >
                    {/* <Text style={{ fontWeight: '500', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}>
                    {quantityGet}10
                  </Text> */}
                    <TextInput
                      placeholder="0"
                      placeholderTextColor={Platform.select({ ios: "#a9a9a9" })}
                      placeholderStyle={{
                        fontFamily: "NunitoSans-Regular",
                        fontSize: 14,
                      }}
                      style={[
                        {
                          backgroundColor: Colors.fieldtBgColor,
                          width: 50,
                          height: 40,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: "#E5E5E5",
                          paddingLeft: 10,
                          fontFamily: "NunitoSans-Regular",
                          fontSize: 14,
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            width: "100%",
                            height: 35,
                          }
                          : {},
                      ]}
                      keyboardType={"decimal-pad"}
                      // onChangeText={(text) => {
                      //   setQuantityGet(text)
                      // }}
                      // defaultValue={quantityGet ? quantityGet : '0'}
                      ////value={quantityGet}
                      onChangeText={(text) => {
                        // setSelectedVariationItems(selectedVariationItems.map((stampItem, i) => (i === index ? {
                        //   ...stampItem,
                        //   quantity: text,

                        // } : stampItem)))
                        setLsItemsBuy(
                          lsItemsBuy.map((lsItem, i) =>
                            i === index
                              ? {
                                ...lsItem,
                                // quantity: text,
                                noOfStamp: text,
                              }
                              : lsItem
                          )
                        );
                      }}
                      defaultValue={lsItemsBuy[index].noOfStamp}
                      value={lsItemsBuy[index].noOfStamp}
                    />
                  </View>

                  <View
                    style={{
                      width: switchMerchant ? "8%" : "10%",
                      marginHorizontal: 5,
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: "500",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                    >
                      of Stamps
                    </Text>
                  </View>
                </View>
                {/* <View style={{ flexDirection: 'row', position: 'relative', alignItems: 'center', paddingHorizontal: switchMerchant ? 0 : 20, }}>
                  <View style={{ width: switchMerchant ? '6%' : '5%', marginHorizontal: 5 }}>
                    <Text style={{ fontWeight: '500', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}>
                      Criteria
                    </Text>
                  </View>
                  <View style={{ width: switchMerchant ? '80%' : '80%', marginHorizontal: 5 }}>
                    <TextInput
                      placeholder='Criteria'
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: 14 }}
                      style={[{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '100%',
                        height: 40,
                        borderRadius: 5,
                        padding: 5,
                        marginVertical: 5,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        paddingLeft: 10,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: 14,
                      }, switchMerchant ? {
                        fontSize: 10,
                        width: '100%',
                        height: 35,
                      } : {}]}
                      //keyboardType={'decimal-pad'}
                      // onChangeText={(text) => {
                      //   setQuantityGet(text)
                      // }}
                      // defaultValue={quantityGet ? quantityGet : '0'}
                      ////value={quantityGet}
                      onChangeText={(text) => {
                        // setSelectedVariationItems(selectedVariationItems.map((stampItem, i) => (i === index ? {
                        //   ...stampItem,
                        //   quantity: text,

                        // } : stampItem)))
                        setLsItems(lsItems.map((lsItem, i) => (i === index ? {
                          ...lsItem,
                          tc: text,
                        } : lsItem)))
                      }}
                      defaultValue={lsItems[index].tc}
                    />
                  </View>
                </View> */}
              </View>
              <View style={{ flexDirection: "column", width: "3%" }}>
                <View style={{ width: "100%" }}>
                  <TouchableOpacity
                    style={{ alignSelf: "flex-start" }}
                    onPress={() => {
                      setLsItemsBuy([
                        ...lsItemsBuy.slice(0, index),
                        ...lsItemsBuy.slice(index + 1),
                      ]);
                    }}
                  >
                    <Feather
                      name="trash-2"
                      size={switchMerchant ? 15 : 20}
                      color="#eb3446"
                    />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          );
        })}
      </>
    );
  };

  const renderStampItems = () => {
    return (
      <>
        {lsItems.map((item, index) => {
          return (
            <View
              style={{
                position: "relative",
                //backgroundColor: '#ffffff',
                flexDirection: "row",
                paddingVertical: 10,
                marginLeft: switchMerchant ? "3.5%" : 0,

                // borderBottomWidth: StyleSheet.hairlineWidth,
                // borderBottomColor: '#c4c4c4',
                alignItems: "center",
                width: "100%",
                zIndex: 10001 + lsItems.length - index,
              }}
            >
              <View style={{ flexDirection: "column", width: "90%" }}>
                <View
                  style={{
                    flexDirection: "row",
                    position: "relative",
                    alignItems: "center",
                    paddingHorizontal: switchMerchant ? 0 : 20,
                  }}
                >
                  <View
                    style={{
                      width: switchMerchant ? "3%" : "5%",
                      marginHorizontal: 5,
                      justifyContent: "center",
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: "500",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                        textAlignVertical: "center",
                      }}
                    >
                      Get
                    </Text>
                  </View>

                  <View
                    style={{
                      flexDirection: "row",
                      width: switchMerchant ? "7%" : "7%",
                      marginHorizontal: 5,
                    }}
                  >
                    {/* <Text style={{ fontWeight: '500', height: 28, paddingTop: 5, marginRight: 2, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}>
            {numberOfStamp}10
          </Text>
          <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, alignSelf: 'flex-start' }}>
            {ordinals}th
          </Text> */}
                    <TextInput
                      placeholder="No."
                      placeholderTextColor={Platform.select({ ios: "#a9a9a9" })}
                      placeholderStyle={{
                        color: "grey",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: 14,
                      }}
                      style={[
                        {
                          backgroundColor: Colors.fieldtBgColor,
                          width: 50,
                          height: 40,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: "#E5E5E5",
                          paddingLeft: 10,
                          fontFamily: "NunitoSans-Regular",
                          fontSize: 14,
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            width: "100%",
                            height: 35,
                          }
                          : {},
                      ]}
                      //   onChangeText={(text) => {
                      //     setNumberOfStamp(text)
                      //   }}
                      //   defaultValue={numberOfStamp ? numberOfStamp : 'No.'}
                      // //value={numberOfStamp}
                      keyboardType={"decimal-pad"}
                      onChangeText={(text) => {
                        // setSelectedVariationItems(selectedVariationItems.map((stampItem, i) => (i === index ? {
                        //   ...stampItem,
                        //   noOfStamp: text,

                        // } : stampItem)))
                        setLsItems(
                          lsItems.map((lsItem, i) =>
                            i === index
                              ? {
                                ...lsItem,
                                // noOfStamp: text,
                                quantity: text,
                              }
                              : lsItem
                          )
                        );
                      }}
                      defaultValue={lsItems[index].quantity}
                    />
                  </View>

                  <View
                    style={{
                      width: switchMerchant ? "3%" : "3%",
                      marginHorizontal: 5,
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: "500",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                    >
                      of
                    </Text>
                  </View>

                  <View
                    style={{
                      width: switchMerchant ? "23%" : "23%",
                      marginHorizontal: 5,
                    }}
                  >
                    <View
                      style={{
                        zIndex: 10001 + lsItems.length - index,
                      }}
                    >
                      <View
                        style={{
                          flexDirection: "row",
                          //paddingBottom: 10,
                          //marginBottom: 15,
                          alignItems: "center",
                        }}
                      >
                        <View
                          style={{
                            //width: '40%', marginHorizontal: 5,
                            height: switchMerchant ? 35 : 40,
                            justifyContent: "center",
                            //alignItems: 'flex-end',
                            alignSelf: "flex-end",
                            width: switchMerchant
                              ? "100%"
                              : windowWidth < 1750
                                ? 200
                                : "100%",
                          }}
                        >
                          {
                            [
                              // {
                              //   label: 'Product Category',
                              //   value: PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY,
                              // },
                              {
                                label: 'Products',
                                value: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
                              },
                            ].find(findItem => {
                              if (findItem.value === lsItems[index].variation) {
                                return true;
                              }
                            })
                              ?
                              <DropDownPicker
                                style={{
                                  backgroundColor: Colors.fieldtBgColor,
                                  // width: 210,
                                  height: 40,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  flexDirection: "row",
                                }}
                                dropDownContainerStyle={{
                                  // width: 210,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderColor: "#E5E5E5",
                                }}
                                labelStyle={{
                                  marginLeft: 5,
                                  flexDirection: "row",
                                }}
                                textStyle={{
                                  fontSize: 14,
                                  fontFamily: 'NunitoSans-Regular',

                                  marginLeft: 5,
                                  paddingVertical: 10,
                                  flexDirection: "row",
                                }}
                                selectedItemContainerStyle={{
                                  flexDirection: "row",
                                }}

                                showArrowIcon={true}
                                ArrowDownIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-down-outline"
                                  />
                                )}
                                ArrowUpIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-up-outline"
                                  />
                                )}

                                showTickIcon={true}
                                TickIconComponent={({ press }) => (
                                  <Ionicon
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    color={
                                      press ? Colors.fieldtBgColor : Colors.primaryColor
                                    }
                                    name={'md-checkbox'}
                                    size={25}
                                  />
                                )}
                                placeholderStyle={{
                                  color: Colors.fieldtTxtColor,
                                  // marginTop: 15,
                                }}
                                dropDownDirection="BOTTOM"
                                // placeholder="Choose Outlet"
                                // multipleText={`${selectedOutletList.length} outlet(s) selected`}
                                // multipleText={'%d outlet(s) selected'}
                                // items={PROMOTION_TYPE_VARIATION_DROPDOWN_LIST}
                                items={[
                                  // {
                                  //   label: 'Product Category',
                                  //   value: PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY,
                                  // },
                                  {
                                    label: 'Products',
                                    value: PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS,
                                  },
                                ]}
                                value={lsItems[index].variation}
                                // multiple={true}
                                open={openList3[index]}
                                setOpen={(value) => {
                                  setOpenList3((prevOpenList) => {
                                    const newOpenList = [...prevOpenList];
                                    newOpenList[index] = value;
                                    return newOpenList;
                                  });
                                }}
                                onSelectItem={(item) => {
                                  if (item) {
                                    if (lsItems[index].variation !== item) {
                                      setLsItems(
                                        lsItems.map((lsItem, i) =>
                                          i === index
                                            ? {
                                              ...lsItem,
                                              variation: item.value,

                                              outletItemSku:
                                                item ===
                                                  PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                                                  ? variationItemsProducts[0]
                                                    .value
                                                  : variationItemsCategories[0]
                                                    .value,
                                              itemName:
                                                item ===
                                                  PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                                                  ? variationItemsProducts[0].name
                                                  : variationItemsCategories[0]
                                                    .name,
                                              itemDescription:
                                                item ===
                                                  PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                                                  ? variationItemsProducts[0]
                                                    .description
                                                  : variationItemsCategories[0]
                                                    .description,
                                              itemImage:
                                                item ===
                                                  PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS
                                                  ? variationItemsProducts[0]
                                                    .image
                                                  : variationItemsCategories[0]
                                                    .image,
                                            }
                                            : lsItem
                                        )
                                      );
                                    } else {
                                      setLsItems(
                                        lsItems.map((lsItem, i) =>
                                          i === index
                                            ? {
                                              ...lsItem,
                                              variation: item.value,
                                            }
                                            : lsItem
                                        )
                                      );
                                    }
                                  }
                                }}
                              />
                              :
                              <></>
                          }
                        </View>
                      </View>
                    </View>
                  </View>

                  <View
                    style={{
                      width: switchMerchant ? "23%" : "23%",
                      marginHorizontal: 5,
                    }}
                  >
                    <View
                      style={{
                        zIndex: 10001 + lsItems.length - index,
                      }}
                    >
                      <View
                        style={{
                          flexDirection: "row",
                          //paddingBottom: 10,
                          //marginBottom: 15,
                          alignItems: "center",
                        }}
                      >
                        <View
                          style={{
                            //width: '40%', marginHorizontal: 5,
                            height: switchMerchant ? 35 : 40,
                            justifyContent: "center",
                            //alignItems: 'flex-end',
                            alignSelf: "flex-end",
                            width: switchMerchant
                              ? "100%"
                              : windowWidth < 1750
                                ? 200
                                : "100%",
                          }}
                        >
                          {
                            // (variationItemsDropdownList.find(variationItem => variationItem.value === item.outletItemSku) || item.outletItemSku === '')
                            (
                              lsItems[index].variation ===
                                PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                                ? variationItemsCategories.length > 0 &&
                                variationItemsCategories.find(
                                  (item) =>
                                    item.value ===
                                    lsItems[index].outletItemSku
                                )
                                : variationItemsProducts.length > 0 &&
                                variationItemsProducts.find(
                                  (item) =>
                                    item.value ===
                                    lsItems[index].outletItemSku
                                )
                            ) ? (
                              <DropDownPicker
                                style={{
                                  backgroundColor: Colors.fieldtBgColor,
                                  // width: 210,
                                  height: 40,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  flexDirection: "row",
                                }}
                                dropDownContainerStyle={{
                                  // width: 210,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderColor: "#E5E5E5",
                                }}
                                labelStyle={{
                                  marginLeft: 5,
                                  flexDirection: "row",
                                }}
                                textStyle={{
                                  fontSize: 14,
                                  fontFamily: 'NunitoSans-Regular',

                                  marginLeft: 5,
                                  paddingVertical: 10,
                                  flexDirection: "row",
                                }}
                                selectedItemContainerStyle={{
                                  flexDirection: "row",
                                }}

                                showArrowIcon={true}
                                ArrowDownIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-down-outline"
                                  />
                                )}
                                ArrowUpIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-up-outline"
                                  />
                                )}

                                showTickIcon={true}
                                TickIconComponent={({ press }) => (
                                  <Ionicon
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    color={
                                      press ? Colors.fieldtBgColor : Colors.primaryColor
                                    }
                                    name={'md-checkbox'}
                                    size={25}
                                  />
                                )}
                                placeholderStyle={{
                                  color: Colors.fieldtTxtColor,
                                  // marginTop: 15,
                                }}
                                dropDownDirection="BOTTOM"
                                // placeholder="Choose Outlet"
                                // multipleText={`${selectedOutletList.length} outlet(s) selected`}
                                // multipleText={'%d outlet(s) selected'}
                                items={
                                  lsItems[index].variation ===
                                    PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                                    ? variationItemsCategories
                                    : variationItemsProducts
                                }
                                value={lsItems[index].outletItemSku}
                                // multiple={true}
                                open={openList4[index]}
                                setOpen={(value) => {
                                  setOpenList4((prevOpenList) => {
                                    const newOpenList = [...prevOpenList];
                                    newOpenList[index] = value;
                                    return newOpenList;
                                  });
                                }}
                                onSelectItem={(item) => {
                                  if (item) {
                                    setLsItems(
                                      lsItems.map((lsItem, i) =>
                                        i === index
                                          ? {
                                            ...lsItem,
                                            outletItemSku: item.value,
                                            itemImage: item.image,
                                            itemName: item.name,
                                            itemDescription: item.description,
                                          }
                                          : lsItem
                                      )
                                    );
                                  }
                                }}
                              />
                            ) : (
                              <DropDownPicker
                                style={{
                                  backgroundColor: Colors.fieldtBgColor,
                                  // width: 210,
                                  height: 40,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  flexDirection: "row",
                                }}
                                dropDownContainerStyle={{
                                  // width: 210,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderColor: "#E5E5E5",
                                }}
                                labelStyle={{
                                  marginLeft: 5,
                                  flexDirection: "row",
                                }}
                                textStyle={{
                                  fontSize: 14,
                                  fontFamily: 'NunitoSans-Regular',

                                  marginLeft: 5,
                                  paddingVertical: 10,
                                  flexDirection: "row",
                                }}
                                selectedItemContainerStyle={{
                                  flexDirection: "row",
                                }}

                                showArrowIcon={true}
                                ArrowDownIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-down-outline"
                                  />
                                )}
                                ArrowUpIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-up-outline"
                                  />
                                )}

                                showTickIcon={true}
                                TickIconComponent={({ press }) => (
                                  <Ionicon
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    color={
                                      press ? Colors.fieldtBgColor : Colors.primaryColor
                                    }
                                    name={'md-checkbox'}
                                    size={25}
                                  />
                                )}
                                placeholderStyle={{
                                  color: Colors.fieldtTxtColor,
                                  // marginTop: 15,
                                }}
                                dropDownDirection="BOTTOM"
                                // placeholder="Choose Outlet"
                                // multipleText={`${selectedOutletList.length} outlet(s) selected`}
                                // multipleText={'%d outlet(s) selected'}
                                items={
                                  lsItems[index].variation ===
                                    PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY
                                    ? variationItemsCategories
                                    : variationItemsProducts
                                }
                                value={lsItems[index].outletItemSku}
                                // multiple={true}
                                open={openList4[index]}
                                setOpen={(value) => {
                                  setOpenList4((prevOpenList) => {
                                    const newOpenList = [...prevOpenList];
                                    newOpenList[index] = value;
                                    return newOpenList;
                                  });
                                }}
                                onSelectItem={(item) => {
                                  if (item) {
                                    setLsItems(
                                      lsItems.map((lsItem, i) =>
                                        i === index
                                          ? {
                                            ...lsItem,
                                            outletItemSku: item.value,
                                            itemImage: item.image,
                                            itemName: item.name,
                                            itemDescription: item.description,
                                          }
                                          : lsItem
                                      )
                                    );
                                  }
                                }}
                              />
                            )
                          }
                        </View>
                      </View>
                    </View>
                  </View>

                  <View
                    style={{
                      width: switchMerchant
                        ? "4%"
                        : windowWidth < 1750
                          ? 30
                          : "4%",
                      marginHorizontal: 5,
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: "500",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                    >
                      with
                    </Text>
                  </View>

                  <View
                    style={{
                      width: switchMerchant ? "7%" : "7%",
                      marginHorizontal: 5,
                    }}
                  >
                    {/* <Text style={{ fontWeight: '500', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}>
                    {quantityGet}10
                  </Text> */}
                    <TextInput
                      placeholder="0"
                      placeholderTextColor={Platform.select({ ios: "#a9a9a9" })}
                      placeholderStyle={{
                        fontFamily: "NunitoSans-Regular",
                        fontSize: 14,
                      }}
                      style={[
                        {
                          backgroundColor: Colors.fieldtBgColor,
                          width: 50,
                          height: 40,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: "#E5E5E5",
                          paddingLeft: 10,
                          fontFamily: "NunitoSans-Regular",
                          fontSize: 14,
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            width: "100%",
                            height: 35,
                          }
                          : {},
                      ]}
                      keyboardType={"decimal-pad"}
                      // onChangeText={(text) => {
                      //   setQuantityGet(text)
                      // }}
                      // defaultValue={quantityGet ? quantityGet : '0'}
                      ////value={quantityGet}
                      onChangeText={(text) => {
                        // setSelectedVariationItems(selectedVariationItems.map((stampItem, i) => (i === index ? {
                        //   ...stampItem,
                        //   quantity: text,

                        // } : stampItem)))
                        setLsItems(
                          lsItems.map((lsItem, i) =>
                            i === index
                              ? {
                                ...lsItem,
                                noOfStamp: text,
                              }
                              : lsItem
                          )
                        );
                      }}
                      defaultValue={lsItems[index].noOfStamp}
                    />
                  </View>

                  <View
                    style={{
                      width: switchMerchant ? "8%" : "10%",
                      marginHorizontal: 5,
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: "500",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                    >
                      of Stamps
                    </Text>
                  </View>
                </View>
                {/* <View style={{ flexDirection: 'row', position: 'relative', alignItems: 'center', paddingHorizontal: switchMerchant ? 0 : 20, }}>
                  <View style={{ width: switchMerchant ? '6%' : '5%', marginHorizontal: 5 }}>
                    <Text style={{ fontWeight: '500', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}>
                      Criteria
                    </Text>
                  </View>
                  <View style={{ width: switchMerchant ? '80%' : '80%', marginHorizontal: 5 }}>
                    <TextInput
                      placeholder='Criteria'
                      placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                      placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: 14 }}
                      style={[{
                        backgroundColor: Colors.fieldtBgColor,
                        width: '100%',
                        height: 40,
                        borderRadius: 5,
                        padding: 5,
                        marginVertical: 5,
                        borderWidth: 1,
                        borderColor: '#E5E5E5',
                        paddingLeft: 10,
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: 14,
                      }, switchMerchant ? {
                        fontSize: 10,
                        width: '100%',
                        height: 35,
                      } : {}]}
                      //keyboardType={'decimal-pad'}
                      // onChangeText={(text) => {
                      //   setQuantityGet(text)
                      // }}
                      // defaultValue={quantityGet ? quantityGet : '0'}
                      ////value={quantityGet}
                      onChangeText={(text) => {
                        // setSelectedVariationItems(selectedVariationItems.map((stampItem, i) => (i === index ? {
                        //   ...stampItem,
                        //   quantity: text,

                        // } : stampItem)))
                        setLsItems(lsItems.map((lsItem, i) => (i === index ? {
                          ...lsItem,
                          tc: text,
                        } : lsItem)))
                      }}
                      defaultValue={lsItems[index].tc}
                    />
                  </View>
                </View> */}
              </View>
              <View style={{ flexDirection: "column", width: "10%" }}>
                <View style={{ width: "100%" }}>
                  <TouchableOpacity
                    style={{ alignSelf: "flex-start" }}
                    onPress={() => {
                      setLsItems([
                        ...lsItems.slice(0, index),
                        ...lsItems.slice(index + 1),
                      ]);
                    }}
                  >
                    <Feather
                      name="trash-2"
                      size={switchMerchant ? 15 : 20}
                      color="#eb3446"
                    />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          );
        })}
      </>
    );
  };

  const createLoyaltyStamp = async () => {
    // if (
    //   !stampName ||
    //   !totalNumberOfStamp ||
    //   !startDate ||
    //   !endDate ||
    //   !startTime ||
    //   !endTime ||
    //   // !terms ||
    //   lsItems.length < 1
    // ) {
    //   window.confirm(
    //     "Error\nPlease fill in the following information:\n\nStamp Name\nTotal Stamp\nAt least one product & stamp item",
    //     [{ text: "OK", onPress: () => { } }],
    //     { cancelable: false }
    //   );
    //   return;
    const missingFields = [];

    if (!stampName) {
      missingFields.push("Stamp Name");
    }

    if (!totalNumberOfStamp) {
      missingFields.push("Total Stamp");
    }

    if (!startDate) {
      missingFields.push("Start Date");
    }

    if (!endDate) {
      missingFields.push("End Date");
    }

    if (!startTime) {
      missingFields.push("Start Time");
    }

    if (!endTime) {
      missingFields.push("End Time");
    }

    if (lsItems.length < 1) {
      missingFields.push("At least one product & stamp item");
    }

    if (missingFields.length > 0) {
      const errorMessage = "Error\nPlease fill in the following information:\n\n" + missingFields.join("\n");
      window.confirm(errorMessage, [{ text: "OK", onPress: () => { } }], { cancelable: false });
      return;
    } else {
      var body = {
        name: stampName,
        totalNumberOfStamp: parseInt(totalNumberOfStamp),
        startDate: moment(startDate).valueOf(),
        endDate: moment(endDate).valueOf(),
        startTime: moment(startTime).valueOf(),
        endTime: moment(endTime).valueOf(),
        terms: terms,

        lsItems: lsItems.map((item) => ({
          lsItemId: item.lsItemId,
          outletItemSku: item.outletItemSku,
          variation: item.variation,
          isActive: item.isActive,
          noOfStamp: parseInt(item.noOfStamp),
          quantity: parseInt(item.quantity),

          itemName: item.itemName,
          itemDescription: item.itemDescription,
          itemImage: item.itemImage,
        })),

        lsItemsBuy: lsItemsBuy.map((item) => ({
          lsItemId: item.lsItemId,
          outletItemSku: item.outletItemSku,
          variation: item.variation,
          isActive: item.isActive,
          noOfStamp: parseInt(item.noOfStamp),
          quantity: parseInt(item.quantity),

          itemName: item.itemName,
          itemDescription: item.itemDescription,
          itemImage: item.itemImage,
        })),

        outletIdList: [currOutlet.uniqueId],
        outletNameList: [currOutlet.name],

        outletId: currOutlet.uniqueId,

        merchantId: merchantId,
        merchantName: merchantName,
        merchantLogo: merchantLogo,

        targetSegments: targetUsers,
      };

      console.log("BODY", body);

      CommonStore.update((s) => {
        s.isLoading = true;
      });

      ApiClient.POST(API.createLoyaltyStamp, body).then((result) => {
        // console.log('RESULT', result);

        if (result && result.status === "success") {
          window.confirm(
            "Success\nLoyalty stamp has been created",
            [
              {
                text: "OK",
                onPress: () => {
                  setStampName("");
                  setTotalNumberOfStamp("0");
                  setStartDate(moment().valueOf());
                  setStartTime(moment().valueOf());
                  setEndDate(moment().valueOf());
                  setEndTime(moment().valueOf());
                  setTerms("");

                  setAddLoyaltyStamp(
                    ADD_LOYALTY_STAMP_SCREEN.LOYALTY_STAMP_LIST
                  );
                },
              },
            ],
            { cancelable: false }
          );
          CommonStore.update((s) => {
            s.isLoading = false;
          });
        } else {
          window.confirm(
            "Error, Failed to create loyalty stamp",
            [
              {
                text: "OK",
                onPress: () => { },
              },
            ],
            { cancelable: false }
          );

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        }
      });
    }
  };

  const updateLoyaltyStamp = async () => {
    // if (
    //   !stampName ||
    //   !totalNumberOfStamp ||
    //   !startDate ||
    //   !endDate ||
    //   !startTime ||
    //   !endTime ||
    //   // !terms ||
    //   lsItems.length < 1
    // ) {
    //   window.confirm(
    //     "Error",
    //     "Please fill in the following information:\n\nStamp Name\nTotal Stamp\nAt least one product & stamp item",
    //     [{ text: "OK", onPress: () => { } }],
    //     { cancelable: false }
    //   );
    //   return;
    const missingFields = [];

    if (!stampName) {
      missingFields.push("Stamp Name");
    }

    if (!totalNumberOfStamp) {
      missingFields.push("Total Stamp");
    }

    if (!startDate) {
      missingFields.push("Start Date");
    }

    if (!endDate) {
      missingFields.push("End Date");
    }

    if (!startTime) {
      missingFields.push("Start Time");
    }

    if (!endTime) {
      missingFields.push("End Time");
    }

    if (lsItems.length < 1) {
      missingFields.push("At least one product & stamp item");
    }

    if (missingFields.length > 0) {
      const errorMessage = "Error\nPlease fill in the following information:\n\n" + missingFields.join("\n");
      window.confirm(errorMessage, [{ text: "OK", onPress: () => { } }], { cancelable: false });
      return;
    } else {
      var body = {
        name: stampName,
        totalNumberOfStamp: parseInt(totalNumberOfStamp),
        startDate: moment(startDate).valueOf(),
        endDate: moment(endDate).valueOf(),
        startTime: moment(startTime).valueOf(),
        endTime: moment(endTime).valueOf(),
        terms: terms,

        lsItems: lsItems.map((item) => ({
          lsItemId: item.lsItemId,
          outletItemSku: item.outletItemSku,
          variation: item.variation,
          isActive: item.isActive,
          noOfStamp: parseInt(item.noOfStamp),
          quantity: parseInt(item.quantity),

          itemName: item.itemName,
          itemDescription: item.itemDescription,
          itemImage: item.itemImage,
        })),

        lsItemsBuy: lsItemsBuy.map((item) => ({
          lsItemId: item.lsItemId,
          outletItemSku: item.outletItemSku,
          variation: item.variation,
          isActive: item.isActive,
          noOfStamp: parseInt(item.noOfStamp),
          quantity: parseInt(item.quantity),

          itemName: item.itemName,
          itemDescription: item.itemDescription,
          itemImage: item.itemImage,
        })),

        outletIdList: [currOutlet.uniqueId],
        outletNameList: [currOutlet.name],

        outletId: currOutlet.uniqueId,

        merchantId: merchantId,
        merchantName: merchantName,
        merchantLogo: merchantLogo,

        targetSegments: targetUsers,

        lsId: lsId,
      };

      // console.log('BODY', body);

      CommonStore.update((s) => {
        s.isLoading = true;
      });

      ApiClient.POST(API.updateLoyaltyStamp, body).then((result) => {
        // console.log('RESULT', result);

        if (result && result.status === "success") {
          window.confirm(
            "Success",
            "Loyalty stamp has been updated",
            [
              {
                text: "OK",
                onPress: () => {
                  setStampName("");
                  setTotalNumberOfStamp("0");
                  setStartDate(moment().valueOf());
                  setStartTime(moment().valueOf());
                  setEndDate(moment().valueOf());
                  setEndTime(moment().valueOf());
                  setTerms("");

                  setAddLoyaltyStamp(
                    ADD_LOYALTY_STAMP_SCREEN.LOYALTY_STAMP_LIST
                  );
                },
              },
            ],
            { cancelable: false }
          );

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        } else {
          window.confirm(
            "Error",
            "Failed to update loyalty stamp",
            [
              {
                text: "OK",
                onPress: () => { },
              },
            ],
            { cancelable: false }
          );

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        }
      });
    }
  };

  const renderItems = ({ item, index }) => {
    return (
      <View
        style={{
          position: "relative",
          //backgroundColor: '#ffffff',
          flexDirection: "row",
          paddingVertical: 20,
          paddingHorizontal: 20,
          // borderBottomWidth: StyleSheet.hairlineWidth,
          // borderBottomColor: '#c4c4c4',
          alignItems: "center",
          zIndex: 1,
        }}
      >
        <View
          style={{
            flexDirection: "row",
            width: switchMerchant ? "20%" : "10%",
            marginRight: 5,
            marginTop: 1,
          }}
        >
          {/* <Text style={{ fontWeight: '500', height: 28, paddingTop: 5, marginRight: 2, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}>
            {numberOfStamp}10
          </Text>
          <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, alignSelf: 'flex-start' }}>
            {ordinals}th
          </Text> */}
          <TextInput
            placeholder="No."
            placeholderTextColor={Platform.select({ ios: "#a9a9a9" })}
            placeholderStyle={{
              color: "grey",
              fontFamily: "NunitoSans-Regular",
              fontSize: 14,
            }}
            style={[
              {
                backgroundColor: Colors.fieldtBgColor,
                width: 100,
                height: 40,
                borderRadius: 5,
                padding: 5,
                marginVertical: 5,
                borderWidth: 1,
                borderColor: "#E5E5E5",
                paddingLeft: 10,
                fontFamily: "NunitoSans-Regular",
                fontSize: 14,
              },
              switchMerchant
                ? {
                  fontSize: 10,
                  width: "85%",
                  height: 35,
                }
                : {},
            ]}
            //   onChangeText={(text) => {
            //     setNumberOfStamp(text)
            //   }}
            //   defaultValue={numberOfStamp ? numberOfStamp : 'No.'}
            // //value={numberOfStamp}
            onChangeText={(text) => {
              setLsItems(
                lsItems.map((stampItem, i) =>
                  i === index
                    ? {
                      ...stampItem,
                      noOfStamp: parseInt(text),
                    }
                    : stampItem
                )
              );
            }}
            defaultValue={lsItems[index].noOfStamp}
          />
        </View>

        <View style={{ width: "10%", marginHorizontal: 5 }}>
          <Text
            style={{
              fontWeight: "500",
              fontFamily: "NunitoSans-Regular",
              fontSize: switchMerchant ? 10 : 14,
            }}
          >
            Stamp will get
          </Text>
        </View>

        <View
          style={{ width: switchMerchant ? "20%" : "15%", marginHorizontal: 5 }}
        >
          {/* <Text style={{ fontWeight: '500', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}>
            {quantityGet}10
          </Text> */}
          <TextInput
            placeholder="0"
            placeholderTextColor={Platform.select({ ios: "#a9a9a9" })}
            placeholderStyle={{
              fontFamily: "NunitoSans-Regular",
              fontSize: 14,
            }}
            style={[
              {
                backgroundColor: Colors.fieldtBgColor,
                width: 100,
                height: 40,
                borderRadius: 5,
                padding: 5,
                marginVertical: 5,
                borderWidth: 1,
                borderColor: "#E5E5E5",
                paddingLeft: 10,
                fontFamily: "NunitoSans-Regular",
                fontSize: 14,
              },
              switchMerchant
                ? {
                  fontSize: 10,
                  width: "85%",
                  height: 35,
                }
                : {},
            ]}
            keyboardType={"decimal-pad"}
            // onChangeText={(text) => {
            //   setQuantityGet(text)
            // }}
            // defaultValue={quantityGet ? quantityGet : '0'}
            ////value={quantityGet}
            onChangeText={(text) => {
              setLsItems(
                lsItems.map((stampItem, i) =>
                  i === index
                    ? {
                      ...stampItem,
                      quantity: parseInt(text),
                    }
                    : stampItem
                )
              );
            }}
            defaultValue={lsItems[index].quantity}
          />
        </View>

        <View style={{ width: "40%", marginHorizontal: 5 }}>
          {/* <Text style={{ fontWeight: '500', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}> */}
          {/* {selectedVariationItems}Chicken Soup */}
          {/* {renderProducts()} */}
          {/* <DropDownPicker
                  containerStyle={{ height: 40 }}
                  arrowColor={'black'}
                  arrowSize={20}
                  arrowStyle={{ fontWeight: 'bold' }}
                  labelStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}
                  style={{ width: 200, paddingVertical: 0, borderRadius: 10 }}
                  placeholderStyle={{ color: Colors.fieldtTxtColor, alignItems: 'center', justifyContent: 'center', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                  placeholder={"Products"}
                  items={variationItemsDropdownList}
                  itemStyle={{ justifyContent: 'flex-start', marginLeft: 5, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, zIndex: 1 }}
                  customTickIcon={(press) => <Ionicon name={"md-checkbox"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                  onChangeItem={(value) =>{
                    setSelectedVariationItems(
                      selectedVariationItems.map((varItem, i) =>
                      i === index ? {
                          ...varItem,
                          noOfStamp: '',
                          productName: '',
                          quantity: 0,
                      } : varItem)
                      )
                    }
                  }
                  //multiple={true}
                  zIndex={1000}
                  searchable={true}
                  dropDownMaxHeight={190}
                  dropDownStyle={{ width: 200, height: 190, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1, zIndex: 1}}
                /> */}
          {/* </Text>  */}
          {/* <View style={{
                              width: 200, 
                              //paddingLeft: 10, 
                              //alignSelf: 'center', 
                              height: switchMerchant ? 35 : 40, 
                              backgroundColor: Colors.fieldtBgColor, 
                              borderRadius: 5, 
                              borderWidth: 1, 
                              borderColor: '#E5E5E5',
                              justifyContent: 'center',
                              //opacity: 0,
                            }}>
          <RNPickerSelect
                  useNativeAndroidPickerStyle={false}
                  style={{
                    inputIOS: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, paddingVertical: 5, color: 'black' },
                    inputAndroid: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, paddingVertical: 5, color: 'black' },
                    inputAndroidContainer: {
                      //backgroundColor: 'red',
                      width: '100%',
                    }
                  }}
                  //contentContainerStyle={{ fontSize: switchMerchant ? 10 : 14, }}
                  items={variationItemsDropdownList}
                  value={selectedVariationItems}
                  //placeholder='Select a Product'
                  onValueChange={(value) =>
                    setSelectedVariationItems(value)
                  }
                />
</View> */}
        </View>

        <View
          style={{
            width: switchMerchant ? "10%" : "19%",
            alignItems: "baseline",
          }}
        >
          <TouchableOpacity
            style={{
              marginLeft: 10,
              alignSelf: switchMerchant ? "flex-start" : "flex-end",
            }}
            onPress={() => {
              setLsItems([
                ...lsItems.slice(0, index),
                ...lsItems.slice(index + 1),
              ]);
            }}
          >
            <Feather name="trash-2" size={20} color="#eb3446" />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  //////////////////////////////////////////////////////////////////
  //Render Start Here
  return (
    // <UserIdleWrapper disabled={!isMounted}>
    <View
      style={[
        styles.container,
        {
          height: windowHeight,
          width: windowWidth,
        },
      ]}
    >
      <View style={{ flex: 0.8 }}>
        <SideBar navigation={navigation} selectedTab={0} />
      </View>

      <View style={{ height: windowHeight, flex: 9 }}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ width: windowWidth * 0.9 }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}
        >
          <Modal
            style={
              {
                //  flex: 1
              }
            }
            visible={stampItemsModal}
            supportedOrientations={["landscape", "portrait"]}
            transparent={true}
            animationType={"slide"}
          >
            <View style={styles.modalContainer}>
              <View style={[styles.modalView, {}]}>
                <TouchableOpacity
                  style={[styles.closeButton, {
                    right: windowWidth * 0.02,
                    top: windowWidth * 0.02,
                  },]}
                  onPress={() => {
                    setStampItemsModal(false);
                  }}
                >
                  <AntDesign
                    name="closecircle"
                    size={25}
                    color={Colors.fieldtTxtColor}
                  />
                </TouchableOpacity>
                <View style={{}}>
                  <Text
                    style={{
                      fontFamily: "NunitoSans-Bold",
                      fontSize: switchMerchant ? 10 : 16,
                      fontWeight: "600",
                      marginBottom: 0,
                    }}
                  >
                    Add Stamp Items
                  </Text>
                </View>
                <View
                  style={{
                    // borderColor: '#E5E5E5',
                    borderWidth: 1,
                    marginVertical: 15,
                  }}
                />
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <Text
                      style={{
                        fontWeight: "500",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                    >
                      The
                    </Text>
                    <View style={{ flexDirection: "row", marginLeft: 6 }}>
                      <TextInput
                        placeholder={"0"}
                        placeholderStyle={{
                          fontFamily: "NunitoSans-Regular",
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholderTextColor={Platform.select({
                          ios: "#a9a9a9",
                        })}
                        onChangeText={(text) => {
                          if (text == 1) {
                            setOrdinals("st");
                          } else if (text == 2) {
                            setOrdinals("nd");
                          } else if (text == 3) {
                            setOrdinals("rd");
                          } else {
                            setOrdinals("th");
                          }
                          setNumberOfStamp(text);
                        }}
                        style={{
                          borderRadius: 5,
                          borderColor: "#E5E5E5",
                          borderWidth: 1,
                          width: 40,
                          height: 40,
                          textAlign: "center",
                        }}
                      />
                      <Text
                        style={{
                          fontFamily: "NunitoSans-Regular",
                          fontSize: switchMerchant ? 10 : 14,
                          marginLeft: 1,
                          marginRight: 5,
                        }}
                      >
                        {ordinals}
                      </Text>
                    </View>

                    <Text
                      style={{
                        fontWeight: "500",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                    >
                      Stamp
                    </Text>
                  </View>

                  <View style={{ marginHorizontal: 6 }}>
                    <Text
                      style={{
                        fontWeight: "500",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                    >
                      Gets
                    </Text>
                  </View>

                  <View style={{ marginRight: 6 }}>
                    <TextInput
                      style={{
                        borderRadius: 5,
                        borderColor: "#E5E5E5",
                        borderWidth: 1,
                        width: 45,
                        height: 40,
                        textAlign: "center",
                      }}
                      placeholder={"0"}
                      placeholderStyle={{
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      placeholderTextColor={Platform.select({ ios: "#a9a9a9" })}
                      onChangeText={(text) => {
                        setQuantityGet(text);
                      }}
                      value={quantityGet}
                    />
                  </View>

                  <View style={{ marginLeft: 6 }}>
                    <Picker
                      containerStyle={{ height: 40 }}
                      arrowColor={"black"}
                      arrowSize={20}
                      arrowStyle={{ fontWeight: "bold" }}
                      labelStyle={{
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      style={{
                        width: 200,
                        paddingVertical: 0,
                        borderRadius: 10,
                      }}
                      placeholderStyle={{
                        color: Colors.fieldtTxtColor,
                        alignItems: "center",
                        justifyContent: "center",
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      placeholder={"Products"}
                      itemStyle={{
                        justifyContent: "flex-start",
                        marginLeft: 5,
                        fontFamily: "NunitoSans-Regular",
                        fontSize: switchMerchant ? 10 : 14,
                      }}
                      customTickIcon={(press) => (
                        <Ionicon
                          name={"md-checkbox"}
                          color={
                            press ? Colors.fieldtBgColor : Colors.primaryColor
                          }
                          size={25}
                        />
                      )}
                      onValueChange={(items) =>
                        setSelectedVariationItems(items)
                      }
                      //multiple={true}
                      searchable={true}
                      dropDownMaxHeight={190}
                      dropDownStyle={{
                        width: 200,
                        height: 190,
                        backgroundColor: Colors.fieldtBgColor,
                        borderRadius: 10,
                        borderWidth: 1,
                        zIndex: 1,
                      }}
                    >
                      {variationItemsDropdownList.map((value, index) => {
                        return (
                          <Picker.Item
                            key={index}
                            value={value.value}
                            label={value.label}
                          />
                        );
                      })}
                    </Picker>
                  </View>
                </View>
                <View
                  style={{ alignItems: "flex-end", marginTop: 30, zIndex: -1 }}
                >
                  <TouchableOpacity
                    style={{
                      justifyContent: "center",
                      flexDirection: "row",
                      borderWidth: 1,
                      borderColor: Colors.primaryColor,
                      backgroundColor: "#4E9F7D",
                      borderRadius: 5,
                      width: switchMerchant ? 100 : 120,
                      paddingHorizontal: 10,
                      height: switchMerchant ? 35 : 40,
                      alignItems: "center",
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      zIndex: -1,
                    }}
                  >
                    <Text
                      style={{
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: "NunitoSans-Bold",
                      }}
                    >
                      {isLoading ? "Loading..." : "Add"}
                    </Text>

                    {/* {isLoading ?
                  <ActivityIndicator style={{
                    marginLeft: 5,
                  }} color={Colors.primaryColor} size={'small'} />
                  : <></>
                } */}
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>
          {addLoyaltyStamp === ADD_LOYALTY_STAMP_SCREEN.LOYALTY_STAMP_LIST ? (
            <>
              <View
                style={{
                  paddingVertical: 30,
                  marginHorizontal: 30,
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginHorizontal: 30,
                    marginTop: 20,
                    height: windowHeight * 0.1,
                    width: windowWidth * 0.877,
                    alignSelf: 'center',
                  }}
                >
                  <View style={{ justifyContent: "center" }}>
                    {/* fontSize: 26, fontFamily: 'NunitoSans-Bold' */}
                    <Text
                      style={{
                        fontSize: switchMerchant ? 20 : 26,
                        marginRight: 30,
                        fontFamily: "NunitoSans-Bold",
                      }}
                    >
                      Summary
                      {/* List of Created Redemption Packages */}
                    </Text>
                  </View>

                  <View style={{ flexDirection: "row" }}>
                    <View style={{ flexDirection: "row" }}>
                      {/* {isTablet && ( */}
                      <View
                        style={{
                          alignSelf: "flex-start",
                          marginRight: switchMerchant ? 18 : 10,
                        }}
                      >
                        <TouchableOpacity
                          style={{
                            justifyContent: "center",
                            flexDirection: "row",
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: "#4E9F7D",
                            borderRadius: 5,
                            // width: 160,
                            paddingHorizontal: 10,
                            height: 40,
                            alignItems: "center",
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          }}
                          onPress={() => {
                            CommonStore.update((s) => {
                              s.selectedLoyaltyStampEdit = null;
                            });

                            setAddLoyaltyStamp(
                              ADD_LOYALTY_STAMP_SCREEN.ADD_LOYALTY_STAMP
                            );
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              alignContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <AntDesign
                              name="pluscircle"
                              size={switchMerchant ? 11 : 20}
                              style={{
                                color: Colors.whiteColor,
                                alignSelf: "center",
                              }}
                            />
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: "NunitoSans-Bold",
                              }}
                            >
                              {/* Set Credit */}
                              LOYALTY STAMP
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                      {/* )} */}
                    </View>

                    <View style={{ flexDirection: "row" }}>
                      <View
                        style={[
                          {
                            height: 40,
                          },
                        ]}
                      >
                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "flex-end",
                          }}
                        >
                          <View
                            style={{
                              backgroundColor: "white",
                              borderRadius: 5,
                              flexDirection: "row",
                              width: 250,
                              height: 40,
                              alignContent: "center",
                              alignItems: "center",
                              shadowColor: "#000",
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 3,
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                            }}
                          >
                            <Icon
                              name="search"
                              size={switchMerchant ? 13 : 25}
                              color={Colors.primaryColor}
                              style={{ marginLeft: 15 }}
                            />
                            <View style={{ flex: 3 }}>
                              <TextInput
                                underlineColorAndroid={Colors.whiteColor}
                                style={{
                                  width: 220,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: "NunitoSans-Regular",
                                  paddingLeft: 5,
                                  height: 45,
                                }}
                                placeholderTextColor={Platform.select({
                                  ios: "#a9a9a9",
                                })}
                                clearButtonMode="while-editing"
                                placeholder=" Search"
                              /* onChangeText={(text) => {
                        setSearch(text);
                      }}
                      defaultValue={search} */
                              />
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                    width: windowWidth * 0.877,
                    height: windowHeight * 0.7,
                    marginTop: 10,
                    marginBottom: 30,
                    marginHorizontal: 30,
                    alignSelf: "center",
                    borderRadius: 5,
                    shadowOpacity: 0,
                    shadowColor: "#000",
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}
                >
                  <View
                    style={{
                      flexDirection: "row",
                      marginTop: 20,
                      borderBottomWidth: StyleSheet.hairlineWidth,
                      borderBottomColor: "#c4c4c4",
                      padding: 10,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: "NunitoSans-Bold",
                        color: "black",
                        // fontWeight: '600',
                        width: "26%",
                        marginLeft: 5,
                      }}
                    >
                      Stamp Name
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: "NunitoSans-Bold",
                        color: "black",
                        // fontWeight: '600',
                        width: "18%",
                        marginHorizontal: 5,
                      }}
                    >
                      Total Stamp(s)
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: "NunitoSans-Bold",
                        color: "black",
                        // fontWeight: '600',
                        width: "26%",
                        marginHorizontal: 5,
                      }}
                    >
                      Available Outlet(s)
                    </Text>
                    <Text
                      style={{
                        fontSize: switchMerchant ? 10 : 14,
                        fontFamily: "NunitoSans-Bold",
                        color: "black",
                        // fontWeight: '600',
                        width: "21%",
                        marginLeft: 5,
                      }}
                    >
                      Durations
                    </Text>
                  </View>

                  <FlatList
                    data={loyaltyStamps}
                    renderItem={renderLoyaltyStamp}
                    nestedScrollEnabled={true}
                    keyExtractor={(item, index) => String(index)}
                  />
                </View>
              </View>
            </>
          ) : null}

          <Modal
            style={
              {
                // flex: 1
              }
            }
            visible={showDateTimePicker}
            transparent={true}
            animationType={"slide"}
            supportedOrientations={["portrait", "landscape"]}
          >
            <View
              style={{
                flex: 1,
                backgroundColor: Colors.modalBgColor,
                alignItems: "center",
                justifyContent: "center",
                // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
              }}
            >
              <View
                style={{
                  // height: windowWidth * 0.2,
                  // width: windowWidth * 0.4,
                  backgroundColor: Colors.whiteColor,
                  borderRadius: 12,
                  padding: windowWidth * 0.02,
                  // borderWidth:1
                }}
              >
                <View style={{ flexDirection: "row" }}>
                  <View style={{ flex: 1.8 }}>
                    <Text
                      style={{
                        fontFamily: "NunitoSans-Bold",
                        fontSize: 20,
                        textAlign: "right",
                      }}
                    >
                      {pickDate === "startDate"
                        ? "Start Date Picker"
                        : "End Date Picker"}
                    </Text>
                  </View>
                  <View style={{ flex: 1.2 }}>
                    <TouchableOpacity
                      style={{
                        elevation: 1000,
                        zIndex: 1000,
                        alignSelf: "flex-end",
                      }}
                      onPress={() => {
                        setShowDateTimePicker(false);
                      }}
                    >
                      <AntDesign
                        name="closecircle"
                        size={25}
                        color={Colors.fieldtTxtColor}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
                <View
                  style={{
                    flexDirection: "row",
                    marginTop: 60,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Text
                    style={{
                      fontFamily: "NunitoSans-Bold",
                      fontSize: 18,
                      textAlign: "right",
                      marginRight: 10,
                    }}
                  >
                    Date:
                  </Text>
                  <View
                    style={{
                      borderWidth: 0.5,
                      borderRadius: 5,
                      width: 100,
                    }}
                  >
                    <DatePicker
                      // isVisible={showDateTimePicker}
                      // mode={'date'}
                      selected={rev_PickDate.toDate()}
                      onChange={(date) => {
                        if (pickDate === "startDate") {
                          setStartDate(moment(date));
                          setRev_PickDate(moment(date));
                        } else {
                          setEndDate(moment(date));
                          setRev_PickDate(moment(date));
                        }
                        // setShowDateTimePicker(false);
                      }}
                    // onCancel={() => {
                    //   setShowDateTimePicker(false);
                    // }}
                    />
                  </View>
                </View>
              </View>
            </View>
          </Modal>

          <Modal
            style={
              {
                // flex: 1
              }
            }
            visible={showDateTimePicker1}
            transparent={true}
            animationType={"slide"}
            supportedOrientations={["portrait", "landscape"]}
          >
            <View
              style={{
                flex: 1,
                backgroundColor: Colors.modalBgColor,
                alignItems: "center",
                justifyContent: "center",
                // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
              }}
            >
              <View
                style={{
                  // height: windowWidth * 0.4,
                  // width: windowWidth * 0.4,
                  backgroundColor: Colors.whiteColor,
                  borderRadius: 12,
                  padding: windowWidth * 0.02,
                  // borderWidth:1
                }}
              >
                <View style={{ flexDirection: "row" }}>
                  <View style={{ flex: 1.8 }}>
                    <Text
                      style={{
                        fontFamily: "NunitoSans-Bold",
                        fontSize: 20,
                        textAlign: "right",
                      }}
                    >
                      {pickTime === "startTime"
                        ? "Start Time Picker"
                        : "End Time Picker"}
                    </Text>
                  </View>
                  <View style={{ flex: 1.2 }}>
                    <TouchableOpacity
                      style={{
                        elevation: 1000,
                        zIndex: 1000,
                        alignSelf: "flex-end",
                      }}
                      onPress={() => {
                        setShowDateTimePicker1(false);
                      }}
                    >
                      <AntDesign
                        name="closecircle"
                        size={25}
                        color={Colors.fieldtTxtColor}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
                <View
                  style={{
                    flexDirection: "row",
                    marginTop: 60,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                ></View>
              </View>
            </View>
          </Modal>

          {addLoyaltyStamp === ADD_LOYALTY_STAMP_SCREEN.ADD_LOYALTY_STAMP ? (
            <>
              <View
                style={{
                  paddingVertical: 30,
                  marginHorizontal: 30,
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    width: windowWidth * 0.877,
                    marginHorizontal: 30,
                    alignSelf: 'center',
                    marginTop: 20,
                  }}
                >
                  <TouchableOpacity
                    style={{ height: 35, justifyContent: "center" }}
                    onPress={() => {
                      setAddLoyaltyStamp(
                        ADD_LOYALTY_STAMP_SCREEN.LOYALTY_STAMP_LIST
                      );
                    }}
                  >
                    <View
                      style={{
                        flexDirection: "row",
                        // paddingHorizontal: "10%",
                        alignContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <View style={{ justifyContent: "center" }}>
                        <Feather
                          name="chevron-left"
                          size={switchMerchant ? 20 : 30}
                          style={{
                            color: Colors.primaryColor,
                            alignSelf: "center",
                          }}
                        />
                      </View>
                      <Text
                        style={[
                          {
                            fontSize: 17,
                            color: Colors.primaryColor,
                            fontWeight: "600",
                            marginBottom: Platform.OS === "ios" ? 0 : 1,
                          },
                          switchMerchant
                            ? {
                              fontSize: 14,
                            }
                            : {},
                        ]}
                      >
                        Back
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                    width: windowWidth * 0.877,
                    marginTop: 10,
                    marginBottom: 30,
                    marginHorizontal: 30,
                    alignSelf: 'center',
                    borderRadius: 5,
                    shadowOpacity: 0,
                    shadowColor: "#000",
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}
                >
                  <ScrollView
                    showsVerticalScrollIndicator={false}
                    nestedScrollEnabled={false}
                    contentContainerStyle={
                      {
                        marginBottom: 30,
                        // backgroundColor: 'red',
                        // top: Platform.OS === 'ios' ? -keyboardHeight * 0.3 : 0,
                      }
                    }
                  >
                    <View
                      style={{
                        marginTop: 10,
                        justifyContent: "center",
                        alignItems: "center",
                        zIndex: 99999,
                        // width: switchMerchant ? windowWidth * 0.8 : windowWidth * 0.87,
                        // backgroundColor: Colors.whiteColor,
                        // borderRadius: 5,
                        // shadowColor: '#000',
                        // shadowOffset: {
                        //   width: 0,
                        //   height: 2,
                        // },
                        // shadowOpacity: 0.22,
                        // shadowRadius: 3.22,
                        // elevation: 3,
                        // paddingHorizontal : -30
                      }}
                    >
                      <View
                        style={{
                          justifyContent: "flex-end",
                          padding: 10,
                          paddingVertical: 15,
                          flexDirection: "row",
                          width: windowWidth * 0.877,
                        }}
                      >
                        <TouchableOpacity
                          style={{
                            justifyContent: "center",
                            flexDirection: "row",
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: "#4E9F7D",
                            borderRadius: 5,
                            width: 130,
                            paddingHorizontal: 10,
                            height: 40,
                            alignItems: "center",
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginBottom: 10,
                          }}
                          disabled={loading}
                          onPress={() => {
                            if (selectedLoyaltyStampEdit) {
                              updateLoyaltyStamp();
                            } else {
                              createLoyaltyStamp();
                            }
                          }}
                        >
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: switchMerchant ? 10 : 16,
                              fontFamily: "NunitoSans-Bold",
                            }}
                          >
                            {loading ? "LOADING..." : "SAVE"}
                          </Text>
                        </TouchableOpacity>
                      </View>

                      {/* <View style={{
                        //flexDirection:'row',
                        width: Dimensions.get('screen').width * 0.87,
                        height: Dimensions.get('screen').height * 0.65,
                        padding: 20,
                        borderBottomWidth: StyleSheet.hairlineWidth,
                      }}>
                        <View style={{ justifyContent: 'center', }}>
                          <Text
                            style={{
                              alignSelf: 'center',
                              marginTop: 30,
                              fontSize: switchMerchant ? 20 : 40,
                              fontWeight: 'bold',
                            }}>
                            {'THANKS FOR YOUR LOYALTY'}
                          </Text>
                          <Text
                            numberOfLines={(2)}
                            style={{
                              //width: Dimensions.get('screen').width * 0.38,
                              //height: Dimensions.get('screen').height * 0.1,
                              alignSelf: 'center',
                              fontSize: switchMerchant ? 16 : 32,
                              color: '#adadad',
                              textAlign: 'center',
                            }}>
                            {'AFTER 10 SESSIONS YOU CAN \nGET 50% OFF FOR EVERY MENU'}
                          </Text>
                        </View>
                        <View style={{
                          flexDirection: 'row',
                          justifyContent: 'center',
                        }}>
                          <View style={{
                            flexDirection: 'row',
                            backgroundColor: Colors.highlightColor,
                            marginTop: 20,
                            width: Dimensions.get('window').width * 0.75,
                            height: Dimensions.get('window').height * 0.4,
                            justifyContent: 'space-between',
                          }}>
                            <View style={{
                              //flexDirection: 'row',
                              //justifyContent: 'flex-start',
                              paddingHorizontal: 5,
                            }}>
                              <TouchableOpacity
                                style={{
                                  //height: switchMerchant ? 35 : 45,
                                  height: Dimensions.get('window').height * 0.4,
                                  zIndex: 2,
                                  opacity: 0.8,
                                  backgroundColor: Colors.highlightColor,
                                  //paddingTop: 10,
                                  justifyContent: 'center',
                                }}
                                onPress={() => {
                                }}>
                                <AIcon
                                  name="left"
                                  size={switchMerchant ? 24 : 30}
                                  color={Colors.primaryColor}
                                />
                              </TouchableOpacity>
                            </View>

                            <View style={{
                              width: Dimensions.get('window').width * 0.65,
                              justifyContent: 'space-evenly',
                            }}>
                              <View style={{ flexDirection: 'row', justifyContent: 'space-evenly' }}>

                                <View style={{
                                  width: Dimensions.get('window').width * 0.1,
                                  height: Dimensions.get('window').width * 0.1,
                                  justifyContent: 'center',
                                  borderRadius: 15,
                                  backgroundColor: Colors.tabGrey,
                                }}>
                                  <View style={{ flexDirection: 'row', justifyContent: 'center', }}>
                                    <Text style={[styles.cardText]}>1</Text>
                                  </View>
                                </View>

                                <View style={{
                                  width: Dimensions.get('window').width * 0.1,
                                  height: Dimensions.get('window').width * 0.1,
                                  justifyContent: 'center',
                                  borderRadius: 15,
                                  backgroundColor: Colors.tabGrey,
                                }}>
                                  <View style={{ flexDirection: 'row', justifyContent: 'center', }}>
                                    <Text style={[styles.cardText]}>2</Text>
                                  </View>
                                </View>

                                <View style={{
                                  width: Dimensions.get('window').width * 0.1,
                                  height: Dimensions.get('window').width * 0.1,
                                  justifyContent: 'center',
                                  borderRadius: 15,
                                  backgroundColor: Colors.tabGrey,
                                }}>
                                  <View style={{ flexDirection: 'row', justifyContent: 'center', }}>
                                    <Text style={[styles.cardText]}>3</Text>
                                  </View>
                                </View>

                                <View style={{
                                  width: Dimensions.get('window').width * 0.1,
                                  height: Dimensions.get('window').width * 0.1,
                                  justifyContent: 'center',
                                  borderRadius: 15,
                                  backgroundColor: Colors.tabGrey,
                                }}>
                                  <View style={{ flexDirection: 'row', justifyContent: 'center', }}>
                                    <Text style={[styles.cardText]}>4</Text>
                                  </View>
                                </View>

                                <View style={{
                                  width: Dimensions.get('window').width * 0.1,
                                  height: Dimensions.get('window').width * 0.1,
                                  justifyContent: 'center',
                                  borderRadius: 15,
                                  backgroundColor: Colors.tabGrey,
                                }}>
                                  <View style={{ flexDirection: 'row', justifyContent: 'center', }}>
                                    <Text style={[styles.cardText]}>5</Text>
                                  </View>
                                </View>
                              </View>

                              <View style={{ flexDirection: 'row', justifyContent: 'space-evenly' }}>

                                <View style={{
                                  width: Dimensions.get('window').width * 0.1,
                                  height: Dimensions.get('window').width * 0.1,
                                  justifyContent: 'center',
                                  borderRadius: 15,
                                  backgroundColor: Colors.tabGrey,
                                }}>
                                  <View style={{ flexDirection: 'row', justifyContent: 'center', }}>
                                    <Text style={[styles.cardText]}>6</Text>
                                  </View>
                                </View>

                                <View style={{
                                  width: Dimensions.get('window').width * 0.1,
                                  height: Dimensions.get('window').width * 0.1,
                                  justifyContent: 'center',
                                  borderRadius: 15,
                                  backgroundColor: Colors.tabGrey,
                                }}>
                                  <View style={{ flexDirection: 'row', justifyContent: 'center', }}>
                                    <Text style={[styles.cardText]}>7</Text>
                                  </View>
                                </View>

                                <View style={{
                                  width: Dimensions.get('window').width * 0.1,
                                  height: Dimensions.get('window').width * 0.1,
                                  justifyContent: 'center',
                                  borderRadius: 15,
                                  backgroundColor: Colors.tabGrey,
                                }}>
                                  <View style={{ flexDirection: 'row', justifyContent: 'center', }}>
                                    <Text style={[styles.cardText]}>8</Text>
                                  </View>
                                </View>

                                <View style={{
                                  width: Dimensions.get('window').width * 0.1,
                                  height: Dimensions.get('window').width * 0.1,
                                  justifyContent: 'center',
                                  borderRadius: 15,
                                  backgroundColor: Colors.tabGrey,
                                }}>
                                  <View style={{ flexDirection: 'row', justifyContent: 'center', }}>
                                    <Text style={[styles.cardText]}>9</Text>
                                  </View>
                                </View>

                                <View style={{
                                  width: Dimensions.get('window').width * 0.1,
                                  height: Dimensions.get('window').width * 0.1,
                                  justifyContent: 'center',
                                  borderRadius: 15,
                                  backgroundColor: Colors.tabGrey,
                                }}>
                                  <View style={{ flexDirection: 'row', justifyContent: 'center', }}>
                                    <Text style={[styles.cardText]}>10</Text>
                                  </View>
                                </View>
                              </View>

                            </View>

                            <View style={{
                              //flexDirection: 'row',
                              //justifyContent: 'flex-end',
                              paddingHorizontal: 5,
                            }}>
                              <TouchableOpacity
                                style={{
                                  //height: switchMerchant ? 35 : 45,
                                  height: Dimensions.get('window').height * 0.4,
                                  zIndex: 2,
                                  opacity: 0.8,
                                  backgroundColor: Colors.highlightColor,
                                  //paddingTop: 10,
                                  justifyContent: 'center',
                                }}
                                onPress={() => {
                                }}>
                                <AIcon
                                  name="right"
                                  size={switchMerchant ? 24 : 30}
                                  color={Colors.primaryColor}
                                />
                              </TouchableOpacity>
                            </View>

                          </View>
                        </View>
                      </View> */}
                      {/* <View style={{
                          height: Dimensions.get('screen').height * 0.55,
                          //backgroundColor: Colors.lightPrimary,
                          marginBottom: 20
                        }}>

                        </View> */}

                      {/*                       <View style={{
                        borderBottomWidth: StyleSheet.hairlineWidth,
                      }} /> */}
                      <View
                        style={{
                          borderBottomWidth: StyleSheet.hairlineWidth,
                          width: windowWidth * 0.877,
                        }}
                      >
                        <View>
                          <Text
                            style={{
                              alignSelf: "center",
                              // marginTop: 30,
                              fontSize: switchMerchant ? 20 : 40,
                              fontWeight: "bold",
                            }}
                          >
                            {selectedLoyaltyStampEdit
                              ? selectedLoyaltyStampEdit.name
                              : "Add Loyalty Stamp"}
                          </Text>

                          <Text
                            style={{
                              alignSelf: "center",
                              fontSize: switchMerchant ? 10 : 16,
                              color: "#adadad",
                            }}
                          >
                            Fill in the information
                          </Text>
                        </View>

                        <View
                          style={{
                            flexDirection: "row",
                            marginTop: 40,
                            width: "90%",
                            alignSelf: "center",
                            marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                            justifyContent: "space-evenly",
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              alignItems: "center",
                              width: "50%",
                            }}
                          >
                            <Text
                              style={{
                                fontFamily: "NunitoSans-Bold",
                                fontSize: switchMerchant ? 10 : 14,
                                width: "30%",
                                textAlign: "left",
                              }}
                            >
                              Stamp Name
                            </Text>
                            <TextInput
                              placeholder="Name"
                              placeholderTextColor={Platform.select({
                                ios: "#a9a9a9",
                              })}
                              placeholderStyle={{
                                fontFamily: "NunitoSans-Regular",
                                fontSize: 14,
                              }}
                              style={[
                                {
                                  backgroundColor: Colors.fieldtBgColor,
                                  width: windowWidth < 1750 ? 200 : 250,
                                  height: 40,
                                  borderRadius: 5,
                                  padding: 5,
                                  marginVertical: 5,
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  paddingLeft: 10,
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: 14,
                                  marginLeft: 20,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: 200,
                                    height: 35,
                                  }
                                  : {},
                              ]}
                              onChangeText={(text) => {
                                setStampName(text);
                              }}
                              defaultValue={stampName}
                            />
                          </View>
                          <View
                            style={{
                              flexDirection: "row",
                              alignItems: "center",
                              width: "50%",
                            }}
                          >
                            <Text
                              style={{
                                fontFamily: "NunitoSans-Bold",
                                fontSize: switchMerchant ? 10 : 14,
                                width: "35%",
                                textAlign: "left",
                              }}
                            >
                              Total Stamp(s)
                            </Text>
                            <TextInput
                              placeholder={"0"}
                              placeholderStyle={{
                                fontFamily: "NunitoSans-Regular",
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                              placeholderTextColor={Platform.select({
                                ios: "#a9a9a9",
                              })}
                              style={[
                                {
                                  backgroundColor: Colors.fieldtBgColor,
                                  // width: 60,
                                  width: windowWidth < 1750 ? 200 : 250,
                                  height: 40,
                                  borderRadius: 5,
                                  padding: 5,
                                  marginVertical: 5,
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  paddingLeft: 10,
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: 14,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: 200,
                                    height: 35,
                                  }
                                  : {},
                              ]}
                              keyboardType={"decimal-pad"}
                              onChangeText={(text) => {
                                setTotalNumberOfStamp(text);
                              }}
                              defaultValue={totalNumberOfStamp}
                              value={totalNumberOfStamp}
                            />
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: "row",
                            marginTop: 30,
                            width: "90%",
                            alignSelf: "center",
                            marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                            zIndex: 1000,
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              alignItems: "center",
                              width: "50%",
                              zIndex: 1000,
                            }}
                          >
                            <Text
                              style={{
                                fontFamily: "NunitoSans-Bold",
                                fontSize: switchMerchant ? 10 : 14,
                                width: "33%",
                                textAlign: "left",
                              }}
                            >
                              Target Segment(s)
                            </Text>
                            <View style={{ width: 250, zIndex: 1000 }}>
                              <DropDownPicker
                                style={{
                                  backgroundColor: Colors.fieldtBgColor,
                                  // width: 210,
                                  height: 40,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  flexDirection: "row",
                                }}
                                dropDownContainerStyle={{
                                  // width: 210,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderColor: "#E5E5E5",
                                }}
                                labelStyle={{
                                  marginLeft: 5,
                                  flexDirection: "row",
                                }}
                                textStyle={{
                                  fontSize: 14,
                                  fontFamily: 'NunitoSans-Regular',

                                  marginLeft: 5,
                                  paddingVertical: 10,
                                  flexDirection: "row",
                                }}
                                selectedItemContainerStyle={{
                                  flexDirection: "row",
                                }}

                                showArrowIcon={true}
                                ArrowDownIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-down-outline"
                                  />
                                )}
                                ArrowUpIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-up-outline"
                                  />
                                )}

                                showTickIcon={true}
                                TickIconComponent={({ press }) => (
                                  <Ionicon
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    color={
                                      press ? Colors.fieldtBgColor : Colors.primaryColor
                                    }
                                    name={'md-checkbox'}
                                    size={25}
                                  />
                                )}
                                placeholderStyle={{
                                  color: Colors.fieldtTxtColor,
                                  // marginTop: 15,
                                }}
                                dropDownDirection="BOTTOM"
                                placeholder="Select..."
                                multipleText={`${targetUsers.length} segment(s) selected`}
                                // multipleText={'%d outlet(s) selected'}
                                items={CRM_SEGMENT_DROPDOWN_LIST.concat(
                                  crmSegmentsDropdownList
                                )}
                                value={targetUsers}
                                multiple={true}
                                open={openTS}
                                setOpen={setOpenTS}
                                onSelectItem={(items) => {
                                  setTargetUsers(items.map(item => item.value))
                                }}
                              />
                            </View>
                            {/* <Multiselect
                              style={{
                                width: 250,
                                paddingVertical: 0,
                                borderRadius: 10,
                              }}
                              //zIndex={10000 + lsItemsBuy.length - index}
                              options={CRM_SEGMENT_DROPDOWN_LIST.concat(
                                crmSegmentsDropdownList,
                              )}
                              onChange={(items) => {
                                setTargetUsers(items);
                              }}
                              onRemove={(items) => {
                                setTargetUsers(items);
                              }}
                              selectedValues={targetUsers}
                              displayValue='label'
                            /> */}
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "space-evenly",
                            marginTop: 40,
                            width: "90%",
                            alignSelf: "center",
                            marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                            zIndex: -1,
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              flex: 1,
                              alignItems: "center",
                              width: "50%",
                            }}
                          >
                            <Text
                              style={{
                                fontFamily: "NunitoSans-Bold",
                                fontSize: switchMerchant ? 10 : 14,
                                width: "33%",
                                textAlign: "left",
                              }}
                            >
                              Start Date
                            </Text>
                            <View
                              style={{
                                flexDirection: "row",
                                alignItems: "center",
                              }}
                            >
                              {/* <View
                              style={
                                {
                                  // height: 35,
                                  // paddingHorizontal: 10,
                                  // width: 160,
                                  // flexDirection: 'row',
                                  // alignItems: 'center',
                                  // justifyContent: "center",
                                  //paddingLeft: 15,
                                  // borderWidth: 1,
                                  // borderColor: '#E5E5E5',
                                  // borderRadius: 5,
                                }
                              }>
                              <TouchableOpacity
                                onPress={() => {
                                  setPickDate('startDate');
                                  setShowDateTimePicker(true);
                                }}
                                style={[
                                  {
                                    // height: 50,
                                    height: switchMerchant ? 35 : 40,
                                    paddingHorizontal: 20,
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 160,
                                    width: 250,
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    alignContent: 'center',
                                    borderColor: '#E5E5E5',
                                    borderWidth: 1,
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    borderRadius: 5,
                                    //marginTop: 10,
                                  },
                                  switchMerchant
                                    ? {
                                      width: 200,
                                      height: 40,
                                    }
                                    : {},
                                ]}>
                                <View
                                  style={{ alignSelf: 'center' }}
                                  onPress={() => {
                                    // setState({
                                    //   pickerMode: 'date',
                                    //   showDateTimePicker: true,
                                    // });
                                  }}>
                                  <GCalendar
                                    width={switchMerchant ? 15 : 20}
                                    height={switchMerchant ? 15 : 20}
                                  />
                                </View>
                                <Text
                                  style={[
                                    {
                                      // marginRight: 0,
                                      marginRight: '20%',
                                      //color: '#B6B6B6',
                                      fontSize: 14,
                                      fontFamily: 'NunitoSans-Regular',
                                      fontVariant: ['tabular-nums'],
                                    },
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                      }
                                      : {},
                                  ]}>
                                  {startDate
                                    ? moment(startDate).format('DD MMM YYYY')
                                    : 'Start Date'}
                                </Text>
                              </TouchableOpacity>
                            </View> */}
                              <View
                                style={{
                                  borderWidth: 1,
                                  padding: 5,
                                  borderRadius: 5,
                                  zIndex: -1,
                                  width: windowWidth < 1750 ? 200 : 250,
                                  alignItems: "center",
                                  borderColor: "#d5d5d5",
                                }}
                              >
                                <DatePicker
                                  selected={moment(startDate).toDate()}
                                  onChange={(date) => {
                                    setStartDate(moment(date));
                                  }}
                                  maxDate={moment(endDate).toDate()}
                                />
                              </View>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: "row",
                              flex: 1,
                              alignItems: "center",
                              width: "50%",
                            }}
                          >
                            <Text
                              style={{
                                fontFamily: "NunitoSans-Bold",
                                fontSize: switchMerchant ? 10 : 14,
                                width: "35%",
                                textAlign: "left",
                              }}
                            >
                              End Date
                            </Text>
                            <View
                              style={
                                {
                                  // height: 35,
                                  // paddingHorizontal: 10,
                                  // width: 160,
                                  // flexDirection: 'row',
                                  // alignItems: 'center',
                                  // justifyContent: "center",
                                  // paddingLeft: 15,
                                  // borderWidth: 1,
                                  // borderColor: '#E5E5E5',
                                  // borderRadius: 5,
                                }
                              }
                            >
                              {/* <TouchableOpacity
                                onPress={() => {
                                  setPickDate('endDate');
                                  setShowDateTimePicker(true);
                                }}
                                style={[
                                  {
                                    // height: 50,
                                    height: switchMerchant ? 35 : 40,
                                    paddingHorizontal: 20,
                                    backgroundColor: Colors.fieldtBgColor,
                                    //marginBottom: 20,
                                    // width: 160,
                                    width: 250,
                                    //marginHorizontal: 10,
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    alignContent: 'center',
                                    borderColor: '#E5E5E5',
                                    borderWidth: 1,
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    borderRadius: 5,
                                    //marginTop: 10,
                                  },
                                  switchMerchant
                                    ? {
                                      width: 200,
                                      height: 40,
                                    }
                                    : {},
                                ]}>
                                <View
                                  style={{ alignSelf: 'center' }}
                                  onPress={() => {
                                    // setState({
                                    //   pickerMode: 'date',
                                    //   showDateTimePicker: true,
                                    // });
                                  }}>
                                  <GCalendar
                                    width={switchMerchant ? 15 : 20}
                                    height={switchMerchant ? 15 : 20}
                                  />
                                </View>
                                <Text
                                  style={[
                                    {
                                      // marginRight: 0,
                                      marginRight: '20%',
                                      //color: '#B6B6B6',
                                      fontSize: 14,
                                      fontFamily: 'NunitoSans-Regular',
                                      fontVariant: ['tabular-nums'],
                                    },
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                      }
                                      : {},
                                  ]}>
                                  {endDate
                                    ? moment(endDate).format('DD MMM YYYY')
                                    : 'End Date'}
                                </Text>
                              </TouchableOpacity> */}
                              <View
                                style={{
                                  borderWidth: 1,
                                  padding: 5,
                                  borderRadius: 5,
                                  zIndex: 999,
                                  width: windowWidth < 1750 ? 200 : 250,
                                  alignItems: "center",
                                  borderColor: "#d5d5d5",
                                }}
                              >
                                <DatePicker
                                  selected={moment(endDate).toDate()}
                                  onChange={(date) => {
                                    setEndDate(moment(date));
                                  }}
                                  minDate={moment(startDate).toDate()}
                                />
                              </View>
                            </View>
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: "row",
                            justifyContent: "space-evenly",
                            marginTop: 40,
                            width: "90%",
                            alignSelf: "center",
                            marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                            zIndex: -1,
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              flex: 1,
                              alignItems: "center",
                              width: "50%",
                            }}
                          >
                            <Text
                              style={{
                                fontFamily: "NunitoSans-Bold",
                                fontSize: switchMerchant ? 10 : 14,
                                width: "33%",
                                textAlign: "left",
                              }}
                            >
                              Start Time
                            </Text>
                            <View
                              style={{
                                flexDirection: "row",
                                alignItems: "center",
                                zIndex: -1,
                              }}
                            >
                              {/* <View
                              style={
                                {
                                  // height: 35,
                                  // paddingHorizontal: 10,
                                  // width: 160,
                                  // flexDirection: 'row',
                                  // alignItems: 'center',
                                  // justifyContent: "center",
                                  // paddingLeft: 15,
                                  // borderWidth: 1,
                                  // borderColor: '#E5E5E5',
                                  // borderRadius: 5,
                                }
                              }>
                              <TouchableOpacity
                                onPress={() => {
                                  setPickTime('startTime');
                                  setShowDateTimePicker1(true);
                                }}
                                style={[
                                  {
                                    // height: 50,
                                    height: switchMerchant ? 35 : 40,
                                    paddingHorizontal: 20,
                                    backgroundColor: Colors.fieldtBgColor,
                                    //marginBottom: 20,
                                    // width: 160,
                                    width: 250,
                                    //marginHorizontal: 10,
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    alignContent: 'center',
                                    borderColor: '#E5E5E5',
                                    borderWidth: 1,
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    borderRadius: 5,
                                    //marginTop: 10,
                                  },
                                  switchMerchant
                                    ? {
                                      width: 200,
                                      height: 40,
                                    }
                                    : {},
                                ]}>
                                <Clock
                                  size={switchMerchant ? 20 : 25}
                                  color={Colors.primaryColor}
                                  style={{ marginLeft: 0 }}
                                />

                                <Text
                                  style={[
                                    {
                                      // marginRight: 0,
                                      marginRight: '20%',
                                      //color: '#B6B6B6',
                                      fontSize: 14,
                                      fontFamily: 'NunitoSans-Regular',
                                      fontVariant: ['tabular-nums'],
                                    },
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                      }
                                      : {},
                                  ]}>
                                  {startTime
                                    ? moment(startTime).format('hh:mmA')
                                    : 'Start Time'}
                                </Text>
                              </TouchableOpacity>
                            </View> */}

                              <TouchableOpacity
                                onPress={() => {
                                  if (clockTimePickerStart) {
                                    setClockTimePickerStart(false);
                                  } else {
                                    setClockTimePickerStart(true);
                                  }
                                }}
                                style={[
                                  {
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: windowWidth < 1750 ? 200 : 250,
                                    // width: '45%',
                                    height: 40,
                                    borderRadius: 5,
                                    padding: 1,
                                    marginVertical: 5,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      height: 35,
                                    }
                                    : {},
                                ]}
                              >
                                <Text
                                  style={[
                                    {
                                      fontVariant: ["tabular-nums"],
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: 14,
                                    },
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                      }
                                      : {},
                                  ]}
                                >
                                  {moment(startTime).format("hh:mm A")}
                                </Text>
                              </TouchableOpacity>
                              {clockTimePickerStart ? (
                                <View
                                  style={{
                                    position: "absolute",
                                    left: 0,
                                    top: 50,
                                    zIndex: 1000,
                                  }}
                                >
                                  <TimeKeeper
                                    time={moment(startTime).format("hh:mm A")}
                                    onChange={(time) => {
                                      setStartTime(
                                        moment(
                                          `${moment(startTime).format(
                                            "MM/DD/YYYY"
                                          )} ${time.formatted12}`
                                        )
                                      );
                                    }}
                                    onDoneClick={() => {
                                      setClockTimePickerStart(false);
                                    }}
                                  ></TimeKeeper>
                                </View>
                              ) : null}
                            </View>
                          </View>
                          {/* End Time */}
                          <View
                            style={{
                              flexDirection: "row",
                              flex: 1,
                              alignItems: "center",
                              width: "50%",
                            }}
                          >
                            <Text
                              style={{
                                fontFamily: "NunitoSans-Bold",
                                fontSize: switchMerchant ? 10 : 14,
                                width: "35%",
                                textAlign: "left",
                              }}
                            >
                              End Time
                            </Text>
                            <View
                              style={
                                {
                                  // height: 35,
                                  // paddingHorizontal: 10,
                                  // width: 160,
                                  // flexDirection: 'row',
                                  // alignItems: 'center',
                                  // justifyContent: "center",
                                  // paddingLeft: 15,
                                  // borderWidth: 1,
                                  // borderColor: '#E5E5E5',
                                  // borderRadius: 5,
                                }
                              }
                            >
                              <TouchableOpacity
                                onPress={() => {
                                  if (clockTimePickerEnd) {
                                    setClockTimePickerEnd(false);
                                  } else {
                                    setClockTimePickerEnd(true);
                                  }
                                }}
                                style={[
                                  {
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: windowWidth < 1750 ? 200 : 250,
                                    // width: '44.5%',
                                    height: 40,
                                    borderRadius: 5,
                                    padding: 1,
                                    marginVertical: 5,
                                    alignItems: "center",
                                    justifyContent: "center",
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: 14,
                                  },
                                  switchMerchant
                                    ? {
                                      height: 35,
                                    }
                                    : {},
                                ]}
                              >
                                <Text
                                  style={[
                                    {
                                      fontVariant: ["tabular-nums"],
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: 14,
                                    },
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                      }
                                      : {},
                                  ]}
                                >
                                  {moment(endTime).format("hh:mm A")}
                                </Text>
                              </TouchableOpacity>
                              {clockTimePickerEnd ? (
                                <View
                                  style={{
                                    position: "absolute",
                                    right: -11,
                                    top: 50,
                                    zIndex: 1003,
                                  }}
                                >
                                  <TimeKeeper
                                    time={moment(endTime).format("hh:mm A")}
                                    onChange={(time) => {
                                      setEndTime(
                                        moment(
                                          `${moment(endTime).format(
                                            "MM/DD/YYYY"
                                          )} ${time.formatted12}`
                                        )
                                      );
                                    }}
                                    onDoneClick={() => {
                                      setClockTimePickerEnd(false);
                                    }}
                                  ></TimeKeeper>
                                </View>
                              ) : null}
                            </View>
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: "row",
                            marginTop: 40,
                            marginBottom: 40,
                            width: "90%",
                            alignSelf: "center",
                            marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                            zIndex: 8888,
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              //justifyContent: 'space-between',
                              width: "100%",
                            }}
                          >
                            <Text
                              style={{
                                fontFamily: "NunitoSans-Bold",
                                fontSize: switchMerchant ? 10 : 14,
                                width: "16.5%",
                                textAlign: "left",
                              }}
                            >
                              Terms & Conditions
                            </Text>
                            <TextInput
                              //style={{ borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5, height: 100, width: 400, paddingLeft: 5 }}
                              style={[
                                {
                                  borderColor: "#E5E5E5",
                                  borderRadius: 5,
                                  borderWidth: 1,
                                  backgroundColor: Colors.fieldtBgColor,
                                  height: 100,
                                  width: switchMerchant
                                    ? 350
                                    : windowWidth * 0.563,
                                  paddingTop: 10,
                                  paddingLeft: 10,
                                  textAlignVertical: "top",
                                  fontFamily: "NunitoSans-Regular",
                                  fontSize: 14,
                                  marginRight: "auto",
                                  // marginTop: -10,
                                  // marginLeft: 60,
                                },
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                              placeholderStyle={{
                                fontFamily: "NunitoSans-Regular",
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                              placeholder={"Terms & Conditions"}
                              multiline={true}
                              placeholderTextColor={Platform.select({
                                ios: "#a9a9a9",
                              })}
                              defaultValue={terms}
                              onChangeText={(text) => {
                                setTerms(text);
                              }}
                            />
                          </View>
                        </View>
                      </View>
                    </View>

                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        alignItems: "center",
                        marginTop: 30,
                        //marginBottom: 20
                      }}
                    >
                      <Text
                        style={{
                          fontSize: switchMerchant ? 20 : 25,
                          fontWeight: "bold",
                          marginLeft: "42%",
                        }}
                      >
                        Product Eligible
                      </Text>
                    </View>

                    <View
                      style={
                        switchMerchant
                          ? {
                            //marginBottom: 40,
                            minHeight: windowHeight * 0.9,
                          }
                          : {
                            //marginBottom: 40,
                            minHeight:
                              Platform.OS === "ios"
                                ? windowHeight * 0.3
                                : windowHeight * 0.35,
                          }
                      }
                    >
                      {/* <View
                        style={{
                          backgroundColor: '#ffffff',
                          flexDirection: 'row',
                          paddingVertical: 10,
                          paddingHorizontal: 20,
                          marginTop: 10,
                          //borderBottomWidth: StyleSheet.hairlineWidth,
                          marginLeft: '3.5%'
                        }}>
                        <Text style={{ width: switchMerchant ? '15%' : '13%', marginRight: 5, fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                          No. of Stamp
                        </Text>
                        <Text style={{ width: switchMerchant ? '15%' : '6.2%', marginHorizontal: 5, fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                        </Text>
                        <Text style={{ width: switchMerchant ? '15%' : '14.5%', marginHorizontal: 5, fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                          Quantity
                        </Text>
                        <Text style={{ width: switchMerchant ? '30%' : '19%', marginHorizontal: 5, fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                          Items
                        </Text>
                      </View> */}
                      {/* <View style={{ borderBottomWidth: StyleSheet.hairlineWidth, }} /> */}
                      <View
                        style={{
                          borderColor: "#E5E5E5",
                          borderWidth: 1,
                          // width: 1100,
                          padding: switchMerchant ? 0 : 16,
                          paddingTop: switchMerchant ? 16 : 0,
                          borderRadius: 10,
                          marginTop: 20,
                          marginHorizontal: switchMerchant ? 20 : 25,
                          paddingBottom: windowWidth * 0.1,
                        }}
                      >
                        {renderProductEligible()}
                      </View>
                      {/* <FlatList
                        data={lsItems}
                        extraData={lsItems}
                        renderItem={renderItems}
                        keyExtractor={(item, index) => String(index)}
                        //contentContainerStyle={{ borderRadius: 5 }}
                        style={{ zIndex: 2 }}
                      /> */}

                      {/* contentContainerStyle={{ borderRadius: 5, paddingBottom: 40}}
                        style={{ zIndex: 2 }}
                      /> */}

                      <View
                        style={{
                          flexDirection: "row",
                          justifyContent: "flex-start",
                          padding: 10,
                          zIndex: -1,
                        }}
                      >
                        <TouchableOpacity
                          style={{
                            paddingVertical: 5,
                            paddingHorizontal: 20,
                            flexDirection: "row",
                            color: "#4cd964",
                            textAlign: "center",
                            alignSelf: "flex-end",
                            marginRight: 20,
                            //marginTop: 10

                            // justifyContent:'flex-start',
                            // // marginRight: 20,
                            // marginTop: 10,

                            // borderColor: Colors.primaryColor,
                            // borderRadius: 5,
                            // borderWidth: 1,
                            // padding: 5,
                          }}
                          onPress={() => {
                            //setStampItemsModal(true)
                            addProduct();
                            // height : 810
                            // width : 1080
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              alignItems: "center",
                            }}
                          >
                            <Icon2
                              name="plus-circle"
                              size={switchMerchant ? 15 : 20}
                              color={Colors.primaryColor}
                            />
                            <Text
                              style={{
                                marginLeft: 5,
                                color: Colors.primaryColor,
                                fontFamily: "NunitoSans-Regular",
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                            >
                              Add Product
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                    <View
                      style={{
                        borderBottomWidth: StyleSheet.hairlineWidth,
                        zIndex: -2,
                      }}
                    />

                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        alignItems: "center",
                        marginTop: 30,
                        zIndex: -2,
                        //marginBottom: 20
                      }}
                    >
                      <Text
                        style={{
                          fontSize: switchMerchant ? 20 : 25,
                          fontWeight: "bold",
                          marginLeft: "42%",
                        }}
                      >
                        Stamp Items
                      </Text>
                    </View>

                    <View
                      style={
                        switchMerchant
                          ? {
                            //marginBottom: 40,
                            minHeight: windowHeight * 0.9,
                          }
                          : {
                            //marginBottom: 40,
                            minHeight:
                              Platform.OS === "ios"
                                ? windowHeight * 0.3
                                : windowHeight * 0.35,
                          }
                      }
                    >
                      {/* <View
                        style={{
                          backgroundColor: '#ffffff',
                          flexDirection: 'row',
                          paddingVertical: 10,
                          paddingHorizontal: 20,
                          marginTop: 10,
                          //borderBottomWidth: StyleSheet.hairlineWidth,
                          marginLeft: '3.5%'
                        }}> */}
                      {/* <Text style={{ width: switchMerchant ? '15%' : '13%', marginRight: 5, fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                          No. of Stamp
                        </Text>
                        <Text style={{ width: switchMerchant ? '15%' : '6.2%', marginHorizontal: 5, fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                        </Text>
                        <Text style={{ width: switchMerchant ? '15%' : '14.5%', marginHorizontal: 5, fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}> */}
                      {/* <Text style={{ width: switchMerchant ? '10%' : '7%', marginHorizontal: 5, fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                  </Text>
                  <Text style={{ width: switchMerchant ? '20%' : '15%', marginHorizontal: 5, fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}> */}
                      {/* Quantity
                        </Text>
                        <Text style={{ width: switchMerchant ? '30%' : '19%', marginHorizontal: 5, fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                          Items
                        </Text> */}
                      {/* </View> */}
                      <View
                        style={{
                          borderColor: "#E5E5E5",
                          borderWidth: 1,
                          // width: 1100,
                          padding: switchMerchant ? 0 : 16,
                          paddingTop: switchMerchant ? 16 : 0,
                          borderRadius: 10,
                          marginTop: 20,
                          marginHorizontal: switchMerchant ? 20 : 25,
                          paddingBottom: windowWidth * 0.1,
                        }}
                      >
                        {renderStampItems()}
                      </View>
                      {/* <FlatList
                        data={lsItems}
                        extraData={lsItems}
                        renderItem={renderItems}
                        keyExtractor={(item, index) => String(index)}
                        //contentContainerStyle={{ borderRadius: 5 }}
                        style={{ zIndex: 2 }}
                      /> */}

                      {/* contentContainerStyle={{ borderRadius: 5, paddingBottom: 40}}
                        style={{ zIndex: 2 }}
                      /> */}

                      <View
                        style={{
                          flexDirection: "row",
                          justifyContent: "flex-start",
                          padding: 10,
                          zIndex: -1,
                        }}
                      >
                        <TouchableOpacity
                          style={{
                            paddingVertical: 5,
                            paddingHorizontal: 20,
                            flexDirection: "row",
                            color: "#4cd964",
                            textAlign: "center",
                            alignSelf: "flex-end",
                            marginRight: 20,
                            //marginTop: 10

                            // justifyContent:'flex-start',
                            // // marginRight: 20,
                            // marginTop: 10,

                            // borderColor: Colors.primaryColor,
                            // borderRadius: 5,
                            // borderWidth: 1,
                            // padding: 5,
                          }}
                          onPress={() => {
                            //setStampItemsModal(true)
                            addStamp();
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                              alignItems: "center",
                            }}
                          >
                            <Icon2
                              name="plus-circle"
                              size={switchMerchant ? 15 : 20}
                              color={Colors.primaryColor}
                            />
                            <Text
                              style={{
                                marginLeft: 5,
                                color: Colors.primaryColor,
                                fontFamily: "NunitoSans-Regular",
                                fontSize: switchMerchant ? 10 : 14,
                              }}
                            >
                              Add Stamp Item
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </ScrollView>
                </View>
              </View>
            </>
          ) : null}
        </ScrollView>
      </View>
    </View>
    // </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: "row",
    fontFamily: "NunitoSans-Regular",
  },
  list: {
    backgroundColor: Colors.whiteColor,
    // width: windowWidth * 0.85,
    // height: windowHeight,
    marginTop: 40,
    marginHorizontal: 30,
    //alignSelf: 'center',
    //justifyContent: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
  },
  listItem: {
    fontFamily: "NunitoSans-Regular",
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    // width: windowWidth * Styles.sideBarWidth,
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,
    // elevation: 16,
  },
  content: {
    padding: 20,
    // width: windowWidth * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.fieldtBgColor,
  },
  textInput: {
    fontFamily: "NunitoSans-Regular",
    width: 300,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: "row",
  },
  textInputLocation: {
    fontFamily: "NunitoSans-Regular",
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textSize: {
    fontSize: 19,
    fontFamily: "NunitoSans-SemiBold",
  },
  merchantDisplayView: {
    flexDirection: "row",
    flex: 1,
    marginLeft: "17%",
  },
  shiftText: {
    marginLeft: "15%",
    color: Colors.primaryColor,
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 25,
  },
  confirmBox: {
    width: "30%",
    height: "30%",
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  closeButton: {
    position: "absolute",
    right: 10,
    top: 10,
    elevation: 1000,
    zIndex: 1000,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: "center",
    justifyContent: "center",
  },
  modalView: {
    height: 200,
    width: 500,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: 25,
    //alignItems: 'center',
    //justifyContent: 'center'
  },
  cardText: {
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 40,
    color: "#ffffff",
  },
  headerLeftStyle: {
    width: Dimensions.get("window").width * 0.17,
    justifyContent: "center",
    alignItems: "center",
  },
});
export default LoyaltyStampScreen;
