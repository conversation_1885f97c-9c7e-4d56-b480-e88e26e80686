import html2pdf from 'html2pdf.js';
import moment from 'moment';
import { Alert, Platform } from 'react-native';
import { MerchantStore } from '../store/merchantStore';

export const convertPOtoPDF = async (po) => {
  // Initialize variables
  let itemStrList = [];
  let totalAmount = 0;
  // const merchantName = MerchantStore.useState(s => s.name);
  const merchantName = global.merchantName;

  // Build item list HTML
  for (var i = 0; i < po.poItems.length; i++) {
    const item = po.poItems[i];
    const quantity = item.orderQuantity || 0;
    const unitPrice = item.price || 0;
    const subtotal = quantity * unitPrice;
    totalAmount += subtotal;
    const supplyItem = po.poItems[i].supplyItem

    itemStrList.push(
      `<tr>
        <td>
          <b>${(supplyItem && supplyItem.name) || '-'}</b>
          <br />
          ${(supplyItem && supplyItem.description) || '-'}
        </td>
        <td>${(supplyItem && supplyItem.skuMerchant) || '-'}</td>
        <td>${item.unit || '-'}${supplyItem && supplyItem.subUnit ? `<br />(${supplyItem.subUnit})` : ''}</td>
        <td>${quantity}${(quantity && supplyItem && supplyItem.cAmount) ? `<br />(${(quantity / supplyItem.cAmount).toFixed(2)})` : ''}</td>
        <td>${unitPrice.toFixed(2)}</td>
        <td>${subtotal.toFixed(2)}</td>
      </tr>`
    );
  }

  // Calculate tax, discount and final total
  const subtotal = totalAmount;
  const taxAmount = po.tax || 0;
  const discountAmount = po.discount || 0;
  const finalTotal = po.finalTotal || (subtotal + taxAmount - discountAmount);

  // Get supplier contact person
  const supplierPIC = po.supplierPIC || (po.supplierPICList && po.supplierPICList.length > 0 ? po.supplierPICList[0].name : '-');

  // Replace template variables
  let replacedHtml = PURCHASE_ORDER_HTML
    .replaceAll('_MERCHANT_NAME_', merchantName || '-')
    .replaceAll('_PO_ID_', po.poId || '-')
    .replaceAll('_HEADER_DATE_', moment(po.createdAt).format('YYYY-MM-DD'))
    .replaceAll('_STATUS_', po.status || '-')
    .replaceAll('_OUTLET_NAME_', po.outletName || '-')
    .replaceAll('_OUTLET_EMAIL_', po.outletEmail || '-')
    .replaceAll('_SUPPLIER_NAME_', po.supplierName || '-')
    .replaceAll('_SUPPLIER_PIC_', supplierPIC)
    .replaceAll('_REMARKS_', po.remarks || 'N/A')
    .replaceAll('_ESTIMATED_ARRIVAL_DATE_', po.estimatedArrivalDate ? moment(po.estimatedArrivalDate).format('YYYY-MM-DD') : '-')
    .replaceAll('_EMPLOYEE_EMAIL_', po.employeeEmail || '-')
    .replaceAll('_PO_ITEMS_', itemStrList.join(''))
    .replaceAll('_TOTAL_PRICE_', subtotal.toFixed(2))
    .replaceAll('_TAX_', taxAmount.toFixed(2))
    .replaceAll('_DISCOUNT_', discountAmount.toFixed(2))
    .replaceAll('_FINAL_TOTAL_', finalTotal.toFixed(2));

  // Create a temporary div to hold the HTML content
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = replacedHtml;

  // Configure PDF export options
  const options = {
    margin: [20, 20],
    filename: `PO-${po.poId || 'unknown'}.pdf`,
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: { 
      scale: 2,
      useCORS: true,
      logging: false
    },
    jsPDF: { 
      unit: 'mm', 
      format: 'a4', 
      orientation: 'portrait' 
    }
  };

  try {
    // Convert HTML to PDF and get base64
    const pdf = await html2pdf().set(options).from(tempDiv).outputPdf('datauristring');
    
    // Create a file-like object with base64 property
    const file = {
      base64: pdf.split(',')[1], // Remove the data URL prefix
      filePath: `PO-${po.poId || 'unknown'}.pdf`
    };

    // Download the PDF file
    const pdfBlob = await html2pdf().set(options).from(tempDiv).outputPdf('blob');
    const downloadUrl = URL.createObjectURL(pdfBlob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `PO-${po.poId || 'unknown'}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(downloadUrl);

    return file;
  } catch (error) {
    console.error(error);
    Alert.alert('Error', `Failed to generate PDF: ${error.message}`);
    throw error;
  }
};

export const PURCHASE_ORDER_HTML = `<style>
  /* 
  Import the desired font from Google fonts. 
  */
  @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap');
  
  /* 
  Define all colors used in this template 
  */
  :root{
    --font-color: black;
    --highlight-color: #4E9F7D;
    --header-bg-color: #DCECE5;
    --footer-bg-color: #F7F7F7;
    --table-row-separator-color: #BFC0C3;
  }
  
  @page{
    size:A4;
    margin:2cm 0 3cm 0;
  
    @top-left{
      content:element(header);
    }
  
    @bottom-left{
      content:element(footer);
    }
  }
  
  body{
    margin:0;
    padding:1cm 2cm;
    color:var(--font-color);
    font-family: 'Montserrat', sans-serif;
    font-size:10pt;
  }
  
  main {
    margin-top: 20px;
  }
  
  a{
    color:inherit;
    text-decoration:none;
  }
  
  hr{
    margin:1cm 0;
    height:0;
    border:0;
    border-top:1mm solid var(--highlight-color);
  }
  
  header{
    position:running(header);
    /* background-color:var(--header-bg-color); */
  }
  
  header .headerSection{
    display:flex;
    justify-content:space-between;
  }
  
  header .headerSection:first-child{
    /* padding-top:.5cm; */
  }
  
  header .headerSection:last-child{
    padding-bottom:.5cm;
  }
  
  header .headerSection div:last-child{
    width:35%;
  }
  
  header .logoAndName{
    display:flex;
    align-items:center;
    justify-content:space-between;
  }
  
  header .logoAndName svg{
    width:1.5cm;
    height:1.5cm;
    margin-right:.5cm;
  }
  
  header .headerSection .estimateDetails{
    padding-top:1cm;
  }
  
  header .headerSection .issuedTo{
    display:flex;
    justify-content:space-between;
  }
  
  header .headerSection .issuedTo h3{
    margin:0 .75cm 0 0;
    color:var(--highlight-color);
  }
  
  header .headerSection div p{
    margin-top:2px;
  }
  
  header h1,
  header h2,
  header h3,
  header p{
    margin:0;
  }
  
  header h2,
  header h3{
    text-transform:uppercase;
  }
  
  header hr{
    margin:1cm 0 .5cm 0;
  }
  
  main table{
    width:100%;
    border-collapse:collapse;
  }
  
  main table thead th{
    height:1cm;
    color:var(--highlight-color);
  }
  
  main table thead th:nth-of-type(2),
  main table thead th:nth-of-type(3),
  main table thead th:last-of-type{
    width:2.5cm;
  }
  
  main table tbody td{
    padding:2mm 0;
    border-bottom:0.5mm solid var(--table-row-separator-color);
  }
  
  main table thead th:last-of-type,
  main table tbody td:last-of-type{
    text-align:right;
  }
  
  main table th{
    text-align:left;
  }
  
  main table.summary{
    width:calc(40% + 2cm);
    margin-left:60%;
    margin-top:.5cm;
  }
  
  main table.summary tr.total{
    font-weight:bold;
  }
  
  main table.summary th{
    padding:4mm 0 4mm 1cm;
    border-bottom:0;
  }
  
  main table.summary td{
    padding:4mm 2cm 4mm 0;
    border-bottom:0;
  }
  
  .order-info {
    margin: 1cm 0;
    display: flex;
    justify-content: space-between;
  }
  
  .order-info-section {
    width: 48%;
  }
  
  .order-info-section h3 {
    color: var(--highlight-color);
    margin-bottom: 5mm;
    text-transform: uppercase;
  }
  
  .order-info-section p {
    margin: 2mm 0;
  }
  
  aside {
    -prince-float: bottom;
    padding:0 0cm .5cm 0cm;
  }
  
  aside p{
    margin:0;
    column-count:2;
  }
  
  footer{
    height:3cm;
    line-height:3cm;
    padding:0 2cm;
    position:running(footer);
    background-color:var(--footer-bg-color);
    font-size:8pt;
    display:flex;
    align-items:baseline;
    justify-content:space-between;
  }
  
  footer a:first-child{
    font-weight:bold;
  }
  
  </style>
  
  <header>
    <div class="headerSection">
      <div class="logoAndName">
        <h1>_MERCHANT_NAME_</h1>
      </div>
      <div>
        <h2>Purchase Order</h2>
        <p>
          <b>PO ID:</b> _PO_ID_
        </p>
        <p>
          <b>Date Issued:</b> _HEADER_DATE_
        </p>
        <p>
          <b>Status:</b> _STATUS_
        </p>
      </div>
    </div>
    <hr />
    <div class="headerSection">
      <div class="issuedTo">
        <h3>From (Outlet)</h3>
        <p>
          <b>_OUTLET_NAME_</b>
          <br />
        </p>
      </div>
  
      <div class="issuedTo">
        <h3>To (Supplier)</h3>
        <p>
          <b>_SUPPLIER_NAME_</b>
          <br />
          <br />
          <b>Contact Person:</b> _SUPPLIER_PIC_
        </p>
      </div>
  
      <div>
        <p>
          <b>Remarks</b>
          <br />
          _REMARKS_
        </p>
      </div>
    </div>
  </header>
  
  <main>
    <div class="order-info">
      <div class="order-info-section">
        <h3>Order Details</h3>
        <p><b>Estimated Arrival Date:</b> _ESTIMATED_ARRIVAL_DATE_</p>
        <p><b>Employee Email:</b> _EMPLOYEE_EMAIL_</p>
      </div>
    </div>
    
    <table>
      <thead>
        <tr>
          <th>Item Name</th>
          <th>SKU</th>
          <th>Packaging</th>
          <th>Quantity</th>
          <th>Unit Price</th>
          <th>Subtotal</th>
        </tr>
      </thead>
      <tbody>
        _PO_ITEMS_
        <!-- 
        示例行项目格式:
        <tr>
          <td>SKU123</td>
          <td>
            <b>Item Name</b>
            <br />
            Description
          </td>
          <td>KG</td>
          <td>10</td>
          <td>$25.00</td>
          <td>$250.00</td>
        </tr>
        -->
      </tbody>
    </table>
    
    <table class="summary">
      <tr>
        <th>
          Subtotal
        </th>
        <td>
          _TOTAL_PRICE_
        </td>
      </tr>
      <tr>
        <th>
          Tax
        </th>
        <td>
          _TAX_
        </td>
      </tr>
      <tr class="total">
        <th>
          Final Total
        </th>
        <td>
          _FINAL_TOTAL_
        </td>
      </tr>
    </table>
  </main>
  
  <aside>
    <hr />
    <div>
      <b>
      &copy;
      <span id="copyright">
          <script>document.getElementById('copyright').appendChild(document.createTextNode(new Date().getFullYear()))</script>
      </span>
      KooDoo</b>
    </div>
  </aside>`;
