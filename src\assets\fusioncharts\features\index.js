import MessageLogger from"@fusioncharts/features/src/messagelogger";import linkedChartsExtension from"@fusioncharts/features/src/linkedcharts";import XMLTranscoder from"@fusioncharts/features/src/xml-transcoder";import CSVTranscoder from"@fusioncharts/features/src/csv-transcoder";import JSONUrlTranscoder from"@fusioncharts/features/src/json-url-transcoder";import XMLUrlTranscoder from"@fusioncharts/features/src/xml-url-transcoder";import IgnoreCaseExt from"@fusioncharts/features/src/ignore-case-ext";import ThemeEngine from"@fusioncharts/features/src/theme-engine";import DefaultTheme from"@fusioncharts/features/src/default-theme";import PlotHighlighter from"@fusioncharts/features/src/highlighter";import ToolTipController from"@fusioncharts/features/src/tooltip-controller";import ConsolidatedToolTip from"@fusioncharts/features/src/consolidated-tooltip";import AlertManager from"@fusioncharts/features/src/alertmanager";import DataSkipping from"@fusioncharts/features/src/data-skipping";import Annotation from"@fusioncharts/features/src/annotation";import RegressionExtension from"@fusioncharts/features/src/regression-extension";import inputAdapter from"@fusioncharts/features/src/input-ext";import LegendEventManagerLinker from"@fusioncharts/features/src/legend-event-manager-linker";import exportModule from"@fusioncharts/features/src/export-module";import BatchExportLinker from"@fusioncharts/features/src/batchexport";import LogoExtension from"@fusioncharts/features/src/logo";import supportOldThemes from"@fusioncharts/features/src/support-old-themes-ext";import debug from"@fusioncharts/features/src/debugger";import CenterLabel from"@fusioncharts/features/src/center-label-extension";import CrossLine from"@fusioncharts/features/src/crossline";import MultiCanvasCrossLine from"@fusioncharts/features/src/multicanvas-crossline-manager";export{MessageLogger,linkedChartsExtension,XMLTranscoder,CSVTranscoder,JSONUrlTranscoder,XMLUrlTranscoder,IgnoreCaseExt,ThemeEngine,DefaultTheme,PlotHighlighter,ToolTipController,ConsolidatedToolTip,AlertManager,DataSkipping,Annotation,RegressionExtension,inputAdapter,LegendEventManagerLinker,exportModule,BatchExportLinker,LogoExtension,supportOldThemes,debug,CenterLabel,CrossLine,MultiCanvasCrossLine};export default{name:"features",type:"package",requiresFusionCharts:true,extension:FusionCharts=>{FusionCharts.addDep(linkedChartsExtension);FusionCharts.addDep(XMLTranscoder);FusionCharts.addDep(CSVTranscoder);FusionCharts.addDep(JSONUrlTranscoder);FusionCharts.addDep(XMLUrlTranscoder);FusionCharts.addDep(IgnoreCaseExt);FusionCharts.addDep(ThemeEngine);FusionCharts.addDep(PlotHighlighter);FusionCharts.addDep(DefaultTheme);FusionCharts.addDep(ToolTipController);FusionCharts.addDep(ConsolidatedToolTip);FusionCharts.addDep(AlertManager);FusionCharts.addDep(MessageLogger);FusionCharts.addDep(DataSkipping);FusionCharts.addDep(Annotation);FusionCharts.addDep(exportModule);FusionCharts.addDep(RegressionExtension);FusionCharts.addDep(inputAdapter);FusionCharts.addDep(LegendEventManagerLinker);FusionCharts.addDep(BatchExportLinker);FusionCharts.addDep(LogoExtension);FusionCharts.addDep(supportOldThemes);FusionCharts.addDep(debug);FusionCharts.addDep(CenterLabel);FusionCharts.addDep(CrossLine);FusionCharts.addDep(MultiCanvasCrossLine)}};