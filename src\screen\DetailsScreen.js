import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useCallback,
} from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Dimensions,
  Alert,
  Button,
  Modal,
  TextInput,
  KeyboardAvoidingView,
  ActivityIndicator,
  TextInputBase,
  Linking,
  useWindowDimensions,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
//import { firebase } from '@react-native-firebase/storage';
import firebase from "firebase";
import { Platform } from 'react-native';
// import Switch from 'react-native-switch-pro';
// import Swipeout from 'react-native-swipeout';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Footer from './Footer';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Plus from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import PlusSvg from '../assets/svg/Plus.svg';
import Coins from '../assets/svg/Coins.svg';
import DropDownPicker from 'react-native-dropdown-picker';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import API from '../constant/API';
import * as User from '../util/User';
import * as Cart from '../util/Cart';
// import Dash from 'react-native-dash';
import moment from 'moment';
import Styles from '../constant/Styles';
// import QRCode from 'react-native-qrcode-svg';
import EIcon from 'react-native-vector-icons/Entypo';
//import CurrencyInput, { formatValue } from 'react-currency-input-field';
import AIcon from 'react-native-vector-icons/AntDesign';
import OrderModal from './components/OrderModal';
// import Barcode from 'react-native-barcode-builder';
//import molpay from 'molpay-mobile-xdk-reactnative-beta';
import {
  //isTablet,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
} from '../util/common';
// import RNPopover from 'react-native-popover-menu';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
// import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
  DELAY_LONG_PRESS_TIME,
  MODE_ADD_CART,
  OFFLINE_BILL_TYPE,
  OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
  OFFLINE_PAYMENT_METHOD_TYPE,
  ORDER_TYPE,
  USER_ORDER_PRIORITY,
  USER_ORDER_STATUS,
  USER_RESERVATION_STATUS,
  RESERVATIONS_SHIFT_TYPE,
  EMAIL_REPORT_TYPE,
  USER_RESERVATION_STATUS_PARSED,
  USER_ORDER_STATUS_PARSED,
} from '../constant/common';
// import { useKeyboard } from '../hooks';
// import RNPickerSelect from 'react-native-picker-select';
import {
  getObjectDiff,
  isObjectEqual,
  listenToCurrOutletIdChangesWaiter,
  naturalCompare,
  generateEmailReport,
  uploadImageToFirebaseStorage,
  sliceUnicodeStringV2WithDots,
} from '../util/common';
import { qrUrl } from '../constant/env';
import { printUserOrder } from '../util/printer';
import { Collections } from '../constant/firebase';
//import firestore from '@react-native-firebase/firestore';
// import CheckBox from 'react-native-check-box';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';

//import CheckBox from '@react-native-community/checkbox';
// import { DIMENTIONS } from 'react-native-numeric-input';
import { color } from 'react-native-reanimated';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
//import GCalendar from '../assets/svg/GCalendar';
import APILocal from '../util/apiLocalReplacers';
import { useNetInfo } from '@react-native-community/netinfo';
// import {CountryPicker} from "react-native-country-codes-picker/components/CountryPicker";
import {
  filterChartItems,
  getDataForChartDashboardTodaySales,
  getDataForSalesLineChart,
} from '../util/chart';
// const RNFS = require('react-native-fs');
import { useFocusEffect } from '@react-navigation/native';
//import UserIdleWrapper from '../components/userIdleWrapper';

import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";
import Switch from 'react-switch'
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import TimeKeeper from "react-timekeeper";
import Ionicon from "react-native-vector-icons/Ionicons";

import { ReactComponent as Seat } from "../assets/svg/Seat.svg"

const alphabet = '0123456789';
const nanoid = customAlphabet(alphabet, 12);

const DetailsScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, []),
  );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const merchantLogo = MerchantStore.useState((s) => s.logo);

  //flat list usestate
  const [switchMerchant, setSwitchMerchant] = useState(false);
  const [showReservations, setShowReservations] = useState(true);
  const [showFinished, setShowFinished] = useState(false);
  const [showWaitlist, setShowWaitlist] = useState(false);
  const [search, setSearch] = useState('');

  const [image, setImage] = useState('');
  const [imageType, setImageType] = useState('');
  const [isImageChanged, setIsImageChanged] = useState(false);

  // filter modal useState
  const [showFilter, setShowFilter] = useState(false);
  const [filterTags, setFilterTags] = useState(false);
  const [filterNotes, setFilterNotes] = useState(false);

  // filter reservation status modal usestate
  const [reservationStatusModal, setReservationStatusModal] = useState(false);

  // filter room modal usestate
  const [roomModal, setRoomModal] = useState(false);
  const [roomAll, setRoomAll] = useState(false);
  const [roomIndoor, setRoomIndoor] = useState(false);
  const [roomDinnerRoom, setRoomDinnerRoom] = useState(false);

  // filter party size modal usestate
  const [partyModal, setPartyModal] = useState(false);
  const [partyAll, setPartyAll] = useState(false);
  const [partySmall, setPartySmall] = useState(false);
  const [partyBig, setPartyBig] = useState(false);

  const [reservationsArr, setReservationsArr] = useState([]);
  const [finishedArr, setFinishedArr] = useState([]);
  const [waitListArr, setWaitListArr] = useState([]);

  const [isFinished, setIsFinished] = useState(true);
  const [isNoShow, setIsNoShow] = useState(false);
  const [isCancelled, setIsCancelled] = useState(false);

  const [roomChecked, setRoomChecked] = useState({});
  const [roomCount, setRoomCount] = useState(0);
  const [partyChecked, setPartyChecked] = useState({});
  const [partyCount, setPartyCount] = useState(0);

  const [seatingModal, setSeatingModal] = useState(false);
  const firebaseUid = UserStore.useState((s) => s.firebaseUid);

  const partySizeArr = [
    { label: '0-6', value: 0 },
    { label: '7-12', value: 1 },
    { label: '13-18', value: 2 },
    { label: '19-24', value: 3 },
    { label: '25-30', value: 4 },
    { label: '31-36', value: 5 },
  ];

  const selectedCalendarData = CommonStore.useState(
    (s) => s.selectedCalendarData,
  );

  const userReservations = OutletStore.useState((s) => s.userReservations);
  const userReservationsWaitList = OutletStore.useState(
    (s) => s.userReservationsWaitList,
  );
  const crmUsers = OutletStore.useState((s) => s.crmUsers);
  const crmUserTags = OutletStore.useState((s) => s.crmUserTags);
  const outletSections = OutletStore.useState((s) => s.outletSections);
  const outletTables = OutletStore.useState((s) => s.outletTables);

  // const userOrders = OutletStore.useState(
  //   (s) => s.userOrders,
  // );

  const userOrders = OutletStore.useState((s) => s.userOrders);

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);

  //const userId = UserStore.useState((s) => s.firebaseUid);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const merchantName = MerchantStore.useState((s) => s.name);

  const [customerGender, setCustomerGender] = useState('Others');
  const [customerDob, setCustomerDob] = useState(Date.now());

  const [addReservationModal, setAddReservationModal] = useState(false);
  const [showAddCustomer, setShowAddCustomer] = useState(false);

  const [pax, setPax] = useState([]);
  const [seatingPax, setSeatingPax] = useState('1');
  const [selectedPax, setSelectedPax] = useState(0);

  const [reservationId, setReservationId] = useState('');
  const [reservationToUsed, setReservationToUsed] = useState(null);

  const [reservationCustomerName, setReservationCustomerName] = useState('');
  const [reservationPhone, setReservationPhone] = useState('');
  const [reservationRemark, setReservationRemark] = useState('');
  const [reservationDate, setReservationDate] = useState(Date.now());
  const [reservationTime, setReservationTime] = useState(Date.now());
  const [reservationDateTime, setReservationDateTime] = useState(Date.now());
  const [selectTable, setSelectTable] = useState('');

  const [dietaryRestrictions, setDietaryRestrictions] = useState('');
  const [specialOccasions, setSpecialOccasions] = useState('');

  const isLoading = CommonStore.useState((s) => s.isLoading);
  const selectedOutletTable = CommonStore.useState(
    (s) => s.selectedOutletTable,
  );
  const [openSelectTable, setOpenSelectTable] = useState(false);
  const [filteredOutletTablesForRendered, setFilteredOutletTablesForRendered] =
    useState([]);
  const [filteredOutletTables, setFilteredOutletTables] = useState([]);

  const [filteredOutletTablesReservation, setFilteredOutletTablesReservation] = useState([]);

  const [reservationIntervalMin, setReservationIntervalMin] = useState(120);
  const selectedOutletSection = CommonStore.useState(
    (s) => s.selectedOutletSection,
  );
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const userOrdersTableDict = OutletStore.useState(
    (s) => s.userOrdersTableDict,
  );
  const [filteredReservations, setFilteredReservations] = useState([]);
  const [reservationIntervalStartTime, setReservationIntervalStartTime] =
    useState(moment());
  const [reservationIntervalEndTime, setReservationIntervalEndTime] = useState(
    moment(),
  );
  const [showReservationDatePicker, setShowReservationDatePicker] =
    useState(false);
  const [showReservationTimePicker, setShowReservationTimePicker] =
    useState(false);
  const [selectedOutletTableId, setSelectedOutletTableId] = useState('');
  const [selectedReservation, setSelectedReservation] = useState({});
  const [tableModalVisibility, setTableModalVisibility] = useState(false);
  const netInfo = useNetInfo();
  // const [keyboardHeight] = useKeyboard();

  const [exportModalVisibility, setExportModalVisibility] = useState(false);
  const [selectedReservationType, setSelectedReservationType] = useState('');
  const [selectedReservationStatus, setSelectedReservationStatus] =
    useState('ALL');
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState(null);
  const [items, setItems] = useState([
    { label: 'Reservations', value: 'reservation' },
    { label: 'Finished/CXL', value: 'finished' },
    { label: 'Waitlist', value: 'waitlist' },
  ]);

  const [addReservation, setAddReservation] = useState(false);

  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('+60');
  const [customerEmail, setCustomerEmail] = useState('');

  const [customerUsername, setCustomerUsername] = useState('');

  const [customerAddress, setCustomerAddress] = useState('');
  const [customerAddressLat, setCustomerAddressLat] = useState('');
  const [customerAddressLng, setCustomerAddressLng] = useState('');

  const [customerPhoneSecond, setCustomerPhoneSecond] = useState('');
  const [customerEmailSecond, setCustomerEmailSecond] = useState('');
  const [customerAddressSecond, setCustomerAddressSecond] = useState('');
  const [customerAddressLatSecond, setCustomerAddressLatSecond] = useState('');
  const [customerAddressLngSecond, setCustomerAddressLngSecond] = useState('');

  const [isCustomer, setIsCustomer] = useState(false);
  const [searchCustomer, setSearchCustomer] = useState('');
  const [showCustomerList, setShowCustomerList] = useState(false);

  const [registeredCRMUsersDropdownList, setRegisteredCRMUsersDropdownList] =
    useState([]);
  const [selectedRegisteredCRMUserId, setSelectedRegisteredCRMUserId] =
    useState('');

  const [toAppear, setToAppear] = useState(false);
  const [isFiltered, setIsFiltered] = useState(false);

  const [isSelected, setIsSelected] = useState('');

  useEffect(() => {
    setRegisteredCRMUsersDropdownList(
      crmUsers
        .filter(
          (user) => user.name && user.name.trim(),
          // && user.userId !== undefined
        )
        .map((item) => ({
          label: item.name,
          value: item.userId || item.email, // use email, if no userId
          phoneNumber: item.number || '',
          email: item.email || '',
        })),
    );

    if (
      registeredCRMUsersDropdownList.length > 0 &&
      selectedRegisteredCRMUserId === ''
    ) {
      setSelectedRegisteredCRMUserId(registeredCRMUsersDropdownList[0].value);
    }
  }, [crmUsers]);

  const [rev_date, setRev_date] = useState(moment().endOf(Date.now()).toDate());

  const [resDays, setResDays] = useState([]);
  const [resMonths, setResMonths] = useState([]);
  const [resYears, setResYears] = useState([]);
  const [selectedResDay, setSelectedResDay] = useState(
    moment().endOf(Date.now()).format('DD'),
  );
  const [selectedResMonth, setSelectedResMonth] = useState(
    moment().endOf(Date.now()).format('MMM'),
  );
  const [selectedResYear, setSelectedResYear] = useState(
    moment().endOf(Date.now()).format('YYYY'),
  );

  ///////// 08/23 add time slot selection
  const [reservationAvailabilityToday, setReservationAvailabilityToday] = useState([]);
  const [reservationAvailabilityTodayNames, setReservationAvailabilityTodayNames] = useState([]);
  const [selectedReservationName, setSelectedReservationName] = useState('All Day');
  const [reservationAvailabilityFiltered, setReservationAvailabilityFiltered] = useState([]);
  const [dateSelectedTimestamp, setDateSelectedTimestamp] = useState(moment().startOf('day').valueOf());
  const [allReservationAvailabilityList, setAllReservationAvailabilityList] = useState([]);
  const [userReservation, setUserReservation] = useState([]);
  const [timeSelected, setTimeSelected] = useState('');
  const [timeSelectedMilli, setTimeSelectedMilli] = useState('');
  const [selectedReservationId, setSelectedReservationId] = useState('');
  const [partyAmount, setPartyAmount] = useState('2');
  const allOutletTables = OutletStore.useState((s) => s.outletTables);
  const calendarDataArray = CommonStore.useState((s) => s.calendarDataArray);
  const [outletTable, setOutletTables] = useState([]);
  const [headerSorting, setHeaderSorting] = useState([]);
  const [paxNum, setPaxNum] = useState(0);
  const [revNum, setRevNum] = useState(0);
  const [reservationAvailability, setReservationAvailability] = useState({});

  const [exportEmail, setExportEmail] = useState('');
  const [isCsv, setIsCsv] = useState(false);
  const [isExcel, setIsExcel] = useState(false);

  const BreakFastTimeSelection = [
    { timeSection: 'Break Fast', time: '8:30 AM' },
    { timeSection: 'Break Fast', time: '9:00 AM' },
    { timeSection: 'Break Fast', time: '9:30 AM' },
    { timeSection: 'Break Fast', time: '10:00 AM' },
    { timeSection: 'Break Fast', time: '10:30 AM' },
    { timeSection: 'Break Fast', time: '11:00 AM' },
    { timeSection: 'Break Fast', time: '11:30 AM' },
  ];
  const LunchTimeSelection = [
    { timeSection: 'Lunch', time: '12:00 PM' },
    { timeSection: 'Lunch', time: '12:30 PM' },
    { timeSection: 'Lunch', time: '1:00 PM' },
    { timeSection: 'Lunch', time: '1:30 PM' },
    { timeSection: 'Lunch', time: '2:00 PM' },
    { timeSection: 'Lunch', time: '2:30 PM' },
    { timeSection: 'Lunch', time: '3:00 PM' },
    { timeSection: 'Lunch', time: '3:30 PM' },
    { timeSection: 'Lunch', time: '4:00 PM' },
    { timeSection: 'Lunch', time: '4:30 PM' },
    { timeSection: 'Lunch', time: '5:00 PM' },
    { timeSection: 'Lunch', time: '5:30 PM' },
    { timeSection: 'Lunch', time: '6:00 PM' },
    { timeSection: 'Lunch', time: '6:30 PM' },
  ];
  const DinnerTimeSelection = [
    { timeSection: 'Dinner', time: '7:00 PM' },
    { timeSection: 'Dinner', time: '7:30 PM' },
    { timeSection: 'Dinner', time: '8:00 PM' },
    { timeSection: 'Dinner', time: '8:30 PM' },
    { timeSection: 'Dinner', time: '9:00 PM' },
    { timeSection: 'Dinner', time: '9:30 PM' },
    { timeSection: 'Dinner', time: '10:00 PM' },
  ];

  ////////////////////////////////////////////

  // 2023-06-14 - Highlight for custom time

  var highlightCustomTime = true;
  if (BreakFastTimeSelection.find(timeslot => timeslot.time === timeSelected)) {
    highlightCustomTime = false;
  }
  else if (LunchTimeSelection.find(timeslot => timeslot.time === timeSelected)) {
    highlightCustomTime = false;
  }
  else if (DinnerTimeSelection.find(timeslot => timeslot.time === timeSelected)) {
    highlightCustomTime = false;
  }

  ////////////////////////////////////////////

  useEffect(() => {
    var resDaysTemp = Array.from(
      Array(moment(rev_date).daysInMonth()),
      (_, i) => i + 1,
    );
    setResDays(resDaysTemp);

    var resMonthsTemp = [];
    resMonthsTemp = moment.localeData().monthsShort();
    setResMonths(resMonthsTemp);

    var currYear = parseInt(moment().format('YYYY'), 10);
    var startYear = currYear - 10;

    var resYearsTemp = [];
    for (var i = 0; i < 20; i++) {
      resYearsTemp.push(startYear);
      startYear++;
    }

    setResYears(resYearsTemp);
  }, []);

  // useEffect(() => {
  //   var dateTemp = moment(
  //     selectedResDay + '-' + selectedResMonth + '-' + selectedResYear,
  //   ).format('YYYY-MM-DDTHH:mm:ss.SSSS[Z]');

  //   setRev_date(moment(dateTemp).startOf('day'));
  //   // console.log('time00 + ' + moment(dateTemp).format('DD MM YYYY'));
  //   CommonStore.update((s) => {
  //     s.selectedCalendarData = moment(dateTemp).format('YYYY-MM-DD');
  //   });
  // }, [selectedResDay, selectedResMonth, selectedResYear]);

  useEffect(() => {
    var numOfPax = 1;
    var tempPax = [];
    for (var i = 0; i < 30; i++) {
      tempPax.push(numOfPax);
      numOfPax++;
    }
    setPax(tempPax);
  }, []);

  const [resHour, setResHour] = useState([]);
  const [resMin, setResMin] = useState([]);
  const [selectedHour, setSelectedHour] = useState(
    moment().endOf(Date.now()).format('HH'),
  );
  const [selectedMin, setSelectedMin] = useState(
    moment().endOf(Date.now()).format('mm'),
  );

  const [openPax, setOpenPax] = useState(false);
  const [openDate, setOpenDate] = useState(false);
  const [openMonth, setOpenMonth] = useState(false);
  const [openYear, setOpenYear] = useState(false);
  const [openStatus, setOpenStatus] = useState(false);

  useEffect(() => {
    var hourTemp = [];
    var hour = 0;

    for (var i = 0; i < 25; i++) {
      hourTemp.push(hour);
      hour++;
    }
    setResHour(hourTemp);

    var minTemp = [];
    var min = 0;

    while (min != 60) {
      minTemp.push(min);
      min += 5;
    }
    setResMin(minTemp);
  }, []);

  const renderCrmUser = (item, index) => {
    // // console.log(item, 'here');
    return (
      <TouchableOpacity
        style={{
          backgroundColor:
            selectedRegisteredCRMUserId === item.item.value
              ? Colors.fieldtBgColor
              : Colors.whiteColor,
          width: '100%',
          borderTopWidth: 1,
          borderColor: Colors.fieldtBgColor2,
          paddingVertical: 10,
          paddingHorizontal: 15,
          flexDirection: 'row',
        }}
        onPress={() => {
          setSelectedRegisteredCRMUserId(item.item.value);
          setCustomerName(item.item.label);
          setCustomerPhone(item.item.phoneNumber);
          setCustomerEmail(item.item.email);
          setIsCustomer(true);
          // // console.log(customerPhone, 'here');
        }}>
        <Text
          numberOfLines={1}
          style={[
            styles.modalDescText,
            switchMerchant
              ? {
                fontSize: 10,
                flex: 1,
              }
              : {
                flex: 1,
              },
          ]}>
          {item.item.label}
        </Text>
        <Text
          numberOfLines={1}
          style={[
            styles.modalDescText,
            switchMerchant
              ? {
                fontSize: 10,
                flex: 1,
              }
              : { flex: 1 },
          ]}>
          {item.item.phoneNumber || ''}
        </Text>
      </TouchableOpacity>
    );
  };

  const updateReservation = (item) => {
    // const timeTemp = selectedHour + ':' + selectedMin;
    const dateTemp2 =
      `${selectedResYear} ${selectedResMonth} ${selectedResDay}`;

    // setReservationTime(timeTemp);
    setReservationDate(dateTemp2);

    const newRevDateTime1 = moment(
      `${dateTemp2} ${moment(reservationTime).format('HH:mm')}`, 'YYYY MMM DD HH:mm').valueOf();

    if (isNaN(newRevDateTime1)) {
      window.confirm(
        "Something went wrong, Invalid Reservation Time."
      );
    }

    var body = {
      outletId: currOutlet.uniqueId,
      pax: parseInt(seatingPax),
      reservationTime: newRevDateTime1,

      // merchantId: merchantId,
      outletCover: currOutlet.cover,
      merchantLogo,
      outletName: currOutlet.name,
      // merchantName: merchantName,

      userName: customerName,
      userPhone: customerPhone,
      userEmail: customerEmail,

      // tableId: selectedOutletTableId,
      // tableCode: tableCode,

      tableId: selectedOutletTable.uniqueId,
      tableCode: selectedOutletTable.code,

      timeSelected,

      remarks: reservationRemark,

      crmUserId:
        customerName || customerEmail ? selectedRegisteredCRMUserId || '' : '',

      // reservationAvailabilityId: reservationAvailability.uniqueId,

      reservationId: item,

      dRestrictions: dietaryRestrictions,
      sOccasions: specialOccasions,

      orderId: reservationToUsed.orderId ? reservationToUsed.orderId : '',
      orderIdList: reservationToUsed.orderIdList ? reservationToUsed.orderIdList : [],
    };

    console.log('body', body);

    if (
      // customerName !== '' &&
      // customerPhone !== '' &&
      selectTable !== '' &&
      !selectedOutletTable.capacity < seatingPax &&
      // reservationDate !== '' &&
      reservationTime !== ''
      &&
      timeSelected !== ''
    ) {
      // ApiClient.POST(API.createUserReservationByMerchant, body, false)

      CommonStore.update((s) => {
        s.isLoading = true;
      });

      APILocal.updateUserReservationByMerchant({
        body,
        uid: firebaseUid,
      })
        .then((result) => {
          if (result && result.status) {
            //if (result) {
            window.confirm(
              "Success, Reservation has been updated."
            );

            setAddReservation(false);
            setReservationId('');
            setReservationToUsed(null);
            setReservationCustomerName('');
            setReservationPhone('');
            setReservationDate(Date.now());
            setReservationTime(Date.now());

            setDietaryRestrictions('');
            setSpecialOccasions('');

            setTimeSelected('');
            setIsSelected('');

            setSeatingPax('1');
            setSelectTable('');
            setReservationRemark('');
            setTimeSelected('');

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          }
          else {
            window.confirm(
              "Error, Failed to update reservation."
            );

            CommonStore.update((s) => {
              s.isLoading = false;
            });
          }
        })
        .catch((err) => {
          // console.log(err);

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        });
    }
    // else if (customerName == '') {
    //   alert('Please select the customer first.', [{ text: 'OK', onPress: () => { } }], {
    //     cancelable: false,
    //   });
    // } else if (customerPhone == '') {
    //   alert('Please fill in phone.', [{ text: 'OK', onPress: () => { } }], {
    //     cancelable: false,
    //   });
    // } 
    // else if (reservationDate == '') {
    //   alert(
    //     'Please choose a reservation date.',
    //     [{ text: 'OK', onPress: () => { } }],
    //     {
    //       cancelable: false,
    //     },
    //   );
    // } 
    else if (reservationTime == '') {
      window.confirm('Please choose a reservation time.')
    } else if (timeSelected == '') {
      window.confirm('Please select the timeslot.')
    }
    else if (selectTable == '') {
      window.confirm('Please select a table.')
    } else if (selectedOutletTable.capacity < seatingPax) {
      window.confirm('Please select a bigger table.')
    }
    else {
      window.confirm('Please fill in all the information.')
    }
  };

  const createReservation = () => {
    // const timeTemp = selectedHour + ':' + selectedMin;
    const dateTemp1 =
      `${selectedResYear} ${selectedResMonth} ${selectedResDay}`;

    // setReservationTime(timeTemp);
    setReservationDate(dateTemp1);

    // const newRevDateTime1 = moment(
    //   `${moment(reservationDate).format('DD MMM YYYY')} ${moment(
    //     reservationTime,
    //   ).format('hh:mm')}`,
    //   'DD MMM YYYY hh:mm',
    // ).valueOf();

    const newRevDateTime1 = moment(
      `${dateTemp1} ${moment(reservationTime).format('HH:mm')}`, 'YYYY MMM DD HH:mm').valueOf();

    if (isNaN(newRevDateTime1)) {
      window.confirm(
        "Something went wrong, Invalid Reservation Time."
      );
    }

    var body = {
      outletId: currOutlet.uniqueId,
      pax: parseInt(seatingPax),
      reservationTime: newRevDateTime1,

      // merchantId: merchantId,
      outletCover: currOutlet.cover,
      merchantLogo,
      outletName: currOutlet.name,
      // merchantName: merchantName,

      userName: customerName,
      userPhone: customerPhone,
      userEmail: customerEmail,

      // tableId: selectedOutletTableId,
      // tableCode: tableCode,

      tableId: selectedOutletTable.uniqueId,
      tableCode: selectedOutletTable.code,

      timeSelected: isSelected,

      remarks: reservationRemark,

      userId:
        customerName || customerEmail ? selectedRegisteredCRMUserId || '' : '',

      // reservationAvailabilityId: reservationAvailability.uniqueId,

      dRestrictions: dietaryRestrictions,
      sOccasions: specialOccasions,
    };

    console.log('body', body);

    if (
      // customerName !== '' &&
      // customerPhone !== '' &&
      selectTable !== '' &&
      !selectedOutletTable.capacity < seatingPax &&
      // reservationDate !== '' &&
      reservationTime !== '' &&
      timeSelected !== ''
    ) {
      // ApiClient.POST(API.createUserReservationByMerchant, body, false)
      CommonStore.update((s) => {
        s.isLoading = true;
      });

      APILocal.createUserReservationByMerchant({
        body,
        uid: firebaseUid,
      })
        .then((result) => {
          if (result && result.status) {
            if (window.confirm("Success, Reservation has been created.")) {
              setAddReservation(false);
              setReservationId('');
              setReservationToUsed(null);
              setReservationCustomerName('');
              setReservationPhone('');
              setReservationDate(Date.now());
              setReservationTime(Date.now());

              setDietaryRestrictions('');
              setSpecialOccasions('');

              setTimeSelected('');
              setIsSelected('');

              setSeatingPax('1');
              setSelectTable('');
              setReservationRemark('');
              setTimeSelected('');
              setIsSelected('');

              CommonStore.update((s) => {
                s.isLoading = false;
              });
            }
          }
        })
        .catch((err) => {
          console.log(err);

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        });
    }
    // else if (customerName == '') {
    //   alert('Please select the customer first.', [{ text: 'OK', onPress: () => { } }], {
    //     cancelable: false,
    //   });
    // } else if (customerPhone == '') {
    //   alert('Please fill in phone.', [{ text: 'OK', onPress: () => { } }], {
    //     cancelable: false,
    //   });
    // } 
    // else if (reservationDate == '') {
    //   alert(
    //     'Please choose a reservation date.',
    //     [{ text: 'OK', onPress: () => { } }],
    //     {
    //       cancelable: false,
    //     },
    //   );
    // }
    // else if (timeSelected == '') {
    //   alert(
    //     'Please choose a reservation time.',
    //     [{ text: 'OK', onPress: () => { } }],
    //     {
    //       cancelable: false,
    //     },
    //   );
    // }
    else if (reservationTime == '') {
      window.confirm('Please choose a reservation time.')
    }
    else if (timeSelected == '') {
      window.confirm('Please select the timeslot.')
    }
    else if (selectTable == '') {
      window.confirm('Please select a table.')
    } else if (selectedOutletTable.capacity < seatingPax) {
      window.confirm('Please select a bigger table.')
    }
    else {
      window.confirm('Please fill in all the information.')
    }
  };

  /////// 23/08 time slot
  useEffect(() => {
    for (let i = 0; i < calendarDataArray.length; i++) {
      if (
        moment(calendarDataArray[i].date).format('YYYY-MM-DD') ===
        selectedCalendarData
      ) {
        setPaxNum(calendarDataArray[i].pax);
        setRevNum(calendarDataArray[i].rsv);
        break;
      } else {
        setPaxNum(0);
        setRevNum(0);
      }
    }
    // calendarDataArray.forEach((item, index) => {
    //   if (moment(item.date).format('YYYY-MM-DD') === selectedCalendarData) {
    //     setPaxNum(item.pax);
    //     setRevNum(item.rev);
    //     isValid = true;
    //   }
    // });
    // console.log('refreshed');
  }, [selectedCalendarData]);

  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.selectedCalendarData = moment().format('YYYY-MM-DD');
  //   });
  // }, []);

  useEffect(() => {
    var customerListDict = {};
    setHeaderSorting(crmUsers);
  }, [crmUsers]);

  // use effect to get the current outlet tables from the firestore
  useEffect(() => {
    let tempOutletTables = [];
    // filter allOutletTables for the current outlet
    for (let i = 0; i < allOutletTables.length; i++) {
      if (allOutletTables[i].outletId === currOutletId) {
        tempOutletTables.push(allOutletTables[i]);
      }
    }
    setOutletTables(tempOutletTables);

  }, [currOutletId, allOutletTables]);

  // use effect to get the user reservation from the firestore
  useEffect(() => {
    try {
      // const unsubscribe = firebase.firestore().collection(Collections.UserReservation)
      //   .where('outletId', '==', currOutletId)
      //   .onSnapshot(async (snapshot) => {
      //     const userReservation = [];
      //     if (snapshot && !snapshot.empty) {
      //       snapshot.forEach((doc) => {
      //         const docData = doc.data();

      //         userReservation.push({
      //           ...docData,
      //         });
      //       });
      //       setUserReservation(userReservation);
      //     }
      //     // console.log('User Reservation list', userReservation);
      //   });
      // return (() => {
      //   // unsubscribe the listener
      //   unsubscribe()
      // })
    }
    catch (e) {
      // console.log(e);
    }
  }, [currOutletId]);

  // use effect to get the reservationAvailability from the firestore
  useEffect(() => {
    try {
      // const unsubscribe = firebase.firestore().collection(Collections.ReservationAvailability)
      //   .where('merchantId', '==', merchantId)
      //   .where('outletId', '==', currOutletId)
      //   .onSnapshot(async (snapshot) => {
      //     const reservationAvailability = [];
      //     if (snapshot && !snapshot.empty) {
      //       snapshot.forEach((doc) => {
      //         const docData = doc.data();

      //         reservationAvailability.push({
      //           ...docData,
      //         });
      //       });
      //       setAllReservationAvailabilityList(reservationAvailability);
      //     }
      //     // console.log('Reservation Availability List', reservationAvailability)

      //     // need move whole logic to start of app (dashboard ?) or add to store ???
      //   });
      // return (() => {
      //   // unsubscribe the listener
      //   unsubscribe()
      // })
    }
    catch (e) {
      // console.log(e);
    }
  }, [merchantId, currOutletId]);

  const getAllAvailableTimeSlot = (startInput, end, availability, userReservation, partyAmount, outletTable,) => {
    let tempAvailabilityList = [];
    let start = moment(startInput);

    // interval, add start time with each interval
    while (end >= start.add(availability.intervalMin, 'minutes')) {
      // get the interval start time
      const intervalStartTime = moment(start).subtract(availability.intervalMin, 'minutes');

      // check if the intervalStartTime is before the current time
      if (moment(intervalStartTime).isBefore(moment())) {
        continue;
      }

      let guestNum = 0;
      userReservation.forEach((eachUserReservation) => {
        if (eachUserReservation.status === USER_RESERVATION_STATUS.ACCEPTED &&
          moment(eachUserReservation.reservationTime).isSame(
            moment(intervalStartTime))) {
          guestNum += eachUserReservation.pax;
        }
      });

      // skip current interval if the number of guest is more than the max
      if (guestNum + partyAmount > availability.defaultGuestLimit) {
        continue;
      }

      let haveSpace = false;

      // for each table look for a user reservation at that time
      outletTable.forEach((table) => {
        if (haveSpace) return;

        let isReserved = false;

        userReservation.forEach((eachUserReservation) => {
          // exit if already have space or table is reserved
          if (haveSpace || isReserved) return;

          // if table is reserved by the reservation
          if (eachUserReservation.tableId &&
            eachUserReservation.status === USER_RESERVATION_STATUS.ACCEPTED &&
            eachUserReservation.tableId === table.uniqueId) {

            // check if the reservation is between each interval
            if (moment(eachUserReservation.reservationTime).isBetween(
              moment(intervalStartTime), moment(start), 'minutes', '[]')) {
              isReserved = true;
            }

            // check if there is a table turn time clashed
            if (moment(intervalStartTime).isBetween(
              moment(eachUserReservation.reservationTime),
              moment(eachUserReservation.reservationTime).add(table.turnTime || 60, 'minutes'),
              'minutes', '[]')) {
              isReserved = true;
            }
          }
        });

        if (!isReserved) {
          // if there is no reservation, check the pax size
          // let maxPax = table.paxMax || 9999;
          let maxPax = table.capacity || 9999;
          let minPax = table.paxMin || 1;

          // check if it can fit and the pax is greater than the min
          if (table.capacity >= (isNaN(+partyAmount) ? 10 : +partyAmount) &&
            maxPax >= (isNaN(+partyAmount) ? 10 : +partyAmount) &&
            minPax <= (isNaN(+partyAmount) ? 10 : +partyAmount)) {
            // if pax size is less than the table size add timeslot to the list
            haveSpace = true;
          }
        }
      });

      if (haveSpace) {
        tempAvailabilityList.push({
          ...availability,
          startTime: intervalStartTime.format('hh:mm A'),
          endTime: start.format('hh:mm A'),
          startTimeMilli: intervalStartTime.valueOf(),
        });
      }
    }

    return tempAvailabilityList;
  }

  // when date changed, filter the time slot available to display
  useEffect(() => {
    let tempAvailabilityList = [];
    let tempAvailabilityNames = [];

    // repeat logic
    allReservationAvailabilityList.forEach((availability) => {
      let dailyHours = [];  // store start time and end time from weekdaysTimeRepeat
      let start;
      let end;
      // make sure the weekdaysTimeRepeat time is length 4 (0830)

      // check if there is stop date and already pass the date
      if (availability.dateStopRepeat &&
        moment(dateSelectedTimestamp).startOf('day').isAfter(moment(availability.dateStopRepeat).startOf('day'))) {
        // console.log('after end date')
        return;
      }

      // push unique names only
      if (!tempAvailabilityNames.includes(availability.reservationName)) {
        tempAvailabilityNames.push(availability.reservationName);
      }

      if (availability.repeatShiftType === RESERVATIONS_SHIFT_TYPE.DOES_NOT_REPEAT) {
        // DOES NOT REPEAT

        // check if dateStartSingle is within this day
        if (moment(availability.dateStartSingle).isBetween(
          moment(dateSelectedTimestamp).startOf('day'),
          moment(dateSelectedTimestamp).endOf('day'))) {
          dailyHours = availability.weekdaysTimeRepeat.split('-');
          // start = moment(availability.dateStartSingle).startOf('day')
          //   .add(dailyHours[0].slice(0, 2), 'hours')
          //   .add(dailyHours[0].slice(2, 4), 'minutes');
          // end = moment(availability.dateStartSingle).startOf('day')
          //   .add(dailyHours[1].slice(0, 2), 'hours')
          //   .add(dailyHours[1].slice(2, 4), 'minutes');
          start = moment(availability.dateStartSingle).startOf('day').set({
            hour: dailyHours[0].slice(0, 2),
            minute: dailyHours[0].slice(2, 4),
          });
          end = moment(availability.dateStartSingle).startOf('day').set({
            hour: dailyHours[1].slice(0, 2),
            minute: dailyHours[1].slice(2, 4),
          });

          tempAvailabilityList.push(...getAllAvailableTimeSlot(start, end, availability, userReservations, partyAmount, outletTable,))
        }
      } else if (availability.repeatShiftType === RESERVATIONS_SHIFT_TYPE.DAILY) {
        // DAILY

        dailyHours = availability.weekdaysTimeRepeat.split('-');
        // start = moment(dateSelectedTimestamp).startOf('day')
        //   .add(dailyHours[0].slice(0, 2), 'hours')
        //   .add(dailyHours[0].slice(2, 4), 'minutes');
        // end = moment(dateSelectedTimestamp).startOf('day')
        //   .add(dailyHours[1].slice(0, 2), 'hours')
        //   .add(dailyHours[1].slice(2, 4), 'minutes');
        start = moment(dateSelectedTimestamp).startOf('day').set({
          hour: dailyHours[0].slice(0, 2),
          minute: dailyHours[0].slice(2, 4),
        });
        end = moment(dateSelectedTimestamp).startOf('day').set({
          hour: dailyHours[1].slice(0, 2),
          minute: dailyHours[1].slice(2, 4),
        });

        tempAvailabilityList.push(...getAllAvailableTimeSlot(start, end, availability, userReservations, partyAmount, outletTable,))
      } else if (availability.repeatShiftType === RESERVATIONS_SHIFT_TYPE.WEEKLY) {
        // WEEKLY

        let timeLapse = 0;
        // repeatEveryShiftPeriod to check if it intersects the calendar week
        while (true) {
          const repeatedWeekDate = moment(availability.dateStartSingle)
            .isoWeekday(1)
            .add(availability.repeatEveryShiftPeriod * timeLapse, 'weeks')

          // if past calendar week range then break
          if (moment(repeatedWeekDate).isAfter(moment(dateSelectedTimestamp).endOf('isoweek'))) {
            break;
          }

          // check if it is in the calendar week range
          if (moment(repeatedWeekDate).isBetween(
            moment(dateSelectedTimestamp).startOf('isoweek'),
            moment(dateSelectedTimestamp).endOf('isoweek'))) {

            // specify the start day of weekday (Mon is 0)
            const day = moment(dateSelectedTimestamp).isoWeekday() - 1;

            // check which day is true to repeat [mon,tue,wed,thu,fri,sat,sun]
            if (availability.repeatOnWeekdays[day] === true) {
              if (availability.isWeekdaysTimeSame) {
                // repeating day is same time (weekdaysTimeRepeat)
                dailyHours = availability.weekdaysTimeRepeat.split('-');
                // start = moment(dateSelectedTimestamp)
                //   .add(dailyHours[0].slice(0, 2), 'hours')
                //   .add(dailyHours[0].slice(2, 4), 'minutes');
                // end = moment(dateSelectedTimestamp)
                //   .add(dailyHours[1].slice(0, 2), 'hours')
                //   .add(dailyHours[1].slice(2, 4), 'minutes');
                start = moment(dateSelectedTimestamp).startOf('day').set({
                  hour: dailyHours[0].slice(0, 2),
                  minute: dailyHours[0].slice(2, 4),
                });
                end = moment(dateSelectedTimestamp).startOf('day').set({
                  hour: dailyHours[1].slice(0, 2),
                  minute: dailyHours[1].slice(2, 4),
                });

                tempAvailabilityList.push(...getAllAvailableTimeSlot(start, end, availability, userReservations, partyAmount, outletTable,))
              } else {
                // repeating day is different time
                // use weekdaysTime and find the time for current day
                dailyHours = availability.weekdaysTime[day].split('-');
                // start = moment(dateSelectedTimestamp)
                //   .add(dailyHours[0].slice(0, 2), 'hours')
                //   .add(dailyHours[0].slice(2, 4), 'minutes');
                // end = moment(dateSelectedTimestamp)
                //   .add(dailyHours[1].slice(0, 2), 'hours')
                //   .add(dailyHours[1].slice(2, 4), 'minutes');
                start = moment(dateSelectedTimestamp).startOf('day').set({
                  hour: dailyHours[0].slice(0, 2),
                  minute: dailyHours[0].slice(2, 4),
                });
                end = moment(dateSelectedTimestamp).startOf('day').set({
                  hour: dailyHours[1].slice(0, 2),
                  minute: dailyHours[1].slice(2, 4),
                });

                tempAvailabilityList.push(...getAllAvailableTimeSlot(start, end, availability, userReservations, partyAmount, outletTable,))
              }
            }
            return;
          }

          timeLapse++;
          if (timeLapse > 100) {
            // console.log('%cdangerous INFINITE LOOP return ########################',
            // "color: blue; font-family:monospace; font-size: 20px")
            break;
          }
          if (availability.repeatEveryShiftPeriod === 0) {
            break;
          }
        }
      }
    });

    // sort the list by start time
    tempAvailabilityList = tempAvailabilityList.sort((a, b) => a.startTimeMilli - b.startTimeMilli);
    setReservationAvailabilityToday(tempAvailabilityList);

    // filter the reservation names that has time slot
    tempAvailabilityNames = tempAvailabilityNames.filter((name) => {
      let isAvailable = false;
      tempAvailabilityList.forEach((availability) => {
        if (isAvailable) return;
        if (availability.reservationName === name) {
          isAvailable = true;
        }
      });
      return isAvailable;
    });

    // sort according to the starting time
    tempAvailabilityNames = tempAvailabilityNames.sort((a, b) => {
      let aStartTime = 0;
      let bStartTime = 0;
      tempAvailabilityList.forEach((availability) => {
        if (availability.reservationName === a) {
          // get the smallest start time
          if (aStartTime === 0) {
            aStartTime = availability.startTimeMilli;
          } else if (availability.startTimeMilli < aStartTime) {
            aStartTime = availability.startTimeMilli;
          }
        }
        if (availability.reservationName === b) {
          // get the smallest start time
          if (bStartTime === 0) {
            bStartTime = availability.startTimeMilli;
          } else if (availability.startTimeMilli < bStartTime) {
            bStartTime = availability.startTimeMilli;
          }
        }
      });
      return aStartTime - bStartTime;
    });
    setReservationAvailabilityTodayNames(tempAvailabilityNames);

    // display all the reservation
    setSelectedReservationName('All Day');

    // // console.log('tempList', tempAvailabilityList);
    // console.table(tempAvailabilityNames);
  }, [dateSelectedTimestamp, allReservationAvailabilityList, partyAmount, userReservations, outletTable]);

  // when selected reservation name changed, filter the time slot available to display
  useEffect(() => {
    if (selectedReservationName === 'All Day') {
      setReservationAvailabilityFiltered(reservationAvailabilityToday);
    } else {
      let tempAvailabilityList = [];

      reservationAvailabilityToday.forEach((item, index) => {
        if (item.reservationName === selectedReservationName) {
          tempAvailabilityList.push(item);
        }
      });

      setReservationAvailabilityFiltered(tempAvailabilityList);
    }
  }, [reservationAvailabilityToday, selectedReservationName]);

  // when selected time slot changed or party amount changed, chose table
  useEffect(() => {
    let seatSizeDiff = 9999;
    let bestTable = null;
    let isBestAlreadyFound = false;

    // get the table list
    outletTable.forEach((table) => {
      // check if the table is already reserve at that time
      let isReserved = false;

      // if already found a best fit (0 seat difference) then skip
      if (isBestAlreadyFound) {
        return;
      }

      userReservations.forEach((userReservation) => {
        if (userReservation.tableId &&
          userReservation.status === USER_RESERVATION_STATUS.ACCEPTED &&
          userReservation.tableId === table.uniqueId) {

          // check if there reservation at that time
          if (moment(userReservation.reservationTime).isSame(
            moment(timeSelectedMilli))) {
            isReserved = true;
          }

          // check if there is a table turn time clashed
          if (moment(timeSelectedMilli).isBetween(
            moment(userReservation.reservationTime),
            moment(userReservation.reservationTime).add(table.turnTime || 60, 'minutes'),
            'minutes', '[]')) {
            isReserved = true;
          }
        }
      });

      // not reserved, get the best suited table
      if (!isReserved) {
        let maxPax = table.capacity || 9999;
        let minPax = table.paxMin || 1;

        // able to fit and more than minimum pax
        if (table.capacity >= (isNaN(+partyAmount) ? 10 : +partyAmount) &&
          maxPax >= (isNaN(+partyAmount) ? 10 : +partyAmount) &&
          minPax <= (isNaN(+partyAmount) ? 10 : +partyAmount)) {

          // found better table
          if (table.capacity - partyAmount < seatSizeDiff) {
            seatSizeDiff = table.capacity - partyAmount;
            bestTable = table;

            // check for the best fit
            if (seatSizeDiff === 0) {
              isBestAlreadyFound = true;
            }
          }
        }
      }
    });

    if (bestTable == null) return;

    CommonStore.update((s) => {
      s.selectedReservationTableId = bestTable.uniqueId;
      s.selectedReservationTableCode = bestTable.code;
    });
  }, [reservationAvailabilityToday, timeSelectedMilli, partyAmount, userReservations, outletTable]);

  useEffect(() => {
    const newRevDateTime = moment(
      `${moment(reservationDate).format('DD MMM YYYY')} ${moment(
        reservationTime,
      ).format('hh:mm A')}`,
      'DD MMM YYYY hh:mm A',
    ).valueOf();

    setReservationIntervalStartTime(
      moment(newRevDateTime).subtract(reservationIntervalMin, 'minute'),
    );
    setReservationIntervalEndTime(
      moment(newRevDateTime).add(reservationIntervalMin, 'minute'),
    );
  }, [reservationIntervalMin, reservationDate, reservationTime]);

  useEffect(() => {
    if (
      currOutlet &&
      currOutlet.reservationIntervalHour &&
      currOutlet.reservationIntervalMin
    ) {
      setReservationIntervalMin(
        currOutlet.reservationIntervalHour * 60 +
        currOutlet.reservationIntervalMin,
      );
    } else {
      setReservationIntervalMin(120);
    }
  }, [currOutlet]);

  useEffect(() => {
    if (selectedOutletTableId === '' && outletTables.length > 0) {
      const outletTablesFiltered = outletTables.filter((table) => {
        if (table.seated <= 0) {
          return true;
        } else {
          return false;
        }
      });

      if (outletTablesFiltered.length > 0) {
        setSelectedOutletTableId(outletTablesFiltered[0].uniqueId);
      }
    }
  }, [outletTables, selectedOutletTableId]);

  const acceptReservation = (param) => {
    const foundTable = outletTables.find(
      (table) => table.uniqueId === param.tableId,
    );

    var body = {
      reservationId: param.uniqueId,
      tableId: foundTable ? foundTable.uniqueId : '',
      tableCode: foundTable ? foundTable.code : '',

      userId: param.userId,
      pax: param.pax,
      reservationTime: param.reservationTime,
      outletName: currOutlet.name,
    };

    (
      APILocal.acceptReservation({ body })
      // netInfo.isInternetReachable && netInfo.isConnected
      // ? ApiClient.POST(API.acceptReservation, body)
      // : APILocal.acceptReservation({ body })
    ).then((result) => {
      if (result.status) {
        window.confirm(
          "Success, Reservation has been accepted."
        );
      }
    });
  };
  // 2023-06-14 - New Changes
  // for this will seated, will actual 'seat' the table
  // and also link the order to the table
  const seatedReservation = (param) => {
    const foundTable = outletTables.find(
      (table) => table.uniqueId === param.tableId,
    );

    if (param.tableId && foundTable) {
      var body = {
        reservationId: param.uniqueId,
        // seated: 1,
        tableCode: foundTable.code,
        tableId: param.tableId,

        tablePax: param.pax,

        orderId: param.orderId ? param.orderId : '',
        orderIdList: param.orderIdList ? param.orderIdList : [],
      };

      // ApiClient.POST(API.seatedReservation, body, false)
      APILocal.seatedReservation({ body })
        .then((result) => {
          if (result.status) {
            // getReservationList()
            window.confirm(
              "Success, Reservation has been seated. (Order will be placed to the table as well)"
            );

            setTableModalVisibility(false);
          }
        })
        .catch((err) => {
          // console.log(err);
        });
    }
    else {
      window.confirm(
        "Info, Please assign a table to the reservation first."
      );
    }
  };

  const PhoneonNumPadBtn = (key) => {
    var plus = customerPhone.split('+')[1];
    if (key >= 0 || key == '+') {
      var phoneLength = 12;
      if (customerPhone.startsWith('+6011')) {
        phoneLength = 13;
      }

      if (customerPhone.includes('+'))
        if (customerPhone.length < phoneLength && plus.length < phoneLength)
          setCustomerPhone(customerPhone + key);
      if (!customerPhone.includes('+')) {
        if (customerPhone.length < phoneLength) setCustomerPhone(customerPhone + key);
      }
    } else if (customerPhone.length > 0)
      setCustomerPhone(customerPhone.slice(0, key));
  };

  useEffect(() => {
    if (selectedOutletSection && selectedOutletSection.uniqueId === undefined &&
      outletSections.length > 0) {
      CommonStore.update(s => {
        s.selectedOutletSection = outletSections[0];
      });
    }
  }, [outletSections]);

  useEffect(() => {
    if (selectedOutletSection.uniqueId) {
      var filteredOutletTablesTemp = outletTables.filter(
        (table) => table.outletSectionId === selectedOutletSection.uniqueId,
      );

      filteredOutletTablesTemp.sort((a, b) => {
        if (a.joinedTables && b.joinedTables) {
          return b.joinedTables.length - a.joinedTables.length;
        } else if (a.joinedTables && !b.joinedTables) {
          return -1;
        } else if (!a.joinedTables && b.joinedTables) {
          return 1;
        } else if (!a.joinedTables && !b.joinedTables) {
          return 0;
        }
      });

      filteredOutletTablesTemp.sort((a, b) => {
        return (
          (a.orderIndex
              ? a.orderIndex
              : filteredOutletTablesTemp.length) -
          (b.orderIndex
              ? b.orderIndex
              : filteredOutletTablesTemp.length)
        );
      });

      const MAX_COL_NUM = 6;

      var filteredOutletTablesTempFirstPass = [];

      if (filteredOutletTablesTemp.length > 0) {
        filteredOutletTablesTempFirstPass = [filteredOutletTablesTemp[0]];

        if (filteredOutletTablesTemp.length > 1) {
          var colRemaining = MAX_COL_NUM;
          if (filteredOutletTablesTemp[0].joinedTables) {
            colRemaining -= filteredOutletTablesTemp[0].joinedTables.length;
          } else {
            colRemaining -= 1;
          }

          for (var i = 1; i < filteredOutletTablesTemp.length; i++) {
            const currCol = i % MAX_COL_NUM;

            const currTable = filteredOutletTablesTemp[i];

            // console.log(currTable);

            if (colRemaining <= 0) {
              // start with new row

              colRemaining = MAX_COL_NUM;

              filteredOutletTablesTempFirstPass.push(currTable);
            } else if (colRemaining > 0) {
              if (!currTable.joinedTables) {
                // squeeze unjoined table

                filteredOutletTablesTempFirstPass.push(currTable);
              } else if (
                currTable.joinedTables &&
                currTable.joinedTables.length <= colRemaining
              ) {
                // squeeze remaining tables

                filteredOutletTablesTempFirstPass.push(currTable);
              } else {
                for (var j = 0; j < colRemaining; j++) {
                  filteredOutletTablesTempFirstPass.push({
                    emptyLayout: true,
                  });
                }

                colRemaining = MAX_COL_NUM;

                filteredOutletTablesTempFirstPass.push(currTable);
              }
            }

            if (currTable.joinedTables) {
              colRemaining -= currTable.joinedTables.length;
            } else {
              colRemaining -= 1;
            }
          }
        }
      }

      setFilteredOutletTables(filteredOutletTablesTemp);
      setFilteredOutletTablesForRendered(filteredOutletTablesTempFirstPass);
    }
    if (selectedOutletTable && selectedOutletTable.uniqueId) {
      CommonStore.update((s) => {
        s.selectedOutletTable =
          outletTables.find(
            (table) => table.uniqueId === selectedOutletTable.uniqueId,
          ) || {};
      });
    }
  }, [selectedOutletSection, outletTables, userOrdersTableDict]);

  //toggle reservation status
  useEffect(() => {
    var tempArr = [];
    console.log('userReservations', userReservations)
    if (userReservations) {
      for (let i = 0; i < userReservations.length; i++) {
        if (
          moment(userReservations[i].reservationTime).format('YYYY-MM-DD') ===
          selectedCalendarData
          // moment(userReservations[i].reservationTime).isSameOrBefore(selectedCalendarData, 'day')
        ) {
          tempArr.push(userReservations[i]);
        }
      }
    }
    console.log('userReservations', userReservations)

    var tempResArr = [];
    //var tempFinishedArr = [];
    //var tempWaitArr = [];

    if (tempArr) {
      tempArr.forEach((item) => {
        if (selectedReservationStatus === USER_RESERVATION_STATUS.ALL) {
          if (
            item.status === USER_RESERVATION_STATUS.ACCEPTED ||
            item.status === USER_RESERVATION_STATUS.SEATED ||
            item.status === USER_RESERVATION_STATUS.CANCELED ||
            item.status === USER_RESERVATION_STATUS.NO_SHOW ||
            item.status === USER_RESERVATION_STATUS.PENDING
          ) {
            tempResArr.push(item);
          }
        } else if (item.status === selectedReservationStatus) {
          tempResArr.push(item);
        }
      });

      // setReservationsArr(tempResArr);
    }

    // reservations filter start

    let filteredUserReservationsTemp = tempResArr;

    // section filter
    if (Object.keys(roomChecked).length > 0) {
      // console.log('room check!');
      var roomTemp = [];
      var roomFilter = Object.entries(roomChecked).map(([key, value]) => {
        if (value) {
          return key;
        }
      });

      roomFilter
        .filter((room) => room !== undefined)
        .forEach((room) => {
          filteredUserReservationsTemp.forEach((item) => {
            outletTables.forEach((table) => {
              if (
                item.tableId === table.uniqueId &&
                table.outletSectionId === room
              ) {
                roomTemp.push(item);
              }
            });
          });
        });

      setIsFiltered(true);
      filteredUserReservationsTemp = roomTemp;
    }

    if (Object.keys(partyChecked).length > 0) {
      var partyTemp = [];
      var partyFilter = Object.entries(partyChecked).map(([key, value]) => {
        if (value) {
          return key;
        }
      });

      partyFilter
        .filter((party) => party !== undefined)
        .forEach((party) => {
          filteredUserReservationsTemp.forEach((item) => {
            if (party === '0') {
              if (item.pax > 0 && item.pax <= 6) {
                partyTemp.push(item);
              }
            } else if (party === '1') {
              if (item.pax > 6 && item.pax <= 12) {
                partyTemp.push(item);
              }
            } else if (party === '2') {
              if (item.pax > 12 && item.pax <= 18) {
                partyTemp.push(item);
              }
            } else if (party === '3') {
              if (item.pax > 18 && item.pax <= 24) {
                partyTemp.push(item);
              }
            } else if (party === '4') {
              if (item.pax > 24 && item.pax <= 30) {
                partyTemp.push(item);
              }
            } else if (party === '5') {
              if (item.pax > 30 && item.pax <= 36) {
                partyTemp.push(item);
              }
            }
          });
        });

      setIsFiltered(true);
      filteredUserReservationsTemp = partyTemp;
    }

    if (filterTags) {
      setIsFiltered(true);
      filteredUserReservationsTemp = filteredUserReservationsTemp.filter(
        (item) => { return item.rsvTags || item.guestTags }
      );
    }

    if (filterNotes) {
      setIsFiltered(true);
      filteredUserReservationsTemp = filteredUserReservationsTemp.filter(
        (item) => { return item.remarks || item.guestNotes }
      );
    }

    filteredUserReservationsTemp.sort((a, b) => b.reservationTime - a.reservationTime);

    setReservationsArr(filteredUserReservationsTemp);

    // reservation filter end
  }, [
    selectedCalendarData,
    userReservations,
    selectedReservationStatus,
    roomChecked,
    partyChecked,
    filterTags,
    filterNotes,
  ]);

  // reset to empty if user untick all
  useEffect(() => {
    if (Object.keys(roomChecked).length > 0) {
      var count = 0;
      var { length } = Object.keys(roomChecked);
      var roomFilter = Object.entries(roomChecked).map(([key, value]) => {
        if (value === false) {
          count++;
        }
      });

      if (count === length) {
        setRoomChecked({});
        setIsFiltered(false);
      }
    }

    if (Object.keys(partyChecked).length > 0) {
      var count = 0;
      var { length } = Object.keys(partyChecked);
      var partyFilter = Object.entries(partyChecked).map(([key, value]) => {
        if (value === false) {
          count++;
        }
      });

      if (count === length) {
        setPartyChecked({});
        setIsFiltered(false);
      }
    }
  }, [roomChecked, partyChecked]);

  /* useEffect(() => {
    var tempArr = [];
    // console.log('userReservations', userReservations)
    if (userReservations) {
      for (let i = 0; i < userReservations.length; i++) {
        if (
          moment(userReservations[i].reservationTime).format('YYYY-MM-DD') ===
          selectedCalendarData
        ) {
          tempArr.push(userReservations[i]);
        }
      }
    }

    var tempResArr = [];
    var tempFinishedArr = [];
    var tempWaitArr = [];

    if (tempArr) {
      tempArr.forEach((item) => {
        if (item.status === USER_RESERVATION_STATUS.ACCEPTED ||
          item.status === USER_RESERVATION_STATUS.SEATED ||
          item.status === USER_RESERVATION_STATUS.CANCELED ||
          item.status === USER_RESERVATION_STATUS.NO_SHOW ||
          item.status === USER_RESERVATION_STATUS.PENDING) {
          tempResArr.push(item);
        }
        if (
          item.status === USER_RESERVATION_STATUS.SEATED ||
          item.status === USER_RESERVATION_STATUS.CANCELED ||
          item.status === USER_RESERVATION_STATUS.NO_SHOW
        ) {
          tempFinishedArr.push(item);
        }
        if (item.status === USER_RESERVATION_STATUS.PENDING) {
          tempWaitArr.push(item);
        }
      });
    }

    setReservationsArr(tempResArr);
    setFinishedArr(tempFinishedArr);
    setWaitListArr(tempWaitArr);
    // console.log('refreshed');
  }, [selectedCalendarData, userReservations]); */

  useEffect(() => {
    var count = 0;
    outletSections.forEach((item, index) => {
      if (roomChecked[item.uniqueId]) {
        count += 1;
      }
    });
    var countParty = 0;
    partySizeArr.forEach((item, index) => {
      if (partyChecked[item.value]) {
        countParty += 1;
      }
    });
    setRoomCount(count);
    setPartyCount(countParty);
  }, [roomChecked, partyChecked]);

  useEffect(() => {
    const dateTemp1 = `${selectedResYear}-${selectedResMonth}-${selectedResDay}`;
    const formattedDate = moment(dateTemp1).format('DD MMM YYYY');
    setDateSelectedTimestamp(formattedDate);
  }, [selectedResYear, selectedResMonth, selectedResYear]);

  useEffect(() => {
    const filteredOutletTablesReservationTemp = filteredOutletTables.map(table => {
      const dateStr =
        `${selectedResYear} ${selectedResMonth} ${selectedResDay}`;

      const reservationTimeParsed = moment(
        `${dateStr} ${moment(reservationTime).format('HH:mm')}`, 'YYYY MMM DD HH:mm').valueOf();

      const availabilityStartTime = reservationTimeParsed;
      const availabilityEndTime = moment(reservationTimeParsed).add(2, 'hour').valueOf();

      // const selectedYearMonthDay = moment(`${selectedResYear} ${selectedResMonth} ${selectedResDay}`, 'YYYY MMM DD')

      // var availabilityStartTime = moment().set({
      //   year: moment(selectedYearMonthDay).year(),
      //   month: moment(selectedYearMonthDay).month(),
      //   day: moment(selectedYearMonthDay).day(),
      //   hour: moment(reservationAvailability.startTime, 'hh:mm A').hour(),
      //   minute: moment(reservationAvailability.startTime, 'hh:mm A').minute(),
      // });
      // var availabilityEndTime = moment().set({
      //   year: moment(selectedYearMonthDay).year(),
      //   month: moment(selectedYearMonthDay).month(),
      //   day: moment(selectedYearMonthDay).day(),
      //   hour: moment(reservationAvailability.endTime, 'hh:mm A').hour(),
      //   minute: moment(reservationAvailability.endTime, 'hh:mm A').minute(),
      // });

      var availabilityTurnTimeMinute = reservationAvailability.intervalMin;
      // var tableTurnTimeMinute = table.turnTime;

      var isValidToBook = true;
      var bookedReservation = null;

      for (var i = 0; i < userReservations.length; i++) {
        // if (userReservations[i].tableId === table.uniqueId) {
        //   console.log('reservation check');

        //   console.log(userReservations[i]);
        //   console.log(availabilityTurnTimeMinute);

        //   var reservationStartTime = userReservations[i].reservationTime;
        //   var reservationEndTime = moment(userReservations[i].reservationTime)
        //     .add((availabilityTurnTimeMinute), 'minute');

        //   console.log(reservationStartTime);
        //   console.log(reservationEndTime);
        //   console.log(availabilityStartTime);
        //   console.log(availabilityEndTime);
        //   console.log(moment(reservationStartTime).format('YYYY MM DD HH:mm'));
        //   console.log(moment(reservationEndTime).format('YYYY MM DD HH:mm'));
        //   console.log(moment(availabilityStartTime).format('YYYY MM DD HH:mm'));
        //   console.log(moment(availabilityEndTime).format('YYYY MM DD HH:mm'));

        //   console.log(userReservations[i].tableId === table.uniqueId);
        //   console.log(moment(availabilityStartTime).isSameOrBefore(reservationEndTime));
        //   console.log(moment(reservationStartTime).isSameOrBefore(availabilityEndTime));
        // }

        if (userReservations[i].uniqueId === reservationId) {
          // same one, just skip

          console.log('same reservation, skip');

          continue;
        }

        var reservationStartTime = userReservations[i].reservationTime;
        var reservationEndTime = moment(userReservations[i].reservationTime)
          .add((120), 'minute');

        if (
          (
            userReservations[i].tableId === table.uniqueId
          )
        ) {
          console.log('same table');
          console.log(moment(reservationStartTime).format('YYYY MM DD HH:mm'));
          console.log(moment(reservationEndTime).format('YYYY MM DD HH:mm'));
          console.log(moment(availabilityStartTime).format('YYYY MM DD HH:mm'));
          console.log(moment(availabilityEndTime).format('YYYY MM DD HH:mm'));

          if (
            moment(availabilityStartTime).isSameOrBefore(reservationEndTime) &&
            moment(reservationStartTime).isSameOrBefore(availabilityEndTime)
          ) {
            // means overlapped

            console.log('not valid to book');

            isValidToBook = false;

            bookedReservation = userReservations[i];

            break;
          }
        }
        else {
          if (table.seated > 0) {
            // means if seated

            var tableSeatStartTime = moment();
            var tableSeatEndTime = moment()
              .add((120), 'minute');

            // console.log('table seat info');
            // console.log(table.code);
            // console.log(moment(tableSeatStartTime).format('YYYY/MM/DD HH:mm'));
            // console.log(moment(tableSeatEndTime).format('YYYY/MM/DD HH:mm'));
            // console.log(moment(availabilityStartTime).format('YYYY/MM/DD HH:mm'));
            // console.log(moment(availabilityEndTime).format('YYYY/MM/DD HH:mm'));

            if (
              (
                moment(availabilityStartTime).isSameOrBefore(tableSeatEndTime) &&
                moment(tableSeatStartTime).isSameOrBefore(availabilityEndTime)
              )
            ) {
              // means overlapped
              isValidToBook = false;

              break;
            }
            else {
              continue;
            }
          }

          continue;
        }
      }

      return {
        ...table,
        isValidToBook, // use this to change color/behaviour (block user booking, etc)
        bookedReservation,
      };
    });

    setFilteredOutletTablesReservation(filteredOutletTablesReservationTemp);
  }, [
    filteredOutletTables,
    userReservations,

    selectedResDay,
    selectedResMonth,
    selectedResYear,

    reservationTime,

    reservationId,
  ]);

  // useEffect(() => {
  //   const timeTemp1 = `${selectedHour}:${selectedMin}`;
  //   setTimeSelected(moment(timeTemp1, ["h:mm A"]).format('hh:mm A'));
  // }), [selectedHour, selectedMin]

  const renderGuestTags = ({ item, index }) => {
    return (
      <View
        style={{
          backgroundColor: 'blue',
          borderRadius: 5,
          marginBottom: 5,
          width: windowWidth * 0.07,
          height: windowHeight * 0.04,
          justifyContent: 'center',
          alignItems: 'center',
          alignSelf: 'center',
        }}>
        <Text style={[styles.table, { color: Colors.whiteColor }]}>
          {`${item.tag}`}
        </Text>
      </View>
    );
  };
  const noShowReservation = (param) => {
    var body = {
      reservationId: param,
      // seated: 1,
    };

    // ApiClient.POST(API.noShowReservation, body, false)
    APILocal.noShowReservation({ body })
      .then((result) => {
        if (result.status) {
          // getReservationList()
          window.confirm(
            "Success, Reservation has been updated"
          );
        }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const cancelReservation = (param) => {
    var body = {
      reservationId: param.uniqueId,
      // seated: 1,

      userId: param.userId,
      pax: param.pax,
      reservationTime: param.reservationTime,
      outletName: currOutlet.name,
    };

    (netInfo.isInternetReachable && netInfo.isConnected
      ? ApiClient.POST(API.cancelReservation, body, false)
      : APILocal.cancelReservation({ body })
    )
      .then((result) => {
        if (result.status) {
          // getReservationList()
          window.confirm(
            "Success, Reservation has been cancelled"
          );
        }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  const undoReservation = (param) => {
    var body = {
      reservationId: param,
      // seated: 1,
    };

    // ApiClient.POST(API.undoReservation, body, false)
    APILocal.undoReservation({ body })
      .then((result) => {
        if (result.status) {
          // getReservationList()
          window.confirm(
            "Success, Reservation has been undone"
          );
        }
      })
      .catch((err) => {
        // console.log(err);
      });
  };

  /* const renderReservations = ({ item, index }) => {
    var swipeBtns = [];

    if (item.status === USER_RESERVATION_STATUS.SEATED) {
      swipeBtns.push(
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Undo{' '}
              </Text>
              {switchMerchant ? (
                <Feather
                  name="rotate-ccw"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              ) : (
                <Feather
                  name="rotate-ccw"
                  size={30}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.tabCyan,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            Alert.alert('Alert', 'Do you want to undo this reservation?', [
              {
                text: 'YES',
                onPress: () => {
                  // setStatusReject(true);

                  undoReservation(item.uniqueId);
                },
              },
              {
                text: 'NO',
                onPress: () => { },
              },
            ]);
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Call{' '}
              </Text>
              {switchMerchant ? (
                <Feather
                  name="phone-call"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              ) : (
                <Feather
                  name="phone-call"
                  size={30}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.primaryColor,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            Linking.openURL(`tel:${item.userPhone}`);
            //   deleteNote(rowData);
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                No-Show
              </Text>
              {switchMerchant ? (
                <Feather
                  name="alert-triangle"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              ) : (
                <Feather
                  name="alert-triangle"
                  size={30}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.tabGrey,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            //   deleteNote(rowData);
            // setStatusNoShow(true)

            noShowReservation(item.uniqueId);
          },
        },
      );
    } else if (item.status === USER_RESERVATION_STATUS.PENDING) {
      swipeBtns.push(
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Call{' '}
              </Text>
              {switchMerchant ? (
                <Feather
                  name="phone-call"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              ) : (
                <Feather
                  name="phone-call"
                  size={30}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.primaryColor,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            Linking.openURL(`tel:${item.userPhone}`);
            //   deleteNote(rowData);
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                No-Show
              </Text>
              {switchMerchant ? (
                <Feather
                  name="alert-triangle"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              ) : (
                <Feather
                  name="alert-triangle"
                  size={30}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.tabGrey,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            //   deleteNote(rowData);
            // setStatusNoShow(true)

            noShowReservation(item.uniqueId);
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                {' '}
                {item.status === USER_RESERVATION_STATUS.ACCEPTED
                  ? 'Seated'
                  : 'Accept'}{' '}
              </Text>
              {item.status === USER_RESERVATION_STATUS.ACCEPTED ? (
                // <AntDesign name="checkcircleo" size={30} color={Colors.whiteColor} style={{ marginTop: 5 }} />
                <>
                  {switchMerchant ? (
                    <MaterialCommunityIcons
                      name="seat-outline"
                      size={13}
                      color={Colors.whiteColor}
                    />
                  ) : (
                    <MaterialCommunityIcons
                      name="seat-outline"
                      size={36}
                      color={Colors.whiteColor}
                    />
                  )}
                </>
              ) : (
                <>
                  {switchMerchant ? (
                    <Feather
                      name="user-check"
                      size={10}
                      color={Colors.whiteColor}
                      style={{ marginTop: 3 }}
                    />
                  ) : (
                    <Feather
                      name="user-check"
                      size={31}
                      color={Colors.whiteColor}
                      style={{ marginTop: 3 }}
                    />
                  )}
                </>
              )}
            </View>
          ),
          backgroundColor: Colors.tabGold,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            if (
              item.status === USER_RESERVATION_STATUS.PENDING ||
              item.status === USER_RESERVATION_STATUS.NO_SHOW ||
              item.status === USER_RESERVATION_STATUS.CANCELED
            ) {
              // setSelectedReservation(item);
              // setTableModalVisibility(true);
              acceptReservation(item);
            } else {
              // acceptReservation(item);
              setSelectedReservation(item);
              setTableModalVisibility(true);
            }
            // if (item.status !== USER_RESERVATION_STATUS.SEATED) {
            //   acceptReservation(item);
            // } else {
            //   setSelectedReservation(item);
            //   setTableModalVisibility(true);
            // }
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Reject{' '}
              </Text>
              {switchMerchant ? (
                <Feather
                  name="user-x"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 3 }}
                />
              ) : (
                <Feather
                  name="user-x"
                  size={31}
                  color={Colors.whiteColor}
                  style={{ marginTop: 3 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.tabRed,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            Alert.alert('Alert', 'Do you want to reject this reservation?', [
              {
                text: 'YES',
                onPress: () => {
                  // setStatusReject(true);

                  cancelReservation(item);
                },
              },
              {
                text: 'NO',
                onPress: () => { },
              },
            ]);
          },
        },
      );
    } else if (item.status === USER_RESERVATION_STATUS.CANCELED) {
      swipeBtns.push(
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Undo{' '}
              </Text>
              {switchMerchant ? (
                <Feather
                  name="rotate-ccw"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              ) : (
                <Feather
                  name="rotate-ccw"
                  size={30}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.tabCyan,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            Alert.alert('Alert', 'Do you want to undo this reservation?', [
              {
                text: 'YES',
                onPress: () => {
                  // setStatusReject(true);

                  undoReservation(item.uniqueId);
                },
              },
              {
                text: 'NO',
                onPress: () => { },
              },
            ]);
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Call{' '}
              </Text>
              {switchMerchant ? (
                <Feather
                  name="phone-call"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              ) : (
                <Feather
                  name="phone-call"
                  size={30}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.primaryColor,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            Linking.openURL(`tel:${item.userPhone}`);
            //   deleteNote(rowData);
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                No-Show
              </Text>
              {switchMerchant ? (
                <Feather
                  name="alert-triangle"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              ) : (
                <Feather
                  name="alert-triangle"
                  size={30}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.tabGrey,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            //   deleteNote(rowData);
            // setStatusNoShow(true)

            noShowReservation(item.uniqueId);
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                {' '}
                {item.status === USER_RESERVATION_STATUS.ACCEPTED
                  ? 'Seated'
                  : 'Accept'}{' '}
              </Text>
              {item.status === USER_RESERVATION_STATUS.ACCEPTED ? (
                // <AntDesign name="checkcircleo" size={30} color={Colors.whiteColor} style={{ marginTop: 5 }} />
                <>
                  {switchMerchant ? (
                    <MaterialCommunityIcons
                      name="seat-outline"
                      size={13}
                      color={Colors.whiteColor}
                    />
                  ) : (
                    <MaterialCommunityIcons
                      name="seat-outline"
                      size={36}
                      color={Colors.whiteColor}
                    />
                  )}
                </>
              ) : (
                <>
                  {switchMerchant ? (
                    <Feather
                      name="user-check"
                      size={10}
                      color={Colors.whiteColor}
                      style={{ marginTop: 3 }}
                    />
                  ) : (
                    <Feather
                      name="user-check"
                      size={31}
                      color={Colors.whiteColor}
                      style={{ marginTop: 3 }}
                    />
                  )}
                </>
              )}
            </View>
          ),
          backgroundColor: Colors.tabGold,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            if (
              item.status === USER_RESERVATION_STATUS.PENDING ||
              item.status === USER_RESERVATION_STATUS.NO_SHOW ||
              item.status === USER_RESERVATION_STATUS.CANCELED
            ) {
              // setSelectedReservation(item);
              // setTableModalVisibility(true);
              acceptReservation(item);
            } else {
              // acceptReservation(item);
              setSelectedReservation(item);
              setTableModalVisibility(true);
            }
            // if (item.status !== USER_RESERVATION_STATUS.SEATED) {
            //   acceptReservation(item);
            // } else {
            //   setSelectedReservation(item);
            //   setTableModalVisibility(true);
            // }
          },
        },
      );
    } else if (item.status === USER_RESERVATION_STATUS.NO_SHOW) {
      swipeBtns.push(
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Undo{' '}
              </Text>
              {switchMerchant ? (
                <Feather
                  name="rotate-ccw"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              ) : (
                <Feather
                  name="rotate-ccw"
                  size={30}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.tabCyan,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            Alert.alert('Alert', 'Do you want to undo this reservation?', [
              {
                text: 'YES',
                onPress: () => {
                  // setStatusReject(true);

                  undoReservation(item.uniqueId);
                },
              },
              {
                text: 'NO',
                onPress: () => { },
              },
            ]);
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Call{' '}
              </Text>
              {switchMerchant ? (
                <Feather
                  name="phone-call"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              ) : (
                <Feather
                  name="phone-call"
                  size={30}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.primaryColor,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            Linking.openURL(`tel:${item.userPhone}`);
            //   deleteNote(rowData);
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                {' '}
                {item.status === USER_RESERVATION_STATUS.ACCEPTED
                  ? 'Seated'
                  : 'Accept'}{' '}
              </Text>
              {item.status === USER_RESERVATION_STATUS.ACCEPTED ? (
                // <AntDesign name="checkcircleo" size={30} color={Colors.whiteColor} style={{ marginTop: 5 }} />
                <>
                  {switchMerchant ? (
                    <MaterialCommunityIcons
                      name="seat-outline"
                      size={13}
                      color={Colors.whiteColor}
                    />
                  ) : (
                    <MaterialCommunityIcons
                      name="seat-outline"
                      size={36}
                      color={Colors.whiteColor}
                    />
                  )}
                </>
              ) : (
                <>
                  {switchMerchant ? (
                    <Feather
                      name="user-check"
                      size={10}
                      color={Colors.whiteColor}
                      style={{ marginTop: 3 }}
                    />
                  ) : (
                    <Feather
                      name="user-check"
                      size={31}
                      color={Colors.whiteColor}
                      style={{ marginTop: 3 }}
                    />
                  )}
                </>
              )}
            </View>
          ),
          backgroundColor: Colors.tabGold,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            if (
              item.status === USER_RESERVATION_STATUS.PENDING ||
              item.status === USER_RESERVATION_STATUS.NO_SHOW ||
              item.status === USER_RESERVATION_STATUS.CANCELED
            ) {
              // setSelectedReservation(item);
              // setTableModalVisibility(true);
              acceptReservation(item);
            } else {
              // acceptReservation(item);
              setSelectedReservation(item);
              setTableModalVisibility(true);
            }
            // if (item.status !== USER_RESERVATION_STATUS.SEATED) {
            //   acceptReservation(item);
            // } else {
            //   setSelectedReservation(item);
            //   setTableModalVisibility(true);
            // }
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Reject{' '}
              </Text>
              {switchMerchant ? (
                <Feather
                  name="user-x"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 3 }}
                />
              ) : (
                <Feather
                  name="user-x"
                  size={31}
                  color={Colors.whiteColor}
                  style={{ marginTop: 3 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.tabRed,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            Alert.alert('Alert', 'Do you want to reject this reservation?', [
              {
                text: 'YES',
                onPress: () => {
                  // setStatusReject(true);

                  cancelReservation(item);
                },
              },
              {
                text: 'NO',
                onPress: () => { },
              },
            ]);
          },
        },
      );
    } else {
      swipeBtns.push(
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Undo{' '}
              </Text>
              {switchMerchant ? (
                <Feather
                  name="rotate-ccw"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              ) : (
                <Feather
                  name="rotate-ccw"
                  size={30}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.tabCyan,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            Alert.alert('Alert', 'Do you want to undo this reservation?', [
              {
                text: 'YES',
                onPress: () => {
                  // setStatusReject(true);

                  undoReservation(item.uniqueId);
                },
              },
              {
                text: 'NO',
                onPress: () => { },
              },
            ]);
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Call{' '}
              </Text>
              {switchMerchant ? (
                <Feather
                  name="phone-call"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              ) : (
                <Feather
                  name="phone-call"
                  size={30}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.primaryColor,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            Linking.openURL(`tel:${item.userPhone}`);
            //   deleteNote(rowData);
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                No-Show
              </Text>
              {switchMerchant ? (
                <Feather
                  name="alert-triangle"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              ) : (
                <Feather
                  name="alert-triangle"
                  size={30}
                  color={Colors.whiteColor}
                  style={{ marginTop: 5 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.tabGrey,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            //   deleteNote(rowData);
            // setStatusNoShow(true)

            noShowReservation(item.uniqueId);
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                {' '}
                {item.status === USER_RESERVATION_STATUS.ACCEPTED
                  ? 'Seated'
                  : 'Accept'}{' '}
              </Text>
              {item.status === USER_RESERVATION_STATUS.ACCEPTED ? (
                // <AntDesign name="checkcircleo" size={30} color={Colors.whiteColor} style={{ marginTop: 5 }} />
                <>
                  {switchMerchant ? (
                    <MaterialCommunityIcons
                      name="seat-outline"
                      size={13}
                      color={Colors.whiteColor}
                    />
                  ) : (
                    <MaterialCommunityIcons
                      name="seat-outline"
                      size={36}
                      color={Colors.whiteColor}
                    />
                  )}
                </>
              ) : (
                <>
                  {switchMerchant ? (
                    <Feather
                      name="user-check"
                      size={10}
                      color={Colors.whiteColor}
                      style={{ marginTop: 3 }}
                    />
                  ) : (
                    <Feather
                      name="user-check"
                      size={31}
                      color={Colors.whiteColor}
                      style={{ marginTop: 3 }}
                    />
                  )}
                </>
              )}
            </View>
          ),
          backgroundColor: Colors.tabGold,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            if (
              item.status === USER_RESERVATION_STATUS.PENDING ||
              item.status === USER_RESERVATION_STATUS.NO_SHOW ||
              item.status === USER_RESERVATION_STATUS.CANCELED
            ) {
              // setSelectedReservation(item);
              // setTableModalVisibility(true);
              acceptReservation(item);
            } else {
              if (item.tableId != null) {
                setSelectedReservation(item);
                seatedReservation();
              }
              else {
                // acceptReservation(item);
                setSelectedReservation(item);
                setTableModalVisibility(true);
              }
            }
            // if (item.status !== USER_RESERVATION_STATUS.SEATED) {
            //   acceptReservation(item);
            // } else {
            //   setSelectedReservation(item);
            //   setTableModalVisibility(true);
            // }
          },
        },
        {
          component: (
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
                alignContent: 'center',
                flex: 1,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Bold', color: 'white' },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}>
                Reject{' '}
              </Text>
              {switchMerchant ? (
                <Feather
                  name="user-x"
                  size={10}
                  color={Colors.whiteColor}
                  style={{ marginTop: 3 }}
                />
              ) : (
                <Feather
                  name="user-x"
                  size={31}
                  color={Colors.whiteColor}
                  style={{ marginTop: 3 }}
                />
              )}
            </View>
          ),
          backgroundColor: Colors.tabRed,
          underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          onPress: () => {
            Alert.alert('Alert', 'Do you want to reject this reservation?', [
              {
                text: 'YES',
                onPress: () => {
                  // setStatusReject(true);

                  cancelReservation(item);
                },
              },
              {
                text: 'NO',
                onPress: () => { },
              },
            ]);
          },
        },
      );
    }
    var tempCrm = {};
    for (let j = 0; j < crmUsers.length; j++) {
      if (item.userId === crmUsers[j].uniqueId) {
        tempCrm = crmUsers[j];
        break;
      }
    }

    var userTagListTemp = [];
    if (crmUserTags.length > 0) {
      for (var i = 0; i < crmUserTags.length; i++) {
        if (
          crmUserTags[i].tokenFcmList.includes(tempCrm.tokenFcm || null) ||
          crmUserTags[i].emailList.includes(tempCrm.email) ||
          crmUserTags[i].phoneList.includes(tempCrm.number)
        ) {
          userTagListTemp.push(crmUserTags[i]);
        }
      }
    }

    var orderStatus = '';
    if (item.orderId) {
      // need find the order status using orderId 
      for (let i = 0; i < userOrders.length; i++) {
        if (item.orderId === userOrders[i].uniqueId) {
          orderStatus = userOrders[i].orderStatus;
          break;
        }
      }
    }

    return (
      <Swipeout
        right={swipeBtns}
        autoClose="true"
        buttonWidth={windowWidth * 0.13}
        style={{ marginTop: 10, borderRadius: 5, marginHorizontal: 15, marginBottom: 10 }}
      >
        <View style={{ backgroundColor: Colors.highlightColor }}>
          <View style={{ flexDirection: 'row' }}>
            <View style={styles.flatListHeader}>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.tableFirst}>{`${moment(
                  item.reservationTime,
                ).format('hh:mm A')}`}</Text>
              </View>
              <View style={{ flex: 0.8, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>
                  <Icon
                    name="md-person-outline"
                    size={15}
                    color={Colors.tabGrey}
                    style={{ marginLeft: 5 }}
                  />
                  {item.pax}
                </Text>
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                {/* <Text style={[styles.table]}>
                  <Icon
                    name="person-outline"
                    size={15}
                    color={Colors.tabGrey}
                    style={{
                      opacity: 0.6,
                      paddingLeft: 2,
                      marginLeft: 5,
                    }}
                  />
                  1
                </Text>
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>Order Status</Text>
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>RSV Notes</Text>
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>Guest Notes</Text>
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>RSV Tags</Text>
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>Guest Tags</Text>
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>Created</Text>
              </View>
            </View>
          </View>

          <TouchableOpacity
            onPress={() => {
              // props.navigation.navigate('GuestDetailsScreen');
              setAddReservationModal(true);
              setReservationCustomerName(item.userName);
              setReservationPhone(item.userPhone);
              setReservationDate(item.reservationTime);
              setReservationTime(item.reservationTime);
              setSeatingPax(item.pax);
              setSelectTable('existed');

              setReservationId(item.uniqueId);
              if (item.tableId) {
                CommonStore.update((s) => {
                  s.selectedOutletTable =
                    outletTables.find(
                      (table) => table.uniqueId === item.tableId,
                    ) || {};
                });
              }
              setReservationRemark(item.remarks);

            }}>
            <View style={{ flexDirection: 'column' }}>
              <View style={styles.flatListBody}>
                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                  <Text style={styles.tableFirst}>
                    {item.userName ? `${item.userName}\n\n${item.userPhone}` : `Temporary Guest\n\n${item.userPhone}`}
                  </Text>
                </View>
                <View style={{ flex: 0.8, justifyContent: 'flex-start' }}>
                  <Text style={styles.table}>
                    <Icon
                      name="md-person-outline"
                      size={15}
                      color={Colors.tabGrey}
                      style={{ marginLeft: 5 }}
                    />
                    {`${item.pax}`}
                  </Text>
                </View>
                <View
                  style={{
                    flex: 1,
                    justifyContent: 'flex-start',
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      borderRadius: 5,
                      backgroundColor: 'rgb(0, 200, 0)',
                      paddingRight: 3,
                      paddingVertical: 5,
                    }}>
                    <View
                      style={{
                        flex: 0.7,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <EvilIcons
                        name="check"
                        size={35}
                        color={Colors.whiteColor}
                        style={{
                          marginLeft: 3,
                        }}
                      />
                    </View>
                    <View
                      style={{
                        flex: 1.3,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <Text
                        style={{
                          textAlign: 'center',
                          color: Colors.whiteColor,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        {item.status}
                      </Text>
                    </View>
                  </View>
                </View>
                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                  <Text style={styles.table}>{`${orderStatus ? orderStatus : '-'
                    }`}</Text>
                </View>
                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                  <Text style={styles.table}>{`${item.rsvNotes ? item.rsvNotes : '-'
                    }`}</Text>
                </View>
                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                  <Text style={styles.table}>{`${item.guestNotes ? item.guestNotes : '-'
                    }`}</Text>
                </View>
                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                  <Text style={styles.table}>{`${item.rsvTags ? item.rsvTags : '-'
                    }`}</Text>
                </View>
                <View
                  style={{
                    flex: 1,
                    borderRightColor: Colors.lightGrey,
                    borderRightWidth: 1,
                    // overflow: 'hidden',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexWrap: 'wrap',
                    flexDirection: 'row',
                  }}>
                  <Text>{userTagListTemp.length > 0 ? '' : '-'}</Text>
                  {userTagListTemp.map((tagList, index) => {
                    return (
                      <Text
                        style={{
                          // fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          textAlign: 'center',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          color: Colors.primaryColor,
                          borderRadius: 5,
                          maxWidth: '80%',
                          margin: 3,
                        }}>
                        {`${tagList.name}`}
                      </Text>
                    );
                  })}
                </View>
                <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                  <Text style={styles.table}>
                    {`${moment(item.createdAt).format('DD/MM/YYYY')}`}
                  </Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </Swipeout>
    );
  }; */

  const createCRMUser = async (isAutoPush = false) => {
    if (
      // !customerAvatar ||
      // !customerName ||
      !customerPhone ||
      !customerName
      // !customerEmail
      // !customerAddress
    ) {
      window.confirm(
        "Error, Please fill in all required information:\nName\nContact number"
      );

    } else {
      ///////////////////////////////////

      var parsedPhone = customerPhone.startsWith('+') ? customerPhone.slice(1) : customerPhone;
      if (parsedPhone.length > 0 && !parsedPhone.startsWith('6')) {
        parsedPhone = `6${parsedPhone}`;
      }

      var sameUser = crmUsers.find(crmUser => {
        if (customerEmail && crmUser.email === customerEmail) {
          return true;
        }

        if (parsedPhone && crmUser.number === parsedPhone) {
          return true;
        }
      });

      if (sameUser) {
        window.confirm(
          "Info, Existing email and/or phone number found, please try another one."
        );
        return;
      }

      CommonStore.update((s) => {
        s.isLoading = true;
      });
      // upload image

      var profileImageImagePath = '';
      var profileImageCommonIdLocal = uuidv4();

      if (image && imageType) {
        // promotionCommonIdLocal = uuidv4();

        // profileImageImagePath = await uploadImageToFirebaseStorage(
        //   {
        //     uri: image,
        //     type: imageType,
        //   },
        //   `/merchant/${merchantId}/crm/user/${profileImageCommonIdLocal}/image${imageType}`,
        // );
      }

      // means new item

      var body = {
        merchantId,
        merchantName,
        outletId: currOutletId,

        avatar: profileImageImagePath,
        isImageChanged,
        dob: customerDob,
        email: customerEmail || '',
        gender: customerGender,
        name: customerName,
        number: parsedPhone,

        uniqueName: customerUsername || '',

        address: customerAddress,
        lat: parseFloat(customerAddressLat),
        lng: parseFloat(customerAddressLng),

        emailSecond: customerEmailSecond,
        numberSecond: customerPhoneSecond,
        addressSecond: customerAddressSecond,
        latSecond: parseFloat(customerAddressLatSecond),
        lngSecond: parseFloat(customerAddressLngSecond),

        timeline: {},

        commonId: profileImageCommonIdLocal,
      };

      // console.log(body, 'here' + isLoading);

      ApiClient.POST(API.createCRMUser, body, false)
        .then((result) => {
          if (result && result.status === 'success') {
            // console.log('Passed');
            if (window.confirm("Success, Customer has been added")) {
              setSearchCustomer(customerName);
              setShowAddCustomer(false);
            }
          } else {
            console.log('Failed');
          }
        })
        .catch((err) => {
          // console.log(err, 'here here');
        });
      CommonStore.update((s) => {
        s.isLoading = false;
      });
      // console.log('Create End');
    }
  };

  const renderTableLayout = ({ item, index }) => {
    var { bookedReservation } = item;
    // var bookedReservation = null;

    // var reservationDatePicked = dateSelectedTimestamp; // this is the date picked, rmb replace it
    // var reservationTimePicked = timeSelected; // this is the time picked, rmb replace it

    // var reservationDateTimeStart = moment(reservationDatePicked)
    //   .hour(moment(reservationTimePicked).hour())
    //   .minute(moment(reservationTimePicked).minute())
    //   .second(moment(reservationTimePicked).second).valueOf();

    // console.log('reservationDateTimeStart', reservationDateTimeStart)

    // var reservationDateTimeEnd = moment(reservationDateTimeStart).add(1, 'hour');

    // var currReservationDateTimeStart = moment().valueOf();
    // var currReservationDateTimeEnd = moment().add(1, 'hour');

    // var isAvailable = true;
    // for (var i = 0; i < reservationsArr.length; i++) {
    //   var itemReservationDateTimeStart = reservationsArr[i].reservationTime;
    //   var itemReservationDateTimeEnd = moment(reservationsArr[i].reservationTime).add(1, 'hour');

    //   if (
    //     reservationsArr[i].tableId === item.uniqueId
    //   ) {
    //     console.log('(moment(reservationDateTimeStart))',
    //       (moment(reservationDateTimeStart)))
    //     console.log('moment(itemReservationDateTimeEnd)',
    //       (moment(itemReservationDateTimeEnd)))

    //     if (
    //       (moment(reservationDateTimeStart).isSameOrBefore(moment(itemReservationDateTimeEnd))) &&
    //       (moment(itemReservationDateTimeStart).isSameOrBefore(moment(reservationDateTimeEnd)))
    //     ) {
    //       // means overlapped date time and table

    //       if (reservationsArr[i].status === 'CANCELED') {
    //         isAvailable = true;
    //       }

    //       bookedReservation = reservationsArr[i];

    //       isAvailable = false;
    //       break;
    //     }
    //     else if (
    //       item.seated > 0 &&
    //       (moment(reservationDateTimeStart).isSameOrBefore(moment(currReservationDateTimeEnd))) &&
    //       (moment(currReservationDateTimeStart).isSameOrBefore(moment(reservationDateTimeEnd)))
    //     ) {
    //       isAvailable = false;
    //       break;
    //     }
    //     else {
    //       isAvailable = true;
    //     }
    //   }
    // }

    if (
      item.isValidToBook
      // item.code != null && item.capacity != null && item.seated == 0 &&
      // isAvailable
      // item.isValidToBook &&
      // item.seated <= 0 &&
      // item.code != null
    ) {
      // not seated tables
      return (
        // <Modal style={{ flex: 1 }} visible={seatingModal} transparent={true}>

        <TouchableOpacity
          style={[
            styles.emptyTableDisplay,
            {
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 10,
              borderRadius: 8,
              //backgroundColor: 'blue',

              width: Dimensions.get('window').width * 0.12,
              height: Dimensions.get('window').width * 0.12,
              padding: Dimensions.get('window').width * 0.01,

              ...(item.joinedTables && {
                width:
                  (item.joinedTables.length == 2
                    ? windowWidth * 0.12 * item.joinedTables.length
                    : item.joinedTables.length == 3
                      ? windowWidth * 0.12 * item.joinedTables.length
                      : item.joinedTables.length == 4
                        ? windowWidth * 0.12 * item.joinedTables.length
                        : windowWidth * 0.12) +
                  (item.joinedTables.length - 1) * 20,
              }),
            },
          ]}
          onPress={() => {
            CommonStore.update((s) => {
              s.selectedOutletTable = item;
            });
            setSeatingModal(true);
            // alert(item.code)
            setOpenSelectTable(false);
          }}>
          <View
            style={[
              styles.emptyTableDisplay,
              {
                shadowOpacity: 0,
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },

                backgroundColor: Colors.primaryColor,
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,
                borderRadius: 8,
                ...(item.joinedTables && {
                  width:
                    (item.joinedTables.length == 2
                      ? windowWidth * 0.12 * item.joinedTables.length
                      : item.joinedTables.length == 3
                        ? windowWidth * 0.12 * item.joinedTables.length
                        : item.joinedTables.length == 4
                          ? windowWidth * 0.12 * item.joinedTables.length
                          : windowWidth * 0.12) +
                    (item.joinedTables.length - 1) * 20,
                }),
              },
            ]}>
            <View
              style={{
                flex: 1,
                justifyContent: 'space-between',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}>
                <Text />

                <View
                  style={{
                    //flex:0.2,
                    alignItems: 'flex-end',
                  }}>
                  <Text
                    style={[
                      styles.tableCode,
                      switchMerchant ? { fontSize: 7 } : { fontSize: 11 },
                      {
                        color: Colors.whiteColor,
                      },
                    ]}
                    numberOfLines={1}
                    ellipsizeMode="tail">
                    {item.code}
                  </Text>
                </View>
              </View>

              <View style={{ alignItems: 'center' }}>
                <Text
                  style={[
                    styles.tableCode,
                    {
                      fontSize: switchMerchant ? 10 : 13,
                      color: Colors.whiteColor,
                    },
                  ]}>
                  Seats: 0/{item.capacity}
                  {/* Available */}
                </Text>
              </View>
              <View
                style={[
                  // styles.tableCode,
                  {
                    alignItems: 'flex-end',
                  },
                ]}>
                <Text
                  style={[
                    {
                      fontSize: switchMerchant ? 10 : 13,
                      color: Colors.whiteColor,
                      fontFamily: 'NunitoSans-Bold',
                      // color: Colors.whiteColor,
                    },
                    switchMerchant ? { fontSize: 7 } : { fontSize: 11 },
                  ]}
                  numberOfLines={1}
                  ellipsizeMode="tail">
                  {'Available'}
                </Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>
        // </Modal>
      );
    }
    // else if (item.seated > 0 || tempUserReservation.length > 0) {
    //else if (item.code != null && !isAvailable) {
    else if (
      // item.isValidToBook === false && item.seated > 0 && item.code != null
      // true
      item.isValidToBook === false
    ) {
      // var dys = Math.floor(moment().diff(item.updatedAt, 'hours') / 24);
      // var hrs = Math.floor(
      //   (moment().diff(item.updatedAt, 'minutes') / 60) % 24,
      // );
      // var mins = Math.floor(moment().diff(item.updatedAt, 'minutes') % 60);

      return (
        <TouchableOpacity
          style={[
            styles.emptyTableDisplay,
            {
              backgroundColor: Colors.secondaryColor,
              shadowOpacity: 0,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,
              borderRadius: 8,

              // ...((item.joinedTables) && {
              //   marginRight: 10 * (item.joinedTables.length),
              // }),

              width: Dimensions.get('window').width * 0.12,
              height: Dimensions.get('window').width * 0.12,
              padding: Dimensions.get('window').width * 0.01,

              ...(item.joinedTables && {
                // width: windowWidth * 0.135 * (item.joinedTables.length),
                width:
                  (item.joinedTables.length == 2
                    ? windowWidth * 0.12 * item.joinedTables.length
                    : item.joinedTables.length == 3
                      ? windowWidth * 0.12 * item.joinedTables.length
                      : item.joinedTables.length == 4
                        ? windowWidth * 0.12 * item.joinedTables.length
                        : windowWidth * 0.12) +
                  (item.joinedTables.length - 1) * 20,
              }),
            },
          ]}
          onPress={() => {
            if (bookedReservation === null) {
              window.confirm(
                "Info, This table has been seated."
              );
            } else {
              window.confirm(
                `Info, This table has been seated or reserved at ${moment(
                  bookedReservation.reservationTime,
                ).format(
                  'DD MMM YYYY hh:mm A',
                )}.\n\nPlease choose another reservation date or time.`
              );
            }
          }}>
          <View
            style={{
              flex: 1,
              justifyContent: 'space-between',
            }}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}>
              <Text />

              <View
                style={{
                  //flex:0.2,
                  alignItems: 'flex-end',
                }}>
                <Text
                  style={[
                    styles.tableCode,
                    {
                      color: Colors.blackColor,
                    },
                    switchMerchant ? { fontSize: 7 } : { fontSize: 11 },
                  ]}
                  numberOfLines={1}
                  ellipsizeMode="tail">
                  {item.code}
                </Text>
              </View>
            </View>

            <View style={{ alignItems: 'center' }}>
              <Text
                style={[
                  {
                    fontSize: switchMerchant ? 10 : 13,
                    color: Colors.blackColor,
                    fontFamily: 'NunitoSans-Bold',
                  },
                ]}>
                {bookedReservation
                  ? `Seats: ${bookedReservation.pax}/${item.capacity}`
                  : `Seats: ${item.seated}/${item.capacity}`}
                {/* {bookedReservation ? `Reserved at ${moment(bookedReservation.reservationTime).format('hh:mm A')}` : 'Seated now'} */}
              </Text>
            </View>
            <View
              style={[
                // styles.tableCode,
                {
                  alignItems: 'flex-end',
                },
              ]}>
              <Text
                style={[
                  {
                    fontSize: switchMerchant ? 10 : 13,
                    color: Colors.blackColor,
                    fontFamily: 'NunitoSans-Bold',
                    // color: Colors.whiteColor,
                  },
                  switchMerchant ? { fontSize: 7 } : { fontSize: 11 },
                ]}
                numberOfLines={1}
                ellipsizeMode="tail">
                {bookedReservation
                  ? `Reserved at ${moment(
                    bookedReservation.reservationTime,
                  ).format('DD MMM YYYY hh:mm A')}`
                  : 'Seated now'}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      );
    }
  };

  const renderSection = ({ item }) => {
    return (
      <View style={{ flexDirection: 'row' }}>
        <TouchableOpacity
          onPress={() => {
            CommonStore.update((s) => {
              s.selectedOutletSection = item;
            });
          }}>
          <View
            style={[
              styles.sectionAreaButton,
              item.uniqueId == selectedOutletSection.uniqueId
                ? { backgroundColor: Colors.primaryColor }
                : null,
              switchMerchant ? { height: 35 } : {},
              {
                height: Dimensions.get('window').height * 0.04,
              }
            ]}>
            <Text
              style={[
                styles.sectionAreaButtonTxt,
                item.uniqueId == selectedOutletSection.uniqueId
                  ? { color: Colors.whiteColor }
                  : null,
                switchMerchant ? { fontSize: 10 } : {},
              ]}>
              {item.sectionName}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderFinished = ({ item, index }) => {
    var tempCrm = {};
    for (let j = 0; j < crmUsers.length; j++) {
      if (item.userId === crmUsers[j].uniqueId) {
        tempCrm = crmUsers[j];
        break;
      }
    }

    var userTagListTemp = [];
    if (crmUserTags.length > 0) {
      for (var i = 0; i < crmUserTags.length; i++) {
        if (
          crmUserTags[i].tokenFcmList.includes(tempCrm.tokenFcm || null) ||
          crmUserTags[i].emailList.includes(tempCrm.email) ||
          crmUserTags[i].phoneList.includes(tempCrm.number)
        ) {
          userTagListTemp.push(crmUserTags[i]);
        }
      }
    }
    return (
      <View style={{ backgroundColor: Colors.highlightColor, margin: 10 }}>
        <View style={{ flexDirection: 'row' }}>
          <View style={styles.flatListHeader}>
            <View style={{ flex: 1, justifyContent: 'flex-start' }}>
              <Text style={styles.tableFirst}>{`${moment(
                item.reservationTime,
              ).format('hh:mm A')}`}</Text>
            </View>
            <View style={{ flex: 0.8, justifyContent: 'flex-start' }}>
              <Text style={styles.table}>
                <Icon
                  name="md-person-outline"
                  size={15}
                  color={Colors.tabGrey}
                  style={{ marginLeft: 5 }}
                />
                {item.pax}
              </Text>
            </View>
            <View style={{ flex: 0.8, justifyContent: 'flex-start' }}>
              {/* <Text style={[styles.table]}>
                <Icon
                  name="person-outline"
                  size={15}
                  color={Colors.tabGrey}
                  style={{
                    opacity: 0.6,
                    paddingLeft: 2,
                    marginLeft: 5,
                  }}
                />
                1
              </Text> */}
            </View>
            <View style={{ flex: 1, justifyContent: 'flex-start' }}>
              <Text style={styles.table}>RSV Notes</Text>
            </View>
            <View style={{ flex: 1, justifyContent: 'flex-start' }}>
              <Text style={styles.table}>Guest Notes</Text>
            </View>
            <View style={{ flex: 1, justifyContent: 'flex-start' }}>
              <Text style={styles.table}>RSV Tags</Text>
            </View>
            <View style={{ flex: 1, justifyContent: 'flex-start' }}>
              <Text style={styles.table}>Guest Tags</Text>
            </View>
            <View style={{ flex: 1, justifyContent: 'flex-start' }}>
              <Text style={styles.table}>Created</Text>
            </View>
          </View>
        </View>

        <TouchableOpacity
          onPress={() => {
            // props.navigation.navigate('GuestDetailsScreen');
          }}>
          <View style={{ flexDirection: 'column' }}>
            <View style={styles.flatListBody}>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.tableFirst}>
                  {`${item.userName}\n\n${item.userPhone}`}
                </Text>
              </View>
              <View style={{ flex: 0.8, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>
                  <Icon
                    name="md-person-outline"
                    size={15}
                    color={Colors.tabGrey}
                    style={{ marginLeft: 5 }}
                  />
                  {`${item.pax}`}
                </Text>
              </View>
              <View
                style={{
                  flex: 0.8,
                  justifyContent: 'flex-start',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    borderRadius: 5,
                    backgroundColor: 'rgb(0, 200, 0)',
                    paddingRight: 3,
                    paddingVertical: 5,
                  }}>
                  <View
                    style={{
                      flex: 0.7,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <EvilIcons
                      name="check"
                      size={35}
                      color={Colors.whiteColor}
                      style={{
                        marginLeft: 3,
                      }}
                    />
                  </View>
                  <View
                    style={{
                      flex: 1.3,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        textAlign: 'center',
                        color: Colors.whiteColor,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      {item.status}
                    </Text>
                  </View>
                </View>
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>{`${item.remarks ? item.remarks : '-'
                  }`}</Text>
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>{`${item.guestNotes ? item.guestNotes : '-'
                  }`}</Text>
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>{`${item.rsvTags ? item.rsvTags : '-'
                  }`}</Text>
              </View>
              <View
                style={{
                  flex: 1,
                  borderRightColor: Colors.lightGrey,
                  borderRightWidth: 1,
                  // overflow: 'hidden',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexWrap: 'wrap',
                  flexDirection: 'row',
                }}>
                <Text>{userTagListTemp.length > 0 ? '' : '-'}</Text>
                {userTagListTemp.map((item, index) => {
                  return (
                    <Text
                      style={{
                        // fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        textAlign: 'center',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        color: Colors.primaryColor,
                        borderRadius: 5,
                        maxWidth: '80%',
                        margin: 3,
                      }}>
                      {`${item.name}`}
                    </Text>
                  );
                })}
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>
                  {`${moment(item.createdAt).format('DD/MM/YYYY')}`}
                </Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderWaitList = ({ item, index }) => {
    var tempCrm = {};
    for (let j = 0; j < crmUsers.length; j++) {
      if (item.userId === crmUsers[j].uniqueId) {
        tempCrm = crmUsers[j];
        break;
      }
    }

    var userTagListTemp = [];
    if (crmUserTags.length > 0) {
      for (var i = 0; i < crmUserTags.length; i++) {
        if (
          crmUserTags[i].tokenFcmList.includes(tempCrm.tokenFcm || null) ||
          crmUserTags[i].emailList.includes(tempCrm.email) ||
          crmUserTags[i].phoneList.includes(tempCrm.number)
        ) {
          userTagListTemp.push(crmUserTags[i]);
        }
      }
    }
    return (
      <View style={{ backgroundColor: Colors.highlightColor, margin: 10 }}>
        <View style={{ flexDirection: 'row' }}>
          <View style={styles.flatListHeader}>
            <View style={{ flex: 1, justifyContent: 'flex-start' }}>
              <Text style={styles.tableFirst}>{`${moment(
                item.reservationTime,
              ).format('hh:mm A')}`}</Text>
            </View>
            <View style={{ flex: 0.8, justifyContent: 'flex-start' }}>
              <Text style={styles.table}>
                <Icon
                  name="md-person-outline"
                  size={15}
                  color={Colors.tabGrey}
                  style={{ marginLeft: 5 }}
                />
                {item.pax}
              </Text>
            </View>
            <View style={{ flex: 0.8, justifyContent: 'flex-start' }}>
              {/* <Text style={[styles.table]}>
                <Icon
                  name="person-outline"
                  size={15}
                  color={Colors.tabGrey}
                  style={{
                    opacity: 0.6,
                    paddingLeft: 2,
                    marginLeft: 5,
                  }}
                />
                1
              </Text> */}
            </View>
            <View style={{ flex: 1, justifyContent: 'flex-start' }}>
              <Text style={styles.table}>RSV Notes</Text>
            </View>
            <View style={{ flex: 1, justifyContent: 'flex-start' }}>
              <Text style={styles.table}>Guest Notes</Text>
            </View>
            <View style={{ flex: 1, justifyContent: 'flex-start' }}>
              <Text style={styles.table}>RSV Tags</Text>
            </View>
            <View style={{ flex: 1, justifyContent: 'flex-start' }}>
              <Text style={styles.table}>Guest Tags</Text>
            </View>
            <View style={{ flex: 1, justifyContent: 'flex-start' }}>
              <Text style={styles.table}>Created</Text>
            </View>
          </View>
        </View>

        <TouchableOpacity
          onPress={() => {
            // props.navigation.navigate('GuestDetailsScreen');
          }}>
          <View style={{ flexDirection: 'column' }}>
            <View style={styles.flatListBody}>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.tableFirst}>
                  {`${item.userName}\n\n${item.userPhone}`}
                </Text>
              </View>
              <View style={{ flex: 0.8, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>
                  <Icon
                    name="md-person-outline"
                    size={15}
                    color={Colors.tabGrey}
                    style={{ marginLeft: 5 }}
                  />
                  {`${item.pax}`}
                </Text>
              </View>
              <View
                style={{
                  flex: 0.8,
                  justifyContent: 'flex-start',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    borderRadius: 5,
                    backgroundColor: 'rgb(0, 200, 0)',
                    paddingRight: 3,
                    paddingVertical: 5,
                  }}>
                  <View
                    style={{
                      flex: 0.7,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <EvilIcons
                      name="check"
                      size={35}
                      color={Colors.whiteColor}
                      style={{
                        marginLeft: 3,
                      }}
                    />
                  </View>
                  <View
                    style={{
                      flex: 1.3,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <Text
                      style={{
                        textAlign: 'center',
                        color: Colors.whiteColor,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      {item.status}
                    </Text>
                  </View>
                </View>
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>{`${item.remarks ? item.remarks : '-'
                  }`}</Text>
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>{`${item.guestNotes ? item.guestNotes : '-'
                  }`}</Text>
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>{`${item.rsvTags ? item.rsvTags : '-'
                  }`}</Text>
              </View>
              <View
                style={{
                  flex: 1,
                  borderRightColor: Colors.lightGrey,
                  borderRightWidth: 1,
                  // overflow: 'hidden',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexWrap: 'wrap',
                  flexDirection: 'row',
                }}>
                <Text>{userTagListTemp.length > 0 ? '' : '-'}</Text>
                {userTagListTemp.map((item, index) => {
                  return (
                    <Text
                      style={{
                        // fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        textAlign: 'center',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        color: Colors.primaryColor,
                        borderRadius: 5,
                        maxWidth: '80%',
                        margin: 3,
                      }}>
                      {`${item.name}`}
                    </Text>
                  );
                })}
              </View>
              <View style={{ flex: 1, justifyContent: 'flex-start' }}>
                <Text style={styles.table}>
                  {`${moment(item.createdAt).format('DD/MM/YYYY')}`}
                </Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const rightAction = (item) => {
    return (
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'row',
          //width: '32%',
        }}>
        {item.status !== USER_RESERVATION_STATUS.PENDING ? (
          <TouchableOpacity
            style={{
              height: '100%',
              //width: "45%",
              width: 100,
              pointerEvents: 'box-none',
              justifyContent: 'center',
              alignItems: 'center',
              alignContent: 'center',
              alignSelf: 'center',
              backgroundColor: Colors.tabCyan,
              underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            }}
            onPress={() => {
              if (window.confirm("Alert, Do you want to undo this reservation?")) {
                undoReservation(item.uniqueId);
              }
            }}>
            {switchMerchant ? (
              <Feather
                name="rotate-ccw"
                size={10}
                color={Colors.whiteColor}
                style={{ marginTop: 5 }}
              />
            ) : (
              <Feather
                name="rotate-ccw"
                size={30}
                color={Colors.whiteColor}
                style={{ marginTop: 5 }}
              />
            )}
            <Text
              style={[
                { fontFamily: 'NunitoSans-Bold', color: 'white' },
                switchMerchant ? { fontSize: 10 } : {},
              ]}>
              Undo{' '}
            </Text>
          </TouchableOpacity>
        ) : null}

        <TouchableOpacity
          style={{
            height: '100%',
            //width: "45%",
            width: 100,
            pointerEvents: 'box-none',
            justifyContent: 'center',
            alignItems: 'center',
            alignContent: 'center',
            alignSelf: 'center',
            backgroundColor: Colors.primaryColor,
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          }}
          onPress={() => {
            Linking.openURL(`tel:${item.userPhone}`);
            //   deleteNote(rowData);
          }}>
          {switchMerchant ? (
            <Feather
              name="phone-call"
              size={10}
              color={Colors.whiteColor}
              style={{ marginTop: 5 }}
            />
          ) : (
            <Feather
              name="phone-call"
              size={30}
              color={Colors.whiteColor}
              style={{ marginTop: 5 }}
            />
          )}
          <Text
            style={[
              { fontFamily: 'NunitoSans-Bold', color: 'white' },
              switchMerchant ? { fontSize: 10 } : {},
            ]}>
            Call{' '}
          </Text>
        </TouchableOpacity>

        {item.status !== USER_RESERVATION_STATUS.NO_SHOW ? (
          <TouchableOpacity
            style={{
              height: '100%',
              //width: "45%",
              width: 100,
              pointerEvents: 'box-none',
              justifyContent: 'center',
              alignItems: 'center',
              alignContent: 'center',
              alignSelf: 'center',
              backgroundColor: Colors.tabGrey,
              underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            }}
            onPress={() => {
              //   deleteNote(rowData);
              // setStatusNoShow(true)

              noShowReservation(item.uniqueId);
            }}>
            {switchMerchant ? (
              <Feather
                name="alert-triangle"
                size={10}
                color={Colors.whiteColor}
                style={{ marginTop: 5 }}
              />
            ) : (
              <Feather
                name="alert-triangle"
                size={30}
                color={Colors.whiteColor}
                style={{ marginTop: 5 }}
              />
            )}
            <Text
              style={[
                { fontFamily: 'NunitoSans-Bold', color: 'white' },
                switchMerchant ? { fontSize: 10 } : {},
              ]}>
              No-Show
            </Text>
          </TouchableOpacity>
        ) : null}

        {item.status !== USER_RESERVATION_STATUS.SEATED ? (
          <TouchableOpacity
            style={{
              height: '100%',
              //width: "45%",
              width: 100,
              pointerEvents: 'box-none',
              justifyContent: 'center',
              alignItems: 'center',
              alignContent: 'center',
              alignSelf: 'center',
              backgroundColor: Colors.tabGold,
              underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            }}
            onPress={() => {
              if (
                item.status === USER_RESERVATION_STATUS.PENDING ||
                item.status === USER_RESERVATION_STATUS.NO_SHOW ||
                item.status === USER_RESERVATION_STATUS.CANCELED
              ) {
                // setSelectedReservation(item);
                // setTableModalVisibility(true);
                acceptReservation(item);
              } else if (item.tableId != null) {
                setSelectedReservation(item);
                seatedReservation(item);
              } else {
                // acceptReservation(item);
                setSelectedReservation(item);
                setTableModalVisibility(true);
              }
              // if (item.status !== USER_RESERVATION_STATUS.SEATED) {
              //   acceptReservation(item);
              // } else {
              //   setSelectedReservation(item);
              //   setTableModalVisibility(true);
              // }
            }}>
            {item.status === USER_RESERVATION_STATUS.ACCEPTED ? (
              // <AntDesign name="checkcircleo" size={30} color={Colors.whiteColor} style={{ marginTop: 5 }} />
              <>
                {switchMerchant ? (
                  <Seat width={40} height={40} color={Colors.whiteColor} />
                ) : (
                  <Seat width={40} height={40} color={Colors.whiteColor} />
                )}
              </>
            ) : (
              <>
                {switchMerchant ? (
                  <Feather
                    name="user-check"
                    size={10}
                    color={Colors.whiteColor}
                    style={{ marginTop: 3 }}
                  />
                ) : (
                  <Feather
                    name="user-check"
                    size={31}
                    color={Colors.whiteColor}
                    style={{ marginTop: 3 }}
                  />
                )}
              </>
            )}
            <Text
              style={[
                { fontFamily: 'NunitoSans-Bold', color: 'white' },
                switchMerchant ? { fontSize: 10 } : {},
              ]}>
              {' '}
              {item.status === USER_RESERVATION_STATUS.ACCEPTED
                ? 'Seated'
                : 'Accept'}{' '}
            </Text>
          </TouchableOpacity>
        ) : null}

        {item.status === USER_RESERVATION_STATUS.ACCEPTED ||
          item.status === USER_RESERVATION_STATUS.NO_SHOW ||
          item.status === USER_RESERVATION_STATUS.PENDING ? (
          <TouchableOpacity
            style={{
              height: '100%',
              //width: "45%",
              width: 100,
              pointerEvents: 'box-none',
              justifyContent: 'center',
              alignItems: 'center',
              alignContent: 'center',
              alignSelf: 'center',
              backgroundColor: Colors.tabRed,
              underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            }}
            onPress={() => {
              if (window.confirm("Alert, Do you want to reject this reservation?")) {
                cancelReservation(item);
              }
            }}>
            {switchMerchant ? (
              <Feather
                name="user-x"
                size={10}
                color={Colors.whiteColor}
                style={{ marginTop: 3 }}
              />
            ) : (
              <Feather
                name="user-x"
                size={31}
                color={Colors.whiteColor}
                style={{ marginTop: 3 }}
              />
            )}
            <Text
              style={[
                { fontFamily: 'NunitoSans-Bold', color: 'white' },
                switchMerchant ? { fontSize: 10 } : {},
              ]}>
              Reject{' '}
            </Text>
          </TouchableOpacity>
        ) : null}

        {/* <TouchableOpacity
          style={{
            height: "100%",
            //width: "15%",
            width: 60,
            pointerEvents: 'box-none',
            justifyContent: 'center',
            alignItems: 'center',
            alignContent: 'center',
            alignSelf: "center",
            backgroundColor: '#8fbc8f',
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          }}
          onPress={() => {
            //prioritizeOrder(item.uniqueId);
          }}
        >
          <Feather
            name="check"
            size={30}
            color={Colors.whiteColor}
            style={{ marginTop: 10 }} />
          <Text style={{ color: Colors.whiteColor, fontSize: 8 }}>{'Delivered'}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={{
            height: "100%",
            //width: "15%",
            width: 60,
            //pointerEvents: 'box-none',
            justifyContent: 'center',
            alignItems: 'center',
            alignContent: 'center',
            alignSelf: "center",
            //backgroundColor: Colors.primaryColor,
            backgroundColor: '#d90000',
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            borderTopRightRadius: 10,
            borderBottomRightRadius: 10,
          }}
          onPress={() => {
            // item.customTable == "TAKE AWAY" ?
            //   setState({
            //     currToPrioritizeOrder: item,
            //     visible: true
            //   }) :
            //   setState({
            //     currToPrioritizeOrder: item,
            //     visible1: true
            //   });
            cancelOrder(item);
          }}
        >
          <MaterialCommunityIcons
            name="close"
            size={30}
            color={Colors.whiteColor}
            style={{ marginTop: 10 }} />
          <Text style={{ color: Colors.whiteColor, fontSize: 8 }}>{'Reject Order'}</Text>
        </TouchableOpacity> */}
      </View>
    );
  };

  const renderReservations = ({ item, index }) => {
    var tempCrm = {};
    for (let j = 0; j < crmUsers.length; j++) {
      if (item.userId === crmUsers[j].uniqueId ||
        item.userEmail === crmUsers[j].email ||
        item.userPhone === crmUsers[j].number) {
        tempCrm = crmUsers[j];
        break;
      }
    }

    var userTagListTemp = [];
    if (crmUserTags.length > 0) {
      for (var i = 0; i < crmUserTags.length; i++) {
        if (
          crmUserTags[i].tokenFcmList.includes(tempCrm.tokenFcm || null) ||
          crmUserTags[i].emailList.includes(tempCrm.email) ||
          crmUserTags[i].phoneList.includes(tempCrm.number)
        ) {
          userTagListTemp.push(crmUserTags[i]);
        }
      }
    }

    var orderStatus = '';
    if (item.orderId) {
      // need find the order status using orderId
      for (let i = 0; i < userOrders.length; i++) {
        if (item.orderId === userOrders[i].uniqueId) {
          // orderStatus = userOrders[i].orderStatus;
          orderStatus = `#${userOrders[i].orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}${userOrders[i].orderId} (${USER_ORDER_STATUS_PARSED[userOrders[i].orderStatus]})`;
          break;
        }
      }
    }

    if (item.orderIdList && item.orderIdList.length > 0) {
      // need find the order status using orderId

      for (let orderListIndex = 0; orderListIndex < item.orderIdList.length; orderListIndex++) {
        for (let i = 0; i < userOrders.length; i++) {
          if (item.orderIdList[orderListIndex] === userOrders[i].uniqueId) {
            // orderStatus = userOrders[i].orderStatus;
            orderStatus = orderStatus.concat(`\n#${userOrders[i].orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}${userOrders[i].orderId} (${USER_ORDER_STATUS_PARSED[userOrders[i].orderStatus]})`);
            break;
          }
        }
      }
    }

    let guestNotes = '';
    if (item.remarks) {
      guestNotes = `${item.remarks}`;
    }
    if (item.dRestrictions) {
      guestNotes = `${guestNotes}${guestNotes.length > 0 ? ', ' : ''}${item.dRestrictions}`;
    }
    if (item.sOccasions) {
      guestNotes = `${guestNotes}${guestNotes.length > 0 ? ', ' : ''}${item.sOccasions}`;
    }

    let zoneTableInfo = '-';
    if (item.outletSectionName) {
      zoneTableInfo = `${item.outletSectionName}`;
    }
    if (item.tableCode) {
      zoneTableInfo = `${zoneTableInfo}${zoneTableInfo.length > 0 ? ' ' : ''}(${item.tableCode})`;
    }
    // else {
    //   zoneTableInfo = `${zoneTableInfo}${zoneTableInfo.length > 0 ? ' ' : ''}(N/A)`;
    // }

    return (
      <View style={{ padding: 3 }}>
        {/* <Swipeable
          renderRightActions={() => rightAction(item)}
          onSwipeableWillOpen={() => { }}> */}
        <TouchableOpacity
          onPress={() => {
            const currentDate = reservationDate;
            const checkDate = item.reservationDate;
            if (
              // currentDate > checkDate || checkDate == null
              true
            ) {
              if (item.timeSelected) {
                setTimeSelected(item.timeSelected);
                setIsSelected(item.timeSelected);
              }
              else {
                setTimeSelected(moment(item.reservationTime).format('h:mm A'));
                setIsSelected(moment(item.reservationTime).format('h:mm A'));
              }

              setAddReservation(true);
              setReservationCustomerName(item.customerName);
              setReservationPhone(item.customerPhone);
              setReservationDate(moment(item.reservationTime));
              setReservationTime(moment(item.reservationTime).valueOf());

              setSelectedResDay(moment(item.reservationTime).format('DD'));
              setSelectedResMonth(moment(item.reservationTime).format('MMM'));
              setSelectedResYear(moment(item.reservationTime).format('YYYY'));

              setDietaryRestrictions(item.dRestrictions ? item.dRestrictions : '');
              setSpecialOccasions(item.sOccasions ? item.sOccasions : '');

              setSeatingPax(item.pax.toString());
              setSelectTable('existed');

              var availability = reservationAvailabilityFiltered.find(availability => availability.startTime === item.timeSelected);

              // if (item.reservationAvailabilityId && availability) {
              //   setSelectedReservationId(item.reservationAvailabilityId);
              //   setTimeSelected(item.timeSelected);
              //   setTimeSelectedMilli(availability.startTimeMilli);
              //   setReservationAvailability(availability);
              // }

              setReservationId(item.uniqueId);
              setReservationToUsed(item);

              const foundTable = outletTables.find(
                (table) => table.uniqueId === item.tableId,
              );

              if (item.tableId && foundTable) {
                CommonStore.update((s) => {
                  s.selectedOutletTable = foundTable;
                });

                setSelectTable('existed');
              }
              else {
                CommonStore.update((s) => {
                  s.selectedOutletTable = {};
                });

                setSelectTable('');
              }

              setReservationRemark(item.remarks);

              if (
                item.crmUserId &&
                crmUsers.find(
                  (user) =>
                    (user.email === item.userEmail)
                    ||
                    (user.uniqueId === item.crmUserId)
                    ||
                    (user.number === item.userPhone),
                )
              ) {
                setCustomerName(item.userName || '');
                setCustomerPhone(item.userPhone || '+60');
                setCustomerEmail(item.userEmail || '');
                setSelectedRegisteredCRMUserId(item.crmUserId);
              }
              else {
                console.log('not found');
              }
            }
            else {
              window.confirm(
                "Error, This reservation has expired."
              );
            }
          }}>
          <View
            style={{
              //flexDirection: 'row',
              //marginTop: 10,
              borderRadius: 5,
              padding: 10,
              backgroundColor: '#ffffff',
              height: 185,
              //alignItems: 'center',
            }}>
            <View style={{
              flexDirection: 'row',
              //justifyContent: 'center',
              //alignItems: 'center',
            }}>
              <View
                style={[
                  {},
                  switchMerchant
                    ? {
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '12.9%',
                    }
                    : {
                      alignItems: 'center',
                      //marginLeft: '2.5%',
                      width: '13.7%',
                    },
                ]}>
                <Text style={styles.tableItem}>
                  {item.userName
                    ? `${item.userName}\n${item.userPhone}`
                    : `Guest\n${(item.userPhone && item.userPhone !== '+60') ? item.userPhone : 'N/A'}`}
                </Text>
              </View>

              <View
                style={[
                  {},
                  switchMerchant
                    ? {
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '6.9%',
                    }
                    : {
                      alignItems: 'center',
                      paddingLeft: '2.5%',
                      width: '7.7%',
                      flexDirection: 'row',
                    },
                ]}><Icon
                  name="md-person-outline"
                  size={15}
                  color={Colors.tabGrey}
                  style={{ marginLeft: 5 }}
                />
                <Text style={{
                  fontFamily: 'NunitoSans-Bold',
                  fontSize: 16,
                  //textAlign: 'center',
                  color: Colors.blackColor,
                  //width: Dimensions.get('window').width * 0.11,
                }}>
                  {`${item.pax}`}
                </Text>
              </View>

              <View
                style={[
                  {},
                  switchMerchant
                    ? {
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '15.9%',
                    }
                    : {
                      alignItems: 'center',
                      //marginLeft: '2.5%',
                      width: '16.7%',
                    },
                ]}>
                <View
                  style={{
                    flexDirection: 'row',
                    borderRadius: 5,
                    //backgroundColor: 'rgb(0, 200, 0)',
                    backgroundColor: item.status == USER_RESERVATION_STATUS.PENDING ? Colors.tabGold
                      : item.status == USER_RESERVATION_STATUS.CANCELED ? Colors.tabRed
                        : item.status == USER_RESERVATION_STATUS.NO_SHOW ? Colors.tabGrey
                          : Colors.primaryColor,
                    paddingRight: 3,
                    paddingVertical: 5,
                  }}>
                  <View
                    style={{
                      //flex: 0.7,
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: 40,
                    }}>
                    <Feather
                      name="check"
                      size={25}
                      color={Colors.whiteColor}
                      style={{
                        marginLeft: 5,
                      }}
                    />
                  </View>
                  <View
                    style={{
                      //flex: 1.3,
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: 100,
                    }}>
                    <Text style={[styles.tableItem, { color: '#ffffff' }]}>
                      {USER_RESERVATION_STATUS_PARSED[item.status]}
                    </Text>
                  </View>
                </View>
                <Text style={[styles.tableItem, {
                  marginTop: 5,
                }]}>
                  {/* {`${item.timeSelected ? item.timeSelected : '-'}`} */}
                  {moment(item.reservationTime).format('h:mm A')}
                </Text>
              </View>

              <View
                style={[
                  {},
                  switchMerchant
                    ? {
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '21.8%',
                    }
                    : {
                      alignItems: 'center',
                      //marginLeft: '2.5%',
                      width: '23.4%',
                    },
                ]}>
                <Text style={styles.tableItem}>
                  {`${orderStatus ? orderStatus : '-'}`}
                </Text>
              </View>

              {/* <View
                style={[
                  {},
                  switchMerchant
                    ? {
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '10.9%',
                    }
                    : {
                      alignItems: 'center',
                      //marginLeft: '2.5%',
                      width: '11.7%',
                    },
                ]}>
                <Text style={styles.tableItem}>
                  {`${item.remarks ? item.remarks : '-'}`}
                </Text>
              </View> */}

              <View
                style={[
                  {},
                  switchMerchant
                    ? {
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '10.9%',
                    }
                    : {
                      alignItems: 'center',
                      //marginLeft: '2.5%',
                      width: '11.7%',
                    },
                ]}>
                <Text style={styles.tableItem}>
                  {`${guestNotes ? guestNotes : '-'}`}
                </Text>
              </View>
              <View
                style={[
                  {},
                  switchMerchant
                    ? {
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '10.9%',
                    }
                    : {
                      alignItems: 'center',
                      //marginLeft: '2.5%',
                      width: '11.7%',
                    },
                ]}>
                <Text style={styles.tableItem}>
                  {/* {`${item.tableId ? outletTables.find(
                    (table) => table.uniqueId === item.tableId,
                  ).code
                    : '-'}`} */}
                  {zoneTableInfo}
                </Text>
              </View>
              <View
                style={[
                  {
                    // flexDirection: 'row',
                    // alignItems: 'flex-start',
                    // flexWrap: 'wrap',

                    // backgroundColor: 'red',
                  },
                  switchMerchant
                    ? {
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '10.9%',
                    }
                    : {
                      alignItems: 'center',
                      //marginLeft: '2.5%',
                      width: '11.7%',
                    },
                ]}>
                {
                  userTagListTemp.length === 0
                    ?
                    <Text style={styles.tableItem}>
                      {userTagListTemp.length > 0 ? '' : '-'}
                    </Text>
                    :
                    <Text style={[styles.tableItem, {
                      fontSize: 14,
                    }]}
                      numberOfLines={3}
                    >
                      {userTagListTemp.map(tagItem => tagItem.name).join(', ')}
                    </Text>
                }

                {/* {userTagListTemp.map((item, index) => {
                  return (
                    <Text
                      style={{
                        // fontSize: switchMerchant ? 10 : 14,
                        fontFamily: 'NunitoSans-Regular',
                        textAlign: 'center',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        color: Colors.primaryColor,
                        borderRadius: 5,
                        maxWidth: '80%',
                        margin: 3,
                        borderSpacing: 1,
                      }}>
                      {`${item.name}`}
                    </Text>
                  );
                })} */}
              </View>
              {/* <View style={[
                {},
                switchMerchant
                  ? {
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '9.9%',
                  }
                  : {
                    alignItems: 'center',
                    //marginLeft: '2.5%',
                    width: '10.7%',
                  },
              ]}>
                <Text style={styles.tableItem}>
                  {`${moment(item.createdAt).format('DD/MM/YYYY')}\n${moment(
                    item.reservationTime,
                  ).format('hh:mm A')}`}
                </Text>
              </View> */}
            </View>
            <View
              style={{
                // justifyContent: 'center',
                // alignItems: 'center',
                flexDirection: 'row',
                //width: '32%',
                marginTop: 25,
              }}>
              {item.status !== USER_RESERVATION_STATUS.PENDING ? (
                <TouchableOpacity
                  style={{
                    height: '150%',
                    //width: "45%",
                    width: 100,
                    pointerEvents: 'box-none',
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignContent: 'center',
                    alignSelf: 'center',
                    backgroundColor: Colors.tabCyan,
                    underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                    marginLeft: '5%',

                    borderRadius: 5,
                    shadowOpacity: 0,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}
                  onPress={() => {
                    if (window.confirm("Alert, Do you want to undo this reservation?")) {
                      undoReservation(item.uniqueId);
                    }
                  }}>
                  {switchMerchant ? (
                    <Feather
                      name="rotate-ccw"
                      size={10}
                      color={Colors.whiteColor}
                      style={{ marginTop: 5 }}
                    />
                  ) : (
                    <Feather
                      name="rotate-ccw"
                      size={30}
                      color={Colors.whiteColor}
                      style={{ marginTop: 5 }}
                    />
                  )}
                  <Text
                    style={[
                      { fontFamily: 'NunitoSans-Bold', color: 'white' },
                      switchMerchant ? { fontSize: 10 } : {},
                    ]}>
                    Undo{' '}
                  </Text>
                </TouchableOpacity>
              ) : null}

              <TouchableOpacity
                style={{
                  height: '150%',
                  //width: "45%",
                  width: 100,
                  pointerEvents: 'box-none',
                  justifyContent: 'center',
                  alignItems: 'center',
                  alignContent: 'center',
                  alignSelf: 'center',
                  backgroundColor: Colors.primaryColor,
                  underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                  marginLeft: item.status !== USER_RESERVATION_STATUS.PENDING ? 25 : '5%',

                  borderRadius: 5,
                  shadowOpacity: 0,
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 3,
                }}
                onPress={() => {
                  Linking.openURL(`tel:${item.userPhone}`);
                  //   deleteNote(rowData);
                }}>
                {switchMerchant ? (
                  <Feather
                    name="phone-call"
                    size={10}
                    color={Colors.whiteColor}
                    style={{ marginTop: 5 }}
                  />
                ) : (
                  <Feather
                    name="phone-call"
                    size={30}
                    color={Colors.whiteColor}
                    style={{ marginTop: 5 }}
                  />
                )}
                <Text
                  style={[
                    { fontFamily: 'NunitoSans-Bold', color: 'white' },
                    switchMerchant ? { fontSize: 10 } : {},
                  ]}>
                  Call{' '}
                </Text>
              </TouchableOpacity>

              {item.status !== USER_RESERVATION_STATUS.NO_SHOW ? (
                <TouchableOpacity
                  style={{
                    height: '150%',
                    //width: "45%",
                    width: 100,
                    pointerEvents: 'box-none',
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignContent: 'center',
                    alignSelf: 'center',
                    backgroundColor: Colors.tabGrey,
                    underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                    marginLeft: 25,

                    borderRadius: 5,
                    shadowOpacity: 0,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}
                  onPress={() => {
                    //   deleteNote(rowData);
                    // setStatusNoShow(true)

                    noShowReservation(item.uniqueId);
                  }}>
                  {switchMerchant ? (
                    <Feather
                      name="alert-triangle"
                      size={10}
                      color={Colors.whiteColor}
                      style={{ marginTop: 5 }}
                    />
                  ) : (
                    <Feather
                      name="alert-triangle"
                      size={30}
                      color={Colors.whiteColor}
                      style={{ marginTop: 5 }}
                    />
                  )}
                  <Text
                    style={[
                      { fontFamily: 'NunitoSans-Bold', color: 'white' },
                      switchMerchant ? { fontSize: 10 } : {},
                    ]}>
                    No-Show
                  </Text>
                </TouchableOpacity>
              ) : null}

              {item.status !== USER_RESERVATION_STATUS.SEATED ? (
                <TouchableOpacity
                  style={{
                    height: '150%',
                    //width: "45%",
                    width: 100,
                    pointerEvents: 'box-none',
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignContent: 'center',
                    alignSelf: 'center',
                    backgroundColor: Colors.tabGold,
                    underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                    marginLeft: 25,

                    borderRadius: 5,
                    shadowOpacity: 0,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}
                  onPress={() => {
                    if (
                      item.status === USER_RESERVATION_STATUS.PENDING ||
                      item.status === USER_RESERVATION_STATUS.NO_SHOW ||
                      item.status === USER_RESERVATION_STATUS.CANCELED
                    ) {
                      // setSelectedReservation(item);
                      // setTableModalVisibility(true);
                      acceptReservation(item);
                    } else if (item.tableId != null) {
                      setSelectedReservation(item);
                      seatedReservation();
                    } else {
                      // acceptReservation(item);
                      setSelectedReservation(item);
                      setTableModalVisibility(true);
                    }
                    // if (item.status !== USER_RESERVATION_STATUS.SEATED) {
                    //   acceptReservation(item);
                    // } else {
                    //   setSelectedReservation(item);
                    //   setTableModalVisibility(true);
                    // }
                  }}>
                  {item.status === USER_RESERVATION_STATUS.ACCEPTED ? (
                    // <AntDesign name="checkcircleo" size={30} color={Colors.whiteColor} style={{ marginTop: 5 }} />
                    <>
                      {switchMerchant ? (
                        <Seat width={40} height={40} color={Colors.whiteColor} />
                      ) : (
                        <Seat width={40} height={40} color={Colors.whiteColor} />
                      )}
                    </>
                  ) : (
                    <>
                      {switchMerchant ? (
                        <Feather
                          name="user-check"
                          size={10}
                          color={Colors.whiteColor}
                          style={{ marginTop: 3 }}
                        />
                      ) : (
                        <Feather
                          name="user-check"
                          size={31}
                          color={Colors.whiteColor}
                          style={{ marginTop: 3 }}
                        />
                      )}
                    </>
                  )}
                  <Text
                    style={[
                      { fontFamily: 'NunitoSans-Bold', color: 'white' },
                      switchMerchant ? { fontSize: 10 } : {},
                    ]}>
                    {' '}
                    {item.status === USER_RESERVATION_STATUS.ACCEPTED
                      ? 'Seated'
                      : 'Accept'}{' '}
                  </Text>
                </TouchableOpacity>
              ) : null}

              {item.status === USER_RESERVATION_STATUS.ACCEPTED ||
                item.status === USER_RESERVATION_STATUS.NO_SHOW ||
                item.status === USER_RESERVATION_STATUS.PENDING ? (
                <TouchableOpacity
                  style={{
                    height: '150%',
                    //width: "45%",
                    width: 100,
                    pointerEvents: 'box-none',
                    justifyContent: 'center',
                    alignItems: 'center',
                    alignContent: 'center',
                    alignSelf: 'center',
                    backgroundColor: Colors.tabRed,
                    underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                    marginLeft: 25,

                    borderRadius: 5,
                    shadowOpacity: 0,
                    shadowColor: '#000',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}
                  onPress={() => {
                    if (window.confirm("Alert, Do you want to reject this reservation?")) {
                      cancelReservation(item);
                    }
                  }}>
                  {switchMerchant ? (
                    <Feather
                      name="user-x"
                      size={10}
                      color={Colors.whiteColor}
                      style={{ marginTop: 3 }}
                    />
                  ) : (
                    <Feather
                      name="user-x"
                      size={31}
                      color={Colors.whiteColor}
                      style={{ marginTop: 3 }}
                    />
                  )}
                  <Text
                    style={[
                      { fontFamily: 'NunitoSans-Bold', color: 'white' },
                      switchMerchant ? { fontSize: 10 } : {},
                    ]}>
                    Reject{' '}
                  </Text>
                </TouchableOpacity>
              ) : null}

              {/* <TouchableOpacity
          style={{
            height: "100%",
            //width: "15%",
            width: 60,
            pointerEvents: 'box-none',
            justifyContent: 'center',
            alignItems: 'center',
            alignContent: 'center',
            alignSelf: "center",
            backgroundColor: '#8fbc8f',
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
          }}
          onPress={() => {
            //prioritizeOrder(item.uniqueId);
          }}
        >
          <Feather
            name="check"
            size={30}
            color={Colors.whiteColor}
            style={{ marginTop: 10 }} />
          <Text style={{ color: Colors.whiteColor, fontSize: 8 }}>{'Delivered'}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={{
            height: "100%",
            //width: "15%",
            width: 60,
            //pointerEvents: 'box-none',
            justifyContent: 'center',
            alignItems: 'center',
            alignContent: 'center',
            alignSelf: "center",
            //backgroundColor: Colors.primaryColor,
            backgroundColor: '#d90000',
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            borderTopRightRadius: 10,
            borderBottomRightRadius: 10,
          }}
          onPress={() => {
            // item.customTable == "TAKE AWAY" ?
            //   setState({
            //     currToPrioritizeOrder: item,
            //     visible: true
            //   }) :
            //   setState({
            //     currToPrioritizeOrder: item,
            //     visible1: true
            //   });
            cancelOrder(item);
          }}
        >
          <MaterialCommunityIcons
            name="close"
            size={30}
            color={Colors.whiteColor}
            style={{ marginTop: 10 }} />
          <Text style={{ color: Colors.whiteColor, fontSize: 8 }}>{'Reject Order'}</Text>
        </TouchableOpacity> */}
            </View>
          </View>
        </TouchableOpacity>
        {/* </Swipeable> */}
      </View>
    );
  };

  const convertTemplateToExcelFormat = () => {
    var excelTemplate = [];

    // var allOutletsStr = allOutlets
    //   .map(
    //     (item) =>
    //       `${item.name}:${Math.floor(Math.random() * (100 - 0 + 1)) + 0}`,
    //   )
    //   .join(';');

    // var taxName = currOutletTaxes[0].name;

    var excelColumn = {
      time: '3.00pm',
      name: 'Ryan Tan',
      phone: '012-9129013',
      place: 'Indoor',
      two: '2',
      one: '1',
      rsvNotes: 'Testing',
      guestNotes: 'Chicken is raw',
      rsvTags: 'Something',
      guestTags: ['Regular', 'Big Spender', 'No Peanut'],
      created: '01/01/2021',
      createdWay: 'Widget',
    };
    excelTemplate.push(excelColumn);

    var excelColumn2 = {
      time: '10.00am',
      name: 'Handsome Boy',
      phone: '012-9129013',
      place: 'Outdoor',
      two: '2',
      one: '1',
      rsvNotes: '',
      guestNotes: 'Chicken is raw',
      rsvTags: '',
      guestTags: ['Regular', 'Big Spender', 'No Peanut'],
      created: '01/01/2021',
      createdWay: 'Widget',
    };
    excelTemplate.push(excelColumn2);

    // console.log('excelTemplate');
    // console.log(excelTemplate);

    return excelTemplate;
  };

  const convertDataToExcelFormat = () => {
    var excelData = [];
    var orderStatus = '';
    if (reservationsArr.orderId) {
      // need find the order status using orderId
      for (let i = 0; i < userOrders.length; i++) {
        if (reservationsArr.orderId === userOrders[i].uniqueId) {
          orderStatus = userOrders[i].orderStatus;
          break;
        }
      }
    }
    var tempCrm = {};
    for (let j = 0; j < crmUsers.length; j++) {
      if (reservationsArr.userId === crmUsers[j].uniqueId) {
        tempCrm = crmUsers[j];
        break;
      }
    }
    var userTagListTemp = [];
    if (crmUserTags.length > 0) {
      for (var i = 0; i < crmUserTags.length; i++) {
        if (
          crmUserTags[i].tokenFcmList.includes(tempCrm.tokenFcm || null) ||
          crmUserTags[i].emailList.includes(tempCrm.email) ||
          crmUserTags[i].phoneList.includes(tempCrm.number)
        ) {
          userTagListTemp.push(crmUserTags[i]);
        }
      }
    }
    //List
    for (var i = 0; i < reservationsArr.length; i++) {
      var excelRow = {
        'Customer': reservationsArr[i].userName ? reservationsArr[i].userName : 'Temporary Guest',
        'Phone Number': reservationsArr[i].userPhone,
        'Pax': reservationsArr[i].pax,
        'Order Status': `${orderStatus ? orderStatus : '-'}`,
        'RSV Notes': `${reservationsArr[i].remarks ? reservationsArr[i].remarks : '-'}`,
        'Guest Notes': `${reservationsArr[i].guestNotes ? reservationsArr[i].guestNotes : '-'}`,
        'RSV Tags': `${reservationsArr[i].rsvTags ? reservationsArr[i].rsvTags : '-'}`,
        'Guest Tags': `${userTagListTemp.map((item, index) => { return userTagListTemp.length > 0 ? `${item.name}` : '-' })}`,
      };

      excelData.push(excelRow);
    }
    return excelData;
  };

  const convertDataToCSVFormat = () => {
    var orderStatus = '';
    if (reservationsArr.orderId) {
      // need find the order status using orderId
      for (let i = 0; i < userOrders.length; i++) {
        if (reservationsArr.orderId === userOrders[i].uniqueId) {
          orderStatus = userOrders[i].orderStatus;
          break;
        }
      }
    }
    var tempCrm = {};
    for (let j = 0; j < crmUsers.length; j++) {
      if (reservationsArr.userId === crmUsers[j].uniqueId) {
        tempCrm = crmUsers[j];
        break;
      }
    }
    var userTagListTemp = [];
    if (crmUserTags.length > 0) {
      for (var i = 0; i < crmUserTags.length; i++) {
        if (
          crmUserTags[i].tokenFcmList.includes(tempCrm.tokenFcm || null) ||
          crmUserTags[i].emailList.includes(tempCrm.email) ||
          crmUserTags[i].phoneList.includes(tempCrm.number)
        ) {
          userTagListTemp.push(crmUserTags[i]);
        }
      }
    }
    var csvData = [];

    csvData.push(`Customer,Pax,Order Status,RSV Notes,Guest Notes,RSV Tags,Guest Tags`);

    for (var i = 0; i < reservationsArr.length; i++) {
      var csvRow = `${reservationsArr[i].userName ? reservationsArr[i].userName : 'Temporary Guest'},${reservationsArr[i].userPhone},${reservationsArr[i].pax},${orderStatus ? orderStatus : '-'},${reservationsArr[i].remarks ? reservationsArr[i].remarks : '-'},${reservationsArr[i].guestNotes ? reservationsArr[i].guestNotes : '-'},${reservationsArr[i].rsvTags ? reservationsArr[i].rsvTags : '-'},${userTagListTemp.map((item, index) => { return userTagListTemp.length > 0 ? `${item.name}` : '-' })}`;

      csvData.push(csvRow);
    }
    return csvData.join('\r\n');
  };
  // const exportTemplate = async () => {
  //   try {
  //     const excelTemplate = convertTemplateToExcelFormat();

  //     const tempFolderName = 'koodoo-crm-user-template';
  //     const tempFolderPath = `${Platform.OS === 'ios'
  //       ? RNFS.DocumentDirectoryPath
  //       : RNFS.DownloadDirectoryPath
  //       }/${tempFolderName}`;

  //     RNFS.mkdir(tempFolderPath);

  //     const tempImageFolderName = 'images/Kylie_Campbell';
  //     const tempImageFolderPath = `${Platform.OS === 'ios'
  //       ? RNFS.DocumentDirectoryPath
  //       : RNFS.DownloadDirectoryPath
  //       }/${tempFolderName}/${tempImageFolderName}`;
  //     RNFS.mkdir(tempImageFolderPath);

  //     const tempImageFolderName2 = 'images/Roy_Cruz';
  //     const tempImageFolderPath2 = `${Platform.OS === 'ios'
  //       ? RNFS.DocumentDirectoryPath
  //       : RNFS.DownloadDirectoryPath
  //       }/${tempFolderName}/${tempImageFolderName2}`;
  //     RNFS.mkdir(tempImageFolderPath2);

  //     var templateImageUrl = '';

  //     // download merchant logo as example image file

  //     if (merchantLogo) {
  //       await new Promise((resolve, reject) => {
  //         if (
  //           merchantLogo.startsWith('http') ||
  //           merchantLogo.startsWith('file')
  //         ) {
  //           templateImageUrl = merchantLogo;

  //           resolve();
  //         } else {
  //           getImageFromFirebaseStorage(merchantLogo, (parsedUrl) => {
  //             templateImageUrl = parsedUrl;

  //             resolve();
  //           });
  //         }
  //       });

  //       var tempImageFileName = `image.${templateImageUrl.split('.').pop()}`;
  //       tempImageFileName = tempImageFileName.split('?')[0];

  //       const tempImageFilePath = `${Platform.OS === 'ios'
  //         ? RNFS.DocumentDirectoryPath
  //         : RNFS.DownloadDirectoryPath
  //         }/${tempFolderName}/${tempImageFolderName}/${tempImageFileName}`;

  //       const downloadJob = RNFS.downloadFile({
  //         fromUrl: templateImageUrl,
  //         toFile: tempImageFilePath,
  //       });

  //       await downloadJob.promise;

  //       const tempImageFilePath2 = `${Platform.OS === 'ios'
  //         ? RNFS.DocumentDirectoryPath
  //         : RNFS.DownloadDirectoryPath
  //         }/${tempFolderName}/${tempImageFolderName2}/${tempImageFileName}`;

  //       const downloadJob2 = RNFS.downloadFile({
  //         fromUrl: templateImageUrl,
  //         toFile: tempImageFilePath2,
  //       });

  //       await downloadJob2.promise;

  //       // var excelFile = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/koodoo-Product${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
  //       var excelFile = `${Platform.OS === 'ios'
  //         ? RNFS.DocumentDirectoryPath
  //         : RNFS.DownloadDirectoryPath
  //         }/${tempFolderName}/koodoo-crm-user-template.xlsx`;
  //       var excelWorkSheet = XLSX.utils.json_to_sheet(excelTemplate);
  //       var excelWorkBook = XLSX.utils.book_new();

  //       excelWorkSheet = autofitColumns(excelTemplate, excelWorkSheet);

  //       XLSX.utils.book_append_sheet(
  //         excelWorkBook,
  //         excelWorkSheet,
  //         'Crm User Template',
  //       );

  //       const workBookData = XLSX.write(excelWorkBook, {
  //         type: 'binary',
  //         bookType: 'xlsx',
  //       });

  //       RNFS.writeFile(excelFile, workBookData, 'ascii')
  //         .then((success) => {
  //           // console.log(`wrote file ${excelFile}`);

  //           // zip the folder

  //           const tempZipPath = `${Platform.OS === 'ios'
  //             ? RNFS.DocumentDirectoryPath
  //             : RNFS.DownloadDirectoryPath
  //             }/${tempFolderName}.zip`;

  //           try {
  //             // try unlink zip first to make sure clean zip file

  //             RNFS.unlink(tempZipPath);
  //           } catch (ex) {
  //             console.error(ex);
  //           }

  //           zip(tempFolderPath, tempZipPath)
  //             .then((path) => {
  //               // console.log(`zip completed at ${path}`);

  //               RNFS.unlink(tempFolderPath);

  //               Alert.alert(
  //                 'Success',
  //                 `Sent to ${tempZipPath}`,
  //                 [{ text: 'OK', onPress: () => { } }],
  //                 { cancelable: false },
  //               );
  //             })
  //             .catch((error) => {
  //               console.error(error);

  //               Alert.alert('Error', 'Failed to export template');
  //             });
  //         })
  //         .catch((err) => {
  //           // console.log(err.message);

  //           Alert.alert('Error', 'Failed to export template');
  //         });
  //     } else {
  //       Alert.alert(
  //         'Info',
  //         'Please set the merchant logo first before proceed.',
  //       );
  //     }
  //   } catch (ex) {
  //     console.error(ex);

  //     Alert.alert('Error', 'Failed to export template');
  //   }
  // };
  // Navigation bar
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );
  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus
  );

  var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));

  const userName = UserStore.useState((s) => s.name);

  const [openO, setOpenO] = useState(false);

  useEffect(() => {
    CommonStore.update((s) => {
      s.outletSelectDropdownView = () => {
        return (
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              borderRadius: 8,
              width: 200,
              backgroundColor: "white",
            }}
          >
            {currOutletId.length > 0 &&
              allOutlets.find((item) => item.uniqueId === currOutletId) ? (
                <DropDownPicker
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: 200,
                  height: 40,
                  borderRadius: 10,
                  borderWidth: 1,
                  borderColor: "#E5E5E5",
                  flexDirection: "row",
                }}
                dropDownContainerStyle={{
                  width: 200,
                  backgroundColor: Colors.fieldtBgColor,
                  borderColor: "#E5E5E5",
                }}
                labelStyle={{
                  marginLeft: 5,
                  flexDirection: "row",
                }}
                textStyle={{
                  fontSize: 14,
                  fontFamily: 'NunitoSans-Regular',

                  marginLeft: 5,
                  paddingVertical: 10,
                  flexDirection: "row",
                }}
                selectedItemContainerStyle={{
                  flexDirection: "row",
                }}

                showArrowIcon={true}
                ArrowDownIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-down-outline"
                  />
                )}
                ArrowUpIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-up-outline"
                  />
                )}

                showTickIcon={true}
                TickIconComponent={({ press }) => (
                  <Ionicon
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    color={
                      press ? Colors.fieldtBgColor : Colors.primaryColor
                    }
                    name={'md-checkbox'}
                    size={25}
                  />
                )}
                placeholderStyle={{
                  color: Colors.fieldtTxtColor,
                  // marginTop: 15,
                }}
                dropDownDirection="BOTTOM"
                placeholder="Choose Outlet"
                items={targetOutletDropdownListTemp}
                value={currOutletId}
                onSelectItem={(item) => {
                  if (item) { // if choose the same option again, value = ''
                    MerchantStore.update((s) => {
                      s.currOutletId = item.value;
                      s.currOutlet =
                        allOutlets.find(
                          (outlet) => outlet.uniqueId === item.value
                        ) || {};
                    });
                  }

                  CommonStore.update((s) => {
                    s.shiftClosedModal = false;
                  });
                }}
                open={openO}
                setOpen={setOpenO}
              />
            ) : (
              <ActivityIndicator size={"small"} color={Colors.whiteColor} />
            )}

            {/* <Select

              placeholder={"Choose Outlet"}
              onChange={(items) => {
                setSelectedOutletList(items);
              }}
              options={outletDropdownList}
              isMulti
            /> */}
          </View>
        );
      };
    });
  }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Details
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo Manager")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
              style={{
                width: windowHeight * 0.05,
              height: windowHeight * 0.05,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const renderTimeSlot = ({ item }) => {
    return (
      <View style={{
        width: windowWidth * 0.07,
        maxWidth: 200,
        height: 50,
        paddingVertical: 10,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center',
        marginVertical: 10,
        marginHorizontal: 5,
        backgroundColor: isSelected === item.time ? Colors.primaryColor : Colors.fieldtBgColor,

        position: 'relative',
        zIndex: 1,

        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,

        zIndex: -10,
      }}>
        {/* {sectionSelection && (selectedReservationStartTime === moment(`${moment(reservationDate).format('DD MMM YYYY')} ${item.time}`, 'DD MMM YYYY hh:mm A').valueOf()) ?
          <View
            style={{
              position: 'absolute',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 0,
              top: -windowHeight * 0.2,
            }}
          >
            <View
              style={{
                height: windowHeight * 0.2,
                width: windowWidth * 0.45,
                backgroundColor: Colors.whiteColor,
                borderRadius: 12,
                padding: 20,
                justifyContent: 'center'
              }}
            >
              {outletSections.length === 0 ?
                <View style={{
                  zIndex: -1,
                  // width: windowWidth * 0.9,
                  // height: '100%',
                  alignSelf: 'center',
                  // marginTop: 20,
                  alignItems: 'center',
                  justifyContent: 'center',

                  // marginTop: windowHeight * 0.1,
                  // backgroundColor: 'red',
                }}>
                  <ActivityIndicator color={Colors.primaryColor} size={"large"} />
                </View>
                :
                <View
                  style={{
                    // alignItems: 'center',
                    justifyContent: 'center',
                    marginTop: 10,
                  }}>
                  <ScrollView style={{ height: windowHeight * 0.18, paddingBottom: 5, }}>
                    <FlatList
                      data={outletSections}
                      renderItem={renderOutletSection}
                      keyExtractor={(item, index) => index}
                      contentContainerStyle={{ height: windowHeight * 0.2 }}
                    />
                  </ScrollView>
                </View>
              }
            </View>
          </View>
          : <></>} */}

        <TouchableOpacity
          style={{}}
          onPress={async () => {
            if (isSelected === item.time) {
              setIsSelected('');
            }
            else {
              setIsSelected(item.time);
              const parsedTime = moment(item.time, "hh:mm A").valueOf();

              //console.log('HELLO',moment(item.time, ["h:mm A"]).format('hh:mm').valueOf())
              setReservationTime(parsedTime);

              setTimeSelected(item.time);

              setSelectTable('');
              setSelectedOutletTableId('');

              CommonStore.update((s) => {
                // s.selectedReservationId = reservationId;
                // s.selectedReservationStartTime = parsedDateTime;
                // s.selectedReservationPax = paxValue;

                // s.selectedAddressUserPhone = userPhone;
                // s.selectedUserEmail = userEmail;
                // s.selectedUserFirstName = userFirstName;
                // s.selectedUserLastName = userLastName;
                // s.selectedUserRemarks = remarks;
                // s.selectedUserDiet = dRestrictions;
                // s.selectedUserOccasion = sOccasions;
              });

              // if (selectedReservationId) {
              //   // if is existed reservation, just skip

              //   const subdomain = await AsyncStorage.getItem("latestSubdomain");

              //   linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation-info`);

              //   TempStore.update(s => {
              //     s.supplementModal = false;
              //   });
              // }
              // else {
              //   TempStore.update(s => {
              //     s.supplementModal = true;
              //   });
              // }
              //setSectionSelection(true)

              // console.log('=========outlet section check========', outletSections);

              // const subdomain = await AsyncStorage.getItem("latestSubdomain");
              // linkTo && linkTo(`${prefix}/outlet/${subdomain}/reservation-info/${reservationId}`);
            }
          }}
        >
          <View style={{ position: 'relative' }}>
            <Text style={{ fontSize: 14, fontFamily: 'NunitoSans-Bold', color: isSelected === item.time ? Colors.whiteColor : Colors.blackColor, }}>
              {item.time}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    )
  };

  return (
    // <UserIdleWrapper disabled={!isMounted}>
    <View
      style={[
        styles.container,
        // !isTablet()
        //   ? {
        //     transform: [{ scaleX: 1 }, { scaleY: 1 }],
        //   }
        //   : {},
        {
          ...getTransformForScreenInsideNavigation(),
        }
      ]}>
      {/* Sidebar */}
      <View
        style={[
          styles.sidebar,
          {
            width: windowWidth * 0.08,
          },
          switchMerchant
            ? {
              width: '10%',
            }
            : {},
          {
            width: windowWidth * 0.08,
          }
        ]}>
        <SideBar
          navigation={props.navigation}
          selectedTab={1}
          expandOperation
        />
      </View>
      {/* <DateTimePickerModal
        locale="en_GB"
        isVisible={showReservationDatePicker}
        mode={'date'}
        minimumDate={Date.now()}
        // date={effectiveStartTime}
        onConfirm={(text) => {
          setReservationDate(moment(text));

          setShowReservationDatePicker(false);

          setSelectTable('');
          setSelectedOutletTableId('');
        }}
        onCancel={() => {
          setShowReservationDatePicker(false);
        }}
      /> */}
      <DateTimePickerModal
        locale="en_GB"
        isVisible={showReservationTimePicker}
        mode={"time"}
        // date={moment().startOf("day").toDate()}
        onConfirm={(text) => {
          setReservationTime(moment(text).valueOf());

          setShowReservationTimePicker(false);

          setIsSelected(moment(text).format('h:mm A'));

          setTimeSelected(moment(text).format('h:mm A'));
        }}
        onCancel={() => {
          setShowReservationTimePicker(false);
        }}
      />
      {addReservation ? (
        <>
          <Modal
            supportedOrientations={['landscape', 'portrait']}
            style={{}}
            visible={showCustomerList}
            transparent>
            <View
              style={{
                flex: 1,
                backgroundColor: Colors.modalBgColor,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <View
                style={{
                  height: windowHeight * 0.8,
                  width: 500,
                  backgroundColor: Colors.whiteColor,
                  //borderRadius: windowWidth * 0.03,
                  borderRadius: 12,
                  padding: 50,
                  alignItems: 'center',
                  justifyContent: 'center',

                  ...getTransformForModalInsideNavigation(),
                }}>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => {
                    setShowCustomerList(false);
                    setSelectedRegisteredCRMUserId('');
                  }}>
                  {switchMerchant ? (
                    <AIcon
                      name="closecircle"
                      size={15}
                      color={Colors.fieldtTxtColor}
                    />
                  ) : (
                    <AIcon
                      name="closecircle"
                      size={25}
                      color={Colors.fieldtTxtColor}
                    />
                  )}
                </TouchableOpacity>
                <View
                  style={{
                    marginTop: 15,
                  }}>
                  <Text
                    style={[
                      {
                        fontSize: 16,
                        color: 'black',
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Select Member
                  </Text>
                </View>
                <View style={{ justifyContent: 'center' }}>
                  <View
                    style={{
                      ...styles.modalBody,
                      justifyContent: 'space-around',
                    }}>
                    <KeyboardAvoidingView
                      style={{
                        flexDirection: 'row',
                        width: '100%',
                        height: 40,
                        borderBottomWidth: 1,
                        borderColor: Colors.fieldtBgColor2,
                        paddingHorizontal: switchMerchant ? 8 : 15,
                      }}>
                      <Feather
                        name="search"
                        size={switchMerchant ? 13 : 18}
                        color={Colors.primaryColor}
                        style={{ paddingTop: 11 }}
                      />
                      <TextInput
                        // editable={!loading}
                        underlineColorAndroid={Colors.whiteColor}
                        style={
                          switchMerchant
                            ? {
                              width: 180,
                              fontSize: 10,
                              // borderWidth: 1,
                              fontFamily: 'NunitoSans-Regular',
                              height: '100%',
                              height: 40,
                            }
                            : {
                              width: 220,
                              fontSize: 15,
                              fontFamily: 'NunitoSans-Regular',
                              paddingLeft: 5,
                              height: 40,
                            }
                        }
                        clearButtonMode="while-editing"
                        placeholder=" Search"
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        onChangeText={(text) => {
                          setSearchCustomer(text);
                        }}
                        value={searchCustomer}
                      />
                      <TouchableOpacity
                        style={{ paddingTop: 11 }}
                        onPress={() => {
                          setSearchCustomer('');
                        }}>
                        {switchMerchant ? (
                          <AIcon
                            name="closecircle"
                            size={10}
                            color={Colors.fieldtTxtColor}
                          />
                        ) : (
                          <AIcon
                            name="closecircle"
                            size={20}
                            color={Colors.fieldtTxtColor}
                          />
                        )}
                      </TouchableOpacity>
                    </KeyboardAvoidingView>
                  </View>
                  <View
                    style={{
                      height: windowHeight * 0.57,
                      marginVertical: 5,
                      width: 400,
                    }}>
                    <FlatList
                      data={registeredCRMUsersDropdownList
                        .slice(0)
                        .filter((item) => {
                          if (searchCustomer !== '') {
                            // // console.log(item, 'here');
                            const searchLowerCase =
                              searchCustomer.toLowerCase();

                            // // console.log(item.label.includes(searchLowerCase), 'here here');
                            if (
                              item.label
                                .toLowerCase()
                                .includes(searchLowerCase) ||
                              item.phoneNumber
                                .toLowerCase()
                                .includes(searchLowerCase)
                            ) {
                              return true;
                            } else {
                              return false;
                            }
                          } else {
                            return true;
                          }
                        })}
                      renderItem={renderCrmUser}
                      keyExtractor={(item, index) => String(index)}
                      showsVerticalScrollIndicator={false}
                    />
                  </View>
                </View>
                <View style={{ flexDirection: 'row', marginTop: 10 }}>
                  <TouchableOpacity
                    style={[
                      {
                        justifyContent: 'center',
                        width: 130,
                        height: 40,
                        flexDirection: 'row',
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        alignItems: 'center',
                        marginBottom: 15,
                      },
                      switchMerchant
                        ? {
                          fontSize: 12,
                        }
                        : {},
                    ]}
                    onPress={() => {
                      setCustomerName('');
                      setCustomerPhone('+60');
                      setCustomerEmail('');
                      setShowAddCustomer(true);
                    }}>
                    <Text
                      style={[
                        styles.modalDescText,
                        {
                          color: Colors.whiteColor,
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                        },
                      ]}>
                      REGISTER
                    </Text>
                  </TouchableOpacity>
                  <View style={{ width: 10 }} />
                  <TouchableOpacity
                    style={[
                      {
                        justifyContent: 'center',
                        width: 130,
                        height: 40,
                        flexDirection: 'row',
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        alignItems: 'center',
                      },
                      switchMerchant
                        ? {
                          fontSize: 12,
                        }
                        : {},
                    ]}
                    onPress={() => {
                      if (isCustomer) {
                        {
                          var crmUser = crmUsers.find(
                            (user) =>
                              user.userId === selectedRegisteredCRMUserId ||
                              user.email === selectedRegisteredCRMUserId,
                          );
                          // Alert.alert(
                          //   'Success',
                          //   'Customer has been added',
                          // );
                        }
                        setShowCustomerList(false);
                        // setSelectedRegisteredCRMUserId('');
                        setIsCustomer(false);
                        //       },
                        //     },
                        //   ],
                        //   {cancelable: true},
                        // );
                      } else {
                        window.confirm(
                          "Info, Please select one customer to proceed."
                        );
                      }
                    }}>
                    <Text
                      style={[
                        styles.modalDescText,
                        {
                          color: Colors.whiteColor,
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                        },
                      ]}>
                      DONE
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Modal>

          <Modal
            supportedOrientations={['landscape', 'portrait']}
            style={{ flex: 1 }}
            visible={showAddCustomer}
            transparent>
            <View style={styles.modalContainer}>
              <View
                style={[
                  styles.modalView,
                  {
                    height: windowHeight * 0.8,
                    width: windowWidth * 0.7,

                    padding: Dimensions.get('window').width * 0.02,
                    paddingHorizontal: Dimensions.get('window').width * 0,

                    ...getTransformForModalInsideNavigation(),
                  },
                ]}>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => {
                    setShowAddCustomer(false);
                  }}>
                  {switchMerchant ? (
                    <AIcon
                      name="closecircle"
                      size={15}
                      color={Colors.fieldtTxtColor}
                    />
                  ) : (
                    <AIcon
                      name="closecircle"
                      size={25}
                      color={Colors.fieldtTxtColor}
                    />
                  )}
                </TouchableOpacity>
                <View
                  style={{
                    marginTop: 20,
                    marginBottom: 20,
                  }}>
                  <Text
                    style={[
                      {
                        fontSize: 22,
                        color: 'black',
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    Add New Member
                  </Text>
                </View>
                <View
                  style={{
                    ...styles.modalBody,
                    justifyContent: 'space-around',
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      flex: 1,
                      paddingHorizontal: switchMerchant ? 8 : 15,
                      // borderWidth: 1,
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        width: '38%',
                        alignItems: 'center',
                        justifyContent: 'flex-end',
                        // borderWidth: 1,
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 20,
                          // color: '#9FA2B4',
                          fontFamily: 'Nunitosans-Bold',
                        }}>
                        Name *
                      </Text>
                      <TextInput
                        // editable={selectedCustomerEdit === null}
                        style={{
                          // backgroundColor: Colors.fieldtBgColor,
                          width: switchMerchant ? 100 : 160,
                          height: switchMerchant ? 30 : 35,
                          borderRadius: 5,
                          borderBottomWidth: 1,
                          padding: 5,
                          marginVertical: 5,
                          // borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                          marginLeft: 5,
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 20,
                        }}
                        placeholder="Johnson"
                        placeholderStyle={{
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        onChangeText={(text) => {
                          setCustomerName(text);
                        }}
                        defaultValue={customerName}
                      //value={}
                      />
                    </View>

                    {/* <View
                      style={{
                        flexDirection: 'row',
                        width: '38%',
                        alignItems: 'center',
                      }}>
                      <Text
                        style={{
                          fontSize: switchMerchant ? 10 : 14,
                          color: '#9FA2B4',
                          fontFamily: 'Nunitosans-Bold',
                        }}>
                        Phone Number *
                      </Text>
                      <TextInput
                        // editable={selectedCustomerEdit === null}
                        style={{
                          // backgroundColor: Colors.fieldtBgColor,
                          width: switchMerchant ? 100 : 160,
                          height: switchMerchant ? 30 : 35,
                          borderRadius: 5,
                          borderBottomWidth: 1,
                          padding: 5,
                          marginVertical: 5,
                          // borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                          marginLeft: 5,
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholder="012-345678"
                        placeholderStyle={{
                          fontFamily: 'NunitoSans-Regular',
                          fontSize: switchMerchant ? 10 : 14,
                        }}
                        placeholderTextColor={Platform.select({
                          ios: '#a9a9a9',
                        })}
                        onChangeText={(text) => {
                          setCustomerPhone(text);
                        }}
                        defaultValue={customerPhone}
                        //value={}
                        keyboardType="decimal-pad"
                      />
                    </View> */}
                    <View
                      style={{
                        width:
                          Platform.OS === 'ios'
                            ? windowWidth * 0.42
                            : windowWidth * 0.43,
                        backgroundColor: Colors.whiteColor,
                        borderRadius: 5,
                      }}>
                      <View
                        style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                          alignContent: 'center',
                          flex: 1,
                          paddingTop: Platform.OS === 'ios' ? 0 : 10,
                          marginTop: Platform.OS === 'ios' ? 10 : 0,
                        }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 15 : 25,
                            fontWeight: 'bold',
                          }}>
                          Enter Your Phone Number
                        </Text>
                      </View>
                      <View
                        style={{
                          justifyContent: 'center',
                          alignItems: 'center',
                          flex: 1,
                        }}>
                        <Text style={{ fontSize: switchMerchant ? 22 : 45 }}>
                          {customerPhone}
                        </Text>
                      </View>
                      <View style={{ justifyContent: 'center' }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            flexWrap: 'wrap',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            alignSelf: 'center',
                            width: Platform.OS === 'ios' ? 270 : '45%',
                            paddingTop: switchMerchant ? 10 : 30,
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              alignSelf: 'center',
                              alignItems: 'center',
                              width: '100%',
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(1);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : { width: 70, height: 70 },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  1
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(2);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : { width: 70, height: 70 },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  2
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(3);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : { width: 70, height: 70 },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  3
                                </Text>
                              </View>
                            </TouchableOpacity>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              alignSelf: 'center',
                              alignItems: 'center',
                              width: '100%',
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(4);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : { width: 70, height: 70 },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  4
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(5);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : { width: 70, height: 70 },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  5
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(6);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : { width: 70, height: 70 },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  6
                                </Text>
                              </View>
                            </TouchableOpacity>
                          </View>

                          <View
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              alignSelf: 'center',
                              alignItems: 'center',
                              width: '100%',
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(7);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : { width: 70, height: 70 },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  7
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(8);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : { width: 70, height: 70 },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  8
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(9);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : { width: 70, height: 70 },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  9
                                </Text>
                              </View>
                            </TouchableOpacity>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                              alignSelf: 'center',
                              alignItems: 'center',
                              width: '100%',
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                if (customerPhone.includes('+')) {

                                }
                                else {
                                  PhoneonNumPadBtn('+');
                                }
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : { width: 70, height: 70 },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  +
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                PhoneonNumPadBtn(0);
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : { width: 70, height: 70 },
                                ]}>
                                <Text
                                  style={[
                                    styles.pinNo,
                                    { fontSize: switchMerchant ? 10 : 20 },
                                  ]}>
                                  0
                                </Text>
                              </View>
                            </TouchableOpacity>

                            <TouchableOpacity
                              onPress={() => {
                                if (customerPhone != '+6') {
                                  PhoneonNumPadBtn(-1);
                                }
                              }}>
                              <View
                                style={[
                                  styles.pinBtn,
                                  switchMerchant
                                    ? {
                                      width: switchMerchant ? 35 : 70,
                                      height: switchMerchant ? 35 : 70,
                                      marginBottom: 7,
                                    }
                                    : { width: 70, height: 70 },
                                ]}>
                                <Feather
                                  name="chevron-left"
                                  size={switchMerchant ? 13 : 30}
                                  color={'black'}
                                  style={{}}
                                />
                              </View>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
                <TouchableOpacity
                  style={[
                    {
                      justifyContent: 'center',
                      width: 130,
                      height: 40,
                      flexDirection: 'row',
                      backgroundColor: '#4E9F7D',
                      borderRadius: 5,
                      alignItems: 'center',
                    },
                    switchMerchant
                      ? {
                        fontSize: 12,
                      }
                      : {},
                  ]}
                  onPress={() => {
                    createCRMUser();
                    // console.log(customerPhone, 'here');
                  }}>
                  <Text
                    style={[
                      {
                        fontSize: 16,
                        color: 'white',
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    {isLoading ? 'LOADING...' : 'ADD'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </Modal>

          <ScrollView
            // showsVerticalScrollIndicator={false}
            style={{
              backgroundColor: Colors.whiteColor,
              width: windowWidth * 0.87,
              height: windowHeight * 0.86,
              marginTop: windowHeight * 0.03,
              marginHorizontal: 30,
              // marginBottom: windowHeight * 0.07,
              // alignSelf: 'center',
              borderRadius: 5,
              shadowOpacity: 0,
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.22,
              shadowRadius: 3.22,
              elevation: 3,

              paddingBottom: 50,
            }}
          // contentContainerStyle={{
          //   paddingBottom: 50,
          // }}
          >
            <View
              style={[
                {
                  height: showReservationTimePicker ? '110%' : '100%',
                },
              ]}>
              <View style={{ marginTop: 20 }}>
                <Text
                  style={[
                    {
                      fontSize: 40,
                      justifyContent: 'center',
                      alignSelf: 'center',
                      fontFamily: 'NunitoSans-Bold',
                    },
                    switchMerchant
                      ? {
                        fontSize: 20,
                      }
                      : {},
                  ]}>
                  {reservationId ? 'Edit Reservation' : 'Add Reservation'}
                </Text>
                <Text
                  style={[
                    {
                      fontSize: 16,
                      justifyContent: 'center',
                      alignSelf: 'center',
                      marginTop: 5,
                      color: Colors.descriptionColor,
                      fontFamily: 'NunitoSans-Regular',
                    },
                    switchMerchant
                      ? {
                        fontSize: 10,
                      }
                      : {},
                  ]}>{`There are currently ${userReservations.filter(
                    (reservation) =>
                      reservation.status !== USER_RESERVATION_STATUS.CANCELED ||
                      reservation.status !== USER_RESERVATION_STATUS.NO_SHOW,
                  ).length
                    } active reservations.`}</Text>
              </View>
              <ScrollView
                style={[
                  { width: '100%', paddingLeft: 20, paddingRight: 20, paddingBottom: 10, },
                  switchMerchant
                    ? {
                      // borderWidth: 1,
                      marginTop: windowHeight * 0.16,
                      bottom: windowHeight * 0.12,
                    }
                    : {},
                ]}>
                <View
                  style={[
                    {
                      //justifyContent: 'center',
                      //alignSelf: 'center',
                      //alignContent: 'center',
                      marginTop: 20,
                      flexDirection: 'row',
                      width: '100%',
                      paddingLeft: windowWidth * 0.01,
                    },
                    switchMerchant
                      ? {
                        marginTop: 1,
                        // borderWidth: 1
                      }
                      : {},
                  ]}>
                  <View style={{ justifyContent: 'center', width: 100 }}>
                    <Text
                      style={[
                        {
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          fontWeight: '500',
                          color: 'black'
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            left: windowWidth * 0.037,
                          }
                          : {},
                      ]}>
                      Guest
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center', marginLeft: '5%', flexDirection: 'row' }}>
                    <TouchableOpacity
                      style={[
                        {
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 5,
                          width: 200,
                          paddingHorizontal: 10,
                          height: 35,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        },
                        switchMerchant
                          ? {
                            width: windowWidth * 0.26,
                            height: windowHeight * 0.08,
                            left: windowWidth * 0.003,
                          }
                          : {},
                      ]}
                      onPress={() => {
                        setSearchCustomer('');
                        setShowCustomerList(true);
                      }}>
                      <Text
                        style={[
                          {
                            color: Colors.blackColor,
                            marginLeft: 5,
                            fontSize: 16,
                            fontFamily: 'NunitoSans-Bold',
                            //textTransform: 'uppercase',
                            width: '100%',
                            textAlign: 'center',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}
                        numberOfLines={1}>
                        {customerName ? customerName : 'SELECT MEMBER'}
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={{
                        marginLeft: 10,
                        justifyContent: 'center',
                      }}
                      onPress={() => {
                        setCustomerName('');
                        setCustomerPhone('+60');
                        setCustomerEmail('');
                        setSelectedRegisteredCRMUserId('');
                      }}
                    >
                      <Icon
                        name="reload"
                        color={Colors.primaryColor}
                        size={24}
                      />
                    </TouchableOpacity>
                  </View>
                </View>

                <View
                  style={{
                    //justifyContent: 'center',
                    //alignSelf: 'center',
                    //alignContent: 'center',
                    marginTop: switchMerchant ? 10 : 30,
                    flexDirection: 'row',
                    width: '100%',
                    paddingLeft: windowWidth * 0.01,
                    zIndex: 1000,
                  }}>
                  <View style={{ justifyContent: 'center', width: 100 }}>
                    <Text
                      style={[
                        {
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          fontWeight: '500',
                          color: 'black'
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            left: windowWidth * 0.037,
                          }
                          : {},
                      ]}>
                      Pax
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center', marginLeft: '5%', zIndex: 1000 }}>
                    <DropDownPicker
                      style={{
                        backgroundColor: Colors.fieldtBgColor,
                        width: 200,
                        height: 40,
                        borderRadius: 10,
                        borderWidth: 1,
                        borderColor: "#E5E5E5",
                        flexDirection: "row",
                      }}
                      dropDownContainerStyle={{
                        width: 200,
                        backgroundColor: Colors.fieldtBgColor,
                        borderColor: "#E5E5E5",
                      }}
                      labelStyle={{
                        marginLeft: 5,
                        flexDirection: "row",
                      }}
                      textStyle={{
                        fontSize: 14,
                        fontFamily: 'NunitoSans-Regular',

                        marginLeft: 5,
                        paddingVertical: 10,
                        flexDirection: "row",
                      }}
                      selectedItemContainerStyle={{
                        flexDirection: "row",
                      }}

                      showArrowIcon={true}
                      ArrowDownIconComponent={({ style }) => (
                        <Ionicon
                          size={25}
                          color={Colors.fieldtTxtColor}
                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                          name="chevron-down-outline"
                        />
                      )}
                      ArrowUpIconComponent={({ style }) => (
                        <Ionicon
                          size={25}
                          color={Colors.fieldtTxtColor}
                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                          name="chevron-up-outline"
                        />
                      )}

                      showTickIcon={true}
                      TickIconComponent={({ press }) => (
                        <Ionicon
                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                          color={
                            press ? Colors.fieldtBgColor : Colors.primaryColor
                          }
                          name={'md-checkbox'}
                          size={25}
                        />
                      )}
                      placeholderStyle={{
                        color: Colors.fieldtTxtColor,
                        // marginTop: 15,
                      }}
                      dropDownDirection="BOTTOM"
                      placeholder={seatingPax}
                      items={Object.entries(pax).map(([key, value]) => {
                        return {
                          label: value.toString(),
                          value: value.toString(),
                        };
                      })}
                      value={seatingPax}
                      onSelectItem={(item) => {
                        setSeatingPax(item.value);
                      }}
                      open={openPax}
                      setOpen={setOpenPax}
                    />
                  </View>
                </View>

                <View
                  style={{
                    //justifyContent: 'center',
                    //alignSelf: 'center',
                    //alignContent: 'center',
                    marginTop: switchMerchant ? 10 : 30,
                    flexDirection: 'row',
                    width: '100%',
                    paddingLeft: windowWidth * 0.01,
                    zIndex: 10
                  }}>
                  <View style={{ justifyContent: 'center', width: 100 }}>
                    <Text
                      style={[
                        {
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          fontWeight: '500',
                          color: 'black'
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            left: windowWidth * 0.037,
                          }
                          : {},
                      ]}>
                      Date
                    </Text>
                  </View>

                  <View
                    style={{
                      justifyContent: 'center',
                      marginLeft: '5%',
                      flexDirection: 'row',
                      zIndex: 10,
                    }}>
                    <View
                      style={{
                        width: 100,
                        height: 40,
                        // backgroundColor: '#fafafa',
                        // borderRadius: 10,
                        marginRight: 10,
                        zIndex: 10,
                        //marginLeft: 10,
                      }}>
                      <DropDownPicker
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: 80,
                          height: 40,
                          borderRadius: 10,
                          borderWidth: 1,
                          borderColor: "#E5E5E5",
                          flexDirection: "row",
                        }}
                        dropDownContainerStyle={{
                          width: 80,
                          backgroundColor: Colors.fieldtBgColor,
                          borderColor: "#E5E5E5",
                        }}
                        labelStyle={{
                          marginLeft: 5,
                          flexDirection: "row",
                        }}
                        textStyle={{
                          fontSize: 14,
                          fontFamily: 'NunitoSans-Regular',

                          marginLeft: 5,
                          paddingVertical: 10,
                          flexDirection: "row",
                        }}
                        selectedItemContainerStyle={{
                          flexDirection: "row",
                        }}

                        showArrowIcon={true}
                        ArrowDownIconComponent={({ style }) => (
                          <Ionicon
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            name="chevron-down-outline"
                          />
                        )}
                        ArrowUpIconComponent={({ style }) => (
                          <Ionicon
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            name="chevron-up-outline"
                          />
                        )}

                        showTickIcon={true}
                        TickIconComponent={({ press }) => (
                          <Ionicon
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            color={
                              press ? Colors.fieldtBgColor : Colors.primaryColor
                            }
                            name={'md-checkbox'}
                            size={25}
                          />
                        )}
                        placeholderStyle={{
                          color: Colors.fieldtTxtColor,
                          // marginTop: 15,
                        }}
                        dropDownDirection="BOTTOM"
                        placeholder={selectedResDay}
                        items={Object.entries(resDays).map(([key, value]) => {
                          return { label: value.toString(), value: value.toString() }
                        })}
                        value={selectedResDay}
                        onSelectItem={(item) => {
                          setSelectedResDay(item.value);
                          //// console.log(selectedResDay)
                          setDateSelectedTimestamp(moment(item.value.timestamp).startOf('day').format('DD'));
                        }}
                        open={openDate}
                        setOpen={setOpenDate}
                      />
                    </View>

                    <View
                      style={{
                        width: 120,
                        height: 40,
                        //backgroundColor: '#fafafa',
                        //borderRadius: 10,
                        // marginRight: 10,
                        zIndex: 10,
                      }}>
                      <DropDownPicker
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: 80,
                          height: 40,
                          borderRadius: 10,
                          borderWidth: 1,
                          borderColor: "#E5E5E5",
                          flexDirection: "row",
                        }}
                        dropDownContainerStyle={{
                          width: 80,
                          backgroundColor: Colors.fieldtBgColor,
                          borderColor: "#E5E5E5",
                        }}
                        labelStyle={{
                          marginLeft: 5,
                          flexDirection: "row",
                        }}
                        textStyle={{
                          fontSize: 14,
                          fontFamily: 'NunitoSans-Regular',

                          marginLeft: 5,
                          paddingVertical: 10,
                          flexDirection: "row",
                        }}
                        selectedItemContainerStyle={{
                          flexDirection: "row",
                        }}

                        showArrowIcon={true}
                        ArrowDownIconComponent={({ style }) => (
                          <Ionicon
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            name="chevron-down-outline"
                          />
                        )}
                        ArrowUpIconComponent={({ style }) => (
                          <Ionicon
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            name="chevron-up-outline"
                          />
                        )}

                        showTickIcon={true}
                        TickIconComponent={({ press }) => (
                          <Ionicon
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            color={
                              press ? Colors.fieldtBgColor : Colors.primaryColor
                            }
                            name={'md-checkbox'}
                            size={25}
                          />
                        )}
                        placeholderStyle={{
                          // color: Colors.fieldtTxtColor,
                          // marginTop: 15,
                        }}
                        dropDownDirection="BOTTOM"
                        placeholder={selectedResMonth}
                        items={Object.entries(resMonths).map(([key, value]) => {
                          return { label: value.toString(), value: value.toString() }
                        })}
                        value={selectedResMonth}
                        onSelectItem={(item) => {
                          setSelectedResMonth(item.value);
                          //// console.log(selectedResMonth)
                          setDateSelectedTimestamp(moment(item.value.timestamp).startOf('day'));
                        }}
                        open={openMonth}
                        setOpen={setOpenMonth}
                      />
                    </View>
                    <View
                      style={{
                        width: 140,
                        height: 40,
                        //backgroundColor: '#fafafa',
                        //borderRadius: 10,
                        marginRight: 10,
                        zIndex: 10,
                      }}>
                      <DropDownPicker
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: 80,
                          height: 40,
                          borderRadius: 10,
                          borderWidth: 1,
                          borderColor: "#E5E5E5",
                          flexDirection: "row",
                        }}
                        dropDownContainerStyle={{
                          width: 80,
                          backgroundColor: Colors.fieldtBgColor,
                          borderColor: "#E5E5E5",
                        }}
                        labelStyle={{
                          marginLeft: 5,
                          flexDirection: "row",
                        }}
                        textStyle={{
                          fontSize: 14,
                          fontFamily: 'NunitoSans-Regular',

                          marginLeft: 5,
                          paddingVertical: 10,
                          flexDirection: "row",
                        }}
                        selectedItemContainerStyle={{
                          flexDirection: "row",
                        }}

                        showArrowIcon={true}
                        ArrowDownIconComponent={({ style }) => (
                          <Ionicon
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            name="chevron-down-outline"
                          />
                        )}
                        ArrowUpIconComponent={({ style }) => (
                          <Ionicon
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            name="chevron-up-outline"
                          />
                        )}

                        showTickIcon={true}
                        TickIconComponent={({ press }) => (
                          <Ionicon
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            color={
                              press ? Colors.fieldtBgColor : Colors.primaryColor
                            }
                            name={'md-checkbox'}
                            size={25}
                          />
                        )}
                        placeholderStyle={{
                          color: Colors.fieldtTxtColor,
                          // marginTop: 15,
                        }}
                        dropDownDirection="BOTTOM"
                        placeholder={selectedResYear}
                        items={Object.entries(resYears).map(([key, value]) => {
                          return { label: value.toString(), value: value.toString() }
                        })}
                        value={selectedResYear}
                        onSelectItem={(item) => {
                          setSelectedResYear(item.value);
                          //// console.log(selectedResYear)
                          setDateSelectedTimestamp(moment(item.value.timestamp).startOf('day'));
                        }}
                        open={openYear}
                        setOpen={setOpenYear}
                      />
                    </View>
                  </View>
                </View>

                <View
                  style={{
                    //justifyContent: 'center',
                    //alignSelf: 'center',
                    //alignContent: 'center',
                    marginTop: switchMerchant ? 10 : 20,
                    flexDirection: 'row',
                    width: '100%',
                    paddingLeft: windowWidth * 0.01,
                    zIndex: -10,
                  }}>
                  <View style={{ justifyContent: 'center', width: 100 }}>
                    <Text
                      style={[
                        {
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          fontWeight: '500',
                          color: 'black'
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            left: windowWidth * 0.037,
                          }
                          : {},
                      ]}>
                      Time
                    </Text>
                  </View>

                  {/* Initial Time select */}
                  {/* <View
                      style={{
                        justifyContent: 'center',
                        marginLeft: '5%',
                        flexDirection: 'row',
                      }}>
                      <View
                        style={{
                          width: 50,
                          height: 40,
                          //backgroundColor: '#fafafa',
                          //borderRadius: 10,
                          marginRight: 10,
                        }}>
                        <RNPickerSelect
                          useNativeAndroidPickerStyle={false}
                          style={pickerStyle2}
                          placeholder={{
                            label: selectedHour,
                            value: selectedHour,
                          }}
                          onValueChange={(item) => {
                            setSelectedHour(item);
                            //// console.log(selectedResMonth)
                          }}
                          items={Object.entries(resHour).map(([key, value]) => {
                            return {
                              label: value.toString(),
                              value: value.toString(),
                            };
                          })}
                          Icon={() => {
                            return (
                              <>
                                <Entypo
                                  name="triangle-up"
                                  size={16}
                                  color={Colors.blackColor}></Entypo>
                                <Entypo
                                  name="triangle-down"
                                  size={16}
                                  color={Colors.blackColor}></Entypo>
                              </>
                            );
                          }}
                        />
                      </View>
                      <View
                        style={{
                          width: 60,
                          height: 40,
                          //backgroundColor: '#fafafa',
                          //borderRadius: 10,
                          marginRight: 10,
                        }}>
                        <RNPickerSelect
                          useNativeAndroidPickerStyle={false}
                          style={pickerStyle3}
                          placeholder={{
                            label: selectedMin,
                            value: selectedMin,
                          }}
                          onValueChange={(item) => {
                            setSelectedMin(item);
                          }}
                          items={Object.entries(resMin).map(([key, value]) => {
                            return {
                              label: value.toString(),
                              value: value.toString(),
                            };
                          })}
                          Icon={() => {
                            return (
                              <>
                                <Entypo
                                  name="triangle-up"
                                  size={16}
                                  color={Colors.blackColor}></Entypo>
                                <Entypo
                                  name="triangle-down"
                                  size={16}
                                  color={Colors.blackColor}></Entypo>
                              </>
                            );
                          }}
                        />
                      </View>
                    </View> */}
                  <View
                    style={{
                      justifyContent: 'center',
                      marginLeft: '4.7%',
                      flexDirection: 'row',
                      zIndex: -10,
                    }}>
                    <View style={{
                      zIndex: -10,
                      width: windowWidth * 0.7,
                      alignSelf: 'center',
                      marginTop: 30,
                      alignItems: 'center',
                      justifyContent: 'center',
                      // backgroundColor: 'blue',
                      // marginLeft: -50,
                    }}>
                      <View style={{ justifyContent: 'flex-start', alignItems: 'flex-start', alignSelf: 'flex-start', paddingLeft: 5, zIndex: -10, }}>
                        <Text style={{ fontSize: 14, fontWeight: '400', fontFamily: 'NunitoSans-SemiBold', }}>Breakfast</Text>
                      </View>
                      <FlatList
                        data={BreakFastTimeSelection}
                        renderItem={renderTimeSlot}
                        keyExtractor={(item, index) => String(index)}
                        //numColumns={5}
                        contentContainerStyle={{
                          paddingBottom: 10,
                          flexDirection: 'row',
                          flexWrap: 'wrap',
                          width: windowWidth * 0.7,
                          zIndex: -10,
                        }}
                      />
                      <View style={{ justifyContent: 'flex-start', alignItems: 'flex-start', alignSelf: 'flex-start', paddingLeft: 5, zIndex: -10, }}>
                        <Text style={{ fontSize: 14, fontWeight: '400', fontFamily: 'NunitoSans-SemiBold', }}>Lunch</Text>
                      </View>
                      <FlatList
                        data={LunchTimeSelection}
                        renderItem={renderTimeSlot}
                        keyExtractor={(item, index) => String(index)}
                        contentContainerStyle={{
                          paddingBottom: 10,
                          flexDirection: 'row',
                          flexWrap: 'wrap',
                          width: windowWidth * 0.7,
                          zIndex: -10,
                        }}
                      />
                      <View style={{ justifyContent: 'flex-start', alignItems: 'flex-start', alignSelf: 'flex-start', paddingLeft: 5, zIndex: -10, }}>
                        <Text style={{ fontSize: 14, fontWeight: '400', fontFamily: 'NunitoSans-SemiBold', }}>Dinner</Text>
                      </View>
                      <FlatList
                        data={DinnerTimeSelection}
                        renderItem={renderTimeSlot}
                        keyExtractor={(item, index) => String(index)}
                        contentContainerStyle={{
                          paddingBottom: 10,
                          flexDirection: 'row',
                          flexWrap: 'wrap',
                          width: windowWidth * 0.7,
                          zIndex: -10,
                        }}
                      />

                      <View style={{ justifyContent: 'flex-start', alignItems: 'flex-start', alignSelf: 'flex-start', paddingLeft: 5, }}>
                        <Text style={{ fontSize: 14, fontWeight: '400', fontFamily: 'NunitoSans-SemiBold', }}>Custom</Text>
                      </View>

                      <View
                        style={{
                          paddingBottom: 10,
                          // flexDirection: 'row',
                          // flexWrap: 'wrap',
                          width: windowWidth * 0.7,
                          zIndex: 1003,
                        }}
                      >
                        <View style={{
                          width: windowWidth * 0.14,
                          maxWidth: 400,
                          height: 50,
                          paddingVertical: 10,
                          borderRadius: 5,
                          alignItems: 'center',
                          justifyContent: 'center',
                          marginVertical: 10,
                          marginHorizontal: 5,
                          backgroundColor: (highlightCustomTime && timeSelected) ? Colors.primaryColor : Colors.fieldtBgColor,
                          // backgroundColor: Colors.fieldtBgColor,

                          position: 'relative',
                          zIndex: 1,

                          shadowOpacity: 0,
                          shadowColor: '#000',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 3,
                        }}>
                          <TouchableOpacity
                            style={{}}
                            onPress={async () => {
                              setShowReservationTimePicker(true);
                            }}>
                            <View style={{ position: 'relative' }}>
                              <Text style={{
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Bold',
                                // color: isSelected === item.time ? Colors.whiteColor : Colors.blackColor,
                                color: (highlightCustomTime && timeSelected) ? Colors.whiteColor : Colors.blackColor,
                              }}>
                                {(highlightCustomTime && timeSelected) ? timeSelected : `CUSTOM TIME`}
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {showReservationTimePicker ? (
                          <View
                            style={{
                              position: "absolute",
                              left: 10,
                              top: 60,
                              zIndex: 1003,
                            }}
                          >
                            <TimeKeeper
                              time={reservationTime}
                              onChange={(time) => {
                                setReservationTime(time.formatted12);


                                setIsSelected(moment(time).format('h:mm A'));

                                setTimeSelected(moment(time).format('h:mm A'));
                              }}
                              onDoneClick={() => {
                                setShowReservationTimePicker(false);
                              }}
                            ></TimeKeeper>
                          </View>
                        ) : null}
                      </View>

                      {/* <View style={{
                          paddingBottom: 10,
                          flexDirection: 'row',
                          flexWrap: 'wrap',
                          width: windowWidth * 0.8,

                          backgroundColor: 'red'
                        }}>

                        </View> */}
                    </View>
                  </View>
                </View>

                <View
                  style={[
                    {
                      //justifyContent: 'center',
                      //alignSelf: 'center',
                      //alignContent: 'center',
                      marginTop: 20,
                      flexDirection: 'row',
                      width: '100%',
                      paddingLeft: windowWidth * 0.01,
                      zIndex: -11,
                    },
                    switchMerchant
                      ? {
                        marginTop: 1,
                        // borderWidth: 1
                      }
                      : {},
                  ]}>
                  <View style={{ justifyContent: 'center', width: 100 }}>
                    <Text
                      style={[
                        {
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          fontWeight: '500',
                          color: 'black'
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            left: windowWidth * 0.037,
                          }
                          : {},
                      ]}>
                      Table
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center', marginLeft: '5%', zIndex: -11 }}>
                    <TouchableOpacity
                      onPress={() => {
                        if (timeSelected === '' || isSelected === '') {
                          window.confirm(
                            "Info, Please select the timeslot first."
                          );
                        }
                        else {
                          setOpenSelectTable(true);
                          setSelectTable(true);
                        }
                      }}
                      style={[
                        {
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 5,
                          width: 200,
                          paddingHorizontal: 10,
                          height: 35,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        },
                        switchMerchant
                          ? {
                            width: windowWidth * 0.20,
                            height: windowHeight * 0.08,
                            left: windowWidth * 0.003,
                          }
                          : {},
                      ]}>
                      <Text
                        style={[
                          {
                            color: Colors.blackColor,
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: 16,
                            textAlign: 'center',
                          },
                          switchMerchant
                            ? {
                              fontSize: 10,
                            }
                            : {},
                        ]}>
                        {(selectTable === '')
                          ? 'SELECT TABLE'
                          :
                          (selectedOutletTable.uniqueId ? selectedOutletTable.code : 'SELECT TABLE')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View
                  style={[
                    {
                      //justifyContent: 'center',
                      //alignSelf: 'center',
                      //alignContent: 'center',
                      marginTop: 20,
                      flexDirection: 'row',
                      width: '100%',
                      paddingLeft: windowWidth * 0.01,
                      zIndex: -11,
                    },
                    switchMerchant
                      ? {
                        marginTop: 5,
                        // borderWidth: 1
                      }
                      : {},
                  ]}>
                  <View style={{ justifyContent: 'center', width: 100 }}>
                    <Text
                      style={[
                        {
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          fontWeight: '500',
                          color: 'black'
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            left: windowWidth * 0.037,
                          }
                          : {},
                      ]}>
                      Remarks
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center', marginLeft: '5%', zIndex: -11 }}>
                    <TextInput
                      multiline={true}
                      style={[
                        {
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 5,
                          width: 400,
                          paddingHorizontal: 10,
                          height: 105,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,

                          paddingTop: 10,
                        },
                        switchMerchant
                          ? {
                            width: windowWidth * 0.26,
                            height: windowHeight * 0.28,
                            left: windowWidth * 0.003,
                          }
                          : {},
                      ]}
                      clearButtonMode="while-editing"
                      placeholder=" Remarks"
                      placeholderTextColor={Platform.select({
                        ios: '#a9a9a9',
                      })}
                      onChangeText={(text) => {
                        setReservationRemark(text);
                      }}
                      value={reservationRemark}
                    />
                  </View>
                </View>

                <View
                  style={[
                    {
                      //justifyContent: 'center',
                      //alignSelf: 'center',
                      //alignContent: 'center',
                      marginTop: 20,
                      flexDirection: 'row',
                      width: '100%',
                      paddingLeft: windowWidth * 0.01,
                      zIndex: -11,
                    },
                    switchMerchant
                      ? {
                        marginTop: 5,
                        // borderWidth: 1
                      }
                      : {},
                  ]}>
                  <View style={{ justifyContent: 'center', width: 100 }}>
                    <Text
                      style={[
                        {
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          fontWeight: '500',
                          color: 'black'
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            left: windowWidth * 0.037,
                          }
                          : {},
                      ]}>
                      Dietary restrictions
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center', marginLeft: '5%', zIndex: -11 }}>
                    <TextInput
                      // multiline

                      style={[
                        {
                          // textAlignVertical: 'top',
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 5,
                          width: 400,

                          paddingHorizontal: 10,
                          // paddingTop: 5,

                          height: 45,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        },
                        switchMerchant
                          ? {
                            width: windowWidth * 0.26,
                            height: windowHeight * 0.28,
                            left: windowWidth * 0.003,
                          }
                          : {},
                      ]}
                      clearButtonMode="while-editing"
                      placeholder=" Vegetarian"
                      placeholderTextColor={Platform.select({
                        ios: '#a9a9a9',
                      })}
                      onChangeText={(text) => {
                        setDietaryRestrictions(text);
                      }}
                      value={dietaryRestrictions}
                    />
                  </View>
                </View>

                <View
                  style={[
                    {
                      //justifyContent: 'center',
                      //alignSelf: 'center',
                      //alignContent: 'center',
                      marginTop: 20,
                      flexDirection: 'row',
                      width: '100%',
                      paddingLeft: windowWidth * 0.01,
                      zIndex: -11,
                    },
                    switchMerchant
                      ? {
                        marginTop: 5,
                        // borderWidth: 1
                      }
                      : {},
                  ]}>
                  <View style={{ justifyContent: 'center', width: 100 }}>
                    <Text
                      style={[
                        {
                          fontSize: switchMerchant ? 10 : 14,
                          fontFamily: 'NunitoSans-Regular',
                          fontWeight: '500',
                          color: 'black'
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                            left: windowWidth * 0.037,
                          }
                          : {},
                      ]}>
                      Special occasions
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center', marginLeft: '5%', zIndex: -11 }}>
                    <TextInput
                      // multiline

                      style={[
                        {
                          // textAlignVertical: 'top',
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 5,
                          width: 400,

                          paddingHorizontal: 10,
                          // paddingTop: 5,

                          height: 45,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        },
                        switchMerchant
                          ? {
                            width: windowWidth * 0.26,
                            height: windowHeight * 0.28,
                            left: windowWidth * 0.003,
                          }
                          : {},
                      ]}
                      clearButtonMode="while-editing"
                      placeholder=" Birthday party"
                      placeholderTextColor={Platform.select({
                        ios: '#a9a9a9',
                      })}
                      onChangeText={(text) => {
                        setSpecialOccasions(text);
                      }}
                      value={specialOccasions}
                    />
                  </View>
                </View>
              </ScrollView>

              <View
                style={[
                  {
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginTop: 35,
                    zIndex: -1000,
                    paddingLeft: 55,
                    marginBottom: 15,
                  },
                  switchMerchant
                    ? {
                      // marginTop: windowHeight * 0.067,
                      // borderWidth: 1,
                      paddingLeft: 55,
                      marginBottom: 1,
                      position: 'absolute',
                      bottom: 0,
                      width: windowWidth * 0.45,
                      left: windowWidth * 0.002,
                    }
                    : {},
                ]}>
                <TouchableOpacity
                  disabled={isLoading}
                  onPress={() => {
                    if (seatingPax <= 0) {
                      if (window.confirm("Info, Pax cannot be 0.")) {
                        return;
                      }
                    }

                    if (reservationId) {
                      updateReservation(reservationId);
                    } else {
                      createReservation();
                    }
                  }}
                  style={{
                    justifyContent: 'center',
                    width: 130,
                    height: 40,
                    flexDirection: 'row',
                    backgroundColor: '#4E9F7D',
                    borderRadius: 5,
                    alignItems: 'center',
                  }}>
                  {isLoading ? (
                    <>
                      {switchMerchant ? (
                        <ActivityIndicator
                          size={'small'}
                          color={Colors.whiteColor}
                        />
                      ) : (
                        <ActivityIndicator
                          size={'small'}
                          color={Colors.whiteColor}
                        />
                      )}
                    </>
                  ) : (
                    <Text
                      style={[
                        {
                          fontSize: 16,
                          color: 'white',
                          fontFamily: 'NunitoSans-Bold',
                        },
                        switchMerchant
                          ? {
                            fontSize: 10,
                          }
                          : {},
                      ]}>
                      {reservationId ? 'UPDATE' : 'CONFIRM'}
                    </Text>
                  )}
                </TouchableOpacity>
                <TouchableOpacity
                  disabled={isLoading}
                  onPress={() => {
                    // setState({ visible: false });
                    setAddReservation(false);
                    setReservationId('');
                    setReservationToUsed(null);
                    setReservationCustomerName('');
                    setReservationPhone('');
                    setReservationDate(Date.now());
                    setReservationTime(Date.now());

                    setDietaryRestrictions('');
                    setSpecialOccasions('');

                    setTimeSelected('');
                    setIsSelected('');

                    setSeatingPax('1');
                    setSelectTable('');
                    setReservationRemark('');
                    setShowCustomerList(false);
                  }}
                  style={{
                    justifyContent: 'center',
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: '#BDBDBD',
                    borderRadius: 5,
                    alignItems: 'center',
                    backgroundColor: '#FFFFFF',
                    width: 130,
                    height: 40,
                    marginLeft: 50,
                  }}>
                  <Text
                    style={[
                      {
                        color: '#BDBDBD',
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      switchMerchant
                        ? {
                          fontSize: 10,
                        }
                        : {},
                    ]}>
                    CANCEL
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>
        </>
      ) : (
        <>
          <View style={{ backgroundColor: Colors.highlightColor, zIndex: -10, width: windowWidth }}>
            {/* Top bar */}
            <View
              style={[
                styles.topBar,
                switchMerchant
                  ? {}
                  : {
                    height: windowHeight * 0.1,
                    width: windowWidth * 0.92,
                  },
              ]}>
              {/* Added add reservation button */}
              <View style={{ justifyContent: 'center' }}>
                <TouchableOpacity
                  style={{
                    justifyContent: 'center',
                    //flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: '#4E9F7D',
                    borderRadius: 5,
                    width: Dimensions.get('window').width * 0.13,
                    height: Dimensions.get('window').height * 0.05,
                    paddingHorizontal: 10,
                    //height: switchMerchant ? 35 : 40,
                    alignItems: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                    zIndex: -1,
                    marginRight: 7,
                    marginBottom: 2,
                  }}
                  onPress={() => {
                    setReservationId('');
                    setReservationToUsed(null);

                    setAddReservation(true);
                  }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <AntDesign
                      name="pluscircle"
                      size={switchMerchant ? 10 : windowWidth < 1000 ? 15 : 20}
                      color={Colors.whiteColor}
                    />
                    <Text
                      style={[{
                        color: Colors.whiteColor,
                        marginLeft: 5,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                      },
                      !switchMerchant && windowWidth < 1000 ?
                        {
                          fontSize: 12,
                        }
                        : {}
                      ]}>
                      RESERVATION
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
              {/* <TouchableOpacity
            style={[
              styles.topBarButton,
              showReservations ? { backgroundColor: Colors.whiteColor } : {},
            ]}
            onPress={() => {
              setShowReservations(true);
              setShowFinished(false);
              setShowWaitlist(false);
            }}>
            <Text
              style={{
                textAlign: 'center',
              }}>
              Reservations
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.topBarButton,
              showFinished ? { backgroundColor: Colors.whiteColor } : {},
            ]}
            onPress={() => {
              setShowReservations(false);
              setShowFinished(true);
              setShowWaitlist(false);
            }}>
            <Text
              style={{
                textAlign: 'center',
              }}>
              Finished/CXL
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.topBarButton,
              showWaitlist ? { backgroundColor: Colors.whiteColor } : {},
            ]}
            onPress={() => {
              setShowReservations(false);
              setShowFinished(false);
              setShowWaitlist(true);
            }}>
            <Text
              style={{
                textAlign: 'center',
              }}>
              Waitlist
            </Text>
          </TouchableOpacity> */}

              {/* Search Bar */}

              <View style={[styles.search,
              {
                marginTop: 22,
                // height: Dimensions.get('window').height * 0.07,
              }]}>
                <Icon
                  name="search"
                  size={16}
                  color={Colors.primaryColor}
                  style={{ marginLeft: 15, marginTop: 1 }}
                />
                <TextInput
                  style={[
                    {
                      fontSize: 16,
                      fontFamily: 'NunitoSans-Regular',
                      paddingLeft: 5,
                      // height: windowHeight * 0.18,
                      // height: Dimensions.get('window').height * 0.168,
                      // height: '100%',
                    },
                    switchMerchant
                      ? { width: 220 }
                      : { width: windowWidth * 0.3 },
                  ]}
                  clearButtonMode="while-editing"
                  placeholder=" Search"
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  onChangeText={(text) => {
                    setSearch(text);
                  }}
                  value={search}
                />
              </View>

              {/* Added RNPickerSelect to select user reservation status*/}
              <View
                style={{
                  backgroundColor: '#fafafa',
                  marginHorizontal: 5,
                  justifyContent: 'center',
                  zIndex: 10,
                }}>
                <DropDownPicker
                  style={{
                    backgroundColor: Colors.fieldtBgColor,
                    width: 200,
                    height: 40,
                    borderRadius: 10,
                    borderWidth: 1,
                    borderColor: "#E5E5E5",
                    flexDirection: "row",
                  }}
                  dropDownContainerStyle={{
                    width: 200,
                    backgroundColor: Colors.fieldtBgColor,
                    borderColor: "#E5E5E5",
                  }}
                  labelStyle={{
                    marginLeft: 5,
                    flexDirection: "row",
                  }}
                  textStyle={{
                    fontSize: 14,
                    fontFamily: 'NunitoSans-Regular',

                    marginLeft: 5,
                    paddingVertical: 10,
                    flexDirection: "row",
                  }}
                  selectedItemContainerStyle={{
                    flexDirection: "row",
                  }}

                  showArrowIcon={true}
                  ArrowDownIconComponent={({ style }) => (
                    <Ionicon
                      size={25}
                      color={Colors.fieldtTxtColor}
                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                      name="chevron-down-outline"
                    />
                  )}
                  ArrowUpIconComponent={({ style }) => (
                    <Ionicon
                      size={25}
                      color={Colors.fieldtTxtColor}
                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                      name="chevron-up-outline"
                    />
                  )}

                  showTickIcon={true}
                  TickIconComponent={({ press }) => (
                    <Ionicon
                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                      color={
                        press ? Colors.fieldtBgColor : Colors.primaryColor
                      }
                      name={'md-checkbox'}
                      size={25}
                    />
                  )}
                  placeholderStyle={{
                    color: Colors.fieldtTxtColor,
                    // marginTop: 15,
                  }}
                  dropDownDirection="BOTTOM"
                  placeholder={selectedReservationStatus}
                  items={Object.entries(USER_RESERVATION_STATUS).map(
                    ([key, value]) => {
                      return { label: USER_RESERVATION_STATUS_PARSED[key], value };
                    },
                  )}
                  value={selectedReservationStatus}
                  onSelectItem={(items) => {
                    // console.log('selected status');
                    // console.log(value);
                    setSelectedReservationStatus(items.value);
                  }}
                  open={openStatus}
                  setOpen={setOpenStatus}
                />
              </View>

              {/* Other buttons */}
              {/* {showReservations || showFinished ? (
                  <View style={{ flexDirection: 'row' }}>
                    {showFinished ? (
                      <View style={{ flexDirection: 'row' }}>
                        <TouchableOpacity
                          style={{
                            margin: 3,
                            width:
                              Platform.OS == 'ios'
                                ? windowWidth * 0.067
                                : windowWidth * 0.06,
                            height: windowHeight * 0.04,
                            backgroundColor: isFinished ? 'green' : 'white',
                            borderRadius: 18,
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'center',
                            alignSelf: 'center',
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 3,
                            borderWidth: 1.5,
                            borderColor: '#90ee90',
                          }}
                          onPress={() => {
                            setIsFinished(true);
                            setIsNoShow(false);
                            setIsCancelled(false);
                          }}>
                          <Text style={{ color: isFinished ? 'white' : 'green' }}>
                            Finished
                          </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={{
                            margin: 3,
                            width:
                              Platform.OS == 'ios'
                                ? windowWidth * 0.067
                                : windowWidth * 0.06,
                            height: windowHeight * 0.04,
                            backgroundColor: isNoShow ? 'black' : 'white',
                            borderRadius: 18,
                            flexDirection: 'row',
                            alignSelf: 'center',
                            justifyContent: 'space-evenly',
                            alignItems: 'center',
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 3,
                            borderWidth: 1.5,
                            borderColor: 'black',
                          }}
                          onPress={() => {
                            setIsFinished(false);
                            setIsNoShow(true);
                            setIsCancelled(false);
                          }}>
                          <Text style={{ color: isNoShow ? 'white' : 'black' }}>
                            No Show
                          </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={{
                            margin: 3,
                            width:
                              Platform.OS == 'ios'
                                ? windowWidth * 0.067
                                : windowWidth * 0.06,
                            height: windowHeight * 0.04,
                            backgroundColor: isCancelled ? 'grey' : 'white',
                            borderRadius: 18,
                            flexDirection: 'row',
                            alignSelf: 'center',
                            justifyContent: 'space-evenly',
                            alignItems: 'center',
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 3,
                            borderWidth: 1.5,
                            borderColor: '#808080',
                          }}
                          onPress={() => {
                            setIsFinished(false);
                            setIsNoShow(false);
                            setIsCancelled(true);
                          }}>
                          <Text style={{ color: isCancelled ? 'white' : 'grey' }}>
                            Cancelled
                          </Text>
                        </TouchableOpacity>
                      </View>
                    ) : null}

                    <View style={{ justifyContent: 'center', marginHorizontal: 5 }}>
                      <TouchableOpacity
                        style={[styles.export, {
                          height: Dimensions.get('window').height * 0.07,
                        }]}
                        onPress={() => {
                          //exportTemplate();
                          setExportModalVisibility(true);
                        }}>
                        <MaterialCommunityIcons
                          name="export-variant"
                          size={switchMerchant ? 10 : windowWidth < 1000 ? 15 : 20}
                          color={Colors.whiteColor}
                          style={{}}
                        />
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            fontSize: switchMerchant ? 10 : windowWidth < 1000 ? 14 : 16,
                            fontFamily: 'NunitoSans-Bold',
                            marginLeft: 5,
                          }}>
                          EXPORT
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                ) : null} */}

              {/* Filter button */}
              {/* <TouchableOpacity
                  style={[styles.filter, {
                    width: Dimensions.get('window').width * 0.06,
                    height: Dimensions.get('window').height * 0.07,
                  }]}
                  onPress={() => {
                    setShowFilter(true);
                    //setIsFiltered(false);
                  }}> */}
              {/* <EIcon
                            name='sound-mix'
                            size={15}
                            color={Colors.tabGrey}
                            style={{ marginLeft: 5 }}
                        /> */}
              {/* <Text
                    style={{
                      color: Colors.whiteColor,
                      fontFamily: 'NunitoSans-Bold',
                      fontSize: switchMerchant ? 10 : windowWidth < 1000 ? 12 : 16,
                    }}>
                    FILTER
                  </Text>
                </TouchableOpacity> */}
            </View>
            {/* <ScrollView style={{backgroundColor: 'red'}}> */}
            {showReservations ? (
              // data need to change to reservationsArr
              <View
                style={[
                  Platform.OS == 'android'
                    ? {
                      height: windowHeight * 0.68,
                      marginTop: 10,
                      justifyContent: 'center',
                    }
                    : {
                      height: windowHeight * 0.73,
                      marginTop: 10,
                      justifyContent: 'center',
                    },
                  {
                    zIndex: -5
                    // backgroundColor: 'green',
                    // marginLeft: 30,
                  }
                ]}>
                {console.log('RESERVATION CHECK', reservationsArr)}
                {reservationsArr && reservationsArr.length > 0 ? (
                  <>
                    <View
                      style={{
                        flexDirection: 'row',
                        paddingVertical: 10,
                        paddingHorizontal: 20,
                        width: Dimensions.get('window').width * 0.92,
                      }}>
                      <View
                        style={[
                          {},
                          switchMerchant
                            ? {
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: '12.9%',
                            }
                            : {
                              alignItems: 'center',
                              //marginLeft: '2.5%',
                              width: '13.7%',
                            },
                        ]}>
                        <TouchableOpacity>
                          <Text style={styles.tableTitle}>Customer</Text>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={[
                          {},
                          switchMerchant
                            ? {
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: '6.9%',
                            }
                            : {
                              alignItems: 'center',
                              //marginLeft: '2.5%',
                              width: '7.7%',
                            },
                        ]}>
                        <TouchableOpacity>
                          <Text style={styles.tableTitle}>Pax</Text>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={[
                          {},
                          switchMerchant
                            ? {
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: '15.9%',
                            }
                            : {
                              alignItems: 'center',
                              //marginLeft: '2.5%',
                              width: '16.7%',
                            },
                        ]}>
                        <TouchableOpacity>
                          <Text style={styles.tableTitle}>Status</Text>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={[
                          {},
                          switchMerchant
                            ? {
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: '21.8%',
                            }
                            : {
                              alignItems: 'center',
                              //marginLeft: '2.5%',
                              width: '23.4%',
                            },
                        ]}>
                        <TouchableOpacity>
                          <Text style={styles.tableTitle}>{`Order ID\n(Order Status)`}</Text>
                        </TouchableOpacity>
                      </View>

                      {/* <View
                          style={[
                            {},
                            switchMerchant
                              ? {
                                justifyContent: 'center',
                                alignItems: 'center',
                                width: '10.9%',
                              }
                              : {
                                alignItems: 'center',
                                //marginLeft: '2.5%',
                                width: '11.7%',
                              },
                          ]}>
                          <TouchableOpacity>
                            <Text style={styles.tableTitle}>RSV Notes</Text>
                          </TouchableOpacity>
                        </View> */}

                      <View
                        style={[
                          {},
                          switchMerchant
                            ? {
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: '10.9%',
                            }
                            : {
                              alignItems: 'center',
                              //marginLeft: '2.5%',
                              width: '11.7%',
                            },
                        ]}>
                        <TouchableOpacity>
                          <Text style={styles.tableTitle}>Guest Notes</Text>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={[
                          {},
                          switchMerchant
                            ? {
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: '10.9%',
                            }
                            : {
                              alignItems: 'center',
                              //marginLeft: '2.5%',
                              width: '11.7%',
                            },
                        ]}>
                        <TouchableOpacity>
                          <Text style={styles.tableTitle}>Zone (Table)</Text>
                        </TouchableOpacity>
                      </View>

                      <View
                        style={[
                          {},
                          switchMerchant
                            ? {
                              justifyContent: 'center',
                              alignItems: 'center',
                              width: '10.9%',
                            }
                            : {
                              alignItems: 'center',
                              //marginLeft: '2.5%',
                              width: '11.7%',
                            },
                        ]}>
                        <TouchableOpacity>
                          <Text style={styles.tableTitle}>Guest Tags</Text>
                        </TouchableOpacity>
                      </View>

                      {/* <View style={[
                    {},
                    switchMerchant
                      ? {
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '9.9%',
                      }
                      : {
                        alignItems: 'center',
                        //marginLeft: '2.5%',
                        width: '10.7%',
                      },
                  ]}>
                    <TouchableOpacity>
                      <Text style={styles.tableTitle}>Created</Text>
                    </TouchableOpacity>
                  </View> */}
                    </View>

                    <FlatList
                      //style={styles.flatList}
                      style={{
                        width: Dimensions.get('window').width * 0.92,
                        paddingHorizontal: 5,
                      }}
                      nestedScrollEnabled
                      showsVerticalScrollIndicator={false}
                      renderItem={renderReservations}
                      data={reservationsArr.filter((item) => {
                        if (search !== '') {
                          if ((item.userName || 'Guest')
                            .toLowerCase()
                            .includes(search.toLowerCase())) {
                            return true;
                          }
                          else if ((item.userPhone || 'N/A')
                            .toLowerCase()
                            .includes(search.toLowerCase())) {
                            return true;
                          }
                          else if ((typeof item.pax === 'number' ? pax.toFixed(0) : '')
                            .toLowerCase()
                            .includes(search.toLowerCase())) {
                            return true;
                          }
                          else if ((item.dRestrictions || '')
                            .toLowerCase()
                            .includes(search.toLowerCase())) {
                            return true;
                          }
                          else if ((item.sOccasions || '')
                            .toLowerCase()
                            .includes(search.toLowerCase())) {
                            return true;
                          }
                          else if ((item.status || '')
                            .toLowerCase()
                            .includes(search.toLowerCase())) {
                            return true;
                          }
                          else if ((item.outletSectionName || '')
                            .toLowerCase()
                            .includes(search.toLowerCase())) {
                            return true;
                          }
                          else if ((item.tableCode || '')
                            .toLowerCase()
                            .includes(search.toLowerCase())) {
                            return true;
                          }
                          else if ((item.timeSelected || '')
                            .toLowerCase()
                            .includes(search.toLowerCase())) {
                            return true;
                          }
                          else {
                            return false;
                          }
                        }
                        else {
                          return true;
                        }
                        // }
                      })}
                      keyExtractor={(item, index) => String(index)}
                    />
                  </>
                ) : (
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'center',

                      // backgroundColor: 'red',
                    }}>
                    {isFiltered ? (
                      <View
                        style={{
                          width: Dimensions.get('window').width * 0.5,
                          //alignItems: 'center',                            
                        }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 16 : 20,
                            fontFamily: 'NunitoSans-Bold',
                            marginBottom: 10,
                            textAlign: 'center',
                          }}>
                          {'No Reservation results found'}
                          {'\nTry turning off some filters?'}
                        </Text>
                      </View>
                    ) : (
                      <View style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',

                        // backgroundColor: 'red',

                        width: '100%',
                        height: '100%',
                      }}>
                        <View style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',

                          marginRight: windowWidth * (Styles.sideBarWidth),

                          // backgroundColor: 'blue',
                        }}>
                          <Text
                            style={{
                              fontSize: switchMerchant ? 16 : 20,
                              fontFamily: 'NunitoSans-Bold',
                              marginBottom: 10,
                              textAlign: 'center',
                            }}>
                            {'No Reservations Available'}
                          </Text>
                          <TouchableOpacity
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              // width: 160,
                              paddingHorizontal: 20,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                              marginRight: 10,
                            }}
                            onPress={() => {
                              setAddReservation(true);
                            }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                              }}>
                              <AntDesign
                                name="pluscircle"
                                size={switchMerchant ? 10 : 16}
                                color={Colors.whiteColor}
                              />
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                RESERVATION
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                      </View>
                    )}
                  </View>
                )}
              </View>
            ) : null}
            {showFinished ? (
              // data need to change to finishedArr
              <View
                style={
                  Platform.OS == 'android'
                    ? { height: windowHeight * 0.68 }
                    : { height: windowHeight * 0.73 }
                }>
                <FlatList
                  style={styles.flatList}
                  nestedScrollEnabled
                  showsVerticalScrollIndicator={false}
                  renderItem={renderFinished}
                  data={finishedArr.filter((item) => {
                    if (isFinished) {
                      if (search !== '') {
                        return (
                          item.userName
                            .toLowerCase()
                            .includes(search.toLowerCase()) &&
                          item.status === USER_RESERVATION_STATUS.SEATED
                        );
                      } else {
                        return item.status === USER_RESERVATION_STATUS.SEATED;
                      }
                    } else if (isCancelled) {
                      if (search !== '') {
                        return (
                          item.userName
                            .toLowerCase()
                            .includes(search.toLowerCase()) &&
                          item.status === USER_RESERVATION_STATUS.CANCELED
                        );
                      } else {
                        return (
                          item.status === USER_RESERVATION_STATUS.CANCELED
                        );
                      }
                    } else if (isNoShow) {
                      if (search !== '') {
                        return (
                          item.userName
                            .toLowerCase()
                            .includes(search.toLowerCase()) &&
                          item.status === USER_RESERVATION_STATUS.NO_SHOW
                        );
                      } else {
                        return (
                          item.status === USER_RESERVATION_STATUS.NO_SHOW
                        );
                      }
                    }
                  })}
                  keyExtractor={(item, index) => String(index)}
                />
              </View>
            ) : null}
            {showWaitlist ? (
              // data need to change to waitListArr
              <View
                style={
                  Platform.OS == 'android'
                    ? { height: windowHeight * 0.68 }
                    : { height: windowHeight * 0.73 }
                }>
                <FlatList
                  style={styles.flatList}
                  nestedScrollEnabled
                  showsVerticalScrollIndicator={false}
                  renderItem={renderWaitList}
                  data={waitListArr.filter((item) => {
                    if (search !== '') {
                      return item.userName
                        .toLowerCase()
                        .includes(search.toLowerCase());
                    } else {
                      return true;
                    }
                  })}
                  keyExtractor={(item, index) => String(index)}
                />
              </View>
            ) : null}

            {/* </ScrollView> */}
          </View>

          <View
            style={{
              flex: 1,
              position: 'absolute',
              bottom: windowHeight * 0.18,
            }}>
            <Footer />
          </View>
        </>
      )
      }

      {/* Modal Export */}
      <Modal
        style={{}}
        visible={exportModalVisibility}
        supportedOrientations={['portrait', 'landscape']}
        transparent
        animationType={'fade'}>
        <View
          style={[
            styles.modalContainer,
            {
              top: 0,
            },
          ]}>
          <View
            style={[
              styles.modalView,
              {
                height: windowWidth * 0.3,
                width: windowWidth * 0.4,

                padding: windowWidth * 0.03,
              },
            ]}>
            <TouchableOpacity
              disabled={isLoading}
              style={[
                styles.closeButton,
                {
                  right: windowWidth * 0.02,
                  top: windowWidth * 0.02,
                },
              ]}
              onPress={() => {
                setExportModalVisibility(false);
              }}>
              <AntDesign
                name="closecircle"
                size={switchMerchant ? 15 : 25}
                color={Colors.fieldtTxtColor}
              />
            </TouchableOpacity>
            <View style={styles.modalTitle}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  textAlign: 'center',
                  fontSize: switchMerchant ? 16 : 24,
                }}>
                Download Report
              </Text>
            </View>
            <View style={{ top: switchMerchant ? '14%' : '10%' }}>
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 16,
                  fontFamily: 'NunitoSans-Bold',
                }}>
                Email Address:
              </Text>
              <TextInput
                underlineColorAndroid={Colors.fieldtBgColor}
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: switchMerchant ? 240 : 370,
                  height: switchMerchant ? 35 : 50,
                  borderRadius: 5,
                  padding: 5,
                  marginVertical: 5,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  paddingLeft: 10,
                  fontSize: switchMerchant ? 10 : 16,
                }}
                autoCapitalize="none"
                placeholderStyle={{ padding: 5 }}
                placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                placeholder="Enter your email"
                onChangeText={(text) => {
                  setExportEmail(text);
                }}
                value={exportEmail}
              />
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 16,
                  fontFamily: 'NunitoSans-Bold',
                  marginTop: 15,
                }}>
                Send As:
              </Text>

              <View
                style={{
                  alignItems: 'center',
                  justifyContent: 'center',
                  //top: '10%',
                  flexDirection: 'row',
                  marginTop: 15,
                }}>
                <TouchableOpacity
                  disabled={isLoading}
                  style={{
                    justifyContent: 'center',
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: '#4E9F7D',
                    borderRadius: 5,
                    width: switchMerchant ? 100 : 100,
                    paddingHorizontal: 10,
                    height: switchMerchant ? 35 : 40,
                    alignItems: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                    marginRight: 15,
                  }}
                  onPress={() => {
                    if (exportEmail.length > 0) {
                      CommonStore.update((s) => {
                        s.isLoading = true;
                      });

                      setIsExcel(true);
                      setIsCsv(false);

                      const excelData = convertDataToExcelFormat();

                      generateEmailReport(
                        EMAIL_REPORT_TYPE.EXCEL,
                        excelData,
                        'Reservation List',
                        'Reservation List.xlsx',
                        `/merchant/${merchantId}/reservation/${uuidv4()}.xlsx`,
                        exportEmail,
                        'Reservation List',
                        'Reservation List',
                        () => {
                          CommonStore.update((s) => {
                            s.isLoading = false;
                          });

                          setIsExcel(false);

                          window.confirm(
                            "Success, Reservation list will be sent to the email address shortly."
                          );

                          setExportModalVisibility(false);
                        },
                      );
                    } else {
                      window.confirm(
                        "Info, Invalid email address."
                      );
                    }
                  }}>
                  {isLoading && isExcel ? (
                    <ActivityIndicator
                      size={'small'}
                      color={Colors.whiteColor}
                    />
                  ) : (
                    <Text
                      style={{
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      EXCEL
                    </Text>
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  disabled={isLoading}
                  style={{
                    justifyContent: 'center',
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: '#4E9F7D',
                    borderRadius: 5,
                    width: switchMerchant ? 100 : 100,
                    paddingHorizontal: 10,
                    height: switchMerchant ? 35 : 40,
                    alignItems: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,
                  }}
                  onPress={() => {
                    if (exportEmail.length > 0) {
                      CommonStore.update((s) => {
                        s.isLoading = true;
                      });

                      setIsCsv(true);
                      setIsExcel(false);

                      const csvData = convertDataToCSVFormat();

                      generateEmailReport(
                        EMAIL_REPORT_TYPE.CSV,
                        csvData,
                        'Reservation List',
                        'Reservation List.csv',
                        `/merchant/${merchantId}/reservation/${uuidv4()}.csv`,
                        exportEmail,
                        'Reservation List',
                        'Reservation List',
                        () => {
                          CommonStore.update((s) => {
                            s.isLoading = false;
                          });

                          setIsCsv(false);

                          window.confirm(
                            "Success, Reservation list will be sent to the email address shortly."
                          );

                          setExportModalVisibility(false);
                        },
                      );
                    } else {
                      window.confirm(
                        "Info, Invalid email address."
                      );
                    }
                  }}>
                  {isLoading && isCsv ? (
                    <ActivityIndicator
                      size={'small'}
                      color={Colors.whiteColor}
                    />
                  ) : (
                    <Text
                      style={{
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      CSV
                    </Text>
                  )}
                </TouchableOpacity>

                {/* <TouchableOpacity
                                            style={[styles.modalSaveButton, {
                                                zIndex: -1
                                            }]}
                                            onPress={() => { downloadPDF() }}>
                                            <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>PDF</Text>
                                        </TouchableOpacity> */}
              </View>
            </View>
          </View>
        </View>
      </Modal>

      {/* Modal filter */}
      <Modal
        supportedOrientations={['landscape', 'portrait']}
        style={{ flex: 1 }}
        visible={showFilter}
        transparent>
        <View style={styles.modalContainer}>
          <View
            style={[
              styles.modalView,
              {
                width: Dimensions.get('window').width * 0.4,
                padding: Dimensions.get('window').width * 0.02,
                paddingHorizontal: Dimensions.get('window').width * 0,

                height: windowHeight * 0.5,
                paddingTop: 0,

                ...getTransformForModalInsideNavigation(),
              },
            ]}>
            <View
              style={{
                flexDirection: 'row',
                borderBottomWidth: 1,
                borderColor: '#D3D3D3',
                alignItems: 'center',
                height: windowHeight * 0.1,
              }}>
              <View
                style={{
                  width: 100,
                  height: 35,
                  justifyContent: 'center',
                  borderRadius: 8,
                  backgroundColor: 'red',
                  left: '20%',
                }}>
                <TouchableOpacity
                  style={{}}
                  onPress={() => {
                    setRoomCount(0);
                    setPartyCount(0);
                    setPartyChecked({});
                    setRoomChecked({});
                    setFilterNotes(false);
                    setFilterTags(false);
                    setIsFiltered(false);
                  }}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans',
                      textAlign: 'center',
                      fontSize: switchMerchant ? 16 : 18,
                      color: 'white',
                    }}>
                    RESET
                  </Text>
                </TouchableOpacity>
              </View>
              <View style={{ flex: 4, justifyContent: 'center' }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    textAlign: 'center',
                    fontSize: switchMerchant ? 18 : 20,
                  }}>
                  Filter Options
                </Text>
              </View>
              <View
                style={{
                  width: 100,
                  height: 35,
                  justifyContent: 'center',
                  borderRadius: 8,
                  backgroundColor: 'rgb(0, 146, 117)',
                  right: '20%',
                }}>
                <TouchableOpacity
                  style={{}}
                  onPress={() => {
                    setShowFilter(false);

                    if (
                      Object.keys(roomChecked).length > 0 ||
                      Object.keys(partyChecked).length > 0 ||
                      filterTags ||
                      filterNotes
                    ) {
                      setIsFiltered(true);
                    } else {
                      setIsFiltered(false);
                    }
                  }}>
                  <Text
                    style={{
                      fontFamily: 'NunitoSans',
                      textAlign: 'center',
                      fontSize: switchMerchant ? 16 : 18,
                      color: 'white',
                    }}>
                    DONE
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            <View
              style={{
                flexDirection: 'row',
                backgroundColor: '#D3D3D3',
                height: windowHeight * 0.05,
                alignItems: 'flex-end',
              }}>
              <View style={{ flex: 1 }}>
                <Text
                  style={{
                    marginLeft: '5%',
                    marginBottom: 11,
                    fontFamily: 'NunitoSans-Bold',
                    fontSize: switchMerchant ? 14 : 16,
                    // textAlign: 'left'
                  }}>
                  Filter
                </Text>
              </View>
            </View>
            {/* <View
              style={{
                flex: 1,
                marginTop: 10,
                borderBottomWidth: 1,
                borderColor: '#D3D3D3',
              }}>
              <TouchableOpacity
                onPress={() => {
                  setReservationStatusModal(true);
                }}>
                <View style={{flexDirection: 'row'}}>
                  <View style={{flex: 1}}>
                    <Text style={{marginLeft: 10}}>Reservation Status</Text>
                  </View>
                  <View style={{flex: 0.9}}>
                    <Text style={{textAlign: 'right'}}>All</Text>
                  </View>
                  <View style={{flex: 0.1, marginRight: 10}}>
                    <MaterialIcons
                      name="arrow-forward-ios"
                      size={15}
                      color="#D3D3D3"
                    />
                  </View>
                </View>
              </TouchableOpacity>
            </View> */}
            <View
              style={{
                width: Dimensions.get('window').width * 0.4,
              }}>
              <View
                style={{
                  // flex: 1,
                  paddingBottom: '2%',
                  paddingTop: '2%',
                  borderBottomWidth: 1,
                  borderColor: '#D3D3D3',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setRoomModal(true);
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      paddingHorizontal: '5%',
                    }}>
                    <View style={{ alignItems: 'center' }}>
                      <Text
                        style={{
                          //marginLeft: '19%',
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Section
                      </Text>
                    </View>
                    <View
                      style={{
                        //marginRight: 10,
                        //marginTop: 2.4,
                        //paddingLeft: '1%',
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <Text
                        style={{
                          //textAlign: 'right',
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        {roomCount > 0 ? `${roomCount} Selected` : 'None'}
                      </Text>
                      <MaterialIcons
                        style={{ marginLeft: 5, alignSelf: 'center' }}
                        name="arrow-forward-ios"
                        size={15}
                        color="#D3D3D3"
                      />
                    </View>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  // flex: 1,
                  paddingBottom: '2%',
                  paddingTop: '2%',
                  borderBottomWidth: 1,
                  borderColor: '#D3D3D3',
                  justifyContent: 'center',
                }}>
                <TouchableOpacity
                  onPress={() => {
                    setPartyModal(true);
                  }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      paddingHorizontal: '5%',
                    }}>
                    <View style={{ alignItems: 'center' }}>
                      <Text
                        style={{
                          //marginLeft: '15%',
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Party Size
                      </Text>
                    </View>
                    <View
                      style={{
                        // marginRight: 10,
                        // marginTop: 2.4,
                        // paddingLeft: '1%',
                        flexDirection: 'row',
                        // justifyContent: 'center',
                      }}>
                      <Text
                        style={{
                          //textAlign: 'right',
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        {partyCount > 0 ? `${partyCount} Selected` : 'None'}
                      </Text>
                      <MaterialIcons
                        style={{ marginLeft: 5, alignSelf: 'center' }}
                        name="arrow-forward-ios"
                        size={15}
                        color="#D3D3D3"
                      />
                    </View>
                  </View>
                </TouchableOpacity>
              </View>
              <View
                style={{
                  // flex: 1,
                  paddingBottom: '2%',
                  paddingTop: '2%',
                  borderBottomWidth: 1,
                  borderColor: '#D3D3D3',
                  justifyContent: 'center',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    paddingHorizontal: '5%',
                  }}>
                  <View style={{ alignItems: 'center' }}>
                    <Text
                      style={{
                        //marginLeft: '26%',
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      Tags
                    </Text>
                  </View>
                  <View style={{ paddingRight: 3 }}>
                    <Switch
                      style={{
                        marginBottom: 5,
                        width: "5%",
                        transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }],
                      }}
                      value={filterTags}
                      onSyncPress={(statusTemp) =>
                        // setState({ status: status })
                        setFilterTags(statusTemp)
                      }
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View>
                </View>
              </View>
              <View
                style={{
                  // flex: 1,
                  paddingBottom: '2%',
                  paddingTop: '2%',
                  borderBottomWidth: 1,
                  borderColor: '#D3D3D3',
                  justifyContent: 'center',
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    paddingHorizontal: '5%',
                  }}>
                  <View style={{ alignItems: 'center' }}>
                    <Text
                      style={{
                        //marginLeft: '25%',
                        fontSize: 16,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                      Notes
                    </Text>
                  </View>
                  <View style={{ paddingRight: 3 }}>
                    <Switch
                      style={{
                        marginBottom: 5,
                        width: "5%",
                        transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }],
                      }}
                      value={filterTags}
                      onSyncPress={(statusTemp) =>
                        // setState({ status: status })
                        setFilterTags(statusTemp)
                      }
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                    />
                  </View>
                </View>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      {/* Modal room */}
      <Modal
        supportedOrientations={['landscape', 'portrait']}
        style={{ flex: 1 }}
        visible={roomModal}
        transparent>
        <View style={[styles.modalContainer]}>
          <View
            style={[
              styles.modalView,
              {
                width: Dimensions.get('window').width * 0.4,
                padding: Dimensions.get('window').width * 0.02,
                paddingHorizontal: Dimensions.get('window').width * 0,

                height: windowHeight * 0.5,
                paddingTop: 0,

                ...getTransformForModalInsideNavigation(),
              },
            ]}>
            <View
              style={{
                flexDirection: 'row',
                borderBottomWidth: 1,
                borderColor: '#D3D3D3',
                height: windowHeight * 0.1,
                alignItems: 'center',
              }}>
              <View style={{ flex: 1 }}>
                <TouchableOpacity
                  style={{
                    alignItems: 'center',
                  }}
                  onPress={() => {
                    setRoomModal(false);
                  }}>
                  <MaterialIcons
                    name="arrow-back-ios"
                    size={20}
                    color="rgb(0, 146, 117)"
                  />
                </TouchableOpacity>
              </View>
              <View style={{ flex: 2 }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    textAlign: 'center',
                    fontSize: switchMerchant ? 18 : 20,
                  }}>
                  Sections
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => {
                  setRoomCount(0);
                  setRoomChecked({});
                }}
                style={{ flex: 1 }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    textAlign: 'center',
                    fontSize: switchMerchant ? 18 : 20,
                    color: Colors.tabRed,
                  }}>
                  Clear
                </Text>
              </TouchableOpacity>
            </View>
            <View
              style={{
                flexDirection: 'row',
                backgroundColor: '#D3D3D3',
                height: windowHeight * 0.05,
                alignItems: 'flex-end',
              }}
            />
            <ScrollView
              style={{
                flex: 1,
                borderBottomWidth: 1,
                borderColor: '#D3D3D3',
              }}>
              {outletSections.map((item, index) => {
                return (
                  <View
                    style={{
                      flexDirection: 'row',
                      borderBottomWidth: 1,
                      borderColor: '#D3D3D3',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text style={{ marginLeft: 10 }}>{item.sectionName}</Text>
                    <input
                      onChange={(statusTemp) => {
                        setRoomChecked({
                          ...roomChecked,
                          [item.uniqueId]: statusTemp,
                        });
                        // setState({ status: status })
                        // setRoomAll(statusTemp);
                        // setRoomDinnerRoom(statusTemp);
                        // setRoomIndoor(statusTemp);
                      }}
                      style={{
                        marginRight: 10,
                        alignSelf: 'flex-end',
                        borderRadius: 15,
                      }}
                      type={'checkbox'}
                      checked={
                        roomChecked[item.uniqueId]
                          ? roomChecked[item.uniqueId]
                          : false
                      }
                    />

                  </View>
                );
              })}
            </ScrollView>
          </View>
        </View>
      </Modal>

      <Modal
        supportedOrientations={['landscape', 'portrait']}
        style={{ flex: 1 }}
        visible={partyModal}
        transparent>
        <View style={[styles.modalContainer]}>
          <View
            style={[
              styles.modalView,
              {
                width: Dimensions.get('window').width * 0.4,
                padding: Dimensions.get('window').width * 0.02,
                paddingHorizontal: Dimensions.get('window').width * 0,

                height: windowHeight * 0.5,
                paddingTop: 0,

                ...getTransformForModalInsideNavigation(),
              },
            ]}>
            <View
              style={{
                flexDirection: 'row',
                borderBottomWidth: 1,
                borderColor: '#D3D3D3',
                height: windowHeight * 0.1,
                alignItems: 'center',
              }}>
              <View style={{ flex: 1 }}>
                <TouchableOpacity
                  style={{
                    alignItems: 'center',
                  }}
                  onPress={() => {
                    setPartyModal(false);
                  }}>
                  <MaterialIcons
                    name="arrow-back-ios"
                    size={20}
                    color="rgb(0, 146, 117)"
                  />
                </TouchableOpacity>
              </View>
              <View style={{ flex: 2 }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    textAlign: 'center',
                    fontSize: switchMerchant ? 18 : 20,
                  }}>
                  Party Size
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => {
                  setPartyCount(0);
                  setPartyChecked({});
                }}
                style={{ flex: 1 }}>
                <Text
                  style={{
                    fontFamily: 'NunitoSans-Bold',
                    textAlign: 'center',
                    fontSize: switchMerchant ? 18 : 20,
                    color: Colors.tabRed,
                  }}>
                  Clear
                </Text>
              </TouchableOpacity>
            </View>
            <View
              style={{
                flexDirection: 'row',
                backgroundColor: '#D3D3D3',
                height: windowHeight * 0.05,
                alignItems: 'flex-end',
              }}
            />
            <ScrollView
              style={{
                flex: 1,
                borderBottomWidth: 1,
                borderColor: '#D3D3D3',
              }}>
              {partySizeArr.map((item, index) => {
                return (
                  <View
                    style={{
                      flexDirection: 'row',
                      borderBottomWidth: 1,
                      borderColor: '#D3D3D3',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <Text style={{ marginLeft: 10 }}>{item ? item.label : ''}</Text>
                    <input
                      onChange={(statusTemp) => {
                        setRoomChecked({
                          ...roomChecked,
                          [item.uniqueId]: statusTemp,
                        });
                        // setState({ status: status })
                        // setRoomAll(statusTemp);
                        // setRoomDinnerRoom(statusTemp);
                        // setRoomIndoor(statusTemp);
                      }}
                      style={{
                        marginRight: 10,
                        alignSelf: 'flex-end',
                        borderRadius: 15,
                      }}
                      type={'checkbox'}
                      checked={
                        roomChecked[item.uniqueId]
                          ? roomChecked[item.uniqueId]
                          : false
                      }
                    />
                  </View>
                );
              })}
            </ScrollView>
          </View>
        </View>
      </Modal>

      <Modal
        supportedOrientations={['landscape', 'portrait']}
        style={{ flex: 1 }}
        visible={openSelectTable}
        transparent>
        <View
          style={{
            flex: 1,
            backgroundColor: Colors.modalBgColor,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <View
            style={{
              backgroundColor: Colors.whiteColor,
              padding: windowHeight * 0.03,
              // height: '90%',
              // width: '90%',
              width: windowWidth * 0.9,
              height: windowHeight * 0.9,
              borderRadius: 12,

              ...getTransformForModalInsideNavigation(),
            }}>
            <TouchableOpacity
              style={[
                styles.closeButton,
                switchMerchant
                  ? {
                    position: 'absolute',
                    top: windowHeight * 0.02,
                    right: windowWidth * 0.02,
                  }
                  : {},
              ]}
              onPress={() => {
                setOpenSelectTable(false);
                setSelectTable('');
              }}>
              {switchMerchant ? (
                <AIcon
                  name="closecircle"
                  size={15}
                  color={Colors.fieldtTxtColor}
                />
              ) : (
                <AIcon
                  name="closecircle"
                  size={25}
                  color={Colors.fieldtTxtColor}
                />
              )}
            </TouchableOpacity>
            <View
              style={{
                height: 40,
              }}>
              <FlatList
                contentContainerStyle={{
                  height: '100%',
                  alignItems: 'center',
                  // width: '100%',
                  // width: '100%',

                  // marginRight: 100,

                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  // paddingTop: 30,
                  // paddingLeft: 5,
                  // paddingRight: 70
                  paddingRight: 20,
                }}
                data={outletSections}
                renderItem={renderSection}
                keyExtractor={(item, index) => String(index)}
                horizontal
                showsHorizontalScrollIndicator={false}
                style={[
                  {
                    width: '93%',
                    // width: 50,
                    height: 100,
                  },
                  switchMerchant
                    ? {
                      // paddingLeft: windowWidth * 0.05
                    }
                    : {},
                ]}
              />
            </View>
            <View
              style={[
                {
                  // paddingLeft: windowWidth * 0.04,
                  paddingTop: windowHeight * 0.01,
                  //marginLeft: windowWidth * 0.04,
                  width: switchMerchant ? windowWidth * 0.8 : '100%',
                  height: switchMerchant
                    ? windowHeight * 0.52
                    : windowHeight * 0.6,
                  display: 'flex',
                  justifyContent: 'center',
                  flexDirection: 'row',
                  // backgroundColor: 'red',
                },
              ]}>
              {/* <FlatList
                  data={filteredOutletTablesForRendered.concat({
                    actionButton: true,
                  })}
                  showsVerticalScrollIndicator={false}
                  renderItem={renderTableLayout}
                  keyExtractor={(item, index) => String(index)}
                  // numColumns={6}
                  contentContainerStyle={{
                    // backgroundColor: 'red',
                    paddingBottom: windowHeight * 0.05,
                    // justifyContent: 'center',
                    // alignItems: 'center',
                    flexDirection: 'row',
                    flexWrap: 'wrap',
                    // justifyContent: 'center',
                    // left: switchMerchant? windowWidth * -0.02 : 0,.
                  }}
                  keyboardShouldPersistTaps="handled"
                  maxToRenderPerBatch={1}
                /> */}
              <FlatList
                data={filteredOutletTablesReservation}
                showsVerticalScrollIndicator={false}
                renderItem={renderTableLayout}
                keyExtractor={(item, index) => String(index)}
                // numColumns={6}
                contentContainerStyle={{
                  // backgroundColor: 'red',
                  paddingBottom: windowHeight * 0.05,
                  // justifyContent: 'center',
                  // alignItems: 'center',
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  // justifyContent: 'center',
                  // left: switchMerchant? windowWidth * -0.02 : 0,.
                }}
                keyboardShouldPersistTaps="handled"
                maxToRenderPerBatch={1}
              />
            </View>
            <View
              style={{
                flex: 1,
                alignItems: 'center',
              }}>
              <View
                style={{
                  justifyContent: 'space-around',
                  height: '100%',
                  // paddingTop: 10,
                }}>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  {/* rectangle */}
                  <View
                    style={{
                      backgroundColor: Colors.primaryColor,
                      color: Colors.whiteColor,
                      width: windowHeight * 0.03,
                      height: windowHeight * 0.03,
                    }} />
                  <Text
                    style={{
                      color: Colors.primaryColor,
                      fontSize: switchMerchant ? 10 : 16,
                      fontFamily: 'NunitoSans-Bold',
                      paddingLeft: windowWidth * 0.01,
                    }}>
                    Available
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  {/* rectangle */}
                  <View
                    style={{
                      backgroundColor: Colors.secondaryColor,
                      color: Colors.whiteColor,
                      width: windowHeight * 0.03,
                      height: windowHeight * 0.03,
                    }} />
                  <Text
                    style={{
                      color: Colors.secondaryColor,
                      fontSize: switchMerchant ? 10 : 16,
                      fontFamily: 'NunitoSans-Bold',
                      paddingLeft: windowWidth * 0.01,
                    }}>
                    {`Seated / Reserved within ${reservationIntervalMin} minutes`}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </Modal>
      {
        tableModalVisibility ? (
          <View
            style={{
              flex: 1,
              backgroundColor: Colors.modalBgColor,
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 10000,
              position: 'absolute',
              // borderRadius: windowWidth * 0.03,
              width: switchMerchant ? windowWidth * 0.88 : windowWidth * 1,
              height: switchMerchant ? windowHeight * 0.8 : windowHeight * 0.85,
            }}>
            <View style={styles.modalContainerTable}>
              <View style={[styles.modalViewTable]}>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => {
                    setTableModalVisibility(false);
                  }}>
                  {switchMerchant ? (
                    <AntDesign
                      name="closecircle"
                      size={15}
                      color={Colors.fieldtTxtColor}
                    />
                  ) : (
                    <AntDesign
                      name="closecircle"
                      size={25}
                      color={Colors.fieldtTxtColor}
                    />
                  )}
                </TouchableOpacity>
                <View
                  style={{
                    flexDirection: 'column',
                    paddingTop: switchMerchant ? windowHeight * 0.1 : 80,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}>
                  <Text
                    style={[
                      switchMerchant
                        ? {
                          fontSize: 16,
                          fontFamily: 'NunitoSans-Bold',
                          marginBottom: 30,
                        }
                        : {
                          fontSize: switchMerchant ? 16 : 20,
                          fontFamily: 'NunitoSans-Bold',
                          marginBottom: 30,
                        },
                    ]}>
                    Select Table
                  </Text>

                  {console.log('selectedOutletTableId')}
                  {console.log(selectedOutletTableId)}
                  {selectedOutletTableId !== '' &&
                    outletTables.find(
                      (table) => table.uniqueId === selectedOutletTableId,
                    ) ? (
                    <DropDownPicker
                      arrowSize={20}
                      containerStyle={{
                        height: 40,
                        width: '60%',
                        borderRadius: 15,
                        zIndex: 1000,
                      }}
                      style={{
                        borderWidth: 1,
                        borderRadius: 15,
                        borderColor: '#E5E5E5',
                        backgroundColor: Colors.fieldtBgColor,
                        paddingVertical: 0,
                        zIndex: 1000,
                      }}
                      dropDownStyle={{
                        zIndex: 1000,
                        height: windowHeight * 0.22,
                      }}
                      itemStyle={{ justifyContent: 'flex-start', paddingLeft: 5 }}
                      items={outletTables
                        .filter((table) => {
                          if (table.seated <= 0) {
                            return true;
                          } else {
                            return false;
                          }
                        })
                        .map((table) => {
                          return { label: table.code, value: table.uniqueId };
                        })}
                      onChangeItem={(item) => {
                        setSelectedOutletTableId(item.value);
                      }}
                      defaultValue={selectedOutletTableId}
                    />
                  ) : (
                    <></>
                  )}

                  <View style={{ zIndex: -1000 }}>
                    <TouchableOpacity
                      style={{
                        marginTop: 20,
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        width: 100,
                        paddingHorizontal: 10,
                        height: 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1000,
                      }}
                      onPress={() => {
                        seatedReservation();
                      }}>
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          //marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: 'NunitoSans-Bold',
                        }}>
                        Seated
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          </View>
        ) : null
      }
    </View >
    // </UserIdleWrapper >
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: 'row',
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,
    // elevation: 16,
  },
  headerLeftStyle: {
    width: Dimensions.get('window').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  topBar: {
    flexDirection: 'row',
    height: Dimensions.get('window').height * 0.11,
    width: Dimensions.get('window').width * 0.90,
    backgroundColor: Colors.lightGrey,
    justifyContent: 'flex-start',
    paddingHorizontal: 8,
  },
  topBarButton: {
    padding: 5,
    backgroundColor: Colors.lightGrey,
    width: Dimensions.get('window').width * 0.08,
    justifyContent: 'center',
  },
  search: {
    margin: 3,
    marginTop: 6,
    // width: windowWidth * 0.3,
    height: Dimensions.get('window').height * 0.05,
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 7,
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  export: {
    /* width:
      Platform.OS == 'ios'
        ? Dimensions.get('window').width * 0.063
        : Dimensions.get('window').width * 0.07, */
    height: Dimensions.get('window').height * 0.05,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    backgroundColor: '#4E9F7D',
    borderRadius: 5,
    flexDirection: 'row',
    alignSelf: 'center',
    //justifyContent: 'space-evenly',
    alignItems: 'center',
    paddingHorizontal: 10,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  filter: {
    margin: 3,
    width: Dimensions.get('window').width * 0.06,
    height: Dimensions.get('window').height * 0.05,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    backgroundColor: '#4E9F7D',
    borderRadius: 5,
    flexDirection: 'row',
    alignSelf: 'center',
    justifyContent: 'space-evenly',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  flatListHeader: {
    height: 70,
    //marginTop: 10,
    //marginLeft: 10,
    width: Dimensions.get('window').width * 0.89,
    flexDirection: 'row',
    paddingVertical: 20,
    paddingHorizontal: 15,
    //marginTop: 10,
    borderTopLeftRadius: 5,
    borderTopRightRadius: 5,
    backgroundColor: '#D3D3D3',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  flatListBody: {
    //marginLeft: 10,
    width: Dimensions.get('window').width * 0.89,
    // height: windowHeight * 0.1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomLeftRadius: 5,
    borderBottomRightRadius: 5,
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  clickedFlatListHeader: {
    height: 70,
    marginTop: 10,
    marginLeft: 10,
    width: Dimensions.get('window').width * 0.25,
    flexDirection: 'row',
    paddingVertical: 20,
    paddingHorizontal: 15,
    //marginTop: 10,
    borderTopLeftRadius: 5,
    borderTopRightRadius: 5,
    backgroundColor: '#D3D3D3',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  table: {
    textAlign: 'center',
    width: Dimensions.get('window').width * 0.11,
    fontFamily: 'NunitoSans-Bold',
  },
  tableFirst: {
    textAlign: 'left',
    width: Dimensions.get('window').width * 0.11,
    fontFamily: 'NunitoSans-Bold',
  },
  flatList: {
    height: 100,
    marginLeft: 10,
    width: Dimensions.get('window').width * 0.91,
    // paddingVertical: 20,
    // paddingHorizontal: 15,
    //marginTop: 10,
    borderBottomLeftRadius: 5,
    borderBottomRightRadius: 5,
    backgroundColor: Colors.highlightColor,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  clickedFlatList: {
    height: 100,
    marginLeft: 10,
    width: Dimensions.get('window').width * 0.25,
    flexDirection: 'row',
    // paddingVertical: 20,
    // paddingHorizontal: 15,
    //marginTop: 10,
    borderBottomLeftRadius: 5,
    borderBottomRightRadius: 5,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  detailsContainer: {
    height: Dimensions.get('window').width * 0.54,
    margin: 10,
    width: Dimensions.get('window').width * 0.63,
    // flexDirection: 'row',
    // paddingVertical: 20,
    // paddingHorizontal: 15,
    //marginTop: 10,
    borderRadius: 10,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  detailsButton: {
    padding: 5,
    backgroundColor: Colors.lightGrey,
    width: Dimensions.get('window').width * 0.35,
    height: 70,
    borderRadius: 10,
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  reservationProfileHistoryButton: {
    padding: 5,
    width: Dimensions.get('window').width * 0.3,
    height: 55,
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  changeBackSeatButton: {
    borderRadius: 5,
    borderWidth: 1.5,
    borderColor: 'rgb(0, 146, 117)',
    marginLeft: 20,
    justifyContent: 'center',
    alignItems: 'center',
    width: Dimensions.get('window').width * 0.16,
    height: Dimensions.get('window').height * 0.06,
  },
  guestButtons: {
    width: Dimensions.get('window').width * 0.14,
    height: 70,
    margin: 10,
    // borderWidth: 0,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  reservationsContainer: {
    borderWidth: 0.3,
    borderTopLeftRadius: 2,
    borderTopRightRadius: 2,
    // margin: 10,
    marginRight: 10,
    marginLeft: 10,
    width: Dimensions.get('window').width * 0.3,
  },
  reservationsContainerBelow: {
    borderWidth: 0.3,
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
    marginBottom: 10,
    marginRight: 10,
    marginLeft: 10,
    width: Dimensions.get('window').width * 0.3,
    height: Dimensions.get('window').height * 0.1,
  },
  editButton: {
    marginRight: 10,
    textAlign: 'right',
    color: 'rgb(0, 146, 117)',
  },
  changeGuestButton: {
    borderWidth: 1.5,
    borderColor: 'rgb(0, 146, 117)',
    alignItems: 'center',
    justifyContent: 'center',
    width: Dimensions.get('window').width * 0.08,
    height: Dimensions.get('window').height * 0.04,
    marginLeft: 130,
    borderRadius: 7,
    marginBottom: 10,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalView: {
    height: Dimensions.get('window').height * 0.9,
    width: Dimensions.get('window').width * 0.4,
    backgroundColor: Colors.whiteColor,
    //borderRadius: windowWidth * 0.03,
    borderRadius: 12,
    padding: Dimensions.get('window').width * 0.02,
    paddingHorizontal: Dimensions.get('window').width * 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.04,
    top: Dimensions.get('window').width * 0.04,

    elevation: 1000,
    zIndex: 1000,
  },
  informationTitle: {
    color: '#808080',
    marginLeft: 10,
    marginTop: 10,
  },
  information: {
    marginLeft: 10,
    // borderBottomWidth: 1
  },
  submitButton: {
    backgroundColor: 'green',
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    marginTop: 10,
    width: Dimensions.get('window').width * 0.25,
    height: Dimensions.get('window').width * 0.04,
  },
  reservationDetailsSecondRow: {
    flexDirection: 'column',
    // marginRight: 100
  },
  reservationDetailsFourthRow: {
    flexDirection: 'column',
    // marginRight: 100
  },
  secondRowTextHeader: {
    color: 'gray',
    textAlign: 'center',
  },
  secondRowTextBody: {
    textAlign: 'center',
  },
  fourthRowTextHeader: {
    color: 'gray',
    textAlign: 'center',
  },
  fourthRowTextBody: {
    textAlign: 'center',
  },
  tierDropdown: {
    marginTop: 5,
    marginLeft: 5,
    // alignItems: 'center',
    borderRadius: 10,
    // justifyContent: 'center',
    backgroundColor: Colors.whiteColor,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    zIndex: 1,
  },
  sectionAreaButton: {
    marginLeft: 10,
    // width: Dimensions.get('window').width * 0.085,
    backgroundColor: Colors.whiteColor,
    height: Dimensions.get('window').height * 0.04,
    borderRadius: 8,
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',

    paddingLeft: 15,

    paddingRight: 35,

    elevation: 0,
    shadowOpacity: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  sectionAreaButtonTxt: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 14,
    color: Colors.primaryColor,
    textAlign: 'center',
  },
  emptyTableDisplay: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get('window').width * 0.12,
    height: Dimensions.get('window').width * 0.12,
    margin: 10,
    //borderRadius: windowWidth * 0.02,
    padding: Dimensions.get('window').width * 0.01,
  },
  modalContainerTable: {
    flex: 1,
    // backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalViewTable: {
    height: Dimensions.get('window').width * 0.3,
    width: Dimensions.get('window').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('window').width * 0.03,
    padding: 20,
    paddingTop: 25,
    //alignItems: 'center',
    //justifyContent: 'center'
  },
  tableTitle: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 16,
    textAlign: 'center',
    color: Colors.blackColor,
    width: Dimensions.get('window').width * 0.11,
  },
  tableItem: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 16,
    textAlign: 'center',
    color: Colors.blackColor,
    width: Dimensions.get('window').width * 0.11,
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pinNo: {
    fontSize: 20,
    fontFamily: 'NunitoSans-Bold',
  },
  timebtn: {
    backgroundColor: Colors.whiteColor,
    borderRadius: 5,
    paddingVertical: 10,
    justifyContent: 'center',
    alignContent: 'center',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    zIndex: -1,
  },
  timebtn2: {
    backgroundColor: Colors.primaryColor,
    borderRadius: 5,
    paddingVertical: 10,
    justifyContent: 'center',
    alignContent: 'center',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    zIndex: -1,
  },
});

// setting up picker style
const pickerStyleA = StyleSheet.create({
  // RNPickerSelect Icon Style
  iconContainer: {
    //top: 4,
    //left: '65%',
    top: Dimensions.get('window').height * 0.005,
    left: Dimensions.get('window').width * 0.065,
    //flexDirection: 'row',
    elevation: 1,
    alignItems: 'center',
    borderWidth: 0,
  },

  // RNPickerSelect Style with
  inputAndroid: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 16,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    backgroundColor: Colors.primaryColor,
    color: Colors.whiteColor,
    borderRadius: 5,
    width: Dimensions.get('window').width * 0.1,
    height: Dimensions.get('window').height * 0.05,
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  inputIOS: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 16,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    backgroundColor: Colors.primaryColor,
    color: Colors.whiteColor,
    borderRadius: 5,
    width: Dimensions.get('window').width * 0.1,
    height: Dimensions.get('window').height * 0.05,
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
});

const pickerStyle = StyleSheet.create({
  // RNPickerSelect Icon Style
  iconContainer: {
    //top: 4,
    //left: '65%',
    top: Dimensions.get('window').height * 0.005,
    left: Dimensions.get('window').width * 0.065,
    //flexDirection: 'row',
    elevation: 1,
    alignItems: 'center',
    borderWidth: 0,
  },

  // RNPickerSelect Style with
  inputAndroid: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 16,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    backgroundColor: Colors.fieldtBgColor,
    color: Colors.blackColor,
    borderRadius: 5,
    width: Dimensions.get('window').width * 0.1,
    height: Dimensions.get('window').height * 0.05,
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  inputIOS: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 16,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    backgroundColor: Colors.fieldtBgColor,
    color: Colors.blackColor,
    borderRadius: 5,
    width: Dimensions.get('window').width * 0.1,
    height: Dimensions.get('window').height * 0.05,
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
});

const pickerStyle1 = StyleSheet.create({
  // RNPickerSelect Icon Style
  iconContainer: {
    top: 4,
    left: '85%',
    //flexDirection: 'row',
    elevation: 1,
    alignItems: 'center',
    borderWidth: 0,
  },

  // RNPickerSelect Style with
  inputAndroid: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: Colors.fieldtBgColor,
    color: Colors.blackColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  inputIOS: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: Colors.fieldtBgColor,
    color: Colors.blackColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
});

const pickerStyle2 = StyleSheet.create({
  // RNPickerSelect Icon Style
  iconContainer: {
    top: 4,
    left: '65%',
    //flexDirection: 'row',
    elevation: 1,
    alignItems: 'center',
    borderWidth: 0,
  },

  // RNPickerSelect Style with
  inputAndroid: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: Colors.fieldtBgColor,
    color: Colors.blackColor,
    borderRadius: 5,
    width: 100,
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  inputIOS: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: Colors.fieldtBgColor,
    color: Colors.blackColor,
    borderRadius: 5,
    width: 100,
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
});

const pickerStyle3 = StyleSheet.create({
  // RNPickerSelect Icon Style
  iconContainer: {
    top: 4,
    left: '65%',
    //flexDirection: 'row',
    elevation: 1,
    alignItems: 'center',
    borderWidth: 0,
  },

  // RNPickerSelect Style with
  inputAndroid: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: Colors.fieldtBgColor,
    color: Colors.blackColor,
    borderRadius: 5,
    width: 120,
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  inputIOS: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: Colors.fieldtBgColor,
    color: Colors.blackColor,
    borderRadius: 5,
    width: 120,
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
});

const pickerStyle4 = StyleSheet.create({
  // RNPickerSelect Icon Style
  iconContainer: {
    top: 4,
    left: '65%',
    //flexDirection: 'row',
    elevation: 1,
    alignItems: 'center',
    borderWidth: 0,
  },

  // RNPickerSelect Style with
  inputAndroid: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: Colors.fieldtBgColor,
    color: Colors.blackColor,
    borderRadius: 5,
    width: 140,
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
  inputIOS: {
    fontFamily: 'NunitoSans-Bold',
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: Colors.fieldtBgColor,
    color: Colors.blackColor,
    borderRadius: 5,
    width: 140,
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
  },
});

export default DetailsScreen;
