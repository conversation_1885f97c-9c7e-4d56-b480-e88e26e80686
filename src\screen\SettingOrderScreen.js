import React, { Component, useReducer, useState, useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  TouchableOpacity,
  Dimensions,
  Modal,
  ActivityIndicator,
  useWindowDimensions,
} from "react-native";
import Colors from "../constant/Colors";
import SideBar from "./SideBar";
import Icon from "react-native-vector-icons/Feather";
import AntDesign from "react-native-vector-icons/AntDesign";
import Ionicon from "react-native-vector-icons/Ionicons";
import SimpleLineIcons from "react-native-vector-icons/SimpleLineIcons";
import { TextInput, FlatList } from "react-native-gesture-handler";
import { Picker } from "react-native";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import API from "../constant/API";
import ApiClient from "../util/ApiClient";
import * as User from "../util/User";
import LoginScreen from "./LoginScreen";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { color } from "react-native-reanimated";
import Close from "react-native-vector-icons/AntDesign";
import Styles from "../constant/Styles";
import moment, { isDate } from "moment";
import Ionicons from "react-native-vector-icons/Ionicons";
import { UserStore } from "../store/userStore";
import { MerchantStore } from "../store/merchantStore";
import { CommonStore } from "../store/commonStore";
import { OutletStore } from "../store/outletStore";
import Select from "react-select";
import { 
  sliceUnicodeStringV2WithDots,
  getTransformForScreenInsideNavigation,
} from "../util/common";
import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import APILocal from "../util/apiLocalReplacers";
import Switch from 'react-switch'
import { parseValidPriceText } from '../util/common';

const SettingOrderScreen = (props) => {
  //port til jun 4 2023 changes

  const { navigation } = props;

  const [loading, setLoading] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState(false);
  const [order, setOrder] = useState(true);
  const { height: windowHeight, width: windowWidth } = useWindowDimensions();

  //////////////////////////////////////////////////////////////////////////

  const [deliveryDistance, setDeliveryDistance] = useState("1");
  const [freeDeliveyAboveAmountValue, setFreeDeliveyAboveAmountValue] = useState("");
  const [freeDeliveyAboveAmountFlag, setFreeDeliveyAboveAmountFlag] = useState(false);
  const [discountOrderAboveAmountValue, setDiscountOrderAboveAmountValue] = useState("");
  const [discountOrderAboveAmountThreshold, setDiscountOrderAboveAmountThreshold] = useState("");

  const [deliveryPrice, setDeliveryPrice] = useState("0.00");
  const [pickUpPrice, setPickUpPrice] = useState("0.00");
  const [deliveryPackagingFee, setDeliveryPackagingFee] = useState("0.00");
  const [pickupPackagingFee, setPickupPackagingFee] = useState("0.00");

  const [takeawayAutoAuthorizationMinute, setTakeawayAutoAuthorizationMinute] = useState("0");
  const [takeawayAutoAuthorizationActive, setTakeawayAutoAuthorizationActive] = useState(false);

  const [takeawayAutoApproveOrdersActive, setTakeawayAutoApproveOrdersActive] = useState(false);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState("");

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const merchantId = UserStore.useState((s) => s.merchantId);

  const isLoading = CommonStore.useState((s) => s.isLoading);

  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView
  );

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  useEffect(() => {
    if (currOutlet && currOutlet.uniqueId) {
      setDeliveryDistance(currOutlet.deliveryDistance ? currOutlet.deliveryDistance.toFixed(0) : "1");
      setFreeDeliveyAboveAmountValue(currOutlet.freeDeliveyAboveAmountValue ? currOutlet.freeDeliveyAboveAmountValue.toFixed(2) : "0");
      setFreeDeliveyAboveAmountFlag(currOutlet.freeDeliveyAboveAmountFlag ? currOutlet.freeDeliveyAboveAmountFlag : false);
      setDiscountOrderAboveAmountValue(currOutlet.discountOrderAboveAmountValue ? currOutlet.discountOrderAboveAmountValue.toFixed(2) : "0");
      setDiscountOrderAboveAmountThreshold(currOutlet.discountOrderAboveAmountThreshold ? currOutlet.discountOrderAboveAmountThreshold.toFixed(2) : "0");

      setDeliveryPrice(currOutlet.deliveryPrice ? parseFloat(currOutlet.deliveryPrice).toFixed(2) : "0.00");
      setPickUpPrice(currOutlet.pickUpPrice ? parseFloat(currOutlet.pickUpPrice).toFixed(2) : "0.00");
      setDeliveryPackagingFee(currOutlet.deliveryPackagingFee ? parseFloat(currOutlet.deliveryPackagingFee).toFixed(2) : "0.00");
      setPickupPackagingFee(currOutlet.pickupPackagingFee ? parseFloat(currOutlet.pickupPackagingFee).toFixed(2) : "0.00");

      setTakeawayAutoAuthorizationMinute(currOutlet.takeawayAutoAuthorizationMinute ? parseFloat(currOutlet.takeawayAutoAuthorizationMinute).toFixed(0) : "0");
      setTakeawayAutoAuthorizationActive(currOutlet.takeawayAutoAuthorizationActive ? currOutlet.takeawayAutoAuthorizationActive : false);

      setTakeawayAutoApproveOrdersActive(currOutlet.takeawayAutoApproveOrdersActive ? currOutlet.takeawayAutoApproveOrdersActive : false);
    }
  }, [currOutlet]);

  // useEffect(() => {
  //   const selectedTargetOutlet = allOutlets.find(outlet => outlet.uniqueId === selectedTargetOutletId);

  //   if (selectedTargetOutlet) {
  //     setDeliveryDistance(selectedTargetOutlet.deliveryDistance ? selectedTargetOutlet.deliveryDistance.toFixed(0) : '1');
  //     setFreeDeliveyAboveAmountValue(selectedTargetOutlet.freeDeliveyAboveAmountValue ? selectedTargetOutlet.freeDeliveyAboveAmountValue.toFixed(2) : '0');
  //     setFreeDeliveyAboveAmountFlag(selectedTargetOutlet.freeDeliveyAboveAmountFlag ? selectedTargetOutlet.freeDeliveyAboveAmountFlag : false);
  //     setDiscountOrderAboveAmountValue(selectedTargetOutlet.discountOrderAboveAmountValue ? selectedTargetOutlet.discountOrderAboveAmountValue.toFixed(2) : '0');
  //     setDiscountOrderAboveAmountThreshold(selectedTargetOutlet.discountOrderAboveAmountThreshold ? selectedTargetOutlet.discountOrderAboveAmountThreshold.toFixed(2) : '0');

  //     setDeliveryPrice(selectedTargetOutlet.deliveryPrice ? parseFloat(selectedTargetOutlet.deliveryPrice).toFixed(2) : '0');
  //     setPickUpPrice(selectedTargetOutlet.pickUpPrice ? parseFloat(selectedTargetOutlet.pickUpPrice).toFixed(2) : '0');
  //     setDeliveryPackagingFee(selectedTargetOutlet.deliveryPackagingFee ? parseFloat(selectedTargetOutlet.deliveryPackagingFee).toFixed(2) : '0');
  //     setPickupPackagingFee(selectedTargetOutlet.pickupPackagingFee ? parseFloat(selectedTargetOutlet.pickupPackagingFee).toFixed(2) : '0');
  //   }
  // }, [selectedTargetOutletId]);

  useEffect(() => {
    setTargetOutletDropdownList(allOutlets.map(outlet => ({ label: outlet.name, value: outlet.uniqueId })));

    if (selectedTargetOutletId === "" && allOutlets.length > 0) {
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
    }
  }, [allOutlets]);

  //////////////////////////////////////////////////////////////////////////

  const setState = () => { };

  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus
  );
  // const [outletDropdownList, setOutletDropdownList] = useState([]);
  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  // var outletNames = [];

  // for (var i = 0; i < allOutlets.length; i++) {
  //   for (var j = 0; j < selectedOutletList.length; j++) {
  //     if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
  //       outletNames.push(allOutlets[i].name);
  //       break;
  //     }
  //   }
  // }

  // useEffect(() => {
  //   setOutletDropdownList(
  //     allOutlets.map((item) => {
  //       return { label: item.name, value: item.uniqueId };
  //     })
  //   );
  // }, [allOutlets]);

  var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));

  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.outletSelectDropdownView = () => {
  //       return (
  //         <View
  //           style={{
  //             flexDirection: "row",
  //             alignItems: "center",
  //             borderRadius: 8,
  //             width: 200,
  //             backgroundColor: "white",
  //           }}
  //         >
  //           {currOutletId.length > 0 &&
  //             allOutlets.find((item) => item.uniqueId === currOutletId) ? (
  //             <MultiSelect
  //               clearable={false}
  //               singleSelect={true}
  //               defaultValue={currOutletId}
  //               placeholder={"Choose Outlet"}
  //               onChange={(value) => {
  //                 if (value) { // if choose the same option again, value = ''
  //                   MerchantStore.update((s) => {
  //                     s.currOutletId = value;
  //                     s.currOutlet =
  //                       allOutlets.find(
  //                         (outlet) => outlet.uniqueId === value
  //                       ) || {};
  //                   });
  //                 }

  //                 CommonStore.update((s) => {
  //                   s.shiftClosedModal = false;
  //                 });
  //               }}
  //               options={targetOutletDropdownListTemp}
  //               className="msl-varsHEADER"
  //             />
  //           ) : (
  //             <ActivityIndicator size={"small"} color={Colors.whiteColor} />
  //           )}

  //           {/* <Select

  //             placeholder={"Choose Outlet"}
  //             onChange={(items) => {
  //               setSelectedOutletList(items);
  //             }}
  //             options={outletDropdownList}
  //             isMulti
  //           /> */}
  //         </View>
  //       );
  //     };
  //   });
  // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
        style={{
          width: 124,
          height: 26,
        }}
        resizeMode="contain"
        source={require('../assets/image/logo.png')}
      /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Order Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo Manager")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
            style={{
              width: windowHeight * 0.05,
            height: windowHeight * 0.05,
              alignSelf: 'center',
            }}
            source={require('../assets/image/profile-pic.jpg')}
          /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const orderFunc = () => {
    var body = {
      merchantId: merchantId,
      // outletId: selectedTargetOutletId,
      outletId: currOutletId,
      deliveryDistance: +(parseFloat(deliveryDistance).toFixed(2)),
      freeDeliveyAboveAmountValue: +(parseFloat(freeDeliveyAboveAmountValue).toFixed(2)),
      freeDeliveyAboveAmountFlag: freeDeliveyAboveAmountFlag,
      discountOrderAboveAmountValue: +(parseFloat(discountOrderAboveAmountValue).toFixed(2)),
      discountOrderAboveAmountThreshold: +(parseFloat(discountOrderAboveAmountThreshold).toFixed(2)),
      deliveryPrice: +(parseFloat(deliveryPrice).toFixed(2)),
      pickUpPrice: +(parseFloat(pickUpPrice).toFixed(2)),
      deliveryPackagingFee: +(parseFloat(deliveryPackagingFee).toFixed(2)),
      pickupPackagingFee: +(parseFloat(pickupPackagingFee).toFixed(2)),

      takeawayAutoAuthorizationMinute: +(parseFloat(takeawayAutoAuthorizationMinute).toFixed(0)),
      takeawayAutoAuthorizationActive: takeawayAutoAuthorizationActive,
      takeawayAutoApproveOrdersActive: takeawayAutoApproveOrdersActive ? takeawayAutoApproveOrdersActive : false,
    };

    // console.log(body);

    // ApiClient.POST(API.updateOutletOrderDetails, body, false)
    APILocal.updateOutletOrderDetails({ body: body })
      .then((result) => {
        setLoading(true);
        if (result && result.status === "success") {
          if (window.confirm("Success. Update was successful") == true) {
            setLoading(false);
          }
        }
      });
  }

  return (
    <View
      style={[
        styles.container,
        {
          height: windowHeight,
          width: windowWidth,
          ...getTransformForScreenInsideNavigation(),
        },
      ]}
    >
      <View style={{ flex: 0.8 }}>
        <SideBar
          navigation={props.navigation}
          selectedTab={10}
          expandSettings={true}
        />
      </View>

      <View style={{ height: windowHeight, flex: 9 }}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ width: windowWidth * 0.9 }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}
        >
          {/* <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}> */}

            <View
              style={{
                  paddingVertical: 30,
                  marginHorizontal: 30,
            }}>
              {order ? (
                <View
                  style={{
                      backgroundColor: Colors.whiteColor,
                      width: windowWidth * 0.877,
                      // height: windowHeight * 0.7,
                      marginTop: 10,
                      marginBottom: 30,
                      marginHorizontal: 30,
                      alignSelf: "center",
                      borderRadius: 5,
                      shadowOpacity: 0,
                      shadowColor: "#000",
                      shadowOffset: {
                      width: 0,
                      height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                  }}
                >
                  <ScrollView
                    contentContainerStyle={{
                      // top: Platform.OS === "ios" ? -keyboardHeight * 0.3 : 0,
                    }}>
                    <View style={{}}>
                      <View style={{ flexDirection: "row", padding: 30, zIndex: -3, }}>
                        <View style={{ flex: 3 }}>
                          <View
                            style={{ flexDirection: "row", flex: 2, marginTop: 30, zIndex: -3, }}>
                            {/* left */}
                            <View style={{ justifyContent: "center", flex: 1 }}>
                              <View>
                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: switchMerchant ? 10 : 14, }}>
                                  Takeaway Auto-Authorization Time (minute){" "}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: "row" }}>
                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: "center",
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    {
                                      backgroundColor: Colors.fieldtBgColor,
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: switchMerchant ? 10 : 14,
                                    },
                                    {
                                      width: windowWidth * 0.26,
                                    },
                                  ]}
                                  placeholderStyle={{
                                    color: "black",
                                    height: 40,
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  itemStyle={{
                                    justifyContent: "flex-start",
                                    marginLeft: 5,
                                  }}
                                  keyboardType={"decimal-pad"}
                                  placeholder="60"
                                  defaultValue={takeawayAutoAuthorizationMinute}
                                  onChangeText={(text) => {
                                    setTakeawayAutoAuthorizationMinute(text);
                                  }}
                                />
                              </View>

                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: "center",
                                }}>
                                <Switch
                                  onChange={(statusTemp) =>
                                    // setState({ status: status })
                                    setTakeawayAutoAuthorizationActive(statusTemp)
                                  }
                                  checked={takeawayAutoAuthorizationActive}
                                  width={35}
                                  height={20}
                                  handleDiameter={30}
                                  uncheckedIcon={false}
                                  checkedIcon={false}
                                  boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                  activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                />
                              </View>
                            </View>

                            <View style={{ flex: 1, zIndex: -1, }}>
                            </View>
                          </View>
                          <View
                            style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                            {/* left */}
                            <View style={{ justifyContent: 'center', flex: 1 }}>
                              <View>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                  Scheduled Takeaway Auto-Authorization Time (minute){' '}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: 'row' }}>
                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: 'center',
                                }}>
                                <Switch
                                  onChange={(statusTemp) =>
                                    // setState({ status: status })
                                    setTakeawayAutoApproveOrdersActive(statusTemp)
                                  }
                                  checked={takeawayAutoApproveOrdersActive}
                                  width={35}
                                  height={20}
                                  handleDiameter={30}
                                  uncheckedIcon={false}
                                  checkedIcon={false}
                                  boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                  activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                />
                              </View>
                            </View>

                            <View style={{ flex: 1, justifyContent: 'center' }}>

                            </View>
                          </View>

                          <View
                            style={{ flexDirection: "row", flex: 2, marginTop: 30, zIndex: -3, }}>
                            {/* left */}
                            <View style={{ justifyContent: "center", flex: 1 }}>
                              <View>
                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: switchMerchant ? 10 : 14, }}>
                                  Delivery Max Distance{" "}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, flexDirection: "row" }}>
                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: "center",
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    {
                                      backgroundColor: Colors.fieldtBgColor,
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: switchMerchant ? 10 : 14,
                                    },
                                    {
                                      width: windowWidth * 0.26,
                                    },
                                  ]}
                                  placeholderStyle={{
                                    color: "black",
                                    height: 40,
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  itemStyle={{
                                    justifyContent: "flex-start",
                                    marginLeft: 5,
                                  }}
                                  keyboardType={"decimal-pad"}
                                  placeholder="0"
                                  defaultValue={deliveryDistance}
                                  onChangeText={(text) => {
                                    setDeliveryDistance(text);
                                  }}
                                />
                              </View>
                              <View
                                style={{
                                  marginLeft: switchMerchant ? 5 : 5,
                                  justifyContent: "center",
                                }}>
                                <Text
                                  style={{
                                    fontFamily: "NunitoSans-Regular", fontSize: switchMerchant ? 10 : 14,
                                    color: "black",
                                    opacity: 1,
                                    justifyContent: "center",
                                  }}>
                                  KM
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1 }}></View>
                          </View>

                          <View
                            style={{ flexDirection: "row", flex: 2, marginTop: 30, zIndex: -3, }}>
                            {/* left */}
                            <View style={{ justifyContent: "center", flex: 1 }}>
                              <View>
                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: switchMerchant ? 10 : 14, }}>
                                  Takeaway Charges Per Product (RM){" "}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, }}>
                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: "center",
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    {
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: switchMerchant ? 10 : 14,
                                    },
                                    {
                                      width: windowWidth * 0.26,
                                    },
                                  ]}
                                  placeholderStyle={{
                                    color: "black",
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  itemStyle={{
                                    justifyContent: "flex-start",
                                    marginLeft: 10,
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  keyboardType={"decimal-pad"}
                                  placeholder="0"
                                  containerStyle={{ height: 40 }}
                                  defaultValue={pickUpPrice}
                                  onChangeText={(text) => {
                                    setPickUpPrice(parseValidPriceText(text));
                                  }}
                                />
                              </View>
                            </View>

                            <View style={{ flex: 1 }}></View>
                          </View>

                          <View
                            style={{ flexDirection: "row", flex: 2, marginTop: 30, zIndex: -3, }}>
                            {/* left */}
                            <View style={{ justifyContent: "center", flex: 1 }}>
                              <View>
                                <Text style={{ fontFamily: "NunitoSans-Bold", fontSize: switchMerchant ? 10 : 14, }}>
                                  Delivery Charges Per Product (RM){" "}
                                </Text>
                              </View>
                            </View>
                            <View style={{ flex: 1, }}>
                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                  height: 50,
                                  justifyContent: "center",
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={[
                                    styles.textInputTakeawayCharges,
                                    {
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: switchMerchant ? 10 : 14,
                                    },
                                    {
                                      width: windowWidth * 0.26,
                                    },
                                  ]}
                                  placeholderStyle={{
                                    color: "black",
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  itemStyle={{
                                    justifyContent: "flex-start",
                                    marginLeft: 10,
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  keyboardType={"decimal-pad"}
                                  placeholder="0"
                                  containerStyle={{ height: 40 }}
                                  defaultValue={deliveryPrice}
                                  onChangeText={(text) => {
                                    setDeliveryPrice(parseValidPriceText(text));
                                  }}
                                />
                              </View>
                            </View>

                            <View style={{ flex: 1 }}></View>
                          </View>

                        </View>
                      </View>
                    </View>
                    <View style={{ alignItems: "center" }}>
                      <TouchableOpacity
                        style={{
                          justifyContent: "center",
                          flexDirection: "row",
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: "#4E9F7D",
                          borderRadius: 5,
                          width: 120,
                          paddingHorizontal: 10,
                          height: 35,
                          alignItems: "center",
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        disabled={loading}
                        onPress={() => { orderFunc() }}>
                        <Text style={{
                          color: Colors.whiteColor,
                          //marginLeft: 5,
                          fontSize: switchMerchant ? 10 : 16,
                          fontFamily: "NunitoSans-Bold",
                        }}>
                          {loading ? "LOADING..." : "SAVE"}
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View style={{ marginTop: 20 }}></View>
                  </ScrollView>
                </View>
              ) : null}
            </View>
          {/* </ScrollView> */}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: "row",
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  headerLogo1: {
    width: "100%",
    height: "100%",
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: "row",
    alignItems: "center",
  },
  listItem: {
    fontFamily: "NunitoSans-Regular",
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get("screen").width * Styles.sideBarWidth,
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get("screen").width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.highlightColor,
  },
  textInputTakeawayCharges: {
    backgroundColor: Colors.fieldtBgColor,
    width: 200,
    height: 40,
    borderRadius: 5,
    padding: 5,
    marginVertical: 5,
    borderWidth: 1,
    borderColor: "#E5E5E5",
    paddingLeft: 10,
    //marginLeft: 190,
  },

  textInput: {
    fontFamily: "NunitoSans-Regular",
    width: 300,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: "row",
  },
  textInput1: {
    fontFamily: "NunitoSans-Regular",
    width: 250,
    height: 40,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 10,
  },
  button: {
    backgroundColor: Colors.primaryColor,
    width: 150,
    padding: 8,
    borderRadius: 10,
    alignItems: "center",
    alignSelf: "center",
    marginBottom: 20,
  },
  addButtonView: {
    flexDirection: "row",
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: "center",
  },
  addNewView: {
    flexDirection: "row",
    justifyContent: "flex-start",
    marginBottom: 65,
    marginTop: 7,
    width: "83%",
    alignSelf: "flex-end",
  },
  headerLeftStyle: {
    width: Dimensions.get("screen").width * 0.17,
    justifyContent: "center",
    alignItems: "center",
  },
});
export default SettingOrderScreen;
