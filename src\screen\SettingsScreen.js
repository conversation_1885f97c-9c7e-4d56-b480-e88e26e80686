import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useRef,
  useCallback,
} from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  TouchableOpacity,
  Dimensions,
  Modal,
  Platform,
  ActivityIndicator,
  useWindowDimensions,
} from "react-native";
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicon from 'react-native-vector-icons/Ionicons';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import { TextInput, FlatList } from 'react-native-gesture-handler';
import DropDownPicker from 'react-native-dropdown-picker';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import TimeKeeper from "react-timekeeper";
///import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import * as User from '../util/User';
import LoginScreen from './LoginScreen';
import AsyncStorage from '@react-native-async-storage/async-storage';
//import CheckBox from 'react-native-check-box';
// import { color } from 'react-native-reanimated';
import AIcon from 'react-native-vector-icons/AntDesign';
import Styles from '../constant/Styles';
import moment, { isDate } from 'moment';
import Switch from "react-switch";
import Ionicons from 'react-native-vector-icons/Ionicons';
import {
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  //isTablet
} from '../util/common';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { ORDER_TYPE, RESET_DATA_TYPE, USER_ORDER_PAYMENT_OPTIONS, WEEK, EXPAND_TAB_TYPE, CASH_DRAWER_OPEN_EVENT_TYPE_DROPDOWN_LIST, CASH_DRAWER_OPEN_EVENT_TYPE, USER_IDLE_SIGN_OUT_EVENT_TYPE, USER_IDLE_SIGN_OUT_EVENT_TYPE_DROPDOWN_LIST, KD_PRINT_EVENT_TYPE_DROPDOWN_LIST, KD_PRINT_EVENT_TYPE, KD_PRINT_VARIATION, KD_PRINT_VARIATION_DROPDOWN_LIST, KD_FONT_SIZE, KD_FONT_SIZE_DROPDOWN_LIST, RECEIPT_PRINTING_EVENT_TYPE, RECEIPT_PRINTING_EVENT_TYPE_DROPDOWN_LIST, WEB_ORDER_VARIANT_LAYOUT, REPORT_DATA_SIZE_LIMIT, ORDER_TYPE_DETAILS, ORDER_TYPE_DETAILS_DROPDOWN_LIST, WEB_ORDER_LIST_LAYOUT, REPORT_DISPLAY_TYPE } from '../constant/common';
//import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import Autocomplete from "react-google-autocomplete";
import AsyncImage from '../components/asyncImage';
import {
  uploadImageToFirebaseStorage,
  sliceUnicodeStringV2WithDots,
  uploadImageToFirebaseStorage64,
  _base64ToArrayBuffer,
} from '../util/common';
import { CommonStore } from '../store/commonStore';
import {
  // USBPrinter,
  NetPrinter,
  BLEPrinter,
} from 'react-native-thermal-receipt-printer-image-qr';
import { CODEPAGE, ESCPOS_CMD } from '../constant/printer';
import {
  connectToPrinter,
  convertUtf8ArrayToStr,
  printBarcode,
} from '../util/printer';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { useFocusEffect } from '@react-navigation/native';
// import UserIdleWrapper from '../components/userIdleWrapper';
// import { TableStore } from '../store/tableStore';
import APILocal from '../util/apiLocalReplacers';

import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
// import DeviceInfo from 'react-native-device-info';
import { useFilePicker } from "use-file-picker";
import { ReactComponent as Beach } from "../assets/svg/Beach.svg";
import { ReactComponent as DefaultImage } from "../assets/svg/DefaultImage.svg";
import { ReactComponent as Edit } from "../assets/svg/Edit.svg";
import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";

const SettingScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  // const [keyboardHeight] = useKeyboard();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [selfCollect, setSelfCollect] = useState(true);
  const [openHourPickerVisible, setOpenHourPickerVisible] = useState(false);
  const [closeHourPickerVisible, setCloseHourPickerVisible] = useState(false);
  const [openHour, setOpenHour] = useState('');
  const [closeHour, setCloseHour] = useState('');
  const [isChecked2, setIsChecked2] = useState(false);
  const [isChecked3, setIsChecked3] = useState(false);
  const [isChecked4, setIsChecked4] = useState(false);
  const [isChecked5, setIsChecked5] = useState(false);
  const [isChecked6, setIsChecked6] = useState(false);
  const [isChecked7, setIsChecked7] = useState(false);
  const [isChecked8, setIsChecked8] = useState(false);
  const [isChecked9, setIsChecked9] = useState(false);
  const [isChecked10, setIsChecked10] = useState(false);
  const [isChecked11, setIsChecked11] = useState(false);
  const [isChecked12, setIsChecked12] = useState(false);
  const [isChecked13, setIsChecked13] = useState(false);
  const [value1, setValue1] = useState('');
  const [value2, setValue2] = useState('');
  const [amount, setAmount] = useState('');
  const [hourStart, setHourStart] = useState('');
  const [hourEnd, setHourEnd] = useState('');
  const [days, setDays] = useState(false);
  const [days1, setDays1] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showDistance, setShowDistance] = useState('');
  const [expiryPeriod, setExpiryPeriod] = useState('');
  const [extentionCharges, setExtentionCharges] = useState('');
  const [extentionDuration, setExtentionDuration] = useState('');
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [pickerMode, setPickerMode] = useState('datetime');
  const [switchMerchant, setSwitchMerchant] = useState(false);
  const [merchantDisplay, setMerchantDisplay] = useState(true);
  const [shift, setShift] = useState(false);
  const [tax, setTax] = useState(false);
  const [sample, setSample] = useState(false);
  const [redemption, setRedemption] = useState(false);
  const [redemptionList, setRedemptionList] = useState(true);
  const [redemptionAdd, setRedemptionAdd] = useState(false);
  const [order, setOrder] = useState(false);
  const [previousState, setPreviousState] = useState(false);
  const [receipt, setReceipt] = useState([]);
  const [detail, setDetail] = useState([]);
  const [number, setNumber] = useState([]);
  const [merchantInfo, setMerchantInfo] = useState([]);
  const [outlet, setOutlet] = useState([]);
  const [outletInfo, setOutletInfo] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  // const [outletId, setOutletId] = useState([]);
  const [merInfo, setMerInfo] = useState([]);
  // const [merchantId, setMerchantId] = useState([]);
  const [show, setShow] = useState(false);
  const [showModal3, setShowModal3] = useState(false);
  const [showModal4, setShowModal4] = useState(false);
  const [showModal5, setShowModal5] = useState(false);
  const [closingAmount, setClosingAmount] = useState('');
  const [options, setOptions] = useState([]);
  const [shift1, setShift1] = useState([]);
  const [status, setStatus] = useState(false);
  const [statusHalal, setStatusHalal] = useState(false);
  const [isQRPrintReceipt, setIsQRPrintReceipt] = useState(false);
  const [isQRNotPrintLogo, setIsQRNotPrintLogo] = useState(false);

  const [logo, setLogo] = useState("");
  const [logoType, setLogoType] = useState("");
  const [logoSelected, setLogoSelected] = useState(false);
  const [cover, setCover] = useState("");
  const [coverType, setCoverType] = useState("");
  const [coverSelected, setCoverSelected] = useState(false);

  const [
    openFileSelector,
    {
      plainFiles,
      filesContent,
      loading: loadingImageInput,
      clear: clearImageContainer,
      errors,
    },
  ] = useFilePicker({
    readAs: "DataURL",
    accept: ["image/*"],
    multiple: false,
  });

  // select image
  useEffect(() => {
    if (plainFiles.length && filesContent.length && !loadingImageInput) {
      if (logoSelected === true) {
        setLogo(filesContent[0].content);
        setLogoType(
          filesContent[0].name.slice(filesContent[0].name.lastIndexOf("."))
        );
        setLogoSelected(false);
        setIsLogoChanged(true);

        MerchantStore.update(s => {
          s.merchantLastUpdated = Date.now();
        });
      } else {
        setCover(filesContent[0].content);
        setCoverType(
          filesContent[0].name.slice(filesContent[0].name.lastIndexOf("."))
        );
        setIsCoverChanged(true);
        setCoverSelected(false);
      }
    }

    if (errors.length) console.error(errors);
  }, [plainFiles, filesContent, loadingImageInput, errors]);

  const [name, setName] = useState('');
  const [tname, setTname] = useState('');
  const [rate, setRate] = useState('');
  const [address, setAddress] = useState('');
  const [addressDisplay, setAddressDisplay] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [emailWarningStock, setEmailWarningStock] = useState('');
  const [payment, setPayment] = useState('');
  const [time, setTime] = useState('');
  const [statue, setStatue] = useState('');
  const [status1, setStatus1] = useState(false);
  const [outlets, setOutlets] = useState([]);
  const [outletId, setOutletId] = useState(null);
  const [myTextInput, setMyTextInput] = useState(React.createRef());
  const [start_time, setStart_time] = useState(false);
  const [end_time, setEnd_time] = useState(false);
  const [rev_time, setRev_time] = useState('');
  const [category, setCategory] = useState('');
  const [close, setClose] = useState('Closed');
  const [showNote, setShowNote] = useState(false);
  const [expandView, setExpandView] = useState(false);
  const [value, setValue] = useState('');
  const [extendOption, setExtendOption] = useState([
    { optionId: 1, price: 20, day: 7, days: false },
  ]);
  const [redemptionInfo, setRedemptionInfo] = useState([]);
  const [alloutlet, setAlloutlet] = useState([]);
  const [discount, setDiscount] = useState('');
  const [amount1, setAmount1] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState('');
  const [categoryOutlet, setCategoryOutlet] = useState([]);
  const [extend, setExtend] = useState([]);
  const [outletss, setOutletss] = useState([]);
  const [redemptionDetail, setRedemptionDetail] = useState([]);
  const [outletInfo1, setOutletInfo1] = useState([]);
  const [category1, setCategory1] = useState([]);
  const [merchantName, setMerchantName] = useState('');
  const [addOutletName, setAddOutletName] = useState('');
  const [addOutletWindow, setAddOutletWindow] = useState(false);
  const [taxList, setTaxList] = useState([]);
  const [note1, setNote1] = useState('');
  const [note2, setNote2] = useState('');
  const [note3, setNote3] = useState('');
  const [openings, setOpenings] = useState([]);
  const [editOpeningIndex, setEditOpeningIndex] = useState(0);

  const [editOpeningType, setEditOpeningType] = useState('start');
  const [outletLat, setOutletLat] = useState(0);
  const [outletLng, setOutletLng] = useState(0);

  const inputAcRef = useRef(null);

  const [isLogoChanged, setIsLogoChanged] = useState(false);
  const [isCoverChanged, setIsCoverChanged] = useState(false);

  const [dropDownResetList, setDropDownResetList] = useState([]);
  const [scOrderTypes, setScOrderTypes] = useState([ORDER_TYPE.DINEIN]);

  //////////////////////////////////////////////////////////

  // 2023-01-05 - Add app type and name support for sc

  const [scOrderTypeDetails, setScOrderTypeDetails] = useState([ORDER_TYPE_DETAILS.POS, ORDER_TYPE_DETAILS.QR])
  const [scName, setScName] = useState('');

  //////////////////////////////////////////////////////////

  const [deliveryPaymentDropDown, setDeliveryPaymentDropDown] = useState([
    {
      label: "Pay Now",
      value: USER_ORDER_PAYMENT_OPTIONS.ONLINE,
    },
    {
      label: "Pay Later",
      value: USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
    },
    {
      label: "All",
      value: USER_ORDER_PAYMENT_OPTIONS.ALL,
    },
  ]);
  const [resetDropDown, setResetDropDown] = useState([
    {
      label: "Order",
      value: RESET_DATA_TYPE.USER_ORDER,
    },
    {
      label: "Customer",
      value: RESET_DATA_TYPE.CRM_USER,
    },
  ]);

  // halal toggle and pork free toggle useState
  const [isEnabled, setIsEnabled] = useState(false);
  const toggleSwitch = () => setIsEnabled((previousState) => !previousState);

  const [printerName, setPrinterName] = useState('Printer Name');
  const [printerIP, setPrinterIP] = useState('*************');

  const [sstRate, setSstRate] = useState('6');
  const [sstNumber, setSstNumber] = useState('');
  const [scRate, setScRate] = useState('5');
  const [sstActive, setSstActive] = useState(true);
  const [scActive, setScActive] = useState(false);

  const [showModalBreakTime, setShowModalBreakTime] = useState(false);
  const [showTimePickerBreakTimeStart, setShowTimePickerBreakTimeStart] = useState(false);
  const [showTimePickerBreakTimeEnd, setShowTimePickerBreakTimeEnd] = useState(false);
  const [selectedBreakTimeDay, setSelectedBreakTimeDay] = useState(WEEK[0]);
  const [selectedBreakTimeDayIndex, setSelectedBreakTimeDayIndex] = useState(0);

  /////////////////////////////////////////////////////////////////////  

  const [pickupPaymentOptions, setPickupPaymentOptions] = useState(
    USER_ORDER_PAYMENT_OPTIONS.ONLINE,
  );
  const [deliveryPaymentOptions, setDeliveryPaymentOptions] = useState(
    USER_ORDER_PAYMENT_OPTIONS.ONLINE,
  );
  const [dineinPaymentOptions, setDineinPaymentOptions] = useState(
    USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
  );
  const [dineinPaymentOptionsGeneric, setDineinPaymentOptionsGeneric] = useState(
    USER_ORDER_PAYMENT_OPTIONS.ONLINE,
  );
  const [docketPaymentOptions, setDocketPaymentOptions] = useState(
    USER_ORDER_PAYMENT_OPTIONS.ONLINE,
  );
  const [cashDrawerOpeningOptions, setCashDrawerOpeningOptions] = useState(
    CASH_DRAWER_OPEN_EVENT_TYPE.ALWAYS,
  );
  const [receiptPrintingOptions, setReceiptPrintingOptions] = useState(
    CASH_DRAWER_OPEN_EVENT_TYPE.ALWAYS,
  );
  const [userIdleSignOutOptions, setUserIdleSignOutOptions] = useState(
    USER_IDLE_SIGN_OUT_EVENT_TYPE.NEVER,
  );
  // const switchTableLayout = TableStore.useState(s => s.switchTableLayout);

  // const forceCloseShiftBeforeSignOut = TableStore.useState(s => s.forceCloseShiftBeforeSignOut);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const [currOutletOpening, setCurrOutletOpening] = useState({});
  const [currOutletOpeningOff, setCurrOutletOpeningOff] = useState({});
  const [currOutletBreakTime, setCurrOutletBreakTime] = useState({
    [WEEK[0]]: [],
    [WEEK[1]]: [],
    [WEEK[2]]: [],
    [WEEK[3]]: [],
    [WEEK[4]]: [],
    [WEEK[5]]: [],
    [WEEK[6]]: [],
  });

  //////////////////////////////////////////////////////////

  // 2022-09-14 - Add support to toggle whether to skip user info

  const [skipUserInfo, setSkipUserInfo] = useState(false);

  //////////////////////////////////////////////////////////

  // 2022-10-17 - To control the deliver/reject/undo deliver/undo reject event for printing kitchen docket

  const [kdPrintEventTypes, setKdPrintEventTypes] = useState([]);

  //////////////////////////////////////////////////////////

  // 2022-10-17 - To control the variation for printing kitchen docket

  const [kdPrintVariation, setKdPrintVariation] = useState(KD_PRINT_VARIATION.SUMMARY);

  ////////////////////////////////////////////////////////// 

  const [kdFontSize, setKdFontSize] = useState(KD_FONT_SIZE.NORMAL);

  //////////////////////////////////////////////////////////

  const [kdHeaderFontSize, setKdHeaderFontSize] = useState(KD_FONT_SIZE.EXTRA_LARGE);

  //////////////////////////////////////////////////////////

  const [isKdPrintSku, setIsKdPrintSku] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-03-03 - For toggle offline mode

  const [toggleOfflineMode, setToggleOfflineMode] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-06-12 - For toggle open order

  const [toggleOpenOrder, setToggleOpenOrder] = useState(false);

  //////////////////////////////////////////////////////////


  // 2023-03-06 - For multiple pos terminal mode

  const [multiplePosTerminalMode, setMultiplePosTerminalMode] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-03-22 - For multiple pos terminal mode

  const [toggleDisableAutoPrint, setToggleDisableAutoPrint] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-06-30 - To disable printing alerts

  const [toggleDisablePrintingAlert, setToggleDisablePrintingAlert] = useState(false);

  //////////////////////////////////////////////////////////

  // 2022-12-28 - To control web order variant layout

  const [webOrderVariantLayout, setWebOrderVariantLayout] = useState(
    WEB_ORDER_VARIANT_LAYOUT.HORIZONTAL,
  );

  //////////////////////////////////////////////////////////

  // 2022-12-29 - To control report data size limit

  const [reportDataSizeLimit, setReportDataSizeLimit] = useState(
    REPORT_DATA_SIZE_LIMIT._500,
  );

  //////////////////////////////////////////////////////////

  // 2023-01-03 - Print receipt when user paid online

  const [printReceiptWhenPaidOnline, setPrintReceiptWhenPaidOnline] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-01-06 - Force to enter card info options (for Table > Payment Summary)

  const [forceToEnterCardInfo, setForceToEnterCardInfo] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-02-13 - To control web order list layout

  // const [webOrderListLayout, setWebOrderListLayout] = useState(
  //   WEB_ORDER_LIST_LAYOUT.GRID,
  // );

  //////////////////////////////////////////////////////////

  // 2023-02-21 - For web order v2 layout

  const [webOrderV2Layout, setWebOrderV2Layout] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-04-13 - Ask payment first options

  const [askPaymentFirstOrderTypes, setAskPaymentFirstOrderTypes] = useState([
    ORDER_TYPE.PICKUP,
    ORDER_TYPE.DELIVERY,
  ]);

  //////////////////////////////////////////////////////////

  // 2023-05-10 - Kitchen docket to print user info (name and phone)

  const [isKdPrintUserInfo, setIsKdPrintUserInfo] = useState(false);

  //////////////////////////////////////////////////////////

  // 2023-05-10 - Service charge for other d. orders

  const [scRateOtherD, setScRateOtherD] = useState('5');
  const [scActiveOtherD, setScActiveOtherD] = useState(false);
  const [scNameOtherD, setScNameOtherD] = useState(false);

  //////////////////////////////////////////////////////////

  // 2022-12-29 - To control report data size limit

  const [reportDisplayType, setReportDisplayType] = useState(
    REPORT_DISPLAY_TYPE.DAY,
  );

  //////////////////////////////////////////////////////////

  // 2023-05-19 - Auto print pay slip

  const [autoPrintPaySlip, setAutoPrintPaySlip] = useState(true);

  //////////////////////////////////////////////////////////

  const userName = UserStore.useState((s) => s.name);
  const merchantNameHeader = MerchantStore.useState((s) => s.name);

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const merchantLogo = MerchantStore.useState((s) => s.logo);
  const merchantLastUpdated = MerchantStore.useState((s) => s.merchantLastUpdated);
  const merchantShortcode = MerchantStore.useState((s) => s.shortcode);

  const outletsOpeningDict = OutletStore.useState((s) => s.outletsOpeningDict);

  const merchantId = UserStore.useState((s) => s.merchantId);

  const isOfflineReady = CommonStore.useState((s) => s.isOfflineReady);

  const [temp, setTemp] = useState('');

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const supportCodeData = CommonStore.useState(s => s.supportCodeData);

  const [switchTableLayout, setSwitchTableLayout] = useState(false);

  const [forceCloseShiftBeforeSignOut, setForceCloseShiftBeforeSignOut] = useState(false)

  const [openSC, setOpenSC] = useState(false);
  const [openRDT, setOpenRDT] = useState(false);
  const [openRDSL, setOpenRDSL] = useState(false);
  const [openAPOF, setOpenAPOF] = useState(false);
  const [openDPO, setOpenDPO] = useState(false);
  const [openTPO, setOpenTPO] = useState(false);
  const [openDiPO_D, setOpenDiPO_D] = useState(false);
  const [openDiPO_S, setOpenDiPO_S] = useState(false);
  const [openDocP, setOpenDocP] = useState(false);
  const [openRPO, setOpenRPO] = useState(false);
  const [openCDOO, setOpenCDOO] = useState(false);
  const [openKDPO, setOpenKDPO] = useState(false);
  const [openKDPV, setOpenKDPV] = useState(false);
  const [openKDFS, setOpenKDFS] = useState(false);
  const [openKDFS2, setOpenKDFS2] = useState(false);
  const [openIALO, setOpenIALO] = useState(false);
  const [openRESET, setOpenRESET] = useState(false);


  const setState = () => { };

  useEffect(async () => {
    var toggleDisableAutoPrintRaw = await AsyncStorage.getItem('toggleDisableAutoPrint');

    if (toggleDisableAutoPrintRaw === '1') {
      global.outletToggleDisableAutoPrint = true;

      setToggleDisableAutoPrint(true);
    }
    else if (toggleDisableAutoPrintRaw === '0') {
      global.outletToggleDisableAutoPrint = false;

      setToggleDisableAutoPrint(false);
    }
    else {
      global.outletToggleDisableAutoPrint = false;

      setToggleDisableAutoPrint(false);
    }
    
    //////////////////////////////////////////

    var toggleDisablePrintingAlertRaw = await AsyncStorage.getItem('toggleDisablePrintingAlert');

    if (toggleDisablePrintingAlertRaw === '1') {
      global.outletToggleDisablePrintingAlert = true;

      setToggleDisablePrintingAlert(true);
    }
    else if (toggleDisablePrintingAlertRaw === '0') {
      global.outletToggleDisablePrintingAlert = false;

      setToggleDisablePrintingAlert(false);
    }
    else {
      global.outletToggleDisablePrintingAlert = false;

      setToggleDisablePrintingAlert(false);
    }
  }, []);

  useEffect(() => {
    setMerchantName(merchantNameHeader);
    setLogo(merchantLogo);
  }, [merchantNameHeader, merchantLogo, currOutlet]);

  useEffect(() => {
    if (
      currOutlet &&
      currOutlet.uniqueId &&
      currOutletId === currOutlet.uniqueId
    ) {
      setAddress(currOutlet.address);
      setAddressDisplay(currOutlet.address ? currOutlet.address : '');
      setPhone(currOutlet.phone ? currOutlet.phone : '');
      setEmail(currOutlet.email ? currOutlet.email : '');
      setEmailWarningStock(currOutlet.emailWarningStock ? currOutlet.emailWarningStock : '');

      setStatus(currOutlet.isPickupAccepted);
      setStatus1(currOutlet.isDeliveryAccepted);
      setStatusHalal(currOutlet.isHalal ? currOutlet.isHalal : false);
      setIsQRPrintReceipt(currOutlet.isQRPrintReceipt ? currOutlet.isQRPrintReceipt : false);
      setIsQRNotPrintLogo(currOutlet.isQRNotPrintLogo ? currOutlet.isQRNotPrintLogo : false);

      setSkipUserInfo(currOutlet.skipUserInfo ? currOutlet.skipUserInfo : false);

      setCover(currOutlet.cover);

      setOutletLat(currOutlet.lat);
      setOutletLng(currOutlet.lng);

      inputAcRef &&
        inputAcRef.current &&
        inputAcRef.current.setAddressText(currOutlet.address);

      // if (outletsOpeningDict[currOutletId]) {
      //   setCurrOutletOpening(outletsOpeningDict[currOutletId]);
      // }

      if (currOutlet.outletOpeningOff) {
        setCurrOutletOpeningOff(currOutlet.outletOpeningOff);
      }
      else {
        setCurrOutletOpeningOff({});
      }

      if (currOutlet.outletBreakTime) {
        setCurrOutletBreakTime(currOutlet.outletBreakTime);
      }
      else {
        setCurrOutletBreakTime({
          [WEEK[0]]: [],
          [WEEK[1]]: [],
          [WEEK[2]]: [],
          [WEEK[3]]: [],
          [WEEK[4]]: [],
          [WEEK[5]]: [],
          [WEEK[6]]: [],
        });
      }

      setPickupPaymentOptions(currOutlet.pickupPaymentOptions);
      setDeliveryPaymentOptions(currOutlet.deliveryPaymentOptions);
      setDineinPaymentOptions(currOutlet.dineinPaymentOptions);
      setDineinPaymentOptionsGeneric(currOutlet.dineinPaymentOptionsGeneric || USER_ORDER_PAYMENT_OPTIONS.ONLINE);
      setDocketPaymentOptions(currOutlet.docketPaymentOptions || USER_ORDER_PAYMENT_OPTIONS.ONLINE);
      setCashDrawerOpeningOptions(currOutlet.cashDrawerOpeningOptions || CASH_DRAWER_OPEN_EVENT_TYPE.ALWAYS);
      setReceiptPrintingOptions(currOutlet.receiptPrintingOptions || RECEIPT_PRINTING_EVENT_TYPE.ALWAYS);
      setUserIdleSignOutOptions(currOutlet.userIdleSignOutOptions || USER_IDLE_SIGN_OUT_EVENT_TYPE.NEVER);

      setAskPaymentFirstOrderTypes(currOutlet.askPaymentFirstOrderTypes || [ORDER_TYPE.PICKUP, ORDER_TYPE.DELIVERY]);

      setSstRate((currOutlet.taxRate * 100).toFixed(0));
      setSstNumber(currOutlet.sstNumber ? currOutlet.sstNumber : '');
      setScRate((currOutlet.scRate * 100).toFixed(0));
      setSstActive(currOutlet.taxActive);
      setScActive(currOutlet.scActive);
      setScOrderTypes(currOutlet.scOrderTypes || [ORDER_TYPE.DINEIN]);
      setScOrderTypeDetails(currOutlet.scOrderTypeDetails ? currOutlet.scOrderTypeDetails : [ORDER_TYPE_DETAILS.POS, ORDER_TYPE_DETAILS.QR]);
      setScName(currOutlet.scName ? currOutlet.scName : '');

      setScRateOtherD(((currOutlet.scRateOtherD ? currOutlet.scRateOtherD : 0) * 100).toFixed(0));
      setScActiveOtherD(currOutlet.scActiveOtherD ? currOutlet.scActiveOtherD : false);
      setScNameOtherD(currOutlet.scNameOtherD ? currOutlet.scNameOtherD : '');

      setKdPrintEventTypes(currOutlet.kdPrintEventTypes !== undefined ? currOutlet.kdPrintEventTypes : [
        KD_PRINT_EVENT_TYPE.DELIVER,
        KD_PRINT_EVENT_TYPE.REJECT,
        KD_PRINT_EVENT_TYPE.UNDO_DELIVER,
        KD_PRINT_EVENT_TYPE.UNDO_REJECT,
        KD_PRINT_EVENT_TYPE.SWITCH_TABLE,
      ]);

      setKdPrintVariation(currOutlet.kdPrintVariation !== undefined ? currOutlet.kdPrintVariation : KD_PRINT_VARIATION.SUMMARY);

      setKdFontSize(currOutlet.kdFontSize !== undefined ? currOutlet.kdFontSize : KD_FONT_SIZE.NORMAL);

      setKdHeaderFontSize(currOutlet.kdHeaderFontSize !== undefined ? currOutlet.kdHeaderFontSize : KD_FONT_SIZE.EXTRA_LARGE);

      setIsKdPrintSku(currOutlet.isKdPrintSku !== undefined ? currOutlet.isKdPrintSku : false);

      setIsKdPrintUserInfo(currOutlet.isKdPrintUserInfo !== undefined ? currOutlet.isKdPrintUserInfo : false);

      setToggleOfflineMode(currOutlet.toggleOfflineMode !== undefined ? currOutlet.toggleOfflineMode : false);

      setToggleOpenOrder(currOutlet.toggleOpenOrder !== undefined ? currOutlet.toggleOpenOrder : false);

      setMultiplePosTerminalMode(currOutlet.multiplePosTerminalMode !== undefined ? currOutlet.multiplePosTerminalMode : false);

      // setToggleDisableAutoPrint(currOutlet.toggleDisableAutoPrint !== undefined ? currOutlet.toggleDisableAutoPrint : false);

      setPrintReceiptWhenPaidOnline(currOutlet.printReceiptWhenPaidOnline !== undefined ? currOutlet.printReceiptWhenPaidOnline : false);

      setAutoPrintPaySlip(currOutlet.autoPrintPaySlip !== undefined ? currOutlet.autoPrintPaySlip : true);

      setWebOrderVariantLayout(currOutlet.webOrderVariantLayout !== undefined ? currOutlet.webOrderVariantLayout : WEB_ORDER_VARIANT_LAYOUT.HORIZONTAL);

      // setWebOrderListLayout(currOutlet.webOrderListLayout !== undefined ? currOutlet.webOrderListLayout : WEB_ORDER_LIST_LAYOUT.GRID);

      setReportDataSizeLimit(currOutlet.reportDataSizeLimit !== undefined ? currOutlet.reportDataSizeLimit : REPORT_DATA_SIZE_LIMIT._500);

      setReportDisplayType(currOutlet.reportDisplayType !== undefined ? currOutlet.reportDisplayType : REPORT_DISPLAY_TYPE.DAY);

      setForceToEnterCardInfo(currOutlet.forceToEnterCardInfo !== undefined ? currOutlet.forceToEnterCardInfo : false);

      setWebOrderV2Layout(currOutlet.webOrderV2Layout !== undefined ? currOutlet.webOrderV2Layout : false);
    }
  }, [
    currOutlet,
    currOutletId,
    // outletsOpeningDict,
    // inputAcRef
  ]);

  useEffect(() => {
    if (outletsOpeningDict[currOutletId]) {
      setCurrOutletOpening(outletsOpeningDict[currOutletId]);
    }
  }, [
    outletsOpeningDict,
  ]);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView,
  );

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          General Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo Manager")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
              style={{
                width: windowHeight * 0.05,
              height: windowHeight * 0.05,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  useEffect(() => {
    loadFromAsyncStorage();
  }, []);

  const loadFromAsyncStorage = async () => {
    // const printerIPRaw = await AsyncStorage.getItem('printerIP');

    const printerIPRaw = global.printerIP;

    if (printerIPRaw) {
      setPrinterIP(printerIPRaw);
    }
  };

  const getRedemptionDetail = (param) => {
    // ApiClient.GET(API.getOneSettingRedemption + param).then((result) => {
    //   setState({
    //     redemptionDetail: result,
    //     outletInfo1: result.outlet.name,
    //     category1: result.category.name,
    //     expiryPeriod: result.expiryPeriod,
    //     extentionCharges: result.extentionCharges,
    //     extentionDuration: result.extentionDuration,
    //   });
    // });
  };

  // componentDidMount = () => {

  //   ApiClient.GET(API.getOutletByMerchant + User.getMerchantId()).then((result) => {
  //     setState({ outletInfo: result });
  //     result.map((element) => {
  //       setState({
  //         outletId: element.id,
  //         outletName: element.name,
  //         merchantName: element.merchant.name
  //       });
  //     });
  //   });

  //   ApiClient.GET(API.getAllSettingRedemption + User.getMerchantId()).then((result) => {
  //     setState({ redemptionInfo: result });
  //   });

  //   outletFunc()
  //   ApiClient.GET(API.getOutletCategory + User.getOutletId()).then((result) => {
  //     if (result !== undefined) {
  //       setState({ categoryOutlet: result });
  //     }

  //   });
  // }

  // console.log('device model');
  // console.log(DeviceInfo.getModel());

  // let version = DeviceInfo.getVersion();

  const pushArray1 = (param) => {
    const push = outletss;
    push.push({
      outletId: param,
    });
  };

  const removePush1 = (param) => {
    const items = outletss;
    const filterArray = items.filter((item) => item.id !== param);
    setState({ alloutlet: filterArray });
  };

  const check3 = (param) => {
    if (isChecked12 == false) {
      setState({ isChecked12: true });
      removePush1(param);
    } else {
      setState({ isChecked12: false });
      pushArray1(param);
    }
  };

  const filter = (param) => {
    // ApiClient.GET(API.getAllSettingRedemption + User.getMerchantId()).then((result) => {
    //   const result1 = items.filter((item) => item.outletId !== param)
    //   setState({ redemptionInfo: result1 });
    // });
  };

  const check1 = (price, duration) => {
    if (isChecked11 == false) {
      setState({
        isChecked11: true,
        extentionCharges: '0',
        extentionDuration: '0',
      });
    } else {
      setState({
        isChecked11: false,
        extentionCharges: price,
        extentionDuration: duration,
      });
    }
  };

  const outletFunc = () => {
    // ApiClient.GET(API.getOutletByMerchant).then((result) => {
    //   const tmpCategories = {};
    //   for (const category of result) {
    //     const outletName = category.merchant.name
    //     const outletId = category.id
    //     if (!tmpCategories[outletName]) {
    //       tmpCategories[outletName] = {
    //         label: outletName,
    //         value: outletId,
    //       };
    //     }
    //   }
    //   const categories = Object.values(tmpCategories);
    //   setState({ outlets: categories });
    // });
  };

  const onaddoption = () => {
    const extendOption = extendOption;
    extendOption.push({
      optionId: (extendOption.length + 1).toString(),
      price: '',
      day: '',
      days,
    });
    setState({ extendOption });
  };

  const renderOptions = () => {
    const options = [];
    const extendOption = extendOption;
    for (const opt of extendOption) {
      options.push(
        <View>
          <View style={{ flexDirection: 'row' }}>
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <input
                onChange={() => {
                  setState({
                    isChecked11: !isChecked11,
                  });
                  check1(extentionCharges, extentionDuration);
                }}
                style={{
                  marginRight: 10,
                  alignSelf: 'flex-end',
                  borderRadius: 15,
                }}
                type={'checkbox'}
                checked={isChecked11}
              />
            </View>
            <View>
              <View>
                <Text style={{ color: Colors.descriptionColor }}>RM:</Text>
                <TextInput
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={styles.textInput8}
                  placeholder="Amount"
                  defaultValue={extentionCharges}
                  onChangeText={(text) => {
                    const extendOption = extendOption;
                    const item = extendOption.find(
                      (obj) => obj.id === extendOption.id,
                    );
                    item.price = text;
                    setState({ extendOption, extentionCharges: text });
                  }}
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  value={(value) => {
                    const extendOption = extendOption;
                    const item = extendOption.find(
                      (obj) => obj.id === extendOption.id,
                    );
                    value = item.price;
                  }}
                  ref={myTextInput}
                />
              </View>
            </View>
            <View
              style={{
                width: '6%',
                justifyContent: 'center',
                alignItems: 'center',
                marginLeft: '3%',
              }}>
              <Text style={{ fontSize: 15 }}>For</Text>
            </View>
            <View style={{ marginLeft: '5%' }}>
              <Text
                style={{
                  color: Colors.descriptionColor,
                  fontSize: 15,
                }}>
                Durations:
              </Text>
              <View style={styles.textInput10}>
                <View style={{ flex: 1 }}>
                  <TextInput
                    underlineColorAndroid={Colors.fieldtBgColor}
                    style={{ alignSelf: 'center' }}
                    placeholder="Period"
                    defaultValue={extentionDuration}
                    onChangeText={(text) => {
                      const extendOption = extendOption;
                      const item = extendOption.find(
                        (obj) => obj.id === extendOption.id,
                      );
                      item.day = text;
                      setState({ extendOption, extentionDuration: text });
                    }}
                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                    value={(value) => {
                      const extendOption = extendOption;
                      const item = extendOption.find(
                        (obj) => obj.id === extendOption.id,
                      );
                      value = item.day;
                    }}
                    ref={myTextInput}
                  />
                </View>
                <View
                  style={{
                    flex: 1,
                    flexDirection: 'row',
                    borderLeftWidth: StyleSheet.hairlineWidth,
                    justifyContent: 'center',
                  }}>
                  <Text
                    style={{
                      fontSize: 14,
                      marginTop: 15,
                      marginLeft: '5%',
                      color: Colors.descriptionColor,
                    }}>
                    {days1 == false ? 'Days' : 'Months'}
                  </Text>
                  <TouchableOpacity
                    onPress={() => {
                      setState({ days1: !days1 });
                    }}>
                    <View
                      style={{
                        marginLeft: '30%',
                        marginTop: 18,
                      }}>
                      <SimpleLineIcons name="arrow-down" size={12} />
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </View>,
      );
    }
    return options;
  };

  const orderFunc = () => {
    var body = {
      merchantId: User.getMerchantId(),
      isAllOutlet: isChecked6 == true ? '1' : null,
      outletId,
      deliveryDistance: showDistance,
      deliveryFee: isChecked6 == true ? amount : amount1,
      deliveryHourStart: hourStart,
      deliveryHourEnd: hourEnd,
      deliveryPrice: isChecked8 == true ? value1 : 0,
      pickUpPrice: isChecked9 == true ? value2 : 0,
      fireorder: status1,
      category,
    };
    // ApiClient.POST(API.createSettingOrder, body, false).then((result) => {
    //   if (result.outletId != null) {
    //     Alert.alert(
    //       'Congratulation!',
    //       'You Have Successfully Created',
    //       [
    //         {
    //           text: 'OK',
    //           onPress: () => { },
    //         },
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  };

  const renderItem = ({ item }) => (
    <View
      style={{
        backgroundColor: '#ffffff',
        borderRadius: 5,
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 20,
        borderTopWidth: StyleSheet.hairlineWidth,
        borderTopColor: '#c4c4c4',
      }}>
      <Text
        style={{
          paddingLeft: 15,
          flex: 2,
          alignSelf: 'center',
          fontFamily: 'NunitoSans-SemiBold',
          fontSize: 18,
        }}>
        {item.outlet.name}
      </Text>
      <View
        style={{
          flex: 2,
          alignSelf: 'center',
          alignItems: 'center',
        }}>
        <Text
          style={{
            fontSize: 16,
            fontFamily: 'NunitoSans-SemiBold',
            marginRight: '20%',
          }}>
          {item.category == null ? 'Unknown' : item.category.name}
        </Text>
      </View>
      <View
        style={{
          flex: 2,
          alignItems: 'center',
          alignSelf: 'center',
        }}>
        <Text
          style={{
            alignSelf: 'center',
            fontSize: 16,
            fontFamily: 'NunitoSans-SemiBold',
          }}>
          {item.expiryPeriod} Days
        </Text>
      </View>
      <View
        style={{
          flex: 4,
        }}>
        <View
          style={{
            height: 50,
            width: '80%',
            backgroundColor: Colors.fontDark,
            justifyContent: 'center',
            borderRadius: 5,
            flexDirection: 'row',
            alignItems: 'center',
            marginLeft: '5%',
          }}>
          <Text
            style={{
              color: Colors.whiteColor,
              fontSize: 15,
              textAlign: 'center',
              fontFamily: 'NunitoSans-Bold',
            }}>
            {item.outlet.name}
          </Text>
          <View style={{ alignItems: 'flex-end' }}>
            <AntDesign name="close" size={15} color={Colors.whiteColor} />
          </View>
        </View>
      </View>
      <View style={{ flex: 1, alignItems: 'center' }}>
        <TouchableOpacity
          onPress={() => {
            setState({ redemptionList: !redemptionList });
            getRedemptionDetail(item.id);
          }}
        >
          <FontAwesome5 name="edit" size={23} color={Colors.primaryColor} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const redemptionFunc = () => {
    var body = {
      merchantId: User.getMerchantId(),
      isAllOutlet: isChecked13 == true ? '1' : null,
      outletId,
      category,
      expiryPeriod,
      extentionCharges,
      extentionDuration,
      redemptionOutletId: outletId,
    };
    // ApiClient.POST(API.createSettingRedemption, body, false).then((result) => {
    //   if (result.outletId != null) {
    //     Alert.alert(
    //       'Congratulation!',
    //       'Successfully',
    //       [
    //         {
    //           text: 'OK',
    //           onPress: () => { },
    //         },
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  };

  const addOutlet = () => {
    var body = {
      outletName: addOutletName,
      merchantId: User.getMerchantId(),
      merchantShortcode,
    };

    ApiClient.POST(API.createOutlet, body, false).then((result) => {
      if (result && result.status === 'success') {
        // Alert.alert(
        //   'Success!',
        //   'Outlet has been added',
        //   [
        //     {
        //       text: 'OK',
        //       onPress: () => { },
        //     },
        //   ],
        //   { cancelable: false },
        // );
        alert("Success!\nOutlet has been added");
      }
    });
  };

  const renderTax = ({ item }) => (
    <View
      style={{
        backgroundColor: '#FFFFFF',
        flexDirection: 'row',
        paddingVertical: 20,
        paddingHorizontal: 20,
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#C4C4C4',
      }}>
      <Text style={{ width: '23%', color: Colors.primaryColor }}>
        {item.name}
      </Text>
      <Text style={{ width: '52%' }}>{item.rate}%</Text>
      <TouchableOpacity
        onPress={() => {
          deleteTax(item.id);
        }}
        style={{ width: '40%' }}>
        <Icon name="trash-2" size={20} color="#eb3446" />
      </TouchableOpacity>
    </View>
  );

  // function here
  const onCloseShiftBtn = (key) => {
    var decimal = closingAmount.split('.')[1];
    if (key >= 0 || key == '.') {
      if (closingAmount.includes('.'))
        if (closingAmount.length < 12 && decimal.length < 2)
          setState({ closingAmount: closingAmount + key });
      if (!closingAmount.includes('.')) {
        if (closingAmount.length < 12)
          setState({ closingAmount: closingAmount + key });
      }
    } else if (closingAmount.length > 0)
      setState({ closingAmount: closingAmount.slice(0, key) });
  };

  const getCurrentShift = (outletId) => {
    // ApiClient.GET(API.getCurrentShift + outletId).then((result) => {
    //   setState({ shift1: result.success });
    // });
    // try {
    //   if (result.id != null) {
    //     Alert.alert(
    //       '',
    //       [
    //         {
    //           text: 'OK',
    //           onPress: () => { },
    //         },
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // } catch (error) { }
  };

  const getTax = (outletId) => {
    ApiClient.GET(API.merchantTax + outletId).then((result) => {
      setState({ taxList: result });
    });
  };

  const checkTaxName = (tname) => {
    for (const tax of taxList) {
      if (tname.toLowerCase() == tax.name.toLowerCase()) {
        return true;
      }
    }
    return false;
  };

  const createTax = () => {
    if (!tname) {
      // Alert.alert(
      //   'Error',
      //   'Please fill in the information',
      //   [{ text: 'OK', onPress: () => { } }],
      //   { cancelable: false },
      // );
      alert("Error\nPlease fill in the information");

    } else if (!Number.isInteger(Number(rate))) {
      // Alert.alert(
      //   'Error',
      //   'Tax is only in numeric',
      //   [{ text: 'OK', onPress: () => { } }],
      //   { cancelable: false },
      // );
      alert("Error\Tax is only in numeric");

    } else if (checkTaxName(tname)) {
      // Alert.alert(
      //   'Error',
      //   'Name has been used',
      //   [{ text: 'OK', onPress: () => { } }],
      //   { cancelable: false },
      // );
      alert("Error\Name has been used");

    } else {
      var body = {
        name: tname,
        rate,
        desciption: '',
        outletId: User.getOutletId(),
      };
      // ApiClient.POST(API.taxes, body, false).then((result) => {
      //   try {
      //     if (result) {
      //       Alert.alert(
      //         'Congratulation!',
      //         'You Have Successfully Inserted',
      //         [
      //           {
      //             text: 'OK',
      //             onPress: () => { },
      //           },
      //         ],
      //         { cancelable: false },
      //       );
      //       getTax(User.getOutletId())
      //     }
      //   } catch (error) {
      //     Alert.alert('Failed', [{ text: 'OK', onPress: () => { } }], {
      //       cancelable: false,
      //     });
      //   }
      // });
    }
  };

  const deleteTax = (taxId) => {
    // console.log('TAX ID', taxId);
    var body = {
      taxId,
    };
    // ApiClient.POST(API.deleteTax, body, false).then((result) => {
    //   try {
    //     // console.log("RESULT", result)
    //     if (result) {
    //       Alert.alert(
    //         'Successfully!',
    //         'You Have Successfully Deleted',
    //         [
    //           {
    //             text: 'OK',
    //             onPress: () => { },
    //           },
    //         ],
    //         { cancelable: false },
    //       );
    //       getTax(User.getOutletId())
    //     }
    //   } catch (error) {
    //     Alert.alert('Failed', [{ text: 'OK', onPress: () => { } }], {
    //       cancelable: false,
    //     });
    //   }
    // });
  };

  const renderSearch = (item) => {
    return (
      <View style={{ flexDirection: 'column' }}>
        <Text style={{ fontWeight: '700', fontSize: 14, marginBottom: 5 }}>
          {item.structured_formatting.main_text}
        </Text>

        <Text style={{ fontSize: 12, color: 'grey' }}>{item.description}</Text>
      </View>
    );
  };

  const outletById = (param) => {
    //<= outletId is not used in the param
    ApiClient.GET(API.outlet2 + param).then((result) => {
      //<= all you need to do is add parameter here
      // console.log('RESULT OUTLET', result);
      setState({ outlet: result, openings: result.openings });
      // console.log('openings', openings);
      setState({ status: result.status });
      setState({ merInfo: result.merchant[0] });
      setState({ cover: result.cover });
      setState({ logo: result.merchant[0].logo });
      setState({ openings: result.openings });

      myTextInput.current.clear();
    });
  };

  const editOutlet = (param) => {
    var body = {
      outletId: currOutletId,
      cover,

      merchantLogo: logo,

      address,
      addressDisplay,
      name,
      latlng: '',
      phone,
      email,
      taxId: '',
      status: '1',
      isBusy: '1',
      reservationStatus: true,
      openings: [
        {
          week: 'Monday',
          startTime: openings[0] ? openings[0].startTime : null,
          endTime: openings[0] ? openings[0].endTime : null,
        },
        {
          week: 'Tuesday',
          startTime: openings[1] ? openings[1].startTime : null,
          endTime: openings[1] ? openings[1].endTime : null,
        },
        {
          week: 'Wednesday',
          startTime: openings[2] ? openings[2].startTime : null,
          endTime: openings[2] ? openings[2].endTime : null,
        },
        {
          week: 'Thursday',
          startTime: openings[3] ? openings[3].startTime : null,
          endTime: openings[3] ? openings[3].endTime : null,
        },
        {
          week: 'Friday',
          startTime: openings[4] ? openings[4].startTime : null,
          endTime: openings[4] ? openings[4].endTime : null,
        },
        {
          week: 'Saturday',
          startTime: openings[5] ? openings[5].startTime : null,
          endTime: openings[5] ? openings[5].endTime : null,
        },
        {
          week: 'Sunday',
          startTime: openings[6] ? openings[6].startTime : null,
          endTime: openings[6] ? openings[6].endTime : null,
        },
      ],
      payments: [
        {
          name: payment,
        },
      ],
    };

    // console.log('body', body);

    // ApiClient.PATCH(API.editOutlet, body, false).then((result) => {
    //   if (result.id != null) {
    //     Alert.alert(
    //       'Congratulation!',
    //       'You Have Successfully Inserted',
    //       [
    //         {
    //           text: 'OK',
    //           onPress: () => { },
    //         },
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
    // outletById(param);
    // myTextInput.current.clear();
  };

  const updateOutletDetails = async (param) => {
    setLoading(true);

    // var merchantLogoPath = logo;
    // if (logo && logoType && isLogoChanged) {
    //   // outletItemIdLocal = selectedProductEdit.uniqueId;

    //   merchantLogoPath = await uploadImageToFirebaseStorage(
    //     {
    //       uri: logo,
    //       type: logoType,
    //     },
    //     `/merchant/${merchantId}/logo${logoType}`,
    //   );
    // }
    var merchantLogoPath = "";
    if (logo && logoType && isLogoChanged) {
      const rawBase64 = logo
        .replace("data:image/jpeg;base64,", "")
        .replace("data:image/jpg;base64,", "")
        .replace("data:image/png;base64,", "");

      const arrayBuffer = _base64ToArrayBuffer(rawBase64);

      // outletItemIdLocal = selectedProductEdit.uniqueId;
      merchantLogoPath = await uploadImageToFirebaseStorage64(
        {
          arrayBuffer: arrayBuffer,
          type: coverType,
        },
        `/merchant/${merchantId}/logo${logoType}`,
      );
    }
    // var outletCoverPath = '';
    // if (cover && coverType && isCoverChanged) {
    //   // outletItemIdLocal = selectedProductEdit.uniqueId;
    //   const rawBase64 = cover
    //     .replace("data:image/jpeg;base64,", "")
    //     .replace("data:image/jpg;base64,", "")
    //     .replace("data:image/png;base64,", "");

    //   const arrayBuffer = _base64ToArrayBuffer(rawBase64);

    //   outletCoverPath = await uploadImageToFirebaseStorage64(
    //     {
    //       arrayBuffer: arrayBuffer,
    //       type: coverType,
    //     },
    //     `/merchant/${merchantId}/outlet/${currOutletId}/cover${coverType}`,
    //   );
    // }
    ///UPLOAD IMAGE///

    var outletCoverPath = "";
    if (cover && coverType && isCoverChanged) {
      const rawBase64 = cover
        .replace("data:image/jpeg;base64,", "")
        .replace("data:image/jpg;base64,", "")
        .replace("data:image/png;base64,", "");

      const arrayBuffer = _base64ToArrayBuffer(rawBase64);

      // outletItemIdLocal = selectedProductEdit.uniqueId;
      outletCoverPath = await uploadImageToFirebaseStorage64(
        {
          arrayBuffer: arrayBuffer,
          type: coverType,
        },
        `/merchant/${merchantId}/outlet/${currOutletId}/cover${coverType}`
      );
    }

    var body = {
      outletId: currOutletId,
      cover: outletCoverPath,

      merchantId,
      merchantLogo: merchantLogoPath,

      address: addressDisplay ? addressDisplay : address,
      addressDisplay,
      merchantName,

      lat: outletLat,
      lng: outletLng,

      phone,
      email,

      isPickupAccepted: status,
      isDeliveryAccepted: status1,
      isHalal: statusHalal,
      isQRPrintReceipt,
      isQRNotPrintLogo,

      skipUserInfo,

      outletOpeningDetails: currOutletOpening,
      outletOpeningOff: currOutletOpeningOff,
      outletBreakTime: currOutletBreakTime,

      pickupPaymentOptions,
      deliveryPaymentOptions,
      dineinPaymentOptions,
      dineinPaymentOptionsGeneric,
      docketPaymentOptions,
      cashDrawerOpeningOptions,
      receiptPrintingOptions,
      userIdleSignOutOptions,

      askPaymentFirstOrderTypes,

      taxRate: !isNaN(sstRate) ? +(sstRate / 100).toFixed(2) : 0.06,
      taxNum: sstNumber ? sstNumber : '',
      scRate: !isNaN(scRate) ? +(scRate / 100).toFixed(2) : 0.05,
      taxActive: sstActive,
      scActive,
      scOrderTypes,
      scOrderTypeDetails,
      scName,

      scRateOtherD: !isNaN(scRateOtherD) ? +(scRateOtherD / 100).toFixed(2) : 0.05,
      scActiveOtherD,
      scNameOtherD,

      kdPrintEventTypes,

      kdPrintVariation,

      kdFontSize,

      kdHeaderFontSize,

      isKdPrintSku,

      isKdPrintUserInfo,

      toggleOfflineMode,

      toggleOpenOrder,

      multiplePosTerminalMode,

      // toggleDisableAutoPrint: toggleDisableAutoPrint,

      printReceiptWhenPaidOnline,

      autoPrintPaySlip,

      webOrderVariantLayout,

      // webOrderListLayout: webOrderListLayout,

      reportDataSizeLimit,

      reportDisplayType,

      switchTableLayout,

      forceCloseShiftBeforeSignOut,

      emailWarningStock,

      forceToEnterCardInfo,

      webOrderV2Layout,

      // taxId: '',
      // status: '1',
      // isBusy: '1',
      // reservationStatus: true,
    };

    // console.log('body', body);

    APILocal.updateOutletDetails({ body }).then((result) => {
      // ApiClient.POST(API.updateOutletDetails, body, false).then((result) => {
      // if (result && result.status === 'success') {
      //   Alert.alert(
      //     'Success',
      //     'Outlet details has been updated',
      //     [
      //       {
      //         text: 'OK',
      //         onPress: () => {
      //           setLoading(false);
      //         },
      //       },
      //     ],
      //     { cancelable: false },
      //   );
      // }
    });

    // if (window.confirm('Success! \nOutlet details has been updated, and will be synced to devices across the outlet.')) {
    alert('Success! \nOutlet details has been updated, and will be synced to devices across the outlet.')
    setLoading(false);
    // }
  };

  // const resetCache = async () => {
  //   var dataFolder = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : (`${RNFS.ExternalStorageDirectoryPath}/Documents`)}/KooDooData`;

  //   let exists = await RNFS.exists(dataFolder);
  //   if (exists) {
  //     await RNFS.unlink(dataFolder);
  //   } else {
  //   }

  //   // Alert.alert('Info', 'Please close the app and open again for the changes to take effect.');

  //   // DevSettings.reload();

  //   console.log('done!');
  // };

  const resetOutletData = async () => {
    setLoading(true);

    var body = {
      resetDataTypes: dropDownResetList,

      outletId: currOutletId,
      merchantId,
    };

    // console.log('body', body);

    ApiClient.POST(API.resetOutletData, body, false).then((result) => {
      if (result && result.status === 'success') {
        if (dropDownResetList.includes(RESET_DATA_TYPE.USER_ORDER)) {
          OutletStore.update(s => {
            s.allOutletsUserOrdersDoneCache = [];
            s.allOutletsUserOrdersCache = [];
          });
        }
        else if (dropDownResetList.includes(RESET_DATA_TYPE.CRM_USER)) {
          CommonStore.update(s => {
            s.selectedCustomerEdit = null;
          });
        }

        // setLoading(false);

        // Alert.alert(
        //   'Success',
        //   'Outlet data has been reset.',
        //   [
        //     {
        //       text: 'OK',
        //       onPress: () => {
        //         setLoading(false);
        //       },
        //     },
        //   ],
        //   { cancelable: false },
        // );
        alert("Success\nOutlet data has been reset.");
        setLoading(false);
      }
    });
  };

  const handleChoosePhotoCover = () => {
    const imagePickerOptions = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      maxWidth: 1280,
      maxHeight: 628, // 628
    };

    // launchImageLibrary(imagePickerOptions, (response) => {
    //   if (response.didCancel) {
    //   } else if (response.error) {
    //     Alert.alert(response.error.toString());
    //   } else {
    //     if (response.width > 1280 || response.height > 628) {
    //       Alert.alert('Cover\'s width and height must be same or less than 1280x628.');
    //       return;
    //     }

    //     // setState({ image: response.uri });
    //     setCover(response.uri);
    //     setCoverType(response.uri.slice(response.uri.lastIndexOf('.')));

    //     setIsCoverChanged(true);
    //   }
    // });
  };

  const handleChoosePhotoLogo = () => {
    const imagePickerOptions = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      maxWidth: 256,
      maxHeight: 256,
    };

    // launchImageLibrary(imagePickerOptions, (response) => {
    //   if (response.didCancel) {
    //   } else if (response.error) {
    //     Alert.alert(response.error.toString());
    //   } else {
    //     if (response.width > 256 || response.height > 256) {
    //       Alert.alert('Logo\'s width and height must be same or less than 256x256.');
    //       return;
    //     }

    //     setLogo(response.uri);
    //     setLogoType(response.uri.slice(response.uri.lastIndexOf('.')));

    //     setIsLogoChanged(true);
    //   }
    // });
  };

  const handleChoosePhoto1 = () => {
    const options = {
      mediaType: 'photo',
      quality: 0.5,
      includeBase64: false,
      maxWidth: 512,
      maxHeight: 512,
    };
    // launchImageLibrary(options, (response) => {
    //   if (response.didCancel) {
    //   } else if (response.error) {
    //     Alert.alert(response.error.toString());
    //   } else {
    //     // setState({ cover: response.uri });
    //     setIsCoverChanged(true);

    //     setCover(response.uri);
    //     setCoverType(response.uri.slice(response.uri.lastIndexOf('.')));
    //   }
    // });
  };

  const closingShift = () => {
    var body = {
      outletId: User.getOutletId(),
      amount: closingAmount,
    };
    // ApiClient.POST(API.closeShift, body, false).then((result) => {
    //   if (result) {
    //     Alert.alert(
    //       'Successfully',
    //       'Close Success',
    //       [
    //         {
    //           text: 'OK',
    //           onPress: () => {
    //             _logout();
    //             setState({
    //               showModal3: false,
    //               show: false,
    //               showModal5: false,
    //             });
    //           },
    //         },
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  };

  const getCurrentDate = () => {
    var date = new Date().getDate();
    var month = new Date().getMonth() + 1;
    var year = new Date().getFullYear();
    var hours = new Date().getHours();
    var min = new Date().getMinutes();
    var sec = new Date().getSeconds();
    var day = new Date().getDay();

    return (
      '9.00AM, ' + 'Thursday ' + 'September ' + '20 ' + '2020'
      // hours + '9.00AM' + min + '' + day +'Thursday'  + month + 'September' + date + '20'+ year + '2020'
    );
  };

  const _logout = async () => {
    await AsyncStorage.clear();
    User.setlogin(false);
    User.getRefreshMainScreen();
  };

  const addSection = () => {
    setState({ showNote: true });
  };

  // default = () => {
  //   if (status == 0) {
  //     return false;
  //   } else if (status == 1) {
  //     return true;
  //   }
  // }

  // default1 = () => {
  //   if (status1 == 0) {
  //     return false;
  //   } else if (status1 == 1) {
  //     return true;
  //   }
  // }

  // return = () => {
  //   if (status == false) {
  //     return 0;
  //   } else if (status == true) {
  //     return 1;
  //   }
  // }
  // return1 = () => {
  //   if (status == false) {
  //     return 0;
  //   } else if (status == true) {
  //     return 1;
  //   }
  // }

  // update = () => {
  //   if (status == false) {
  //     return 0;
  //   } else if (status == true) {
  //     return 1;
  //   }
  // }

  const bindPrinter = async () => {
    if (printerIP.length > 0) {
      try {
        await AsyncStorage.setItem('printerIP', printerIP);

        const result = await connectToPrinter();

        if (result) {
          // Alert.alert('Success', 'IP has been binded');
          alert('Success', 'IP has been binded');
        } else {
          // Alert.alert('Error', 'Unable to bind the IP');
          alert('Error', 'Unable to bind the IP');
        }
      } catch (ex) {
        // console.log(ex);
      }
    } else {
      // Alert.alert('Error', 'Invalid IP address');
      alert('Error', 'Invalid IP address');
    }
  };

  const unbindPrinter = async () => {
    try {
      await AsyncStorage.removeItem('printerIP');

      // Alert.alert('Succes', 'IP has been unbinded');
      alert('Succes', 'IP has been unbinded');
    } catch (ex) {
      // console.log(ex);

      // Alert.alert('Error', 'Failed to unbind the IP');
      alert('Error', 'Failed to unbind the IP');
    }
  };

  const testPrinter = async () => {
    // console.log('test printer');

    await connectToPrinter();

    var testItems = [
      {
        name: 'Coconut Coffee',
        remarks: 'LESS SWEET',
        price: 8,
        discount: 0,
        quantity: 1,
        subtotal: 8,
        tax: 0,
      },
      {
        name: 'Yogurt Coffee',
        remarks: 'LESS ICE',
        price: 8,
        discount: 0,
        quantity: 1,
        subtotal: 8,
        tax: 0,
      },
    ];

    try {
      var result = `${ESCPOS_CMD.SIZE_NORMAL}${ESCPOS_CMD.UNDERLINE_OFF}${ESCPOS_CMD.BOLD_OFF}`;
      result += `${ESCPOS_CMD.CENTER}${currOutlet.name}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}${currOutlet.address}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}${currOutlet.phone}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}${merchantName}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}SST ID No. 0012612771${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_2X}ORDER #38${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${[...Array(48)]
        .map((i) => '-')
        .join('')}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}Mark${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}0127148876${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'Receipt Date'.padEnd(
        12,
        ' ',
      )} : ${moment().format('ddd, MMM D, YYYY')}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'Receipt #'.padEnd(12, ' ')} : ${'38'}${ESCPOS_CMD.NEWLINE
        }`;
      result += `${ESCPOS_CMD.LEFT}${'Cashier'.padEnd(
        12,
        ' ',
      )} : ${'Sophie Kim'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'ITEM'.padEnd(20, ' ')}${'PRICE'.padEnd(
        8,
        ' ',
      )}${'DISC'.padEnd(8, ' ')}${'QTY'.padEnd(4, ' ')}${'SUB'.padStart(
        8,
        ' ',
      )}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}${[...Array(48)]
        .map((i) => '-')
        .join('')}${ESCPOS_CMD.NEWLINE}`;

      for (var i = 0; i < testItems.length; i++) {
        const testItem = testItems[i];

        result += `${ESCPOS_CMD.LEFT}${testItem.name
          .slice(0, 20)
          .padEnd(20, ' ')}${(
            `RM${(testItem.price || 0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
          ).padEnd(8, ' ')}${(
            `RM${testItem.discount.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
          ).padEnd(8, ' ')}${testItem.quantity.toString().padEnd(4, ' ')}${(
            `RM${testItem.subtotal.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
          ).padStart(8, ' ')}${ESCPOS_CMD.NEWLINE}`;

        if (testItem.remarks) {
          result += `${ESCPOS_CMD.LEFT}${testItem.remarks
            .slice(0, 20)
            .padEnd(20, ' ')}${''.padEnd(8, ' ')}${''.padEnd(
              8,
              ' ',
            )}${''.padEnd(4, ' ')}${''.padEnd(8, ' ')}${ESCPOS_CMD.NEWLINE}`;
        }

        if (i !== testItems.length - 1) {
          result += `${ESCPOS_CMD.NEWLINE}`;
        }
      }

      result += `${ESCPOS_CMD.CENTER}${[...Array(48)]
        .map((i) => '-')
        .join('')}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'Subtotal'.padEnd(24, ' ')}${(
        `RM${testItems
          .reduce((accum, item) => accum + item.subtotal, 0)
          .toFixed(2)
          .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
      ).padStart(24, ' ')}`;
      result += `${ESCPOS_CMD.LEFT}${'Discount'.padEnd(24, ' ')}${(
        `RM${testItems
          .reduce((accum, item) => accum + item.discount, 0)
          .toFixed(2)
          .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
      ).padStart(24, ' ')}`;
      result += `${ESCPOS_CMD.LEFT}${'Tax (6%)'.padEnd(24, ' ')}${(
        `RM${testItems
          .reduce((accum, item) => accum + item.tax, 0)
          .toFixed(2)
          .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
      ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${ESCPOS_CMD.SIZE_2X}${'Total'.padEnd(
        12,
        ' ',
      )}${(
        `RM${testItems
          .reduce((accum, item) => accum + item.subtotal, 0)
          .toFixed(2)
          .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
      ).padStart(12, ' ')}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${[...Array(48)]
        .map((i) => '-')
        .join('')}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'Received'.padEnd(24, ' ')}${(
        `RM${testItems
          .reduce((accum, item) => accum + item.subtotal, 0)
          .toFixed(2)
          .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
      ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'Balance'.padEnd(24, ' ')}${(
        `RM${(0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`
      ).padStart(24, ' ')}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;

      result += `${ESCPOS_CMD.LEFT}${'Notes:'}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'1.'}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'2.'}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.LEFT}${'3.'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE
        }${ESCPOS_CMD.NEWLINE}`;

      result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_2H
        }${'Thank you for your order'}${ESCPOS_CMD.NEWLINE}${ESCPOS_CMD.NEWLINE}`;
      result += `${ESCPOS_CMD.CENTER}${ESCPOS_CMD.SIZE_NORMAL}${ESCPOS_CMD.BARCODE_HEIGHT}${ESCPOS_CMD.BARCODE_WIDTH}${ESCPOS_CMD.BARCODE_FONT_A}${ESCPOS_CMD.BARCODE_TXT_OFF}${ESCPOS_CMD.BARCODE_EAN13}978020137962${ESCPOS_CMD.NEWLINE}`;
      // result += `${ESCPOS_CMD.CENTER}${printBarcode({
      //   data: 'TEST12345',
      //   type: 'ean',
      // }, 'cp936')}${ESCPOS_CMD.NEWLINE}`;

      NetPrinter.printText(result);
    } catch (ex) {
      // console.log(ex);
    }
  };

  // function end

  const week = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];

  var resetDataTypeDropdownList = [
    // {
    //   label: 'Order',
    //   value: RESET_DATA_TYPE.USER_ORDER,
    // },
    {
      label: 'Customer',
      value: RESET_DATA_TYPE.CRM_USER,
    },
  ];

  if (supportCodeData) {
    resetDataTypeDropdownList.push({
      label: 'Order',
      value: RESET_DATA_TYPE.USER_ORDER,
    });
  }

  return (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>
    // <UserIdleWrapper disabled={!isMounted}>
    <View
      style={[
        styles.container,
        {
          height: windowHeight,
          width: windowWidth,
          ...getTransformForScreenInsideNavigation(),
        },
      ]}
    >
      <View style={{ flex: 0.8 }}>
        <SideBar
          navigation={props.navigation}
          selectedTab={10}
          expandSettings={true}
        />
      </View>

      <View style={{ height: windowHeight, flex: 9 }}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ width: windowWidth * 0.9 }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}
        >
          {/* <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}> */}
            <View
              style={{
                  paddingVertical: 30,
                  marginHorizontal: 30,
            }}>
              {merchantDisplay ? (
                <View
                  style={{
                      backgroundColor: Colors.whiteColor,
                      width: windowWidth * 0.877,
                      // height: windowHeight * 0.7,
                      marginTop: 10,
                      marginBottom: 30,
                      marginHorizontal: 30,
                      alignSelf: "center",
                      borderRadius: 5,
                      shadowOpacity: 0,
                      shadowColor: "#000",
                      shadowOffset: {
                      width: 0,
                      height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                  }}
              >
                  {/* <ScrollView showsVerticalScrollIndicator={false}> */}
                    <View
                      style={{
                        flexDirection: 'row',
                        // zIndex: 1,
                        padding: Platform.OS == 'ios' ? 10 : 30,
                      }}>
                      <View style={{ flex: 1.1, paddingLeft: 15 }}>
                        <Text
                          style={{
                            marginRight: 50,
                            fontFamily: 'NunitoSans-Bold',
                            fontSize: switchMerchant ? 20 : 25,
                          }}>
                          Merchant Logo
                        </Text>
                        <View
                          style={{
                            flexDirection: 'row',
                            flex: 1,
                            alignItems: 'flex-start',

                            // marginLeft: -windowWidth * 0.01,
                          }}>
                          {/* left */}
                          <TouchableOpacity
                            style={{
                              alignItems: 'center',
                              marginTop: 20,
                              marginRight: 20,
                              // flex: 1,
                              zIndex: 1000,
                            }}
                            onPress={handleChoosePhotoLogo}>
                            {merchantLogo ? (
                              <AsyncImage
                                source={{ uri: logo }}
                                item={{
                                  updatedAt: merchantLastUpdated,
                                }}
                                style={{
                                  width: 120,
                                  height: 120,
                                  backgroundColor: Colors.secondaryColor,
                                  borderRadius: 10,
                                }}
                                hideLoading
                              />) : (
                              <View
                                style={{
                                  width: 120,
                                  height: 120,
                                  backgroundColor: Colors.secondaryColor,
                                  borderRadius: 10,
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}>
                                <DefaultImage />
                              </View>)
                            }
                            <View style={{ alignItems: 'center' }}>
                              <TouchableOpacity
                                style={
                                  {
                                    // height: 30,
                                    // width: 50
                                  }
                                }
                                onPress={() => {
                                  openFileSelector();
                                  setLogoSelected(true);
                                }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: 15,
                                    color: Colors.primaryColor,
                                    marginTop: 5,
                                  }}>
                                  Change
                                </Text>
                              </TouchableOpacity>
                            </View>
                            {/* <TouchableOpacity
                              style={{
                                position: 'absolute',
                                bottom: 5,
                                right: -30,
                                // backgroundColor: 'red',
                              }} onPress={handleChoosePhotoLogo}>
                              <FontAwesome5
                                name="edit"
                                size={switchMerchant ? 12 : 23}
                                color={Colors.primaryColor}
                              />
                            </TouchableOpacity> */}
                          </TouchableOpacity>
                          <View
                            style={{
                              flex: 3,
                              marginTop: 20,
                              //marginLeft: '-2%',
                            }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                marginBottom: 10,
                                //justifyContent: 'space-between',
                              }}>
                              <View
                                style={{
                                  justifyContent: "center",
                                  width: "25%",
                                  paddingLeft: 10,
                                }}
                              >
                                <Text
                                  style={{
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                >
                                  Merchant Name:
                                </Text>
                              </View>

                              <View
                                style={{
                                  //width: 210,
                                  justifyContent: "center",
                                  borderRadius: 8,
                                  width: "60%",
                                  marginRight: 20,
                                }}
                              >
                                {/* <Text style={{ fontFamily: 'NunitoSans-SemiBold', color: Colors.descriptionColor, paddingHorizontal: 10 }}>{merchantName}</Text> */}
                                <TextInput
                                  style={{
                                    fontFamily: "NunitoSans-Regular",
                                    // color: Colors.descriptionColor,
                                    color: Colors.blackColor,
                                    padding: 10,
                                    fontSize: switchMerchant ? 10 : 14,
                                    height: 40,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={
                                    merchantName == null
                                      ? " Not Found "
                                      : merchantName
                                  }
                                  onFocus={() => {
                                    setTemp(merchantName);
                                    setMerchantName("");
                                  }}
                                  onBlur={() => {
                                    if (merchantName == "") {
                                      setMerchantName(temp);
                                    }
                                  }}
                                  onChangeText={(text) => {
                                    if (text.charAt(0) === ' ') {
                                      text = text.trimStart(); // remove leading space characters
                                    }
                                    // setState({ merchantName: text });
                                    setMerchantName(text);
                                  }}
                                  value={merchantName}
                                  ref={myTextInput}
                                  spellCheck={false}
                                />
                              </View>
                            </View>

                            {/* <View style={{
                              flexDirection: 'row',
                              marginBottom: 10,

                              paddingLeft: 10,
                            }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <TouchableOpacity
                                  style={{
                                    justifyContent: 'center',
                                    flexDirection: 'row',
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    backgroundColor: '#4E9F7D',
                                    borderRadius: 5,
                                    width: 180,
                                    paddingHorizontal: 10,
                                    height: switchMerchant ? 35 : 40,
                                    alignItems: 'center',
                                    shadowOffset: {
                                      width: 0,
                                      height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -4,
                                  }}
                                  disabled={loading}
                                  onPress={() => {
                                    Alert.alert(
                                      'Info',
                                      'Are your sure want to proceed to reset the cache? (Note: Will auto-restart the app.)',
                                      [
                                        {
                                          text: 'OK',
                                          onPress: () => {
                                            resetCache();
                                          },
                                        },
                                        {
                                          text: 'Cancel',
                                          onPress: () => {
                                          },
                                        },
                                      ],
                                      { cancelable: false },
                                    );
                                  }}>
                                  <Text
                                    style={{
                                      color: Colors.whiteColor,
                                      //marginLeft: 5,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    {'RESET CACHE'}
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            </View> */}
                            {/* <View
                        style={{
                          flexDirection: 'row',
                          //justifyContent: 'space-between',
                        
                        }}>
                        <View style={{ justifyContent: 'center', flex: 1, paddingLeft: 10 }}>
                          <Text style={styles.textSize}>Choose Outlet:</Text>
                        </View>
                        <View style={{ flex: 1.6 }}>
                          <View
                            style={{
                              //width: 210,
                              height: 50,
                              justifyContent: 'center',
                              marginRight: 80
                              //marginLeft: 10,
                            }}>
                            <DropDownPicker
                              // items={outletInfo.map((item) => ({
                              //   label: item.name, //<= after hayden change you need to change it to item.name
                              //   value: item.id,
                              // }))}
                              items={targetOutletDropdownList}
                              // defaultValue={outletId}
                              containerStyle={{ height: 40, width: 220 }}
                              placeholder="Choose outlet"
                              placeholderStyle={{
                                color: Colors.descriptionColor,
                                fontSize: 16,
                                fontFamily: 'NunitoSans-Regular',
                              }}
                              style={{ backgroundColor: '#fafafa' }}
                              labelStyle={{
                                fontSize: 16,
                                fontFamily: 'NunitoSans-Regular',
                              }}
                              itemStyle={{
                                justifyContent: 'flex-start',
                                fontSize: 16,
                                fontFamily: 'NunitoSans-Regular',
                              }}
                              dropDownStyle={{ backgroundColor: '#fafafa' }}
                              onChangeItem={(item) => {
                                // setState({ outletId: item.value });

                                // setOutletId(item.value);
                                // outletById(item.value)

                                MerchantStore.update((s) => {
                                  s.currOutletId = item.value;
                                });
                              }}
                              // defaultValue={currOutletId}
                              defaultValue={selectedTargetOutletId}
                              multiple={false}
                            // onClose={() => {
                            //   outletById(outletId);
                            // }} //you didn't pass in outletid in your function parameter
                            />
                          </View>
                        </View>
                      </View> */}
                          </View>
                        </View>

                        <View
                          style={
                            (styles.merchantDisplayView,
                            {
                              // backgroundColor: 'red'
                              marginTop: 25,
                              marginBottom: 15,
                            })
                          }>
                          <View
                            style={{
                              justifyContent: 'center',
                              alignItems: 'flex-start',
                            }}>
                            <Text
                              style={{
                                marginRight: 50,
                                fontFamily: 'NunitoSans-Bold',
                                fontSize: switchMerchant ? 20 : 25,
                              }}>
                              Merchant Display
                            </Text>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                justifyContent: 'flex-start',
                                width: '80%',
                              }}>
                              <View>
                                <Text
                                  style={{
                                    color: Colors.fieldtTxtColor,
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 16,
                                  }}>
                                  Customer will see this
                                </Text>
                              </View>
                              <View style={{ marginLeft: 10 }}>
                                {/* <TouchableOpacity
                            onPress={() => {
                              handleChoosePhoto1();
                            }}>
                            <Text
                              style={{
                                fontSize: 15,
                                color: Colors.primaryColor,
                                fontFamily: 'NunitoSans-Bold',
                              }}>
                              Change
                            </Text>
                          </TouchableOpacity> */}
                              </View>
                            </View>
                          </View>
                        </View>

                        <View
                          style={{
                            flexDirection: "row",
                            marginBottom: 30,
                          }}
                        >
                          {currOutlet.cover ? (
                            <AsyncImage
                              source={{ uri: cover }}
                              item={currOutlet}
                              style={{
                                resizeMode: "contain",
                                width: 300,
                                height: 120,
                                backgroundColor: Colors.secondaryColor,
                                borderRadius: 10,
                              }}
                              hideLoading={true}
                            />) : (
                            <AsyncImage
                              source={{ uri: cover }}
                              item={currOutlet}
                              style={{
                                resizeMode: "contain",
                                width: 300,
                                height: 120,
                                backgroundColor: Colors.secondaryColor,
                                borderRadius: 10,
                              }}
                              hideLoading={true}
                            />)}

                          <TouchableOpacity
                            style={{
                              //backgroundColor: 'red',
                              //marginBottom: 30,
                              marginLeft: 0,
                            }}
                            onPress={() => {
                              openFileSelector();
                              setCoverSelected(true);
                            }}
                          >
                            <View
                              style={{
                                position: "absolute",
                                bottom: 5,
                                right: 5,
                                //   backgroundColor: 'black',
                                //opacity: 0.5,
                                // width: 120,
                                // height: 120,
                                // borderRadius: 13,
                              }}
                            >
                              <Icon
                                name="edit"
                                size={switchMerchant ? 17 : 23}
                                color={Colors.primaryColor}
                              //  style={{ position: 'absolute', zIndex: -1 }}
                              />
                            </View>
                          </TouchableOpacity>
                        </View>

                        {/* merchantname */}
                        <View style={{}}>
                          <View style={{ width: '75%' }}>
                            <View style={{ flexDirection: 'row', marginVertical: 15 }}>
                              <View style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {`Address\n(GPS)`}
                                </Text>
                              </View>

                              <View style={{ width: "90%" }}>
                                <Autocomplete
                                  // placeholder="📍  Outlet address"
                                  placeholder={address}
                                  minLength={2} // minimum length of text to search
                                  autoFocus={false}
                                  textInputProps={{
                                    placeholderTextColor: "black",
                                  }}
                                  returnKeyType={"search"} // Can be left out for default return key https://facebook.github.io/react-native/docs/textinput.html#returnkeytype
                                  listViewDisplayed="false" // true/false/undefined
                                  fetchDetails={true}
                                  renderDescription={(row) => renderSearch(row)} // custom description render
                                  onPlaceSelected={(
                                    place,
                                    inputRef,
                                    autocomplete
                                  ) => {
                                    // 'place' is provided when fetchDetails = true
                                    console.log("place", place);
                                    console.log("inputRef", inputRef);
                                    console.log("autocomplete", autocomplete);

                                    setAddress(place.formatted_address);
                                    setOutletLat(place.geometry.location.lat);
                                    setOutletLng(place.geometry.location.lng);
                                  }}
                                  options={{
                                    types: ["address"],
                                  }}
                                  defaultValue={address}
                                  // api cannot use
                                  // apiKey={"AIzaSyAt1AOIT_1E5Ivec7PVWmylmDd8jSVm-j8"}
                                  style={{
                                    border: 0,
                                    padding: 10,
                                    height: 80,
                                    // width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                    //   backgroundColor: Colors.fieldtBgColor,
                                    //   width: "80%",
                                    //   height: switchMerchant ? 30 : 35,
                                    //   borderRadius: 5,
                                    //   padding: 5,
                                    //   marginVertical: 5,
                                    //   borderWidth: 1,
                                    //   borderColor: "#E5E5E5",
                                    //   paddingLeft: 10,
                                    //   marginLeft: 5,
                                    //   fontFamily: "NunitoSans-Regular",
                                    //   fontSize: switchMerchant ? 10 : 14,
                                    textInputContainer: {
                                      width: "100%",
                                      borderWidth: 1,
                                      borderRightWidth: 2,
                                      borderRadius: 5,
                                      borderColor: "#E5E5E5",
                                      backgroundColor: Colors.fieldtBgColor,
                                    },
                                    textInput: {
                                      backgroundColor: Colors.fieldtBgColor,
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: switchMerchant ? 10 : 14,
                                      height: 80,
                                      marginBottom: 0,
                                      color: "black",
                                      //backgroundColor: 'red',
                                      textAlignVertical: "top",
                                    },
                                    description: {
                                      // fontWeight: 'bold',
                                      fontFamily: "NunitoSans-Bold",
                                      fontSize: switchMerchant ? 10 : 14,
                                    },
                                    predefinedPlacesDescription: {
                                      color: "#1faadb",
                                      fontFamily: "NunitoSans-Regular",
                                      fontSize: switchMerchant ? 10 : 14,
                                    },
                                    listView: {
                                      //backgroundColor: 'red',
                                      //marginLeft: 85,
                                      //marginTop: 40,
                                      width: "100%",
                                      //alignSelf: 'center',
                                      borderColor: "#E5E5E5",
                                      borderWidth: 1,
                                      borderRadius: 5,
                                      zIndex: 10,
                                      // height: 800,
                                      //position: 'absolute',
                                    },
                                    container: {
                                      //backgroundColor: 'red',
                                    },
                                    zIndex: 10,
                                  }}
                                  enablePoweredByContainer={false}
                                  //currentLocation={false}
                                  currentLocationLabel="Current location"
                                  nearbyPlacesAPI="GooglePlacesSearch"
                                  GoogleReverseGeocodingQuery={{}}
                                  GooglePlacesSearchQuery={{
                                    rankby: "distance",
                                  }}
                                  filterReverseGeocodingByTypes={[
                                    "locality",
                                    "administrative_area_level_3",
                                  ]}
                                  debounce={200}
                                  keepResultsAfterBlur={true}
                                />
                                {/* <GooglePlacesAutocomplete
                                // placeholder="📍  Outlet address"
                                placeholder={address}
                                minLength={2} // minimum length of text to search
                                autoFocus={false}
                                textInputProps={{
                                  placeholderTextColor: "black",
                                }}
                                returnKeyType={"search"} // Can be left out for default return key https://facebook.github.io/react-native/docs/textinput.html#returnkeytype
                                listViewDisplayed="false" // true/false/undefined
                                fetchDetails={true}
                                renderDescription={(row) => renderSearch(row)} // custom description render
                                onPress={(data, details = null) => {
                                  // 'details' is provided when fetchDetails = true
                                  console.log("data", data);
                                  console.log("details", details);

                                  setAddress(details.formatted_address);
                                  setOutletLat(details.geometry.location.lat);
                                  setOutletLng(details.geometry.location.lng);

                                  // console.log("data.description", data.description);
                                  // console.log("data.structured_formatting.main_text", data.structured_formatting.main_text);
                                  // props.navigation.navigate('AddAddress', { test: 3, address: data.description, name: data.structured_formatting.main_text, location: details.geometry.location })
                                }}
                                getDefaultValue={() => address}
                                query={{
                                  // available options: https://developers.google.com/places/web-service/autocomplete
                                  //key: 'AIzaSyB_nKKIGIcEJ6ffsVBdYeIstW8KekjVXa8',
                                  // key: 'AIzaSyCsM9boPkQ7uaJ54RWUzPHGzjZfggkXf8g',
                                  key: "AIzaSyAt1AOIT_1E5Ivec7PVWmylmDd8jSVm-j8",
                                  language: "en", // language of the results
                                  types: "address", // default: 'geocode'
                                  components: "country:my",
                                }}
                                styles={{
                                  textInputContainer: {
                                    width: "100%",
                                    borderWidth: 1,
                                    borderRightWidth: 2,
                                    borderRadius: 5,
                                    borderColor: "#E5E5E5",
                                  },
                                  textInput: {
                                    backgroundColor: Colors.fieldtBgColor,
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: switchMerchant ? 10 : 14,
                                    height: 80,
                                    marginBottom: 0,
                                    color: "black",
                                    //backgroundColor: 'red',
                                    textAlignVertical: "top",
                                  },
                                  description: {
                                    // fontWeight: 'bold',
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                  predefinedPlacesDescription: {
                                    color: "#1faadb",
                                    fontFamily: "NunitoSans-Regular",
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                  listView: {
                                    //backgroundColor: 'red',
                                    //marginLeft: 85,
                                    //marginTop: 40,
                                    width: "100%",
                                    //alignSelf: 'center',
                                    borderColor: "#E5E5E5",
                                    borderWidth: 1,
                                    borderRadius: 5,
                                    zIndex: 10,
                                    // height: 800,
                                    //position: 'absolute',
                                  },
                                  container: {
                                    //backgroundColor: 'red',
                                  },
                                  zIndex: 10,
                                }}
                                enablePoweredByContainer={false}
                                //currentLocation={false}
                                currentLocationLabel="Current location"
                                nearbyPlacesAPI="GooglePlacesSearch"
                                GoogleReverseGeocodingQuery={{}}
                                GooglePlacesSearchQuery={{
                                  rankby: "distance",
                                }}
                                filterReverseGeocodingByTypes={[
                                  "locality",
                                  "administrative_area_level_3",
                                ]}
                                debounce={200}
                                keepResultsAfterBlur={true}
                              /> */}
                              </View>
                            </View>

                            {/* address to show */}
                            <View
                              style={{ flexDirection: 'row', marginVertical: 15 }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {`Address\n(Display)`}
                                </Text>
                              </View>
                              <View style={{ width: '75%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  textAlignVertical={'top'}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  style={{
                                    padding: 10,
                                    height: 80,
                                    width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={
                                    outlet == null
                                      ? 'Fill in the ddress'
                                      : outlet.addressDisplay
                                  }
                                  onChangeText={(text) => {
                                    setAddressDisplay(text);
                                  }}
                                  value={addressDisplay}
                                />
                              </View>
                            </View>

                            <View
                              style={{
                                flexDirection: "row",
                                marginVertical: 15,
                              }}
                            >
                              <View
                                style={{
                                  width: "25%",
                                  justifyContent: "center",
                                }}
                              >
                                <Text
                                  style={{
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                >
                                  Contact
                                </Text>
                              </View>
                              <View style={{ width: "75%" }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  style={{
                                    padding: 10,
                                    height: 40,
                                    width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  keyboardType={'decimal-pad'}
                                  placeholder={
                                    outlet == null
                                      ? ' Fill in contact '
                                      : outlet.phone
                                  }
                                  //iOS
                                  clearTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(phone)
                                    setPhone('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (phone == '' || phone == '6') {
                                      setPhone(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    if (text.length === 0) {
                                      return; // Do nothing if the user deletes the entire input
                                    }
                                    if (text.charAt(0) !== "6") {
                                      text = `6${text}`; // Add a "6" at the beginning of the input
                                    }
                                    // setState({ phone: text });
                                    setPhone(text);
                                  }}
                                  value={phone}
                                  ref={myTextInput}
                                />
                              </View>
                            </View>

                            <View
                              style={{ flexDirection: 'row', marginVertical: 15 }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  Email
                                </Text>
                              </View>
                              <View style={{ width: '75%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={{
                                    padding: 10,
                                    height: 40,
                                    width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholder={
                                    outlet == null
                                      ? ' Fill in email '
                                      : outlet.email
                                  }
                                  //iOS
                                  clearTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(email)
                                    setEmail('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (email == '') {
                                      setEmail(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    // setState({ phone: text });
                                    setEmail(text);
                                  }}
                                  value={email}
                                  ref={myTextInput}
                                />
                              </View>
                            </View>

                            <View
                              style={{ flexDirection: 'row', marginVertical: 15 }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {`Email\n(W. Stock)`}
                                </Text>
                              </View>
                              <View style={{ width: '75%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  style={{
                                    padding: 10,
                                    height: 40,
                                    width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholder={
                                    outlet == null
                                      ? ' Fill in email '
                                      : outlet.email
                                  }
                                  //iOS
                                  clearTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(emailWarningStock)
                                    setEmailWarningStock('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (emailWarningStock == '') {
                                      setEmailWarningStock(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    // setState({ phone: text });
                                    setEmailWarningStock(text);
                                  }}
                                  value={emailWarningStock}
                                  ref={myTextInput}
                                />
                              </View>
                            </View>

                            {/* <View
                            style={{ flexDirection: 'row', marginVertical: 15 }}>
                            <View
                              style={{ width: '25%', justifyContent: 'center' }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Website
                              </Text>
                            </View>
                            <View style={{ width: '61%', flexDirection: 'row' }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                placeholderTextColor={Platform.select({
                                  ios: '#000',
                                })}
                                placeholderTextColor={'#000'}
                                style={[
                                  styles.textInput,
                                  {
                                    padding: 10,
                                    height: 40,
                                    width: 80,
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}
                                placeholder={
                                  // outlet == null
                                  //   ? ' Fill in contact '
                                  //   : outlet.phone
                                  'subdomain'
                                }
                                // onChangeText={(text) => {
                                //   setPhone(text);
                                // }}
                                // value={phone}
                                ref={myTextInput}
                              />
                            </View>
                            <View
                              style={{
                                justifyContent: 'center',
                                marginLeft:
                                  windowWidth * 0.005,
                              }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                .mykoodoo.com
                              </Text>
                            </View>
                          </View> */}

                            <View
                              style={{
                                flexDirection: 'row',
                                marginVertical: 15,
                                position: 'relative',
                                bottom: 7,
                              }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    position: 'relative',
                                    bottom: 1,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {'SST\nRate (%)'}
                                </Text>
                              </View>
                              <View style={{ width: '75%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#000',
                                  })}
                                  //placeholderTextColor={'#000'}
                                  style={{
                                    padding: 5,
                                    height: 40,
                                    // width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={'6'}
                                  //iOS
                                  clearTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(sstRate)
                                    setSstRate('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (sstRate == '') {
                                      setSstRate(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setSstRate(text);
                                  }}
                                  value={sstRate}
                                  ref={myTextInput}
                                />
                              </View>

                              <View style={{
                                width: '10%',
                                // backgroundColor: 'red',

                                alignItems: 'center',
                                justifyContent: 'center',
                                marginLeft: '4%',
                              }}>
                                <Switch
                                  onChange={(statusTemp) => {
                                    setSstActive(statusTemp);
                                  }}
                                  checked={sstActive}
                                  width={35}
                                  height={20}
                                  handleDiameter={30}
                                  uncheckedIcon={false}
                                  checkedIcon={false}
                                  boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                  activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                />
                              </View>
                            </View>

                            <View
                              style={{
                                flexDirection: 'row',
                                marginVertical: 15,
                                position: 'relative',
                                bottom: 7,
                              }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    position: 'relative',
                                    bottom: 1,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {'SST\nNumber'}
                                </Text>
                              </View>
                              <View style={{ width: '75%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  placeholderTextColor={Platform.select({
                                    ios: '#000',
                                  })}
                                  //placeholderTextColor={'#000'}
                                  style={{
                                    padding: 5,
                                    height: 40,
                                    // width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={'J11-8018-2000001'}
                                  //iOS
                                  clearTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(sstNumber)
                                    setSstRate('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (sstNumber == '') {
                                      setSstRate(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setSstNumber(text);
                                  }}
                                  value={sstNumber}
                                  ref={myTextInput}
                                />
                              </View>

                              <View style={{
                                width: '10%',
                                // backgroundColor: 'red',

                                alignItems: 'center',
                                justifyContent: 'center',
                                marginLeft: '4%',

                                opacity: 0,
                              }}>
                                <Switch
                                  onChange={(statusTemp) => {
                                    setSstActive(statusTemp);
                                  }}
                                  checked={sstActive}
                                  width={35}
                                  height={20}
                                  handleDiameter={30}
                                  uncheckedIcon={false}
                                  checkedIcon={false}
                                  boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                  activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                />
                              </View>
                            </View>

                            {/* ///////////////////////////////////////////////// */}

                            {/* service charge for dinein/takeaway/delivery */}

                            <View
                              style={{
                                flexDirection: 'row',
                                marginVertical: 15,
                                position: 'relative',
                                bottom: 7,

                                alignItems: 'center',
                              }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    position: 'relative',
                                    bottom: 1,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {'Service\nCharge (%)'}
                                </Text>
                              </View>

                              <View style={{ width: '35%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderTextColor={Platform.select({
                                    ios: '#000',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  //placeholderTextColor={'#000'}
                                  style={{
                                    padding: 5,
                                    height: 40,
                                    // width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={' 5 '}
                                  //iOS
                                  clearTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(scRate)
                                    setScRate('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (scRate == '') {
                                      setScRate(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setScRate(text);
                                  }}
                                  value={scRate}
                                  ref={myTextInput}
                                />
                              </View>

                              <View style={{
                                width: '10%',
                                // backgroundColor: 'red',

                                alignItems: 'center',
                                justifyContent: 'center',
                                marginLeft: '4%',
                              }}>
                                <Switch
                                  onChange={(value) => {
                                    setScActive(value);
                                  }}
                                  checked={scActive}
                                  width={35}
                                  height={20}
                                  handleDiameter={30}
                                  uncheckedIcon={false}
                                  checkedIcon={false}
                                  boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                  activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                />
                              </View>

                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.2 : windowWidth * 0.2,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: '2%',
                                  // borderWidth:1
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setScOrderTypes(items.map(item => item.value));
                                  }}
                                  value={scOrderTypes}
                                  items={[
                                    {
                                      label: 'Dine In',
                                      value: ORDER_TYPE.DINEIN,
                                    },
                                    {
                                      label: 'Takeaway',
                                      value: ORDER_TYPE.PICKUP,
                                    },
                                    {
                                      label: 'Delivery',
                                      value: ORDER_TYPE.DELIVERY,
                                    },
                                  ]}
                                  multiple={true}
                                  multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openSC}
                                  setOpen={setOpenSC}
                                />
                              </View>
                            </View>

                            {/* 2nd row for service charge */}

                            <View
                              style={{
                                flexDirection: 'row',
                                // marginVertical: 15,
                                marginTop: 0,
                                marginBottom: 10,
                                position: 'relative',
                                bottom: 7,
                              }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    position: 'relative',
                                    bottom: 1,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {''}
                                </Text>
                              </View>

                              <View style={{ width: '35%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderTextColor={Platform.select({
                                    ios: '#000',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  //placeholderTextColor={'#000'}
                                  style={{
                                    padding: 5,
                                    height: 40,
                                    // width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={'Name to shown'}
                                  //iOS
                                  clearTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(scName)
                                    setScName('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (scName == '') {
                                      setScName(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setScName(text);
                                  }}
                                  value={scName}
                                  ref={myTextInput}
                                />
                              </View>

                              {/* <View style={{
                                width: '10%',
                                // backgroundColor: 'red',

                                alignItems: 'center',
                                justifyContent: 'center',
                                marginLeft: '4%',
                              }}>
                              </View> */}

                              {/* 2023-02-10 - Hide medium type selection first */}
                              {/* <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.2 : windowWidth * 0.2,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: '2%',
                                  // borderWidth:1
                                }}>
                                <DropDownPicker
                                  items={ORDER_TYPE_DETAILS_DROPDOWN_LIST}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: switchMerchant ? 35 : 40, width: '90%' }}
                                  placeholder="Order Type"
                                  arrowStyle={{
                                    height: '180%',
                                    bottom: switchMerchant ? 1 : 0,
                                  }}
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{
                                    backgroundColor: '#fafafa',
                                    maxHeight: 45,
                                  }}
                                  onChangeItem={(items) => {
                                    setScOrderTypeDetails(items);
                                  }}
                                  defaultValue={scOrderTypeDetails}
                                  multiple={true}
                                  multipleText='%d medium type(s) selected'
                                />
                              </View> */}
                            </View>

                            {/* ///////////////////////////////////////////////// */}

                            {/* service charge for open orders */}

                            <View
                              style={{
                                flexDirection: 'row',
                                marginVertical: 15,
                                position: 'relative',
                                bottom: 7,

                                alignItems: 'center',
                              }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    position: 'relative',
                                    bottom: 1,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {'Service\nCharge (%)\n(Other D.)'}
                                </Text>
                              </View>

                              <View style={{ width: '35%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderTextColor={Platform.select({
                                    ios: '#000',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  //placeholderTextColor={'#000'}
                                  style={{
                                    padding: 5,
                                    height: 40,
                                    // width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={' 5 '}
                                  //iOS
                                  clearTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(scRateOtherD)
                                    setScRateOtherD('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (scRateOtherD == '') {
                                      setScRateOtherD(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setScRateOtherD(text);
                                  }}
                                  value={scRateOtherD}
                                  ref={myTextInput}
                                />
                              </View>

                              <View style={{
                                width: '10%',
                                // backgroundColor: 'red',

                                alignItems: 'center',
                                justifyContent: 'center',
                                marginLeft: '4%',
                              }}>
                                <Switch
                                  onChange={(value) => {
                                    setScActiveOtherD(value);
                                  }}
                                  checked={scActiveOtherD}
                                  width={35}
                                  height={20}
                                  handleDiameter={30}
                                  uncheckedIcon={false}
                                  checkedIcon={false}
                                  boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                  activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                />
                              </View>

                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.2 : windowWidth * 0.2,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: '2%',
                                  // borderWidth:1
                                }}>
                                {/* <DropDownPicker
                                  items={[
                                    {
                                      label: 'Dine In',
                                      value: ORDER_TYPE.DINEIN,
                                    },
                                    {
                                      label: 'Takeaway',
                                      value: ORDER_TYPE.PICKUP,
                                    },
                                    {
                                      label: 'Delivery',
                                      value: ORDER_TYPE.DELIVERY,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: switchMerchant ? 35 : 40, width: '90%' }}
                                  placeholder="Order Type"
                                  arrowStyle={{
                                    height: '180%',
                                    bottom: switchMerchant ? 1 : 0,
                                  }}
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{
                                    backgroundColor: '#fafafa',
                                    maxHeight: 45,
                                  }}
                                  onChangeItem={(items) => {
                                    setScOrderTypes(items);
                                  }}
                                  defaultValue={scOrderTypes}
                                  multiple
                                  multipleText='%d type(s) selected'
                                /> */}
                              </View>
                            </View>

                            {/* 2nd row for service charge */}

                            <View
                              style={{
                                flexDirection: 'row',
                                // marginVertical: 15,
                                marginTop: 0,
                                marginBottom: 10,
                                position: 'relative',
                                bottom: 7,
                              }}>
                              <View
                                style={{ width: '25%', justifyContent: 'center' }}>
                                <Text
                                  style={{
                                    position: 'relative',
                                    bottom: 1,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}>
                                  {''}
                                </Text>
                              </View>

                              <View style={{ width: '35%' }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderTextColor={Platform.select({
                                    ios: '#000',
                                  })}
                                  placeholderStyle={{
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                  }}
                                  //placeholderTextColor={'#000'}
                                  style={{
                                    padding: 5,
                                    height: 40,
                                    // width: '120%',
                                    fontFamily: 'NunitoSans-Regular',
                                    fontSize: switchMerchant ? 10 : 14,
                                    backgroundColor: Colors.fieldtBgColor,
                                  }}
                                  placeholder={'Name to shown'}
                                  //iOS
                                  clearTextOnFocus
                                  //////////////////////////////////////////////
                                  //Android
                                  onFocus={() => {
                                    setTemp(scNameOtherD)
                                    setScNameOtherD('');
                                  }}
                                  ///////////////////////////////////////////////
                                  //When textinput is not selected
                                  onEndEditing={() => {
                                    if (scNameOtherD == '') {
                                      setScNameOtherD(temp);
                                    }
                                  }}
                                  //////////////////////////////////////////////
                                  onChangeText={(text) => {
                                    setScNameOtherD(text);
                                  }}
                                  value={scNameOtherD}
                                  ref={myTextInput}
                                />
                              </View>
                            </View>

                            {/* ///////////////////////////////////////////////// */}
                          </View>

                          {/* <View style={styles.viewContainer}>
                      <View style={{ flex: 1 }}>
                        <Text style={styles.textSize}>Payment Method:</Text>
                      </View>
                      <View style={{}}>
                        <DropDownPicker
                          items={[
                            {
                              label: 'Cash',
                              value: 'Cash',
                            },
                            {
                              label: 'Credit Card',
                              value: 'CreditCard',
                            },
                          ]}
                          containerStyle={{ height: 40 }}
                          placeholder={outlet == null ? ' Select ' : outlet.payment}
                          placeholderStyle={{ color: Colors.descriptionColor, fontSize: 19 }}
                          labelStyle={{ fontSize: 19 }}
                          style={styles.textInput}
                          itemStyle={{
                            justifyContent: 'flex-start',
                          }}
                          dropDownStyle={{ backgroundColor: '#fafafa' }}
                          onChangeItem={(item) =>
                            // setState({
                            //   payment: item.value,
                            // })
                            setPayment(item.value)
                          }
                        />
                      </View>
                    </View> */}

                          {/* <View style={styles.viewContainer}>
                      <View style={{ flex: 1 }}>
                        <Text style={styles.textSize}>
                          Start reservation time:
                          </Text>
                      </View>
                      <View>
                        <DateTimePickerModal
                          minimumDate={new Date()}
                          minuteInterval={30}
                          isVisible={showDateTimePicker}
                          mode={pickerMode}
                          display={pickerMode == "time" ? "spinner" : "default"} //for iOS to use minuteInterval
                          onConfirm={(dt) => {
                            const date = moment(dt);
                            if (pickerMode == 'time') {
                              // setState({
                              //   rev_time: date.format('HH:mm'),
                              // });
                              setRev_time(date.format('HH:mm'));
                            }
                            // setState({ showDateTimePicker: false });
                            setShowDateTimePicker(false);
                          }}
                          onCancel={() => {
                            // setState({ showDateTimePicker: false });
                            setShowDateTimePicker(false);
                          }}
                        />
                        <View style={styles.textInput}>
                          <View style={{ flexDirection: 'row', flex: 1 }}>
                            <View style={{ flex: 4 }}>
                              <Text style={{ paddingVertical: 16 }}>
                                {rev_time}
                              </Text>
                            </View>
                            <View
                              style={{
                                flex: 1,
                                marginTop: 15,
                                marginLeft: 170
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  // setState({
                                  //   pickerMode: 'time',
                                  //   showDateTimePicker: true,
                                  // });
                                  setPickerMode('time');
                                  setShowDateTimePicker(true);
                                }}>
                                <Icon name="clock" size={18} color="green" />
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      </View>
                    </View> */}

                          {/* Halal toggle selection and pork free */}
                          {/* <View style={{
                      flexDirection:'row',

                    }}>
                    <Text style={[
                            styles.textSize,
                            {
                              width: windowWidth * 0.15,
                            },
                          ]}>
                      { isEnabled? "Pork Free": "Halal" }
                    </Text>
                    <Switch
                      value={isEnabled}
                      style={{
                        width: '10%',
                        marginBottom: '3%',
                      }}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive='#dddddd'
                      onSyncPress ={() => {
                      }}
                    />
                    </View> */}

                          <View style={[styles.viewContainer, { zIndex: -3 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}
                              >
                                QR On Receipt
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                onChange={(value) => {
                                  setIsQRPrintReceipt(value);
                                }}
                                checked={isQRPrintReceipt}
                                width={35}
                                height={20}
                                handleDiameter={30}
                                uncheckedIcon={false}
                                checkedIcon={false}
                                boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                              />
                            </View>
                          </View>

                          {/* <View style={styles.viewContainer}>
                          <View>
                            <Text
                              style={[
                                styles.textSize,
                                {
                                  width: windowWidth * 0.15,
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                },
                              ]}>
                              Logo(s) On Receipt
                            </Text>
                          </View>
                          <View
                            style={{
                              flexDirection: 'row',
                              flex: 1,
                              alignItems: 'center',
                            }}>
                            <Switch
                              width={42}
                              style={{
                                //flexDirection: 'row',
                                //width: '15%',
                                marginRight: 20,
                                // marginLeft: 20,
                                //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                bottom: -2,
                              }}
                              value={!isQRNotPrintLogo}
                              onSyncPress={(statusTemp) =>
                                // setState({ status: status })
                                setIsQRNotPrintLogo(!statusTemp)
                              }
                              circleColorActive={Colors.primaryColor}
                              circleColorInactive={Colors.fieldtTxtColor}
                              backgroundActive="#dddddd"
                            />
                          </View>
                        </View> */}

                          <View style={styles.viewContainer}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}
                              >
                                Halal
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                onChange={(value) => {
                                  setStatusHalal(value);
                                }}
                                checked={statusHalal}
                                width={35}
                                height={20}
                                handleDiameter={30}
                                uncheckedIcon={false}
                                checkedIcon={false}
                                boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                              />
                              {/* <Text
                          style={{
                            fontSize: 18,
                            color: status
                              ? Colors.primaryColor
                              : Colors.fieldtTxtColor,
                            alignSelf: 'center',
                          }}>
                          {status ? 'Yes' : 'No'}
                        </Text> */}
                            </View>
                          </View>

                          <View style={styles.viewContainer}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                Accepting Takeaway
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                onChange={(value) => {
                                  setStatus(value);
                                }}
                                checked={status}
                                width={35}
                                height={20}
                                handleDiameter={30}
                                uncheckedIcon={false}
                                checkedIcon={false}
                                boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                              />
                              {/* <Text
                          style={{
                            fontSize: 18,
                            color: status
                              ? Colors.primaryColor
                              : Colors.fieldtTxtColor,
                            alignSelf: 'center',
                          }}>
                          {status ? 'Yes' : 'No'}
                        </Text> */}
                            </View>
                          </View>
                          <View style={styles.viewContainer}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                Accepting Delivery
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                onChange={(value) => {
                                  setStatus1(value);
                                }}
                                checked={status1}
                                width={35}
                                height={20}
                                handleDiameter={30}
                                uncheckedIcon={false}
                                checkedIcon={false}
                                boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                              />
                              {/* <Text
                          style={{
                            fontSize: 18,
                            color: status1
                              ? Colors.primaryColor
                              : Colors.fieldtTxtColor,
                            alignSelf: 'center',
                          }}>
                          {status1 ? 'Yes' : 'No'}
                        </Text> */}
                            </View>
                          </View>

                          <View style={styles.viewContainer}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {`Allow To Skip\nUser Info`}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                onChange={(value) => {
                                  setSkipUserInfo(value);
                                }}
                                checked={status1}
                                width={35}
                                height={20}
                                handleDiameter={30}
                                uncheckedIcon={false}
                                checkedIcon={false}
                                boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                              />
                              {/* <Text
                          style={{
                            fontSize: 18,
                            color: status1
                              ? Colors.primaryColor
                              : Colors.fieldtTxtColor,
                            alignSelf: 'center',
                          }}>
                          {status1 ? 'Yes' : 'No'}
                        </Text> */}
                            </View>
                          </View>

                          <View style={styles.viewContainer}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {`Switch Table Layout`}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                onChange={(value) => {
                                  setSwitchTableLayout(value);
                                }}
                                checked={switchTableLayout}
                                width={35}
                                height={20}
                                handleDiameter={30}
                                uncheckedIcon={false}
                                checkedIcon={false}
                                boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                              />
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 10 : 14,
                                  color: switchTableLayout === true
                                    ? Colors.primaryColor
                                    : Colors.fieldtTxtColor,
                                  alignSelf: 'center',
                                  marginLeft: 5,
                                }}>
                                {switchTableLayout === true ? 'Fast' : 'Normal'}
                              </Text>
                            </View>
                          </View>

                          <View style={styles.viewContainer}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {`Force Close Shift\nBefore Sign Out`}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                onChange={(value) => {
                                  setForceCloseShiftBeforeSignOut(value);
                                }}
                                checked={forceCloseShiftBeforeSignOut}
                                width={35}
                                height={20}
                                handleDiameter={30}
                                uncheckedIcon={false}
                                checkedIcon={false}
                                boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                              />
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -4 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Report Display\nType'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setReportDisplayType(items.value);
                                  }}
                                  value={reportDisplayType}
                                  items={[
                                    {
                                      label: 'Day',
                                      value: REPORT_DISPLAY_TYPE.DAY,
                                    },
                                    {
                                      label: 'Shift',
                                      value: REPORT_DISPLAY_TYPE.SHIFT,
                                    },
                                  ]}
                                  // multiple={true}
                                  // multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openRDT}
                                  setOpen={setOpenRDT}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -5 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Report Data\nSize Limit'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setReportDataSizeLimit(items.value);
                                  }}
                                  value={reportDataSizeLimit}
                                  items={[
                                    {
                                      label: '100',
                                      value: REPORT_DATA_SIZE_LIMIT._100,
                                    },
                                    {
                                      label: '500',
                                      value: REPORT_DATA_SIZE_LIMIT._500,
                                    },
                                    {
                                      label: '1000',
                                      value: REPORT_DATA_SIZE_LIMIT._1000,
                                    },
                                    {
                                      label: '1500',
                                      value: REPORT_DATA_SIZE_LIMIT._1500,
                                    },
                                    {
                                      label: '2000',
                                      value: REPORT_DATA_SIZE_LIMIT._2000,
                                    },
                                  ]}
                                  // multiple={true}
                                  // multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openSC}
                                  setOpen={setOpenSC}
                                />
                              </View>
                            </View>
                          </View>

                          {/* <View style={[styles.viewContainer, { zIndex: -49 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Web Order\nV2 Layout'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    width={42}
                                    style={{
                                      //flexDirection: 'row',
                                      //width: '15%',
                                      marginRight: 20,
                                      // marginLeft: 20,
                                      //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                                      bottom: -2,
                                    }}
                                    value={webOrderV2Layout}
                                    onSyncPress={(statusTemp) =>
                                      // setState({ status: status })
                                      setWebOrderV2Layout(statusTemp)
                                    }
                                    circleColorActive={Colors.primaryColor}
                                    circleColorInactive={Colors.fieldtTxtColor}
                                    backgroundActive="#dddddd"
                                  />
                                </View>
                              </View>
                            </View>
                          </View> */}

                          <View style={[styles.viewContainer, { zIndex: -9 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Web Order\nVariant Layout'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setWebOrderVariantLayout(items.value);
                                  }}
                                  value={webOrderVariantLayout}
                                  items={[
                                    {
                                      label: "Current (Horizontal)",
                                      value: WEB_ORDER_VARIANT_LAYOUT.HORIZONTAL,
                                    },
                                    {
                                      label: "New (Vertical)",
                                      value: WEB_ORDER_VARIANT_LAYOUT.VERTICAL,
                                    },
                                  ]}
                                  // multiple={true}
                                  // multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openRDSL}
                                  setOpen={setOpenRDSL}
                                />
                              </View>
                            </View>
                          </View>

                          {/* <View style={[styles.viewContainer, { zIndex: -10 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Web Order\nList (Mobile)'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <DropDownPicker
                                  items={[
                                    {
                                      label: 'Current (Grid)',
                                      value: WEB_ORDER_LIST_LAYOUT.GRID,
                                    },
                                    {
                                      label: 'New (List)',
                                      value: WEB_ORDER_LIST_LAYOUT.LIST,
                                    },
                                  ]}
                                  // defaultValue={outletId}
                                  containerStyle={{ height: 40 }}
                                  placeholder="Layout options"
                                  placeholderStyle={{
                                    color: Colors.descriptionColor,
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  style={{ backgroundColor: '#fafafa' }}
                                  labelStyle={{
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                    fontSize: switchMerchant ? 10 : 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                  }}
                                  dropDownStyle={{ backgroundColor: '#fafafa' }}
                                  onChangeItem={(item) => {
                                    setWebOrderListLayout(item.value);
                                  }}
                                  defaultValue={webOrderListLayout}
                                  multiple={false}
                                />
                              </View>
                            </View>
                          </View> */}

                          <View style={[styles.viewContainer, , { zIndex: -11 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {`Force To Enter\nCard Info`}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <Switch
                                onChange={(value) => {
                                  setForceToEnterCardInfo(value)
                                }}
                                checked={forceToEnterCardInfo}
                                width={35}
                                height={20}
                                handleDiameter={30}
                                uncheckedIcon={false}
                                checkedIcon={false}
                                boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                              />
                              {/* <Text
                          style={{
                            fontSize: 18,
                            color: status1
                              ? Colors.primaryColor
                              : Colors.fieldtTxtColor,
                            alignSelf: 'center',
                          }}>
                          {status1 ? 'Yes' : 'No'}
                        </Text> */}
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -11 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                Ask Payment Options First
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setAskPaymentFirstOrderTypes(items.map(item => item.value));
                                  }}
                                  value={askPaymentFirstOrderTypes}
                                  items={[
                                    {
                                      label: 'Dine In',
                                      value: ORDER_TYPE.DINEIN,
                                    },
                                    {
                                      label: 'Takeaway',
                                      value: ORDER_TYPE.PICKUP,
                                    },
                                    {
                                      label: 'Delivery',
                                      value: ORDER_TYPE.DELIVERY,
                                    },
                                  ]}
                                  multiple={true}
                                  multipleText={`${askPaymentFirstOrderTypes.length} type(s) selected`}
                                  open={openAPOF}
                                  setOpen={setOpenAPOF}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -12 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                Delivery Payment Options
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setDeliveryPaymentOptions(items.value);
                                  }}
                                  value={deliveryPaymentOptions}
                                  items={deliveryPaymentDropDown}
                                  // multiple={true}
                                  // multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openDPO}
                                  setOpen={setOpenDPO}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -15 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                Takeaway Payment Options
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setPickupPaymentOptions(items.value);
                                  }}
                                  value={pickupPaymentOptions}
                                  items={deliveryPaymentDropDown}
                                  // multiple={true}
                                  // multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openTPO}
                                  setOpen={setOpenTPO}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -20 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Dine-in Payment Options\n(Dynamic QR)'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setDineinPaymentOptions(items.value);
                                  }}
                                  value={dineinPaymentOptions}
                                  items={deliveryPaymentDropDown}
                                  // multiple={true}
                                  // multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openDiPO_D}
                                  setOpen={setOpenDiPO_D}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -30 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Dine-in Payment Options\n(Static QR)'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setDineinPaymentOptionsGeneric(items.value);
                                  }}
                                  value={dineinPaymentOptionsGeneric}
                                  items={[
                                    {
                                      label: "Pay Now",
                                      value: USER_ORDER_PAYMENT_OPTIONS.ONLINE,
                                    },
                                    {
                                      label: "Pay Later",
                                      value: USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
                                    },
                                    {
                                      label: "All",
                                      value: USER_ORDER_PAYMENT_OPTIONS.ALL,
                                    },
                                  ]}
                                  // multiple={true}
                                  // multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openDiPO_S}
                                  setOpen={setOpenDiPO_S}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -40 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Docket Payment Options'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setDocketPaymentOptions(items.value);
                                  }}
                                  value={docketPaymentOptions}
                                  items={[
                                    {
                                      label: "Pay Now",
                                      value: USER_ORDER_PAYMENT_OPTIONS.ONLINE,
                                    },
                                    {
                                      label: "Pay Later",
                                      value: USER_ORDER_PAYMENT_OPTIONS.OFFLINE,
                                    },
                                    {
                                      label: "All",
                                      value: USER_ORDER_PAYMENT_OPTIONS.ALL,
                                    },
                                  ]}
                                  // multiple={true}
                                  // multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openDocP}
                                  setOpen={setOpenDocP}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -49 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Print Receipt\nWhen Paid Online'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    onChange={(statusTemp) => {
                                      setPrintReceiptWhenPaidOnline(statusTemp)
                                    }}
                                    checked={printReceiptWhenPaidOnline}
                                    width={35}
                                    height={20}
                                    handleDiameter={30}
                                    uncheckedIcon={false}
                                    checkedIcon={false}
                                    boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                    activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -49 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Auto Print\nPay Slip'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    onChange={(statusTemp) =>
                                      // setState({ status: status })
                                      setAutoPrintPaySlip(statusTemp)
                                    }
                                    checked={autoPrintPaySlip}
                                    width={35}
                                    height={20}
                                    handleDiameter={30}
                                    uncheckedIcon={false}
                                    checkedIcon={false}
                                    boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                    activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -50 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Receipt\nPrinting Options'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setReceiptPrintingOptions(items.value);
                                  }}
                                  value={receiptPrintingOptions}
                                  items={RECEIPT_PRINTING_EVENT_TYPE_DROPDOWN_LIST}
                                  // multiple={true}
                                  // multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openRPO}
                                  setOpen={setOpenRPO}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -60 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Cash Drawer\nOpening Options'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setCashDrawerOpeningOptions(items.value);
                                  }}
                                  value={cashDrawerOpeningOptions}
                                  items={CASH_DRAWER_OPEN_EVENT_TYPE_DROPDOWN_LIST}
                                  // multiple={true}
                                  // multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openCDOO}
                                  setOpen={setOpenCDOO}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -70 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Kitchen Docket\nPrinting Options'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setDocketPaymentOptions(items.value);
                                  }}
                                  value={kdPrintEventTypes}
                                  items={KD_PRINT_EVENT_TYPE_DROPDOWN_LIST}
                                  multiple={true}
                                  multipleText={`${kdPrintEventTypes.length} event(s) selected`}
                                  open={openKDPO}
                                  setOpen={setOpenKDPO}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -80 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Kitchen Docket\nPrinting Variation'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setKdPrintVariation(items.value);
                                  }}
                                  value={kdPrintVariation}
                                  items={KD_PRINT_VARIATION_DROPDOWN_LIST}
                                  // multiple={true}
                                  // multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openKDPV}
                                  setOpen={setOpenKDPV}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -90 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Kitchen Docket\nFont Size'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setKdFontSize(items.value);
                                  }}
                                  value={kdFontSize}
                                  items={KD_FONT_SIZE_DROPDOWN_LIST}
                                  // multiple={true}
                                  // multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openKDFS}
                                  setOpen={setOpenKDFS}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -91 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Kitchen Docket\nFont Size\n(Table, Order ID/Type)'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setKdHeaderFontSize(items.value);
                                  }}
                                  value={kdHeaderFontSize}
                                  items={KD_FONT_SIZE_DROPDOWN_LIST}
                                  // multiple={true}
                                  // multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openKDFS2}
                                  setOpen={setOpenKDFS2}
                                />
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -100 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Kitchen Docket\nPrinting SKU'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    onChange={(statusTemp) => {
                                      setIsKdPrintSku(statusTemp)
                                    }}
                                    checked={isKdPrintSku}
                                    width={35}
                                    height={20}
                                    handleDiameter={30}
                                    uncheckedIcon={false}
                                    checkedIcon={false}
                                    boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                    activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -100 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Kitchen Docket\nPrinting\nUser Info'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    onChange={(statusTemp) => {
                                      setIsKdPrintUserInfo(statusTemp)
                                    }}
                                    checked={isKdPrintUserInfo}
                                    width={35}
                                    height={20}
                                    handleDiameter={30}
                                    uncheckedIcon={false}
                                    checkedIcon={false}
                                    boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                    activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -100 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Open Mode'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    onChange={(statusTemp) => {
                                      setToggleOpenOrder(statusTemp)
                                    }}
                                    checked={toggleOpenOrder}
                                    width={35}
                                    height={20}
                                    handleDiameter={30}
                                    uncheckedIcon={false}
                                    checkedIcon={false}
                                    boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                    activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          {
                            currOutlet && currOutlet.showOfflineMode
                              ?
                              <View style={[styles.viewContainer, { zIndex: -100 }]}>
                                <View>
                                  <Text
                                    style={[
                                      styles.textSize,
                                      {
                                        width: windowWidth * 0.2,
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: switchMerchant ? 10 : 14,
                                      },
                                    ]}>
                                    {'Offline Mode'}
                                  </Text>
                                </View>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <View
                                    style={{
                                      width: 200,
                                      height: 50,
                                      justifyContent: 'center',
                                      marginLeft: 5,
                                    }}>
                                    <View
                                      style={{
                                        flexDirection: 'row',
                                        flex: 1,
                                        alignItems: 'center',
                                      }}>
                                      <Switch
                                        onChange={(statusTemp) => {
                                          setToggleOfflineMode(statusTemp)
                                        }}
                                        checked={toggleOfflineMode}
                                        width={35}
                                        height={20}
                                        handleDiameter={30}
                                        uncheckedIcon={false}
                                        checkedIcon={false}
                                        boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                        activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                      />
                                    </View>
                                  </View>
                                </View>
                              </View>
                              :
                              <></>
                          }

                          <View style={[styles.viewContainer, { zIndex: -100 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Multiple POS\nTerminal Mode'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    onChange={(statusTemp) => {
                                      setMultiplePosTerminalMode(statusTemp)
                                    }}
                                    checked={multiplePosTerminalMode}
                                    width={35}
                                    height={20}
                                    handleDiameter={30}
                                    uncheckedIcon={false}
                                    checkedIcon={false}
                                    boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                    activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -101 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Disable\nAuto Printing'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    onChange={async (statusTemp) => {
                                      // setState({ status: status })
                                      setToggleDisableAutoPrint(statusTemp);

                                      await AsyncStorage.setItem('toggleDisableAutoPrint', statusTemp ? '1' : '0');
                                      global.outletToggleDisableAutoPrint = statusTemp ? true : false;
                                    }}
                                    checked={toggleDisableAutoPrint}
                                    width={35}
                                    height={20}
                                    handleDiameter={30}
                                    uncheckedIcon={false}
                                    checkedIcon={false}
                                    boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                    activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -102 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Disable\nPrinting Alerts'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                <View
                                  style={{
                                    flexDirection: 'row',
                                    flex: 1,
                                    alignItems: 'center',
                                  }}>
                                  <Switch
                                    onChange={async (statusTemp) => {
                                      // setState({ status: status })
                                      setToggleDisablePrintingAlert(statusTemp);

                                      await AsyncStorage.setItem('toggleDisablePrintingAlert', statusTemp ? '1' : '0');

                                      global.outletToggleDisablePrintingAlert = statusTemp ? true : false;
                                    }}
                                    checked={toggleDisablePrintingAlert}
                                    width={35}
                                    height={20}
                                    handleDiameter={30}
                                    uncheckedIcon={false}
                                    checkedIcon={false}
                                    boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                    activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                  />
                                </View>
                              </View>
                            </View>
                          </View>

                          <View style={[styles.viewContainer, { zIndex: -110 }]}>
                            <View>
                              <Text
                                style={[
                                  styles.textSize,
                                  {
                                    width: windowWidth * 0.2,
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: switchMerchant ? 10 : 14,
                                  },
                                ]}>
                                {'Idle Auto Logout\nOptions'}
                              </Text>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                              }}>
                              <View
                                style={{
                                  width: 200,
                                  height: 50,
                                  justifyContent: 'center',
                                  marginLeft: 5,
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setUserIdleSignOutOptions(items.value);
                                  }}
                                  value={userIdleSignOutOptions}
                                  items={USER_IDLE_SIGN_OUT_EVENT_TYPE_DROPDOWN_LIST}
                                  // multiple={true}
                                  // multipleText={`${scOrderTypes.length} type(s) selected`}
                                  open={openIALO}
                                  setOpen={setOpenIALO}
                                />
                              </View>
                            </View>
                          </View>
                          
                          {supportCodeData ? (
                            <View style={[styles.viewContainer, { zIndex: -120 }]}>
                              <View
                                style={{
                                  width: switchMerchant ? windowWidth * 0.2 : windowWidth * 0.2,
                                  height: 50,
                                  justifyContent: 'center',
                                  // borderWidth:1
                                }}>
                                {/* 2023-06-27 - Hide to test */}
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    // width: 210,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    // width: 210,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}

                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}

                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  dropDownDirection="BOTTOM"
                                  onSelectItem={(items) => {
                                    setDropDownResetList(items.map(item => item.value));
                                  }}
                                  value={dropDownResetList}
                                  items={resetDropDown}
                                  multiple={true}
                                  multipleText={`${dropDownResetList.length} type(s) selected`}
                                  open={openRESET}
                                  setOpen={setOpenRESET}
                                />
                              </View>
                              
                              <View
                                style={{
                                  flexDirection: 'row',
                                  flex: 1,
                                  alignItems: 'center',
                                }}>
                                <View
                                  style={{
                                    width: 200,
                                    height: 50,
                                    justifyContent: 'center',
                                    marginLeft: 5,
                                  }}>
                                  <TouchableOpacity
                                    style={{
                                      justifyContent: 'center',
                                      flexDirection: 'row',
                                      borderWidth: 1,
                                      borderColor: Colors.primaryColor,
                                      backgroundColor: '#4E9F7D',
                                      borderRadius: 5,
                                      width: 120,
                                      paddingHorizontal: 10,
                                      height: switchMerchant ? 35 : 40,
                                      alignItems: 'center',
                                      shadowOffset: {
                                        width: 0,
                                        height: 2,
                                      },
                                      shadowOpacity: 0.22,
                                      shadowRadius: 3.22,
                                      elevation: 1,
                                      zIndex: -4,
                                    }}
                                    disabled={loading}
                                    onPress={() => {
                                      if (dropDownResetList.length > 0) {
                                        // Alert.alert(
                                        //   'Warning',
                                        //   'Are your sure want to proceed with resetting data?\n\nNote: This will clear your existing data',
                                        //   [
                                        //     {
                                        //       text: 'OK',
                                        //       onPress: () => {
                                        //         resetOutletData();
                                        //       },
                                        //     },
                                        //     {
                                        //       text: 'Cancel',
                                        //       onPress: () => {
                                        //       },
                                        //     },
                                        //   ],
                                        //   { cancelable: false },
                                        // );
                                        if (window.confirm("Warning\nAre your sure want to proceed with resetting data?\n\nNote: This will clear your existing data"))
                                        {
                                          resetOutletData();
                                        }
                                      } else {
                                        // Alert.alert(
                                        //   'Info',
                                        //   'Please select at least one option to proceed',
                                        //   [
                                        //     {
                                        //       text: 'OK',
                                        //       onPress: () => { },
                                        //     },
                                        //   ],
                                        //   { cancelable: false },
                                        // );
                                        alert("Info\nPlease select at least one option to proceed");
                                      }
                                    }}>
                                    <Text
                                      style={{
                                        color: Colors.whiteColor,
                                        //marginLeft: 5,
                                        fontSize: switchMerchant ? 10 : 16,
                                        fontFamily: 'NunitoSans-Bold',
                                      }}>
                                      {'RESET'}
                                    </Text>
                                  </TouchableOpacity>
                                </View>
                              </View>
                            </View>
                          ) : null}


                          {/* <View style={styles.viewContainer}>
                      <View>
                        <Text style={styles.textSize}>Ordering Method:</Text>
                      </View>
                      <View
                        style={{
                          flexDirection: 'row',
                          flex: 1
                        }}>
                        <TouchableOpacity
                          style={{
                            marginVertical: 10,
                            width: '100%',
                            flexDirection: 'row',
                            flex: 1,
                            marginLeft: 35
                          }}
                          onPress={() => {
                            // setState({
                            //   selfCollect: !selfCollect,
                            // });
                            setSelfCollect(!selfCollect);
                          }}
                        >
                          <View style={{
                            marginHorizontal: 15,
                            backgroundColor:
                              selfCollect
                                ? Colors.primaryColor
                                : "#e8e9eb",
                            width: 25,
                            height: 25,
                            justifyContent: "center",
                            alignItems: "center",
                            borderRadius: 6,
                          }}>
                            <Icon
                              name="check"
                              size={17}
                              color={
                                selfCollect
                                  ? Colors.whiteColor
                                  : Colors.descriptionColor
                              }
                            />
                          </View>
                          <Text style={{
                            fontSize: 15,
                            color: selfCollect
                              ? Colors.primaryColor
                              : Colors.descriptionColor
                          }}>Self Collection</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={{
                            marginVertical: 10,
                            width: '100%',
                            flexDirection: 'row',
                            flex: 1,
                            marginLeft: 20
                          }}
                          onPress={() => {
                            // setState({
                            //   selfCollect: !selfCollect,
                            // });
                            setSelfCollect(!selfCollect);
                          }}
                        >
                          <View style={{
                            marginHorizontal: 15,
                            backgroundColor:
                              !selfCollect
                                ? Colors.primaryColor
                                : "#e8e9eb",
                            width: 25,
                            height: 25,
                            justifyContent: "center",
                            alignItems: "center",
                            borderRadius: 6,
                          }}>
                            <Icon
                              name="check"
                              size={17}
                              color={
                                !selfCollect
                                  ? Colors.whiteColor
                                  : Colors.descriptionColor
                              }
                            />
                          </View>
                          <Text style={{
                            fontSize: 15,
                            color: !selfCollect
                              ? Colors.primaryColor
                              : Colors.descriptionColor
                          }}>Table Order</Text>
                        </TouchableOpacity>
                      </View>
                    </View> */}

                          {/* <View style={styles.viewContainer}>
                      <View style={{ flex: 1 }}>
                        <Text style={styles.textSize}>
                          Payment Options:
                            </Text>
                      </View>
                      <TouchableOpacity
                        onPress={() => {
                          // setState({
                          //   expandView: !expandView,
                          // });
                          setExpandView(!expandView);
                        }}>
                        <View style={styles.textInput}>
                          <Text
                            style={{
                              fontSize: 16,
                              alignSelf: 'center',
                              marginLeft: '5%',
                              color: Colors.descriptionColor,
                            }}>
                            Payment Options
                              </Text>
                          <View style={{ marginLeft: '40%', alignSelf: 'center' }}>
                            <SimpleLineIcons name="arrow-down" size={12} />
                          </View>
                        </View>
                      </TouchableOpacity>
                    </View> */}

                          <View style={[styles.viewContainer, { zIndex: -121 }]}>
                            {expandView == true ? (
                              <View
                                style={{
                                  width: 300,
                                  height: 220,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderRadius: 7,
                                  marginLeft: '36%',
                                  flex: 1,
                                }}>
                                <ScrollView
                                  showsVerticalScrollIndicator={false}
                                  style={{ width: '100%', height: '100%' }}>
                                  <TouchableOpacity
                                    style={{
                                      marginVertical: 10,
                                      width: '100%',
                                      flexDirection: 'row',
                                    }}
                                    onPress={() => {
                                      // setState({
                                      //   isChecked2: !isChecked2,
                                      // });
                                      setIsChecked2(!isChecked2);
                                    }}>
                                    <View
                                      style={{
                                        marginHorizontal: 15,
                                        backgroundColor: isChecked2
                                          ? Colors.primaryColor
                                          : '#e8e9eb',
                                        width: 25,
                                        height: 25,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        borderRadius: 6,
                                      }}>
                                      <Icon
                                        name="check"
                                        size={17}
                                        color={
                                          isChecked2
                                            ? Colors.whiteColor
                                            : Colors.descriptionColor
                                        }
                                      />
                                    </View>
                                    <Image
                                      source={{
                                        uri: 'https://i.ibb.co/jLSJNG2/creditc.png',
                                      }}
                                      style={{
                                        resizeMode: 'contain',
                                        width: 100,
                                        height: 30,
                                      }}
                                    />
                                  </TouchableOpacity>
                                  <TouchableOpacity
                                    style={{
                                      marginVertical: 10,
                                      width: '100%',
                                      flexDirection: 'row',
                                    }}
                                    onPress={() => {
                                      // setState({
                                      //   isChecked3: !isChecked3,
                                      // });
                                      setIsChecked3(!isChecked3);
                                    }}>
                                    <View
                                      style={{
                                        marginHorizontal: 15,
                                        backgroundColor: isChecked3
                                          ? Colors.primaryColor
                                          : '#e8e9eb',
                                        width: 25,
                                        height: 25,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        borderRadius: 6,
                                      }}>
                                      <Icon
                                        name="check"
                                        size={17}
                                        color={
                                          isChecked3
                                            ? Colors.whiteColor
                                            : Colors.descriptionColor
                                        }
                                      />
                                    </View>
                                    <Image
                                      source={{
                                        uri: 'https://newsroom.mastercard.com/wp-content/uploads/2016/09/paypal-logo.png',
                                      }}
                                      style={{
                                        resizeMode: 'contain',
                                        width: 100,
                                        height: 30,
                                      }}
                                    />
                                  </TouchableOpacity>

                                  <TouchableOpacity
                                    style={{
                                      marginVertical: 10,
                                      width: '100%',
                                      flexDirection: 'row',
                                    }}
                                    onPress={() => {
                                      // setState({
                                      //   isChecked4: !isChecked4,
                                      // });
                                      setIsChecked4(!isChecked4);
                                    }}>
                                    <View
                                      style={{
                                        marginHorizontal: 15,
                                        backgroundColor: isChecked4
                                          ? Colors.primaryColor
                                          : '#e8e9eb',
                                        width: 25,
                                        height: 25,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        borderRadius: 6,
                                      }}>
                                      <Icon
                                        name="check"
                                        size={17}
                                        color={
                                          isChecked4
                                            ? Colors.whiteColor
                                            : Colors.descriptionColor
                                        }
                                      />
                                    </View>
                                    <Text
                                      style={{
                                        fontSize: 18,
                                        color: isChecked4
                                          ? Colors.primaryColor
                                          : Colors.descriptionColor,
                                      }}>
                                      E-wallet
                                    </Text>
                                  </TouchableOpacity>

                                  <TouchableOpacity
                                    style={{
                                      marginVertical: 10,
                                      width: '100%',
                                      flexDirection: 'row',
                                    }}
                                    onPress={() => {
                                      // setState({
                                      //   isChecked5: !isChecked5,
                                      // });
                                      setIsChecked5(!isChecked5);
                                    }}>
                                    <View
                                      style={{
                                        marginHorizontal: 15,
                                        backgroundColor: isChecked5
                                          ? Colors.primaryColor
                                          : '#e8e9eb',
                                        width: 25,
                                        height: 25,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        borderRadius: 6,
                                      }}>
                                      <Icon
                                        name="check"
                                        size={17}
                                        color={
                                          isChecked5
                                            ? Colors.whiteColor
                                            : Colors.descriptionColor
                                        }
                                      />
                                    </View>
                                    <Text
                                      style={{
                                        fontSize: 18,
                                        color: isChecked5
                                          ? Colors.primaryColor
                                          : Colors.descriptionColor,
                                      }}>
                                      Online Transfer
                                    </Text>
                                  </TouchableOpacity>
                                </ScrollView>
                              </View>
                            ) : null}
                          </View>
                        </View>
                      </View>
                      {/* right */}
                      <View style={{ flex: 1, marginTop: 20 }}>
                        {/* <TouchableOpacity
                    style={[
                      styles.addNewView,
                      {
                        // alignSelf: 'center',
                      },
                    ]}
                    onPress={() => {
                      // setState({ addOutletWindow: true })
                      setAddOutletWindow(true);
                    }}>
                    <View style={styles.addButtonView}>
                      <View style={{ marginLeft: '15%' }}>
                        <AntDesign
                          name="pluscircle"
                          size={20}
                          color={Colors.primaryColor}
                        />
                      </View>
                      <Text
                        style={{ marginLeft: 10, color: Colors.primaryColor }}>
                        Add New Outlet
                      </Text>
                    </View>
                  </TouchableOpacity> */}
                        <Modal
                          supportedOrientations={['landscape', 'portrait']}
                          animationType="fade"
                          visible={addOutletWindow}
                          transparent>
                          <View
                            style={{
                              backgroundColor: Colors.modalBgColor,
                              flex: 1,
                              width: '100%',
                              height: '100%',
                              justifyContent: 'center',
                              alignItems: 'center',
                            }}>
                            <View
                              style={{
                                // width: '40%',
                                // height: '30%',
                                width: windowWidth * 0.4,
                                height: windowHeight * 0.3,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 12,
                                justifyContent: 'center',
                                alignItems: 'center',

                                ...getTransformForModalInsideNavigation(),
                              }}>
                              <TouchableOpacity
                                style={{
                                  position: 'absolute',
                                  top: '8%',
                                  right: '5%',
                                  // width: 40,
                                  // height: 40,
                                  // backgroundColor: 'red'
                                }}
                                onPress={() => setAddOutletWindow(false)}>
                                {/* <Close size={26} color={Colors.blackColor} /> */}
                                <AIcon
                                  name="closecircle"
                                  size={25}
                                  color={Colors.fieldtTxtColor}
                                />
                              </TouchableOpacity>

                              <View style={{ padding: 13 }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-SemiBold',
                                    fontSize: 18,
                                  }}>
                                  Add New Outlet Name
                                </Text>
                              </View>

                              <View
                                style={{
                                  width: '50%',
                                  height: '20%',
                                  backgroundColor: 'white',
                                  borderRadius: 10,
                                  marginBottom: 15,
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}>
                                <TextInput
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  placeholderTextColor={Platform.select({
                                    ios: '#a9a9a9',
                                  })}
                                  style={{
                                    fontFamily: 'NunitoSans-Bold',
                                    fontSize: 15,
                                    color: Colors.descriptionColor,
                                    width: '100%',
                                    textAlign: 'center',
                                  }}
                                  placeholder=" Outlet Name"
                                  onChangeText={(text) => {
                                    // setState({ addOutletName: text });
                                    setAddOutletName(text);
                                  }}
                                  value={addOutletName}
                                />
                              </View>

                              <View
                                style={{
                                  width: '30%',
                                  height: '20%',
                                  backgroundColor: Colors.primaryColor,
                                  borderRadius: 10,
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  padding: 13,
                                }}>
                                <TouchableOpacity
                                  onPress={() => {
                                    addOutlet();
                                    // setState({ addOutletWindow: false });
                                    setAddOutletWindow(false);
                                  }}>
                                  <Text
                                    style={{
                                      fontFamily: 'NunitoSans-SemiBold',
                                      fontSize: 20,
                                      color: 'white',
                                    }}>
                                    Add
                                  </Text>
                                </TouchableOpacity>
                              </View>
                            </View>
                          </View>
                        </Modal>

                        <Modal
                          supportedOrientations={['landscape', 'portrait']}
                          animationType="fade"
                          visible={showModalBreakTime}
                          transparent>
                          <View
                            style={{
                              backgroundColor: Colors.modalBgColor,
                              flex: 1,
                              width: '100%',
                              height: '100%',
                              justifyContent: 'center',
                              alignItems: 'center',
                            }}>
                            <View
                              style={{
                                // width: '40%',
                                // height: '60%',
                                width: windowWidth * 0.4,
                                height: windowHeight * 0.6,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 12,
                                justifyContent: 'center',
                                alignItems: 'center',

                                ...getTransformForModalInsideNavigation(),
                              }}>
                              <TouchableOpacity
                                style={{
                                  position: 'absolute',
                                  top: '8%',
                                  right: '5%',
                                  // width: 40,
                                  // height: 40,
                                  // backgroundColor: 'red'
                                }}
                                onPress={() => setShowModalBreakTime(false)}>
                                {/* <Close size={26} color={Colors.blackColor} /> */}
                                <AIcon
                                  name="closecircle"
                                  size={25}
                                  color={Colors.fieldtTxtColor}
                                />
                              </TouchableOpacity>

                              <View style={{ padding: 13 }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-SemiBold',
                                    fontSize: 18,
                                  }}>
                                  Break Time
                                </Text>
                              </View>

                              {/* <View
                              style={{
                                width: '50%',
                                height: '20%',
                                backgroundColor: 'white',
                                borderRadius: 10,
                                marginBottom: 15,
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                placeholderTextColor={Platform.select({
                                  ios: '#a9a9a9',
                                })}
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: 15,
                                  color: Colors.descriptionColor,
                                  width: '100%',
                                  textAlign: 'center',
                                }}
                                placeholder=" Outlet Name"
                                onChangeText={(text) => {
                                  // setState({ addOutletName: text });
                                  setAddOutletName(text);
                                }}
                                value={addOutletName}
                              />
                            </View> */}

                              <View
                                style={{
                                  //flexDirection: 'row',
                                  //width: '100%',
                                  marginLeft: '6%',

                                  marginTop: '5%',

                                  // backgroundColor: 'green',

                                  width: '100%',
                                  height: '50%',
                                }}>
                                <View style={{ flexDirection: 'row' }}>
                                  <View style={{ flex: 0.6 }}>
                                    <Text style={styles.textSize} />
                                  </View>
                                  <View style={{ flex: 1.3, marginBottom: 20 }}>
                                    <Text
                                      style={{
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}>
                                      Start Time:
                                    </Text>
                                  </View>
                                  <View style={{ flex: 1.3, marginBottom: 20 }}>
                                    <Text
                                      style={{
                                        fontFamily: 'NunitoSans-Bold',
                                        fontSize: switchMerchant ? 10 : 14,
                                      }}>
                                      End Time:
                                    </Text>
                                  </View>
                                  <View style={{ flex: 0.8, marginBottom: 20 }}>
                                    {/* <Text
                                    style={{
                                      fontFamily: 'NunitoSans-Bold',
                                      fontSize: switchMerchant ? 10 : 14,
                                    }}>
                                    Action:
                                  </Text> */}

                                    <TouchableOpacity
                                      style={{
                                        // backgroundColor: 'red'
                                      }}
                                      onPress={() => {
                                        // setCurrOutletOpeningOff({
                                        //   ...currOutletOpeningOff,
                                        //   [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                        // });

                                        // setShowModalBreakTime(true);

                                        if (currOutletBreakTime[selectedBreakTimeDay].length >= 5) {
                                          // Alert.alert('Info', 'Max time slots for break time is 5 for now.')
                                          alert('Info', 'Max time slots for break time is 5 for now.')
                                        }
                                        else {
                                          setCurrOutletBreakTime({
                                            ...currOutletBreakTime,
                                            [selectedBreakTimeDay]: [
                                              ...currOutletBreakTime[selectedBreakTimeDay],
                                              `${moment().format('HHmm')}-${moment().format('HHmm')}`,
                                            ],
                                          });
                                        }
                                      }}>
                                      {/* <FontAwesome5
                                      name='edit'
                                      size={23}
                                      style={{
                                        bottom: 6,
                                      }}
                                      color={Colors.primaryColor}
                                    /> */}
                                      <Icon
                                        name="plus-circle"
                                        size={switchMerchant ? 17 : 20}
                                        color={Colors.primaryColor}
                                      />
                                    </TouchableOpacity>
                                  </View>
                                </View>
                                <View style={{}}>
                                  {currOutletBreakTime &&
                                    currOutletBreakTime[selectedBreakTimeDay] &&
                                    currOutletBreakTime[selectedBreakTimeDay].map((breakTimeGroup, index) => {
                                      // var outletDay = null
                                      // //// console.log("openings", openings)
                                      // for (const d of openings) {
                                      //   if (d.week.toLowerCase() == day.toLowerCase()) {
                                      //     outletDay = d
                                      //   }
                                      // }

                                      var outletOpeningToday = null;

                                      var startTime = '';
                                      var endTime = '';
                                      var startTimeStr = null;
                                      var endTimeStr = null;

                                      ////////////////////////////////////////////////////////

                                      // var outletOpeningWeek = [];

                                      // const weekOrders = [
                                      //   'monday',
                                      //   'tuesday',
                                      //   'wednesday',
                                      //   'thursday',
                                      //   'friday',
                                      //   'saturday',
                                      //   'sunday',
                                      // ];

                                      // const dayRaw = weekOrders[index];

                                      try {
                                        // const outletOpening = currOutletOpening;

                                        // if (outletOpening) {
                                        //   outletOpeningToday =
                                        //     outletOpening[WEEK[index]];
                                        // }

                                        // if (outletOpeningToday) {
                                        //   startTimeStr =
                                        //     outletOpeningToday.split('-')[0];
                                        //   endTimeStr = outletOpeningToday.split('-')[1];

                                        //   startTime = moment(startTimeStr, 'HHmm');
                                        //   endTime = moment(endTimeStr, 'HHmm');

                                        //   // isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                                        // }

                                        // // console.log(outletOpeningToday);

                                        startTimeStr =
                                          breakTimeGroup.split('-')[0];
                                        endTimeStr = breakTimeGroup.split('-')[1];

                                        startTime = moment(startTimeStr, 'HHmm');
                                        endTime = moment(endTimeStr, 'HHmm');

                                        ////////////////////////////////////////////////////////
                                        // do for all days

                                        // for (var i = 0; i < weekOrders.length; i++) {
                                        //   const tempOpeningDay = outletOpening[weekOrders[i]];

                                        //   var startTimeStrDay = '';
                                        //   var endTimeStrDay = '';
                                        //   var isOpeningNowDay = false;

                                        //   if (tempOpeningDay && tempOpeningDay.length > 0) {
                                        //     startTimeStrDay = tempOpeningDay.split('-')[0];
                                        //     endTimeStrDay = tempOpeningDay.split('-')[1];

                                        //     const startTime = moment(startTimeStrDay, 'HHmm');
                                        //     const endTime = moment(endTimeStrDay, 'HHmm');

                                        //     isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                                        //   }

                                        //   outletOpeningWeek.push({
                                        //     startTimeStr: startTimeStrDay,
                                        //     endTimeStr: endTimeStrDay,
                                        //     isOpeningNow: isOpeningNowDay,
                                        //     day: i,
                                        //   });
                                        // }
                                      } catch (ex) {
                                        console.error(ex);
                                      }

                                      return (
                                        <View
                                          style={{
                                            flexDirection: 'row',
                                            marginBottom: 10,
                                          }}>
                                          <View style={{ flex: 0.6 }}>
                                            <Text
                                              style={{
                                                fontFamily: 'NunitoSans-Bold',
                                                fontSize: switchMerchant ? 10 : 14,
                                              }}>
                                              {''}
                                            </Text>
                                          </View>
                                          <View style={{ flex: 1.3, paddingRight: '0%' }}>
                                            <TouchableOpacity
                                              // disabled={currOutletOpeningOff[WEEK[index]]}
                                              style={{}}
                                              onPress={() => {
                                                // setOpenHourPickerVisible(false);
                                                // setCloseHourPickerVisible(true);
                                                // setEditOpeningIndex(index);

                                                setSelectedBreakTimeDayIndex(index);
                                                setShowTimePickerBreakTimeStart(true);
                                              }}>
                                              <View style={{ flexDirection: 'row' }}>
                                                <Text
                                                  style={{
                                                    // color: Colors.fieldtTxtColor,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    color: Colors.blackColor,
                                                    fontSize: switchMerchant ? 10 : 14,
                                                  }}>
                                                  {
                                                    // (currOutletBreakTime[dayRaw] && currOutletBreakTime[dayRaw].length > 0)
                                                    //   ?
                                                    //   currOutletBreakTime[dayRaw].map(breakTimeGroup => breakTimeGroup).join('\n')
                                                    //   :
                                                    //   '-'
                                                    (startTimeStr)
                                                      ?
                                                      startTimeStr
                                                      :
                                                      '-'
                                                  }
                                                </Text>
                                                {/* <MaterialIcons
                                          name="keyboard-arrow-down"
                                          size={switchMerchant ? 15 : 20}
                                          style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                        /> */}
                                              </View>
                                            </TouchableOpacity>
                                          </View>

                                          <View style={{ flex: 1.3, paddingRight: '0%' }}>
                                            <TouchableOpacity
                                              // disabled={currOutletOpeningOff[WEEK[index]]}
                                              style={{}}
                                              onPress={() => {
                                                // setOpenHourPickerVisible(false);
                                                // setCloseHourPickerVisible(true);
                                                // setEditOpeningIndex(index);

                                                setSelectedBreakTimeDayIndex(index);
                                                setShowTimePickerBreakTimeEnd(true);
                                              }}>
                                              <View style={{ flexDirection: 'row' }}>
                                                <Text
                                                  style={{
                                                    // color: Colors.fieldtTxtColor,
                                                    fontFamily: 'NunitoSans-Regular',
                                                    color: Colors.blackColor,
                                                    fontSize: switchMerchant ? 10 : 14,
                                                  }}>
                                                  {
                                                    // (currOutletBreakTime[dayRaw] && currOutletBreakTime[dayRaw].length > 0)
                                                    //   ?
                                                    //   currOutletBreakTime[dayRaw].map(breakTimeGroup => breakTimeGroup).join('\n')
                                                    //   :
                                                    //   '-'
                                                    (endTimeStr)
                                                      ?
                                                      endTimeStr
                                                      :
                                                      '-'
                                                  }
                                                </Text>
                                                {/* <MaterialIcons
                                          name="keyboard-arrow-down"
                                          size={switchMerchant ? 15 : 20}
                                          style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                        /> */}
                                              </View>
                                            </TouchableOpacity>
                                          </View>

                                          {/* <View style={{ flex: 1.3 }}>
                                          <TouchableOpacity
                                            // disabled={currOutletOpeningOff[WEEK[index]]}
                                            style={{}}
                                            onPress={() => {
                                              setOpenHourPickerVisible(false);
                                              setCloseHourPickerVisible(true);
                                              setEditOpeningIndex(index);
                                            }}>
                                            <View style={{ flexDirection: 'row' }}>
                                              <Text
                                                style={{
                                                  // color: Colors.fieldtTxtColor,
                                                  fontFamily: 'NunitoSans-Regular',
                                                  color: Colors.blackColor,
                                                  fontSize: switchMerchant ? 10 : 14,
                                                }}>
                                                {!currOutletOpeningOff[WEEK[index]] ? endTimeStr : '-'}
                                              </Text>
                                              <MaterialIcons
                                                name="keyboard-arrow-down"
                                                size={switchMerchant ? 15 : 20}
                                                style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                              />
                                            </View>
                                          </TouchableOpacity>
                                        </View> */}

                                          <View style={{
                                            flex: 0.8,
                                            justifyContent: 'center',
                                            // alignItems: 'center'                                    
                                          }}>
                                            {/* <Switch
                                      value={currOutletOpeningOff[WEEK[index]]}
                                      onSyncPress={() => {
                                        setCurrOutletOpeningOff({
                                          ...currOutletOpeningOff,
                                          [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                        });
                                      }}
                                      width={switchMerchant ? 34 : 36}
                                      circleColorActive={Colors.primaryColor}
                                      circleColorInactive={Colors.fieldtTxtColor}
                                      backgroundActive="#dddddd"
                                    /> */}
                                            <TouchableOpacity
                                              style={{
                                                // backgroundColor: 'red'
                                              }}
                                              onPress={() => {
                                                // setCurrOutletOpeningOff({
                                                //   ...currOutletOpeningOff,
                                                //   [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                                // });

                                                // setShowModalBreakTime(true);

                                                setCurrOutletBreakTime({
                                                  ...currOutletBreakTime,
                                                  [selectedBreakTimeDay]: currOutletBreakTime[selectedBreakTimeDay].filter((breakTimeStr, index) => {
                                                    return index !== selectedBreakTimeDayIndex;
                                                  }),
                                                });
                                              }}>
                                              {/* <FontAwesome5
                                              name='edit'
                                              size={23}
                                              style={{
                                                bottom: 6,
                                              }}
                                              color={Colors.primaryColor}
                                            /> */}
                                              <Icon
                                                name="minus-circle"
                                                size={switchMerchant ? 17 : 20}
                                                color="#eb3446"
                                              />
                                            </TouchableOpacity>
                                          </View>
                                        </View>
                                      );
                                    })}
                                </View>
                              </View>

                              {/* <View
                              style={{
                                width: '30%',
                                height: '20%',
                                backgroundColor: Colors.primaryColor,
                                borderRadius: 10,
                                justifyContent: 'center',
                                alignItems: 'center',
                                padding: 13,
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  addOutlet();
                                  // setState({ addOutletWindow: false });
                                  setAddOutletWindow(false);
                                }}>
                                <Text
                                  style={{
                                    fontFamily: 'NunitoSans-SemiBold',
                                    fontSize: 20,
                                    color: 'white',
                                  }}>
                                  Add
                                </Text>
                              </TouchableOpacity>
                            </View> */}
                            </View>
                          </View>
                        </Modal>

                        {/* <View style={styles.merchantDisplayView}>
                    <View style={{ justifyContent: 'center', alignItems: 'flex-start' }}>
                      <Text style={{ marginRight: 50, fontSize: 19 }}>Merchant Display</Text>
                      <View style={{ flexDirection: 'row', flex: 1, justifyContent: 'space-between', width: '80%' }}>
                        <View>
                          <Text style={{ color: Colors.fieldtTxtColor, fontSize: 15 }}>Your customer will see this picture</Text>
                        </View>
                        <View style={{ marginLeft: 30 }}>
                          <TouchableOpacity
                            onPress={() => {
                              handleChoosePhoto1();
                            }}>
                            <Text
                              style={{
                                fontSize: 15,
                                color: Colors.primaryColor,
                              }}>
                              change
                              </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </View>
                  <View style={styles.merchantDisplayView}>
                    <Image
                      source={{ uri: cover }}
                      style={{
                        resizeMode: 'contain',
                        width: 300,
                        height: 120,
                        backgroundColor: Colors.secondaryColor
                      }}></Image>
                  </View> */}

                        {/* opening hour */}
                        <View
                          style={{
                            //flexDirection: 'row',
                            //width: '100%',
                            marginLeft: '6%',
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flex: 1.2 }}>
                              <Text style={styles.textSize} />
                            </View>
                            <View style={{ flex: 1.3, marginBottom: 20 }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Opening Hours:
                              </Text>
                            </View>
                            <View style={{ flex: 1.3, marginBottom: 20 }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Closing Hours:
                              </Text>
                            </View>
                            <View style={{ flex: 0.8, marginBottom: 20 }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Off Day:
                              </Text>
                            </View>
                          </View>
                          <View style={{}}>
                            <Modal
                              style={{ flex: 1 }}
                              visible={openHourPickerVisible}
                              transparent={true}
                              animationType="slide"
                            >
                              <View
                                style={{
                                  flex: 1,
                                  backgroundColor: Colors.modalBgColor,
                                  alignItems: "center",
                                  justifyContent: "center",
                                }}
                              >
                                <View
                                  style={{
                                    backgroundColor: Colors.whiteColor,
                                    borderRadius: 12,
                                    padding:
                                      Dimensions.get("screen").width * 0.05,
                                    alignItems: "center",
                                    justifyContent: "center",
                                  }}
                                >
                                  <TimeKeeper
                                    // time={startTimeStr}
                                    onChange={(dt) => {
                                      // const date = moment(dt);
                                      // var newOpenings = openings
                                      // newOpenings[editOpeningIndex].startTime = date.format('hh:mmA')

                                      const openingTime =
                                        currOutletOpening[
                                        WEEK[editOpeningIndex]
                                        ];
                                      const endTimeStr =
                                        openingTime.split("-")[1];

                                      let hour = "";
                                      let minute = "";
                                      if (dt.hour < 10) {
                                        hour = "0" + dt.hour;
                                      } else {
                                        hour = dt.hour;
                                      }
                                      if (dt.minute < 10) {
                                        minute = "0" + dt.minute;
                                      } else {
                                        minute = dt.minute;
                                      }

                                      // setState({
                                      //   openHourPickerVisible: !openHourPickerVisible,
                                      //   closeHourPickerVisible: !closeHourPickerVisible,
                                      //   openings: newOpenings
                                      // });
                                      setCurrOutletOpening({
                                        ...currOutletOpening,
                                        [WEEK[
                                          editOpeningIndex
                                        ]]: `${hour}${minute}-${endTimeStr}`,
                                      });
                                    }}
                                    onDoneClick={() =>
                                      setOpenHourPickerVisible(false)
                                    }
                                    switchToMinuteOnHourSelect
                                  />
                                </View>
                              </View>
                            </Modal>

                            <Modal
                              style={{ flex: 1 }}
                              visible={closeHourPickerVisible}
                              transparent={true}
                              animationType="slide"
                            >
                              <View
                                style={{
                                  flex: 1,
                                  backgroundColor: Colors.modalBgColor,
                                  alignItems: "center",
                                  justifyContent: "center",
                                }}
                              >
                                <View
                                  style={{
                                    backgroundColor: Colors.whiteColor,
                                    borderRadius: 12,
                                    padding:
                                      Dimensions.get("screen").width * 0.05,
                                    alignItems: "center",
                                    justifyContent: "center",
                                  }}
                                >
                                  <TimeKeeper
                                    // time={endTimeStr}
                                    onChange={(dt) => {
                                      // const date = moment(dt);
                                      // var newOpenings = openings
                                      // newOpenings[editOpeningIndex].endTime = date.format('hh:mmA')

                                      const openingTime =
                                        currOutletOpening[
                                        WEEK[editOpeningIndex]
                                        ];
                                      const startTimeStr =
                                        openingTime.split("-")[0];

                                      let hour = "";
                                      let minute = "";
                                      if (dt.hour < 10) {
                                        hour = "0" + dt.hour;
                                      } else {
                                        hour = dt.hour;
                                      }
                                      if (dt.minute < 10) {
                                        minute = "0" + dt.minute;
                                      } else {
                                        minute = dt.minute;
                                      }

                                      // setState({
                                      //   closeHourPickerVisible: !closeHourPickerVisible,
                                      //   openings: newOpenings
                                      // });
                                      setCurrOutletOpening({
                                        ...currOutletOpening,
                                        [WEEK[
                                          editOpeningIndex
                                        ]]: `${startTimeStr}-${hour}${minute}`,
                                      });
                                    }}
                                    onDoneClick={() =>
                                      setCloseHourPickerVisible(false)
                                    }
                                    switchToMinuteOnHourSelect
                                  />
                                </View>
                              </View>
                            </Modal>

                            {currOutletOpening &&
                              currOutletOpening.uniqueId &&
                              week.map((day, index) => {
                                // var outletDay = null
                                // //// console.log("openings", openings)
                                // for (const d of openings) {
                                //   if (d.week.toLowerCase() == day.toLowerCase()) {
                                //     outletDay = d
                                //   }
                                // }

                                var outletOpeningToday = null;

                                var startTime = '';
                                var endTime = '';
                                var startTimeStr = null;
                                var endTimeStr = null;

                                ////////////////////////////////////////////////////////

                                var outletOpeningWeek = [];

                                const weekOrders = [
                                  'monday',
                                  'tuesday',
                                  'wednesday',
                                  'thursday',
                                  'friday',
                                  'saturday',
                                  'sunday',
                                ];

                                try {
                                  const outletOpening = currOutletOpening;

                                  if (outletOpening) {
                                    outletOpeningToday =
                                      outletOpening[WEEK[index]];
                                  }

                                  if (outletOpeningToday) {
                                    startTimeStr =
                                      outletOpeningToday.split('-')[0];
                                    endTimeStr = outletOpeningToday.split('-')[1];

                                    startTime = moment(startTimeStr, 'HHmm');
                                    endTime = moment(endTimeStr, 'HHmm');

                                    // isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                                  }

                                  // console.log(outletOpeningToday);

                                  ////////////////////////////////////////////////////////
                                  // do for all days

                                  // for (var i = 0; i < weekOrders.length; i++) {
                                  //   const tempOpeningDay = outletOpening[weekOrders[i]];

                                  //   var startTimeStrDay = '';
                                  //   var endTimeStrDay = '';
                                  //   var isOpeningNowDay = false;

                                  //   if (tempOpeningDay && tempOpeningDay.length > 0) {
                                  //     startTimeStrDay = tempOpeningDay.split('-')[0];
                                  //     endTimeStrDay = tempOpeningDay.split('-')[1];

                                  //     const startTime = moment(startTimeStrDay, 'HHmm');
                                  //     const endTime = moment(endTimeStrDay, 'HHmm');

                                  //     isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                                  //   }

                                  //   outletOpeningWeek.push({
                                  //     startTimeStr: startTimeStrDay,
                                  //     endTimeStr: endTimeStrDay,
                                  //     isOpeningNow: isOpeningNowDay,
                                  //     day: i,
                                  //   });
                                  // }
                                } catch (ex) {
                                  console.error(ex);
                                }

                                return (
                                  <View
                                    style={{
                                      flexDirection: "row",
                                      justifyContent: "center",
                                      alignItems: "center",
                                      marginVertical: 8,
                                    }}
                                  >
                                    <View style={{ flex: 1.2 }}>
                                      <Text
                                        style={{
                                          fontFamily: 'NunitoSans-Bold',
                                          fontSize: switchMerchant ? 10 : 14,
                                        }}>
                                        {day}
                                      </Text>
                                    </View>
                                    <View style={{ flex: 1.3 }}>
                                      <TouchableOpacity
                                        disabled={currOutletOpeningOff[WEEK[index]]}
                                        onPress={() => {
                                          setOpenHourPickerVisible(true);
                                          setCloseHourPickerVisible(false);
                                          setEditOpeningIndex(index);
                                        }}>
                                        <View style={{ flexDirection: 'row' }}>
                                          <Text
                                            style={{
                                              // color: Colors.fieldtTxtColor,
                                              fontFamily: 'NunitoSans-Regular',
                                              color: Colors.blackColor,
                                              fontSize: switchMerchant ? 10 : 14,
                                            }}>
                                            {!currOutletOpeningOff[WEEK[index]] ? startTimeStr : '-'}
                                          </Text>
                                          {/* <MaterialIcons
                                            name="keyboard-arrow-down"
                                            size={switchMerchant ? 15 : 20}
                                            style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                          /> */}
                                        </View>
                                      </TouchableOpacity>
                                    </View>

                                    <View style={{ flex: 1.3 }}>
                                      <TouchableOpacity
                                        disabled={currOutletOpeningOff[WEEK[index]]}
                                        style={{}}
                                        onPress={() => {
                                          setOpenHourPickerVisible(false);
                                          setCloseHourPickerVisible(true);
                                          setEditOpeningIndex(index);
                                        }}>
                                        <View style={{ flexDirection: 'row' }}>
                                          <Text
                                            style={{
                                              // color: Colors.fieldtTxtColor,
                                              fontFamily: 'NunitoSans-Regular',
                                              color: Colors.blackColor,
                                              fontSize: switchMerchant ? 10 : 14,
                                            }}>
                                            {!currOutletOpeningOff[WEEK[index]] ? endTimeStr : '-'}
                                          </Text>
                                          {/* <MaterialIcons
                                            name="keyboard-arrow-down"
                                            size={switchMerchant ? 15 : 20}
                                            style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                          /> */}
                                        </View>
                                      </TouchableOpacity>
                                    </View>

                                    <View style={{
                                      flex: 0.8,
                                      justifyContent: 'center',
                                      // alignItems: 'center'                                    
                                    }}>
                                      <Switch
                                        onChange={() => {
                                          setCurrOutletOpeningOff({
                                            ...currOutletOpeningOff,
                                            [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                          });
                                        }}
                                        checked={currOutletOpeningOff[WEEK[index]]}
                                        width={35}
                                        height={20}
                                        handleDiameter={30}
                                        uncheckedIcon={false}
                                        checkedIcon={false}
                                        boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                                        activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                                      />
                                      {/* <TouchableOpacity
                                      style={{
                                        // backgroundColor: 'red'
                                      }}
                                      onPress={() => {
                                        setCurrOutletOpeningOff({
                                          ...currOutletOpeningOff,
                                          [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                        });
                                      }}>
                                      <MaterialCommunityIcons
                                        name='beach'
                                        size={30}
                                        style={{
                                          bottom: 6,
                                        }}
                                        color={currOutletOpeningOff[WEEK[index]] ? Colors.primaryColor : Colors.fieldtBgColor2}
                                      />
                                    </TouchableOpacity> */}
                                    </View>
                                  </View>
                                );
                              })}
                          </View>
                        </View>

                        {/* break time */}

                        <View
                          style={{
                            //flexDirection: 'row',
                            //width: '100%',
                            marginLeft: '6%',

                            marginTop: '5%',
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flex: 1.2 }}>
                              <Text style={styles.textSize} />
                            </View>
                            <View style={{ flex: 2.6, marginBottom: 20 }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Break Time:
                              </Text>
                            </View>
                            <View style={{ flex: 0.8, marginBottom: 20 }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Bold',
                                  fontSize: switchMerchant ? 10 : 14,
                                }}>
                                Action:
                              </Text>
                            </View>
                          </View>
                          <View style={{}}>
                            <Modal
                              style={{ flex: 1 }}
                              visible={showTimePickerBreakTimeStart}
                              transparent={true}
                              animationType="slide"
                            >
                              <View
                                style={{
                                  flex: 1,
                                  backgroundColor: Colors.modalBgColor,
                                  alignItems: "center",
                                  justifyContent: "center",
                                }}
                              >
                                <View
                                  style={{
                                    backgroundColor: Colors.whiteColor,
                                    borderRadius: 12,
                                    padding:
                                        Dimensions.get("screen").width * 0.05,
                                    alignItems: "center",
                                    justifyContent: "center",
                                  }}
                                >
                                  <TimeKeeper
                                    onChange={(dt) => {
                                      const openingTime = currOutletBreakTime[selectedBreakTimeDay][selectedBreakTimeDayIndex];
                                      const endTimeStr = openingTime.split('-')[1];

                                      let hour = "";
                                      let minute = "";
                                      if (dt.hour < 10) {
                                        hour = "0" + dt.hour;
                                      } else {
                                        hour = dt.hour;
                                      }
                                      if (dt.minute < 10) {
                                        minute = "0" + dt.minute;
                                      } else {
                                        minute = dt.minute;
                                      }

                                      setCurrOutletBreakTime({
                                        ...currOutletBreakTime,
                                        [selectedBreakTimeDay]: currOutletBreakTime[selectedBreakTimeDay].map((breakTimeStr, index) => {
                                          return index === selectedBreakTimeDayIndex
                                            ?
                                            `${moment(dt).format(
                                              'HHmm',
                                            )}-${endTimeStr}`
                                            :
                                            breakTimeStr;
                                        }),
                                      });
                                    }}
                                    onDoneClick={() =>
                                      setShowTimePickerBreakTimeStart(false)
                                    }
                                    switchToMinuteOnHourSelect
                                  />
                                </View>
                              </View>
                            </Modal>

                            <Modal
                              style={{ flex: 1 }}
                              visible={showTimePickerBreakTimeEnd}
                              transparent={true}
                              animationType="slide"
                            >
                              <View
                                style={{
                                  flex: 1,
                                  backgroundColor: Colors.modalBgColor,
                                  alignItems: "center",
                                  justifyContent: "center",
                                }}
                              >
                                <View
                                  style={{
                                    backgroundColor: Colors.whiteColor,
                                    borderRadius: 12,
                                    padding:
                                        Dimensions.get("screen").width * 0.05,
                                    alignItems: "center",
                                    justifyContent: "center",
                                  }}
                                >
                                  <TimeKeeper
                                    onChange={(dt) => {
                                      const openingTime = currOutletBreakTime[selectedBreakTimeDay][selectedBreakTimeDayIndex];
                                      const startTimeStr = openingTime.split('-')[0];

                                      let hour = "";
                                      let minute = "";
                                      if (dt.hour < 10) {
                                        hour = "0" + dt.hour;
                                      } else {
                                        hour = dt.hour;
                                      }
                                      if (dt.minute < 10) {
                                        minute = "0" + dt.minute;
                                      } else {
                                        minute = dt.minute;
                                      }

                                      setCurrOutletBreakTime({
                                        ...currOutletBreakTime,
                                        [selectedBreakTimeDay]: currOutletBreakTime[selectedBreakTimeDay].map((breakTimeStr, index) => {
                                          return index === selectedBreakTimeDayIndex
                                            ?
                                            `${startTimeStr}-${moment(dt).format(
                                              'HHmm',
                                            )}`
                                            :
                                            breakTimeStr;
                                        }),
                                      });
                                    }}
                                    onDoneClick={() =>
                                      setShowTimePickerBreakTimeEnd(false)
                                    }
                                    switchToMinuteOnHourSelect
                                  />
                                </View>
                              </View>
                            </Modal>
                            
                            {currOutletBreakTime &&
                              week.map((day, index) => {
                                // var outletDay = null
                                // //// console.log("openings", openings)
                                // for (const d of openings) {
                                //   if (d.week.toLowerCase() == day.toLowerCase()) {
                                //     outletDay = d
                                //   }
                                // }

                                var outletOpeningToday = null;

                                var startTime = '';
                                var endTime = '';
                                var startTimeStr = null;
                                var endTimeStr = null;

                                ////////////////////////////////////////////////////////

                                var outletOpeningWeek = [];

                                const weekOrders = [
                                  'monday',
                                  'tuesday',
                                  'wednesday',
                                  'thursday',
                                  'friday',
                                  'saturday',
                                  'sunday',
                                ];

                                const dayRaw = weekOrders[index];

                                try {
                                  const outletOpening = currOutletOpening;

                                  if (outletOpening) {
                                    outletOpeningToday =
                                      outletOpening[WEEK[index]];
                                  }

                                  if (outletOpeningToday) {
                                    startTimeStr =
                                      outletOpeningToday.split('-')[0];
                                    endTimeStr = outletOpeningToday.split('-')[1];

                                    startTime = moment(startTimeStr, 'HHmm');
                                    endTime = moment(endTimeStr, 'HHmm');

                                    // isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                                  }

                                  // console.log(outletOpeningToday);

                                  ////////////////////////////////////////////////////////
                                  // do for all days

                                  // for (var i = 0; i < weekOrders.length; i++) {
                                  //   const tempOpeningDay = outletOpening[weekOrders[i]];

                                  //   var startTimeStrDay = '';
                                  //   var endTimeStrDay = '';
                                  //   var isOpeningNowDay = false;

                                  //   if (tempOpeningDay && tempOpeningDay.length > 0) {
                                  //     startTimeStrDay = tempOpeningDay.split('-')[0];
                                  //     endTimeStrDay = tempOpeningDay.split('-')[1];

                                  //     const startTime = moment(startTimeStrDay, 'HHmm');
                                  //     const endTime = moment(endTimeStrDay, 'HHmm');

                                  //     isOpeningNow = moment().isSameOrAfter(startTime) && moment().isBefore(endTime);
                                  //   }

                                  //   outletOpeningWeek.push({
                                  //     startTimeStr: startTimeStrDay,
                                  //     endTimeStr: endTimeStrDay,
                                  //     isOpeningNow: isOpeningNowDay,
                                  //     day: i,
                                  //   });
                                  // }
                                } catch (ex) {
                                  console.error(ex);
                                }

                                return (
                                  <View
                                    style={{
                                      flexDirection: 'row',
                                      marginBottom: 10,
                                    }}>
                                    <View style={{ flex: 1.2 }}>
                                      <Text
                                        style={{
                                          fontFamily: 'NunitoSans-Bold',
                                          fontSize: switchMerchant ? 10 : 14,
                                        }}>
                                        {day}
                                      </Text>
                                    </View>
                                    <View style={{ flex: 2.6, paddingRight: '0%' }}>
                                      <View style={{ flexDirection: 'row', paddingRight: '10%' }}>
                                        <Text
                                          style={{
                                            // color: Colors.fieldtTxtColor,
                                            fontFamily: 'NunitoSans-Regular',
                                            color: Colors.blackColor,
                                            fontSize: switchMerchant ? 10 : 14,
                                          }}>
                                          {
                                            currOutletOpeningOff[dayRaw]
                                              ?
                                              '-'
                                              :
                                              (
                                                (currOutletBreakTime[dayRaw] && currOutletBreakTime[dayRaw].length > 0)
                                                  ?
                                                  currOutletBreakTime[dayRaw].map(breakTimeGroup => breakTimeGroup).join('\n')
                                                  :
                                                  '-'
                                              )
                                          }
                                        </Text>
                                        {/* <MaterialIcons
                                          name="keyboard-arrow-down"
                                          size={switchMerchant ? 15 : 20}
                                          style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                        /> */}
                                      </View>
                                    </View>

                                    {/* <View style={{ flex: 1.3 }}>
                                    <TouchableOpacity
                                      disabled={currOutletOpeningOff[WEEK[index]]}
                                      style={{}}
                                      onPress={() => {
                                        setOpenHourPickerVisible(false);
                                        setCloseHourPickerVisible(true);
                                        setEditOpeningIndex(index);
                                      }}>
                                      <View style={{ flexDirection: 'row' }}>
                                        <Text
                                          style={{
                                            // color: Colors.fieldtTxtColor,
                                            fontFamily: 'NunitoSans-Regular',
                                            color: Colors.blackColor,
                                            fontSize: switchMerchant ? 10 : 14,
                                          }}>
                                          {!currOutletOpeningOff[WEEK[index]] ? endTimeStr : '-'}
                                        </Text>
                                        <MaterialIcons
                                          name="keyboard-arrow-down"
                                          size={switchMerchant ? 15 : 20}
                                          style={{ color: !currOutletOpeningOff[WEEK[index]] ? Colors.blackColor : 'transparent', paddingLeft: 20 }}
                                        />
                                      </View>
                                    </TouchableOpacity>
                                  </View> */}

                                    <View style={{
                                      flex: 0.8,
                                      justifyContent: 'center',
                                      // alignItems: 'center'                                    
                                    }}>
                                      {/* <Switch
                                      value={currOutletOpeningOff[WEEK[index]]}
                                      onSyncPress={() => {
                                        setCurrOutletOpeningOff({
                                          ...currOutletOpeningOff,
                                          [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                        });
                                      }}
                                      width={switchMerchant ? 34 : 36}
                                      circleColorActive={Colors.primaryColor}
                                      circleColorInactive={Colors.fieldtTxtColor}
                                      backgroundActive="#dddddd"
                                    /> */}
                                      <TouchableOpacity
                                        disabled={currOutletOpeningOff[dayRaw]}
                                        style={{
                                          // backgroundColor: 'red'
                                        }}
                                        onPress={() => {
                                          // setCurrOutletOpeningOff({
                                          //   ...currOutletOpeningOff,
                                          //   [WEEK[index]]: !currOutletOpeningOff[WEEK[index]],
                                          // });

                                          setSelectedBreakTimeDayIndex(0);
                                          setSelectedBreakTimeDay(dayRaw);

                                          setShowModalBreakTime(true);
                                        }}>
                                        <Edit
                                          height={30}
                                          width={30}
                                          color={
                                            currOutletOpeningOff[dayRaw]
                                              ? Colors.fieldtTxtColor
                                              : Colors.primaryColor
                                          }
                                        />
                                      </TouchableOpacity>
                                    </View>
                                  </View>
                                );
                              })}
                          </View>
                        </View>

                        <View
                          style={{
                            marginTop: 20,
                            width: '100%',
                            // backgroundColor: 'red',

                            // flexDirection: 'row',
                            // alignItems: 'center',
                            // justifyContent: 'center',
                          }}>
                          {/* <View
                      style={{
                        // backgroundColor: 'red',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        margin: 15,
                      }}> */}

                          {/* <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 15,
                          color: Colors.fieldtTxtColor,
                          // width: '100%',
                          backgroundColor: Colors.fieldtBgColor,
                          width: 140,
                          textAlign: 'center',
                          height: 40,
                          borderRadius: 8,
                          marginBottom: 10
                        }}
                        placeholder="Printer Name"
                        onChangeText={(text) => {
                          // setState({ addOutletName: text });
                          // setAddOutletName(text);
                          setPrinterName(text);
                        }}
                        defaultValue={printerName}
                        // keyboardType={'decimal-pad'}
                      />       */}

                          {/* <TextInput
                        underlineColorAndroid={Colors.fieldtBgColor}
                        style={{
                          fontFamily: 'NunitoSans-Bold',
                          fontSize: 15,
                          color: Colors.fieldtTxtColor,
                          // width: '100%',
                          backgroundColor: Colors.fieldtBgColor,
                          width: 140,
                          textAlign: 'center',
                          height: 40,
                          borderRadius: 8,
                        }}
                        placeholder="Printer IP"
                        onChangeText={(text) => {
                          // setState({ addOutletName: text });
                          // setAddOutletName(text);
                          setPrinterIP(text);
                        }}
                        defaultValue={printerIP}
                        keyboardType={'decimal-pad'}
                      /> */}

                          {/* <View
                      style={{
                        // backgroundColor: 'red',

                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        margin: 15,

                      }}> */}

                          {/* <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                              width: 0,
                              height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginBottom: 10,
                        }}
                        onPress={() => {
                          // setState({ addOutletWindow: true })
                          bindPrinter();
                        }}>
                        <View>
                          <Text style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: 16,
                              fontFamily: 'NunitoSans-Bold',
                          }}>
                            BIND PRINTER IP
                          </Text>
                        </View>
                      </TouchableOpacity> */}

                          {/* <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: 180,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                              width: 0,
                              height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginBottom: 10,
                        }}
                        onPress={() => {
                          unbindPrinter();
                        }}>
                        <View>
                          <Text style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: 16,
                              fontFamily: 'NunitoSans-Bold',
                          }}>
                            UNBIND PRINTER IP
                          </Text>
                        </View>
                      </TouchableOpacity> */}

                          {/* <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                              width: 0,
                              height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        onPress={() => {
                          testPrinter();
                        }}>
                        <View>
                          <Text style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: 16,
                              fontFamily: 'NunitoSans-Bold',
                          }}>
                            TEST PRINTER
                          </Text>
                        </View>
                      </TouchableOpacity> */}
                          {/* </View> */}
                          {/* </View> */}
                        </View>
                      </View>
                    </View>
                    <View
                      style={{
                        width: '100%',
                        alignItems: 'center',
                        marginBottom: 30,
                        zIndex: -1000,
                        // borderWidth: 1,
                      }}>
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: '#4E9F7D',
                          borderRadius: 5,
                          width: 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -4,
                        }}
                        disabled={loading}
                        onPress={() => {
                          // update();
                          // editOutlet();
                          if (merchantName !== '') {
                            updateOutletDetails();
                          }
                          else {
                            if (window.confirm('Info \nThe merchant name cannot be empty')) {
                              setLoading(false);
                            }
                          }
                        }}>
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                          }}>
                          {loading ? 'LOADING...' : 'SAVE'}
                        </Text>
                      </TouchableOpacity>
                    </View>
                    <View
                      style={{
                        height: 120,
                      }} />
                    <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginHorizontal: '2%', marginBottom: '2%' }}>
                      {/* <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', }}>Offline Ready: {isOfflineReady ? 'Yes | ' : 'No | '}</Text> */}
                      <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', }}>Offline Ready: {isOfflineReady ? 'Yes' : 'No'}</Text>

                      {/* <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', }}>Version: </Text>
                      <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Regular' }}>{version}</Text> */}
                    </View>
                  {/* </ScrollView> */}
                </View>
              ) : null}
            </View>
          {/* </ScrollView> */}
        </ScrollView>
      </View>
    </View>
    // </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: "row",
  },
  iosStyle: {
    paddingHorizontal: Platform.OS !== 'ios' ? 0 : 30,
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  headerLogo1: {
    width: '100%',
    height: '100%',
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.highlightColor,
  },
  textInput: {
    fontFamily: 'NunitoSans-Regular',
    width: 200,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: 'row',
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,

    // paddingLeft: '5%',
    paddingRight: '10%',
  },
  textInput8: {
    fontFamily: 'NunitoSans-Regular',
    width: 75,
    height: 50,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
  },
  textInput9: {
    fontFamily: 'NunitoSans-Regular',
    width: 110,
    height: Platform.OS == 'ios' ? 30 : 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput10: {
    fontFamily: 'NunitoSans-Regular',
    width: 200,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput1: {
    fontFamily: 'NunitoSans-Regular',
    width: 250,
    height: 40,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 10,
  },
  textInput2: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginRight: 30,
  },
  textInput3: {
    fontFamily: 'NunitoSans-Regular',
    width: '85%',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
    alignSelf: 'center',
    paddingHorizontal: 10,
  },
  textInput4: {
    width: '85%',
    height: 70,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
  },
  textInput5: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
  },
  textInput6: {
    fontFamily: 'NunitoSans-Regular',
    width: '80 %',
    padding: 16,
    backgroundColor: Colors.fieldtBgColor,
    marginRight: 30,
    borderRadius: 10,
    alignSelf: 'center',
  },
  textInput7: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 80,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 7,
    marginRight: 30,
  },
  button: {
    backgroundColor: Colors.primaryColor,
    width: 150,
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button1: {
    width: '15%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button2: {
    backgroundColor: Colors.primaryColor,
    width: '60%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    marginLeft: '2%',
  },
  button3: {
    backgroundColor: Colors.primaryColor,
    width: '30%',
    height: 50,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 30,
  },
  textSize: {
    fontSize: 16,
    fontFamily: 'NunitoSans-SemiBold',
  },
  viewContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
    marginVertical: 15,
    height: 40,
  },
  openHourContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    //justifyContent: 'space-between',
    //flex: 1,
    marginBottom: 15,
    //width: '100%',
    //paddingRight: 30,
  },
  addButtonView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
  },
  addButtonView1: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
  },
  addNewView: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 65,
    marginTop: 0,
    width: '83%',
    alignSelf: 'flex-end',
  },

  addNewView1: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 10,
    alignItems: 'center',
  },
  addNewView2: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 65,
    marginTop: 7,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%',
  },
  shiftView: {
    flexDirection: 'row',
    backgroundColor: Colors.fieldtBgColor2,
    borderRadius: 50,
    width: 200,
    height: 60,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,

    fontFamily: 'NunitoSans-Bold',
    fontSize: 25,
  },
  closeView: {
    flexDirection: 'row',
    borderRadius: 5,
    borderColor: Colors.primaryColor,
    borderWidth: 1,
    width: 250,
    height: 40,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
    marginBottom: '20%',
  },
  taxView: {
    flexDirection: 'row',
    borderWidth: 2,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
    marginTop: 20,
    alignSelf: 'flex-end',
  },
  sectionView: {
    flexDirection: 'row',
    borderRadius: 5,
    padding: 16,
    alignItems: 'center',
  },
  receiptView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  pinBtn: {
    backgroundColor: Colors.fieldtBgColor,
    width: 70,
    height: 70,
    marginBottom: 16,
    alignContent: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  pinNo: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  circleIcon2: {
    width: 120,
    height: 120,
    resizeMode: 'contain',
    flex: 1,
  },
  circleIcon: {
    width: 150,
    height: 150,
    resizeMode: 'contain',
    alignSelf: 'center',
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
export default SettingScreen;
