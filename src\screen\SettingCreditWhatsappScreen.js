import React, { Component, useReducer, useState, useEffect } from "react";
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    Alert,
    TouchableOpacity,
    Dimensions,
    Modal,
    useWindowDimensions,
    ActivityIndicator,
} from "react-native";
import Colors from "../constant/Colors";
import SideBar from "./SideBar";
import Feather from "react-native-vector-icons/Feather";
import AntDesign from "react-native-vector-icons/AntDesign";
import Ionicon from "react-native-vector-icons/Ionicons";
import SimpleLineIcons from "react-native-vector-icons/SimpleLineIcons";
import { TextInput, FlatList } from "react-native-gesture-handler";
import DropDownPicker from "react-native-dropdown-picker";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import API from "../constant/API";
import ApiClient from "../util/ApiClient";
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
import { Platform } from "react-native";
// import {launchC<PERSON>ra, launchImageLibrary} from 'react-native-image-picker';
// import DialogInput from 'react-native-dialog-input';
import * as User from "../util/User";
import LoginScreen from "./LoginScreen";
import AsyncStorage from "@react-native-async-storage/async-storage";
// import CheckBox from 'react-native-check-box';
import { color } from "react-native-reanimated";
import Close from "react-native-vector-icons/AntDesign";
// import NumericInput from 'react-native-numeric-input';
import Styles from "../constant/Styles";
import moment, { isDate } from "moment";
// import Barcode from 'react-native-barcode-builder';
// import Switch from 'react-native-switch-pro';
import Ionicons from "react-native-vector-icons/Ionicons";
// import {isTablet} from 'react-native-device-detection';
import { OutletStore } from "../store/outletStore";
import {
    OUTLET_SHIFT_STATUS,
    MERCHANT_PAYMENT_STATUS,
    BANK_TYPE_DROPDOWN_LIST,
    BANK_TYPE,
    BANK_CODE_DROPDOWN_LIST,
    PIC_ID_TYPE_DROPDOWN_LIST,
    PIC_ID_TYPE,
    BANK_CODE,
    PROCESSING_RATE_TYPE,
    PROCESSING_RATE_TYPE_PARSED,
    MPS_CHANNEL,
    MPS_CHANNEL_LIST,
    EXPAND_TAB_TYPE,
    WHATSAPP_CREDIT_TYPE_DROPDOWN_LIST,
    WHATSAPP_CREDIT_TYPE_TO_PRICE,
    WHATSAPP_CREDIT_TYPE_TO_CREDIT,
    WHATSAPP_CREDIT_TYPE,
} from "../constant/common";
import {
    blobToArrayBuffer,
    loadAllPreviousStates,
    sliceUnicodeStringV2WithDots,
} from "../util/common";
import { MerchantStore } from "../store/merchantStore";
import { UserStore } from "../store/userStore";
import { CommonStore } from "../store/commonStore";
// import {openCashDrawer} from '../util/printer';
import AsyncImage from "../components/asyncImage";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import { ReactComponent as Logo } from "../assets/svg/logo_2.svg";
import { useLinkTo } from "@react-navigation/native";
import { DataStore } from "../store/dataStore";
import Select from "react-select";
import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";
import Switch from "react-switch";
import Pdfh5 from "pdfh5";
import "pdfh5/css/pdfh5.css";
// import '../lib/pdf-lib/pdf-lib.min.js';
// import '../lib/forge/forge.min.js';
// import Zga from '../lib/zgapdfsigner/zgapdfsigner.min.js';

import fpx_amb from "../asset/image/banks/logo-ambank.png";
import fpx_bimb from "../asset/image/banks/logo-bank-islam.png";
import fpx_cimbclicks from "../asset/image/banks/logo-cimb-bank.png";
import fpx_hlb from "../asset/image/banks/logo-hong-leong-bank.png";
import fpx_mb2u from "../asset/image/banks/logo-maybank.png";
import fpx_pbb from "../asset/image/banks/logo-public-bank.png";
import fpx_rhb from "../asset/image/banks/logo-rhb.png";
import FPX_OCBC from "../asset/image/banks/logo-ocbc.png";
import FPX_SCB from "../asset/image/banks/logo-standard-chartered.png";
import FPX_ABB from "../asset/image/banks/logo-affinbank.png";
import FPX_BKRM from "../asset/image/banks/logo-bank-rakyat.png";
import FPX_BMMB from "../asset/image/banks/logo-bank-muamalat.png";
import FPX_KFH from "../asset/image/banks/logo-kuwait-finance.png";
import FPX_BSN from "../asset/image/banks/logo-bsn.png";
import FPX_ABMB from "../asset/image/banks/logo-alliance-bank.png";
import FPX_UOB from "../asset/image/banks/logo-uob.png";
import visa from "../asset/image/banks/logo-visa.png";
import mastercard from "../asset/image/banks/logo-mastercard.png";
import ewalletBoost from "../asset/image/offline_payment_method_boost.png";
import ewalletMaybankQRPay from "../asset/image/offline_payment_method_maybank_qrpay.png";
import ewalletGrabPay from "../asset/image/offline_payment_method_grabpay.png";
import ewalletTNG from "../asset/image/offline_payment_method_touchngo_ewallet.png";
import ewalletShopeePay from "../asset/image/offline_payment_method_shopeepay.png";
import SignaturePad from "signature_pad";
import { prefix } from "../constant/env";
import { customAlphabet } from 'nanoid';
const orderIdPrefix = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
const generateUniqueCodeForOrderId = customAlphabet(orderIdPrefix, 2);

const BANK_LOGO = {
    fpx_amb: fpx_amb,
    fpx_bimb: fpx_bimb,
    fpx_cimbclicks: fpx_cimbclicks,
    fpx_hlb: fpx_hlb,
    fpx_mb2u: fpx_mb2u,
    fpx_pbb: fpx_pbb,
    fpx_rhb: fpx_rhb,
    FPX_OCBC: FPX_OCBC,
    FPX_SCB: FPX_SCB,
    FPX_ABB: FPX_ABB,
    FPX_BKRM: FPX_BKRM,
    FPX_BMMB: FPX_BMMB,
    FPX_KFH: FPX_KFH,
    FPX_BSN: FPX_BSN,
    FPX_ABMB: FPX_ABMB,
    FPX_UOB: FPX_UOB,
    visa: visa,
    mastercard: mastercard,

    BOOST: ewalletBoost,
    "MB2U_QRPay-Push": ewalletMaybankQRPay,
    GrabPay: ewalletGrabPay,
    "TNG-EWALLET": ewalletTNG,
    ShopeePay: ewalletShopeePay,
};

var pdfh5 = null;

var signaturePad = null;

const SettingCreditWhatsappScreen = (props) => {
    const { navigation } = props;

    const linkTo = useLinkTo();

    const { height: windowHeight, width: windowWidth } = useWindowDimensions();

    const [switchMerchant, setSwitchMerchant] = useState(false);

    const [temp, setTemp] = useState("");

    //////////////////////////////////////////////////////////

    // 2022-10-27 - Payment

    // const [currMerchantPaymentStatus, setCurrMerchantPaymentStatus] = useState(MERCHANT_PAYMENT_STATUS.PENDING_INFO);

    const [inputVoucherCode, setInputVoucherCode] = useState("");
    const [inputBankType, setInputBankType] = useState({
        label: "Individual",
        value: "Individual",
    });
    const [inputBankCode, setInputBankCode] = useState({
        label: "MALAYAN BANKING BHD",
        value: "MBBEMYKL",
    });
    const [inputBankAccountName, setInputBankAccountName] = useState("");
    const [inputBankAccountNumber, setInputBankAccountNumber] = useState("");
    const [inputBankContactEmail, setInputBankContactEmail] = useState("");
    const [inputBankContactPhone, setInputBankContactPhone] = useState("");
    const [inputPicFullName, setInputPicFullName] = useState("");
    const [inputPicNRICPassport, setInputPicNRICPassport] = useState("");
    const [inputPicIdType, setInputPicIdType] = useState({
        label: "NRIC",
        value: "NRIC",
    });

    const [inputCompanyName, setInputCompanyName] = useState("");
    const [inputCompanyRobRoc, setInputCompanyRobRoc] = useState("");

    const [inputPrivacyPolicy, setInputPrivacyPolicy] = useState(false);
    const [inputTermsConditions, setInputTermsConditions] = useState(false);

    const [showMpsChannelModal, setShowMpsChannelModal] = useState(false);

    const [agreementPreviewLink, setAgreementPreviewLink] = useState("");

    //////////////////////////////////////////////////////////

    // 2022-10-12 - SMS credit

    const whatsappCreditBalance = OutletStore.useState(s => s.whatsappCreditBalance);

    // const [smsCreditBalance, setSMSCreditBalance] = useState(0);

    // const [whatsappCreditType, setWhatsappCreditType] = useState({
    //   label: WHATSAPP_CREDIT_TYPE_DROPDOWN_LIST[0].label,
    //   value: WHATSAPP_CREDIT_TYPE_DROPDOWN_LIST[0].value,
    // });

    const [whatsappCreditType, setWhatsappCreditType] = useState(WHATSAPP_CREDIT_TYPE_DROPDOWN_LIST[0].value);

    const [whatsappCreditPhone, setWhatsappCreditPhone] = useState('');

    //////////////////////////////////////////////////////////

    const isLoading = CommonStore.useState((s) => s.isLoading);

    const allOutlets = MerchantStore.useState((s) => s.allOutlets);

    const allOutletsItems = OutletStore.useState(
        (s) => s.allOutletsItems,
    );


    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);

    const expandTab = CommonStore.useState((s) => s.expandTab);

    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    const currMerchantPayment = OutletStore.useState(
        (s) => s.currMerchantPayment
    );

    const paymentDetails = CommonStore.useState((s) => s.paymentDetailsBoCreditWhatsapp);
    const isPayingCreditWhatsapp = CommonStore.useState((s) => s.isPayingCreditWhatsapp);

    //////////////////////////////////////////////////////////

    const outletPrinters = OutletStore.useState((s) => s.outletPrinters);

    const currOutletShift = OutletStore.useState((s) => s.currOutletShift);
    const currOutletShiftStatus = OutletStore.useState(
        (s) => s.currOutletShiftStatus
    );

    const merchantLogo = MerchantStore.useState((s) => s.logo);
    const merchantName = MerchantStore.useState((s) => s.name);

    const currOutlet = MerchantStore.useState((s) => s.currOutlet);

    const currOutletId = MerchantStore.useState((s) => s.currOutletId);

    const merchantId = UserStore.useState((s) => s.merchantId);

    const userId = UserStore.useState((s) => s.firebaseUid);
    const userName = UserStore.useState((s) => s.name);
    const userEmail = UserStore.useState((s) => s.email);

    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView
    );

    const setState = () => { };

    useEffect(async () => {
        if (linkTo) {
            // await loadAllPreviousStates(null);
        }
    }, [linkTo]);

    useEffect(() => {
        if (paymentDetails !== null && paymentDetails !== undefined) {
            console.log("paymentDetails");
            console.log(paymentDetails);

            const paymentDetailsResult = paymentDetails;

            if (!isLoading) {
                console.log("!isLoading");

                CommonStore.update(
                    (s) => {
                        s.paymentDetailsBoCreditWhatsapp = null;
                        s.isPayingCreditWhatsapp = false;

                        s.isLoading = false;
                    },
                    () => {
                        if (paymentDetailsResult.statusCode === "00") {
                            // submitPendingPaymentMP(paymentDetailsResult);

                            alert("Topup success, your whatsapp credit will be updated soon.");
                        } else {
                            // error

                            CommonStore.update((s) => {
                                s.alertObj = {
                                    title: "Error",
                                    message:
                                        "Payment failed. Please contact the support team for assistant.",
                                };
                            });

                            CommonStore.update((s) => {
                                s.isLoading = false;
                            });
                        }
                    }
                );
            } else {
                console.log("isLoading");

                CommonStore.update(
                    (s) => {
                        s.paymentDetailsBoCreditWhatsapp = null;
                        s.isPayingCreditWhatsapp = false;

                        s.isLoading = false;
                    },
                    () => {
                        if (paymentDetailsResult.statusCode === "00") {
                            // submitPendingPaymentMP(paymentDetailsResult);

                            alert("Topup success, your whatsapp credit will be updated soon.");
                        } else {
                            // error

                            CommonStore.update((s) => {
                                s.alertObj = {
                                    title: "Error",
                                    message:
                                        "Payment failed. Please contact the support team for assistant.",
                                };
                            });
                        }
                    }
                );
            }
        }
    }, [
        paymentDetails,
        // isLoading,
    ]);

    // useEffect(() => {
    //   if (currMerchantPayment && currMerchantPayment.agreementLink) {
    //     setAgreementPreviewLink(currMerchantPayment.agreementLink);
    //   }
    // }, [currMerchantPayment]);

    useEffect(() => {
        if (currMerchantPayment && currMerchantPayment.agreementLink) {
            pdfh5 = new Pdfh5("#pdf_preview", {
                pdfurl: agreementPreviewLink
                    ? agreementPreviewLink
                    : currMerchantPayment.agreementLink,
            });

            //监听完成事件
            pdfh5.on("complete", function (status, msg, time) {
                console.log(
                    "状态：" +
                    status +
                    "，信息：" +
                    msg +
                    "，耗时：" +
                    time +
                    "毫秒，总页数：" +
                    this.totalNum
                );
            });

            // var pdfh5 = new Pdfh5('#demo', {
            //     pdfurl: "https://koodooprod.s3.ap-southeast-1.amazonaws.com/pdf/2022_06_briefing.pdf",
            //   });

            ///////////////////////////////////////////////////////////////

            signaturePad = new SignaturePad(
                document.getElementById("signature_pad"),
                {
                    // backgroundColor: 'rgba(255, 255, 255, 100)',
                    backgroundColor: Colors.fieldtBgColor,
                    penColor: "rgb(0, 0, 0)",
                }
            );
        }
    }, [currMerchantPayment]);

    navigation.dangerouslyGetParent().setOptions({
        tabBarVisible: false,
    });

    // const currOutletShiftStatus = OutletStore.useState(
    //   (s) => s.currOutletShiftStatus,
    // );
    // const [outletDropdownList, setOutletDropdownList] = useState([]);
    // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

    const [openTC, setOpenTC] = useState(false);

    // var outletNames = [];

    // for (var i = 0; i < allOutlets.length; i++) {
    //   for (var j = 0; j < selectedOutletList.length; j++) {
    //     if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
    //       outletNames.push(allOutlets[i].name);
    //       break;
    //     }
    //   }
    // }

    // useEffect(() => {
    //   setOutletDropdownList(
    //     allOutlets.map((item) => {
    //       return { label: item.name, value: item.uniqueId };
    //     })
    //   );
    // }, [allOutlets]);

    var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
        label: sliceUnicodeStringV2WithDots(outlet.name, 20),
        value: outlet.uniqueId,
    }));

    // useEffect(() => {
    //   CommonStore.update((s) => {
    //     s.outletSelectDropdownView = () => {
    //       return (
    //         <View
    //           style={{
    //             flexDirection: "row",
    //             alignItems: "center",
    //             borderRadius: 8,
    //             width: 200,
    //             backgroundColor: "white",
    //           }}
    //         >
    //           {currOutletId.length > 0 &&
    //             allOutlets.find((item) => item.uniqueId === currOutletId) ? (
    //             <MultiSelect
    //               clearable={false}
    //               singleSelect={true}
    //               defaultValue={currOutletId}
    //               placeholder={"Choose Outlet"}
    //               onChange={(value) => {
    //                 if (value) { // if choose the same option again, value = ''
    //                   MerchantStore.update((s) => {
    //                     s.currOutletId = value;
    //                     s.currOutlet =
    //                       allOutlets.find(
    //                         (outlet) => outlet.uniqueId === value
    //                       ) || {};
    //                   });
    //                 }

    //                 CommonStore.update((s) => {
    //                   s.shiftClosedModal = false;
    //                 });
    //               }}
    //               options={targetOutletDropdownListTemp}
    //               className="msl-varsHEADER"
    //             />
    //           ) : (
    //             <ActivityIndicator size={"small"} color={Colors.whiteColor} />
    //           )}

    //           {/* <Select

    //             placeholder={"Choose Outlet"}
    //             onChange={(items) => {
    //               setSelectedOutletList(items);
    //             }}
    //             options={outletDropdownList}
    //             isMulti
    //           /> */}
    //         </View>
    //       );
    //     };
    //   });
    // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

    // navigation.setOptions({
    //     headerLeft: () => (
    //         <View
    //             style={[
    //                 styles.headerLeftStyle,
    //                 {
    //                     width: windowWidth * 0.17,
    //                 },
    //             ]}
    //         >
    //             <img src={headerLogo} width={124} height={26} />
    //             {/* <Image
    //     style={{
    //       width: 124,
    //       height: 26,
    //     }}
    //     resizeMode="contain"
    //     source={require('../assets/image/logo.png')}
    //   /> */}
    //         </View>
    //     ),
    //     headerTitle: () => (
    //         <View
    //             style={[
    //                 {
    //                     justifyContent: "center",
    //                     alignItems: "center",
    //                     // marginRight: Platform.OS === 'ios' ? "27%" : 0,
    //                     // bottom: switchMerchant ? '2%' : 0,
    //                     //width:  "55%",
    //                 },
    //                 Dimensions.get("screen").width <= 768
    //                     ? { right: Dimensions.get("screen").width * 0.12 }
    //                     : {},
    //             ]}
    //         >
    //             <Text
    //                 style={{
    //                     fontSize: 24,
    //                     // lineHeight: 25,
    //                     textAlign: "center",
    //                     fontFamily: "NunitoSans-Bold",
    //                     color: Colors.whiteColor,
    //                     opacity: 1,
    //                 }}
    //             >
    //                 Orders Channel Report
    //             </Text>
    //         </View>
    //     ),
    //     headerRight: () => (
    //         <View
    //             style={{
    //                 flexDirection: "row",
    //                 alignItems: "center",
    //                 justifyContent: "space-between",
    //             }}
    //         >
    //             {/* {console.log('edward test')} */}
    //             {/* {console.log(outletSelectDropdownView)} */}
    //             {outletSelectDropdownView && outletSelectDropdownView()}
    //             <View
    //                 style={{
    //                     backgroundColor: "white",
    //                     width: 0.5,
    //                     height: Dimensions.get("screen").height * 0.025,
    //                     opacity: 0.8,
    //                     marginHorizontal: 15,
    //                     bottom: -1,
    //                     // borderWidth: 1
    //                 }}
    //             ></View>
    //             <TouchableOpacity
    //                 onPress={() => {
    //                     if (global.currUserRole === 'admin') {
    //                         navigation.navigate("General Settings - KooDoo BackOffice")
    //                     }
    //                 }}
    //                 style={{ flexDirection: "row", alignItems: "center" }}
    //             >
    //                 <Text
    //                     style={{
    //                         fontFamily: "NunitoSans-SemiBold",
    //                         fontSize: 16,
    //                         color: Colors.secondaryColor,
    //                         marginRight: 15,
    //                     }}
    //                 >
    //                     {userName}
    //                 </Text>
    //                 <View
    //                     style={{
    //                         //backgroundColor: 'red',
    //                         marginRight: 30,
    //                         width: windowHeight * 0.05,
    //                         height: windowHeight * 0.05,
    //                         borderRadius: windowHeight * 0.05 * 0.5,
    //                         alignItems: "center",
    //                         justifyContent: "center",
    //                         backgroundColor: "white",
    //                     }}
    //                 >
    //                     <img
    //                         src={personicon}
    //                         width={windowHeight * 0.035}
    //                         height={windowHeight * 0.035}
    //                     />
    //                     {/* <Image
    //         style={{
    //           width: windowHeight * 0.05,
    //         height: windowHeight * 0.05,
    //           alignSelf: 'center',
    //         }}
    //         source={require('../assets/image/profile-pic.jpg')}
    //       /> */}
    //                 </View>
    //             </TouchableOpacity>
    //         </View>
    //     ),
    // });

    navigation.setOptions({
        headerLeft: () => (
            <View
                style={[
                    styles.headerLeftStyle,
                    {
                        width: windowWidth * 0.17,
                    },
                ]}
            >
                <img src={headerLogo} width={124} height={26} />
                {/* <Image
        style={{
          width: 124,
          height: 26,
        }}
        resizeMode="contain"
        source={require('../assets/image/logo.png')}
      /> */}
            </View>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        justifyContent: "center",
                        alignItems: "center",
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        //width:  "55%",
                    },
                    Dimensions.get("screen").width <= 768
                        ? { right: Dimensions.get("screen").width * 0.12 }
                        : {},
                ]}
            >
                <Text
                    style={{
                        fontSize: 24,
                        // lineHeight: 25,
                        textAlign: "center",
                        fontFamily: "NunitoSans-Bold",
                        color: Colors.whiteColor,
                        opacity: 1,
                    }}
                >
                    Whatsapp Credit
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                {/* {console.log('edward test')} */}
                {/* {console.log(outletSelectDropdownView)} */}
                {outletSelectDropdownView && outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: "white",
                        width: 0.5,
                        height: Dimensions.get("screen").height * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                        // borderWidth: 1
                    }}
                ></View>
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate("General Settings - KooDoo BackOffice")
                        }
                    }}
                    style={{ flexDirection: "row", alignItems: "center" }}
                >
                    <Text
                        style={{
                            fontFamily: "NunitoSans-SemiBold",
                            fontSize: 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            //backgroundColor: 'red',
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: "white",
                        }}
                    >
                        <img
                            src={personicon}
                            width={windowHeight * 0.035}
                            height={windowHeight * 0.035}
                        />
                        {/* <Image
            style={{
              width: windowHeight * 0.05,
            height: windowHeight * 0.05,
              alignSelf: 'center',
            }}
            source={require('../assets/image/profile-pic.jpg')}
          /> */}
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    const onPressMpsChannel = (channel) => {
        CommonStore.update((s) => {
            s.isLoading = true;
        });

        startMolPay(channel);

        // if (cartItems.length > 0) {
        //   CommonStore.update((s) => {
        //     s.isLoading = true;
        //   });

        //   startMolPay(channel);
        // }
        // else {
        //   window.confirm(
        //     "Info",
        //     "Empty cart items to proceed."
        //   );
        // }
    };

    const renderMpsChannelList = ({ item }) => {
        return (
            <TouchableOpacity
                style={{
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    width: "90%",
                    height: 100,
                    alignSelf: "center",

                    // marginHorizontal: 24,
                    marginVertical: 10,
                    paddingVertical: 12,

                    backgroundColor: Colors.primaryColor,
                    shadowColor: "#000",
                    shadowOffset: {
                        width: 0,
                        height: 1,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 2.22,
                    elevation: 3,

                    justifyContent: "center",
                    borderRadius: 8,

                    paddingBottom:
                        item.value !== MPS_CHANNEL.credit &&
                            item.value !== MPS_CHANNEL.creditAN &&
                            item.value !== MPS_CHANNEL.credit10
                            ? 12
                            : "8%",
                    // paddingTop: item.value !== MPS_CHANNEL.credit ? 12 : '5%',
                }}
                onPress={() => onPressMpsChannel(item)}
            >
                {item.value !== MPS_CHANNEL.credit &&
                    item.value !== MPS_CHANNEL.creditAN &&
                    item.value !== MPS_CHANNEL.credit10 ? (
                    <Image
                        style={{
                            // width: 64,
                            // height: 64,
                            width: "50%",
                            height: "50%",
                            marginBottom: "2%",
                        }}
                        source={BANK_LOGO[item.value]}
                    ></Image>
                ) : (
                    <View
                        style={{
                            flexDirection: "row",
                            justifyContent: "space-around",
                            alignItems: "center",

                            width: "84%",
                            height: "80%",

                            // backgroundColor: 'green',

                            // marginBottom: '-6%',
                            // marginTop: '-2%',
                        }}
                    >
                        <Image
                            style={{
                                // width: 64,
                                // height: 64,
                                width: "40%",
                                height: "60%",
                                // marginBottom: '2%',
                            }}
                            source={BANK_LOGO["visa"]}
                        ></Image>
                        <Image
                            style={{
                                // width: 64,
                                // height: 64,
                                width: "40%",
                                height: "60%",
                                // marginBottom: '2%',
                            }}
                            source={BANK_LOGO["mastercard"]}
                        ></Image>
                    </View>
                )}

                <Text
                    style={{
                        fontSize: 14,
                        textAlign: "center",
                        color: "white",
                        width: "94%",
                        alignSelf: "center",
                        // backgroundColor: 'red',

                        // paddingBottom: item.value !== MPS_CHANNEL.credit ? '0%' : '8%',
                    }}
                // numberOfLines={1}
                >
                    {item.label}
                </Text>
            </TouchableOpacity>
        );
    };

    const startMolPay = (channel) => {
        // need get readable order id from api first

        ////////////////////////////////////////////////////

        // let whatsappCreditTypeParsed = whatsappCreditType.value;
        let whatsappCreditTypeParsed = whatsappCreditType;

        var whatsappCreditPhoneParsed = whatsappCreditPhone
            .replace("-", "")
            .replace(" ", "");
        if (!whatsappCreditPhoneParsed.startsWith("6")) {
            whatsappCreditPhoneParsed = `6${whatsappCreditPhoneParsed}`;
        }

        ////////////////////////////////////////////////////

        let orderIdCreatedTemp = uuidv4();

        var body = {
            outletId: currOutletId,
            // orderType: orderType,
        };

        ApiClient.POST(API.getNextOrderIdWhatsappCredit, body, {
            timeout: 100000,
        }).then(async (result) => {
            if (result && result.status === 'success' && result.orderId) {
                // var orderIdServerTemp = currMerchantPayment.invoiceCount
                //   .toString()
                //   .padStart(3, "0");

                var orderIdServerTemp = result.orderId;

                // setOrderIdServer(orderIdServerTemp);

                var amountToPay = +(
                    WHATSAPP_CREDIT_TYPE_TO_PRICE[whatsappCreditTypeParsed]
                ).toFixed(2);

                // Testing
                // amountToPay = amountToPay > 5 ? 5 : amountToPay;
                // amountToPay = 1.01;

                // const subdomain = await AsyncStorage.getItem("latestSubdomain");
                // const metaInfo = ` | ${subdomain ? `${subdomain}.` : ''}mykoodoo.com`;
                const metaInfo = `|bo-wa-credit`;

                const razerOrderId = `#${orderIdServerTemp}-${generateUniqueCodeForOrderId()}${metaInfo}`;

                CommonStore.update(s => {
                    s.isPayingCreditWhatsapp = true;
                });

                var body = {
                    amount:
                        channel.value === MPS_CHANNEL.creditAN
                            ? amountToPay.toFixed(2)
                            : +parseFloat(amountToPay).toFixed(2),
                    // merchantId: 'mykoodoo_Dev',
                    merchantId: "mykoodoo",
                    orderId: razerOrderId,

                    bodyMerchantOrderWhatsappCreditTemp: {
                        uniqueId: orderIdCreatedTemp,

                        outletId: currOutlet.uniqueId,
                        merchantId: currOutlet.merchantId,
                        outletName: currOutlet.name,
                        merchantName: merchantName,
                        outletCover: currOutlet.cover,
                        merchantLogo: merchantLogo,

                        topupAmount: amountToPay,
                        topupCredit: WHATSAPP_CREDIT_TYPE_TO_CREDIT[whatsappCreditTypeParsed],
                        topupCreditType: whatsappCreditTypeParsed,

                        topupPhoneOTP: whatsappCreditPhoneParsed,

                        currCreditBalance: whatsappCreditBalance,

                        createdAt: moment().valueOf(),
                        updatedAt: moment().valueOf(),
                        deletedAt: null,

                        razerOrderId: razerOrderId,
                        razerAmount: +parseFloat(amountToPay).toFixed(2),
                        razerChannel: channel.value,
                        razerDateTime: Date.now(),
                    },
                };

                console.log("before call getRazerVCode");

                ApiClient.POST(API.getRazerVCode, body).then(async (result) => {
                    console.log("after call getRazerVCode");
                    console.log(result);

                    if (result && result.status === "success") {
                        const vcode = result.vcode;

                        // console.log("subdomain before");
                        // console.log(subdomain);

                        var parsedPhone = whatsappCreditPhoneParsed;
                        if (parsedPhone.length > 0 && !parsedPhone.startsWith("6")) {
                            parsedPhone = `6${parsedPhone}`;
                        }

                        // var paymentDetails = {
                        //   // Optional, REQUIRED when use online Sandbox environment and account credentials.
                        //   'mp_dev_mode': true,

                        //   // Mandatory String. Values obtained from Razer Merchant Services.
                        //   'mp_username': 'api_SB_mykoodoo',
                        //   'mp_password': 'WaaU1IeZ*&(%%',
                        //   'mp_merchant_ID': 'SB_mykoodoo',
                        //   'mp_app_name': 'mykoodoo',
                        //   'mp_verification_key': '0bd0efac623106b4c19bc28b8e9a0d8d',
                        //   // 'mp_username': 'api_mykoodoo_Dev',
                        //   // 'mp_password': 'E81E65f5&^',
                        //   // 'mp_merchant_ID': 'mykoodoo_Dev',
                        //   // 'mp_app_name': 'mykoodoo',
                        //   // 'mp_verification_key': '0bd0efac623106b4c19bc28b8e9a0d8d',

                        //   // Mandatory String. Payment values.
                        //   'mp_amount': amountToPay, // Minimum 1.01
                        //   'mp_order_ID': `#${orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${orderIdServerTemp}${metaInfo}`,
                        //   'mp_currency': 'MYR',
                        //   'mp_country': 'MY',

                        //   // Optional String.
                        //   'mp_channel': 'multi', // Use 'multi' for all available channels option. For individual channel seletion, please refer to https://github.com/RazerMS/rms-mobile-xdk-examples/blob/master/channel_list.tsv.
                        //   'mp_bill_description': selectedOutlet.name + ' purchase RM' + parseFloat(amountToPay).toFixed(2),
                        //   'mp_bill_name': userName,
                        //   'mp_bill_email': userEmail,
                        //   'mp_bill_mobile': userNumber,

                        //   'mp_sandbox_mode': true,
                        //   'mp_tcctype': 'AUTH',
                        // }

                        var paymentDetails = {
                            // Optional, REQUIRED when use online Sandbox environment and account credentials.
                            // sandbox
                            // 'mp_dev_mode': true,
                            // uat
                            // 'mp_dev_mode': false,

                            // Mandatory String. Values obtained from Razer Merchant Services.
                            // 'mp_username': 'api_SB_mykoodoo',
                            // 'mp_password': 'WaaU1IeZ*&(%%',
                            // 'mp_merchant_ID': 'SB_mykoodoo',
                            // 'mp_app_name': 'mykoodoo',
                            // 'mp_verification_key': '0bd0efac623106b4c19bc28b8e9a0d8d',

                            // sandbox
                            // mpsmerchantid: 'SB_mykoodoo',
                            // uat
                            // mpsmerchantid: 'mykoodoo_Dev',
                            mpsmerchantid: "mykoodoo",
                            mpsvcode: vcode,

                            // Mandatory String. Payment values.
                            // 'mp_amount': amountToPay, // Minimum 1.01
                            // 'mp_order_ID': `#${orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${orderIdServerTemp}`,
                            // 'mp_currency': 'MYR',
                            // 'mp_country': 'MY',
                            mpsamount:
                                channel.value === MPS_CHANNEL.creditAN
                                    ? amountToPay.toFixed(2)
                                    : +parseFloat(amountToPay).toFixed(2),
                            mpsorderid: razerOrderId,
                            mpscurrency: "MYR",
                            mpscountry: "MY",

                            // Optional String.
                            // 'mp_channel': 'multi', // Use 'multi' for all available channels option. For individual channel seletion, please refer to https://github.com/RazerMS/rms-mobile-xdk-examples/blob/master/channel_list.tsv.
                            // 'mp_bill_description': selectedOutlet.name + ' purchase RM' + parseFloat(amountToPay).toFixed(2),
                            // mpsbill_name: userName ? userName : userInfoName,
                            // mpsbill_email: userEmail ? userEmail : '',
                            mpsbill_mobile: parsedPhone,
                            // mpsbill_desc:
                            //   `KooDoo Premium Partner Package ` +
                            //   parseFloat(amountToPay).toFixed(2),
                            mpsbill_desc: WHATSAPP_CREDIT_TYPE[whatsappCreditTypeParsed],
                            mpsbill_name: merchantName,

                            // 'mp_sandbox_mode': true,

                            ...((channel.value === MPS_CHANNEL.credit ||
                                channel.value === MPS_CHANNEL.creditAN ||
                                channel.value === MPS_CHANNEL.credit10) && {
                                // 'mp_tcctype': 'AUTH',
                                mpstcctype: "SALS",
                            }),

                            // mpschannel: "fpx",
                            mpschannel: channel.value,

                            //////////////////////////////////////

                            // custom info
                            // mpslangcode: 'en',
                            // mpsbill_email: '',
                            // mpstokenstatus: '1',

                            //////////////////////////////////////
                        };

                        console.log("paymentDetails");
                        console.log(paymentDetails);

                        // molpay.startMolpay(paymentDetails, (data) => {
                        // $('#myPay').MOLPaySeamless(paymentDetails, (data) => {            

                        window.initMolpay(paymentDetails, (data) => { });

                        window.startMolpay(
                            paymentDetails,
                            (data) => {
                                // callback after payment success

                                // setTimeout(() => {
                                //   CommonStore.update(s => {
                                //     s.molpayResult = data;
                                //   });
                                // }, 1000);

                                console.log("razer result v2");
                                console.log(data);

                                // const result = JSON.parse(data);
                                const result = data;
                                console.log(result);
                            },
                            (ex) => {
                                console.log(ex);

                                CommonStore.update((s) => {
                                    s.isLoading = false; // 2022-10-18 - Set to false, if online payment first
                                });
                            }
                        );

                        CommonStore.update((s) => {
                            s.isLoading = false; // 2022-10-18 - Set to false, if online payment first
                        });
                    } else {
                        window.confirm("Error\nSorry, an error occurred, please try again.");
                    }
                });
            }
        });
    };

    const submitPendingInfoMP = async (param) => {
        const inputBankTypeParsed = inputBankType.value;
        const inputBankCodeParsed = inputBankCode.value;
        const inputPicIdTypeParsed = inputPicIdType.value;

        var inputBankContactPhoneParsed = inputBankContactPhone
            .replace("-", "")
            .replace(" ", "");
        if (!inputBankContactPhoneParsed.startsWith("6")) {
            inputBankContactPhoneParsed = `6${inputBankContactPhoneParsed}`;
        }

        if (inputBankTypeParsed === BANK_TYPE.BUSINESS) {
            if (!inputCompanyName) {
                alert("Invalid or empty company name.");
                return;
            }

            if (!inputCompanyRobRoc) {
                alert("Invalid or empty company rob/roc.");
                return;
            }
        }

        if (!inputBankAccountName) {
            alert("Invalid or empty bank account name.");
            return;
        }

        if (!inputBankAccountNumber) {
            alert("Invalid or empty bank account number.");
            return;
        }

        if (!inputBankContactEmail) {
            alert("Invalid or empty bank contact email.");
            return;
        }

        if (!inputBankContactPhone && !/^\d+$/.test(inputBankContactPhone)) {
            alert("Invalid or empty bank contact phone.");
            return;
        }

        if (!inputPicFullName) {
            alert("Invalid or empty pic full name.");
            return;
        }

        if (!inputPicNRICPassport) {
            alert("Invalid or empty pic nric/passport.");
            return;
        }

        if (!inputPrivacyPolicy) {
            alert("Please accept the privacy policy in order to proceed.");
            return;
        }

        if (!inputTermsConditions) {
            alert("Please accept the terms & conditions in order to proceed.");
            return;
        }

        var body = {
            clientEmail: userEmail,

            bankType: inputBankTypeParsed,
            bankCode: inputBankCodeParsed,
            bankAccountName: inputBankAccountName,
            bankAccountNumber: inputBankAccountNumber,
            contactEmail: inputBankContactEmail,
            contactMobile: inputBankContactPhoneParsed,

            picFullName: inputPicFullName,
            picNRICPassport: inputPicNRICPassport,
            picIdType: inputPicIdTypeParsed,
            companyName: inputCompanyName,

            country: "MY",

            outletId: currMerchantPayment.outletId,

            companyRobRoc: inputCompanyRobRoc,

            merchantPaymentId: currMerchantPayment.uniqueId,

            acceptedPrivacyPolicy: inputPrivacyPolicy,
            acceptedTermsConditions: inputTermsConditions,

            voucherCode: inputVoucherCode,
        };

        console.log("body", body);

        CommonStore.update((s) => {
            s.isLoading = true;
        });

        ApiClient.POST(API.submitPendingInfoMP, body, {
            timeout: 100000,
        }).then((result) => {
            if (result && result.status === "success") {
                if (
                    window.confirm("Success. Will redirect to the next page soon.") ==
                    true
                ) {
                    CommonStore.update((s) => {
                        s.isLoading = false;
                    });
                }
            } else {
                var errorMsg = "";

                if (result && result.razerResult && result.razerResult.error_desc) {
                    errorMsg = result.razerResult.error_desc;
                }

                if (result && result.razerResult && result.razerResult.ErrorDesc) {
                    errorMsg = result.razerResult.ErrorDesc;
                }

                if (
                    window.confirm(
                        `Error. Failed to submit the pending info, please try again.${errorMsg ? "\n\n" + errorMsg : ""
                        }`
                    ) == true
                ) {
                    CommonStore.update((s) => {
                        s.isLoading = false;
                    });
                }
            }
        });
    };

    const submitPendingPaymentMP = async (paymentDetails) => {
        // means user already paid, continue to proceed

        if (paymentDetails) {
            var body = {
                paymentDetails: paymentDetails,

                merchantPaymentId: currMerchantPayment.uniqueId,
            };

            console.log("body", body);

            CommonStore.update((s) => {
                s.isLoading = true;
            });

            ApiClient.POST(API.submitPendingPaymentMP, body, false).then((result) => {
                if (result && result.status === "success") {
                    if (
                        window.confirm("Success. Will redirect to the next page soon.") ==
                        true
                    ) {
                        CommonStore.update((s) => {
                            s.isLoading = false;
                        });
                    }
                } else {
                    if (
                        window.confirm(
                            "Error. Failed to submit the information, please contact the support team for assistant."
                        ) == true
                    ) {
                        CommonStore.update((s) => {
                            s.isLoading = false;
                        });
                    }
                }
            });
        }
    };

    const signAgreementOffline = async () => {
        CommonStore.update((s) => {
            s.isLoading = true;
        });

        /////////////////////////////////////////////

        // get p12 cert

        await fetch(`${prefix}/keys/mykoodoo_merchant.pfx`)
            .then(async (response) => {
                var p12cert = await response.arrayBuffer();

                const binaryData = signaturePad.toDataURL();

                var sopt = {
                    p12cert: p12cert,
                    pwd: "mykoodoo_merchant",

                    drawinf: {
                        area: {
                            x: 25, // left
                            y: 150, // top
                            w: 60, // width
                            h: 60, // height
                        },
                        imgData: binaryData,
                        imgType: "png",
                    },
                };

                console.log(window.Zga);

                await fetch(currMerchantPayment.agreementLink)
                    .then((r) => r.blob())
                    .then(async (blobData) => {
                        const arrayBuffer = await blobToArrayBuffer(blobData);

                        // console.log(URL.createObjectURL(blobData));

                        var signer = new window.Zga.PdfSigner(sopt);
                        var u8arr = await signer.sign(arrayBuffer);

                        var pdfPreviewBlob = new Blob([u8arr], { type: "application/pdf" });

                        var pdfPreviewBlobUrl = URL.createObjectURL(pdfPreviewBlob);

                        setAgreementPreviewLink(pdfPreviewBlobUrl);
                    })
                    .catch(console.error);
            })
            .then(function (csv) {
                // convert your csv to an array
            });

        /////////////////////////////////////////////
    };

    return (
        // <View style={styles.container}>
        //   <View style={styles.sidebar}>
        // Whole page view
        <View
            style={[styles.container, { height: windowHeight, width: windowWidth }]}
        >
            {/* Sidebar view */}
            <View
                style={{
                    // borderWidth: 1,
                    // width: Dimensions.get("screen").width * 0.9,
                    // height: Dimensions.get("screen").height * 0.9,
                    flex: 0.8,
                }}
            >
                <SideBar navigation={navigation} selectedTab={8} />
            </View>

            <button
                id="myPay"
                style={{
                    display: "none",
                }}
                type="button"
                data-toggle="molpayseamless"
            >
                Pay by RazerPay
            </button>

            <View style={{ height: windowHeight, flex: 9 }}>
                {/* <ScrollView
        showsVerticalS
        crollIndicator={false}
        scrollEnabled={switchMerchant}
        style={{ flex: 9 }}
        contentContainerStyle={{
          paddingBottom: windowHeight * 0.025,
          backgroundColor: Colors.highlightColor,
          // backgroundColor: 'black',
          height: windowHeight,
          // borderWidth:10,
        }}> */}
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{}}
                    contentContainerStyle={{
                        paddingBottom: windowHeight * 0.1,
                        backgroundColor: Colors.highlightColor,
                    }}
                >
                    <ScrollView horizontal={true} showsHorizontalScrollIndicator={true}>
                        <View
                            style={[
                                styles.content,
                                {
                                    height: windowHeight,
                                    // width: windowWidth * 0.91,
                                    width: Dimensions.get("screen").width * 0.91,
                                    // backgroundColor: "black",
                                },
                            ]}
                        >
                            <View
                                style={[
                                    {
                                        height: "100%",
                                        width: "100%",
                                        backgroundColor: Colors.whiteColor,
                                        // borderWidth1,
                                        // flex: 1,
                                        flexShrink: 0,
                                        minHeight: "85%",
                                        shadowColor: "#000",
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 3,
                                        borderRadius: 5,
                                        justifyContent: "center",
                                        alignItems: "center",
                                        paddingBottom: 25,
                                    },
                                ]}
                            >
                                <ScrollView
                                    style={{
                                        width: "100%",
                                        height: "100%",

                                        padding: 50,
                                    }}
                                >
                                    <View
                                        style={{
                                            // flex: 1,
                                            alignSelf: "center",
                                            justifyContent: "center",
                                            marginBottom: 10,

                                            width: "40%",
                                        }}
                                    >
                                        <Text
                                            style={{
                                                fontFamily: "NunitoSans-Bold",
                                                fontSize: 18,

                                                textAlign: "center",

                                                marginBottom: 25,
                                            }}
                                        >
                                            Whatsapp Credit Overview
                                        </Text>

                                        <View
                                            style={{ flexDirection: "row", marginVertical: 15 }}
                                        >
                                            <View
                                                style={{ width: "25%", justifyContent: "center" }}
                                            >
                                                <Text
                                                    style={{
                                                        fontFamily: "NunitoSans-Bold",
                                                        fontSize: switchMerchant ? 10 : 14,
                                                    }}
                                                >
                                                    Credit Balance
                                                </Text>
                                            </View>
                                            <View style={{ width: "75%" }}>
                                                <Text
                                                    style={{
                                                        fontFamily: "NunitoSans-Bold",
                                                        fontSize: switchMerchant ? 10 : 14,
                                                    }}
                                                >
                                                    {`RM ${whatsappCreditBalance.toFixed(2)} (${(whatsappCreditBalance * 10).toFixed(0)} WhatsApp credit left)`}
                                                </Text>
                                            </View>
                                        </View>

                                        <View
                                            style={{
                                                flexDirection: "row",
                                                marginVertical: 15,
                                                zIndex: -100,
                                            }}
                                        >
                                            <View
                                                style={{ width: "25%", justifyContent: "center" }}
                                            >
                                                <Text
                                                    style={{
                                                        fontFamily: "NunitoSans-Bold",
                                                        fontSize: switchMerchant ? 10 : 14,
                                                    }}
                                                >
                                                    Phone (OTP)
                                                </Text>
                                            </View>
                                            <View style={{ width: "75%" }}>
                                                <TextInput
                                                    underlineColorAndroid={Colors.fieldtBgColor}
                                                    style={{
                                                        padding: 10,
                                                        height: 40,
                                                        width: "100%",
                                                        fontFamily: "NunitoSans-Regular",
                                                        fontSize: switchMerchant ? 10 : 14,
                                                        backgroundColor: Colors.fieldtBgColor,
                                                    }}
                                                    placeholderStyle={{
                                                        fontFamily: "NunitoSans-Regular",
                                                        fontSize: switchMerchant ? 10 : 14,
                                                    }}
                                                    placeholder={"60170225493"}
                                                    onFocus={() => {
                                                        setTemp(whatsappCreditPhone);
                                                        setWhatsappCreditPhone("");
                                                    }}
                                                    onBlur={() => {
                                                        if (whatsappCreditPhone == "") {
                                                            setWhatsappCreditPhone(temp);
                                                        }
                                                    }}
                                                    onChangeText={(text) => {
                                                        setWhatsappCreditPhone(text);
                                                    }}
                                                    value={whatsappCreditPhone}
                                                    keyboardType="decimal-pad"
                                                // ref={myTextInput}
                                                />
                                            </View>
                                        </View>

                                        <View
                                            style={{ flexDirection: "row", marginVertical: 15 }}
                                        >
                                            <View
                                                style={{ width: "25%", justifyContent: "center" }}
                                            >
                                                <Text
                                                    style={{
                                                        fontFamily: "NunitoSans-Bold",
                                                        fontSize: switchMerchant ? 10 : 14,
                                                    }}
                                                >
                                                    Topup Credit
                                                </Text>
                                            </View>
                                            <View style={{ width: "75%" }}>
                                                <DropDownPicker
                                                    style={{
                                                        backgroundColor: Colors.fieldtBgColor,
                                                        // width: 210,
                                                        height: 40,
                                                        borderRadius: 10,
                                                        borderWidth: 1,
                                                        borderColor: "#E5E5E5",
                                                        flexDirection: "row",
                                                    }}
                                                    dropDownContainerStyle={{
                                                        // width: 210,
                                                        backgroundColor: Colors.fieldtBgColor,
                                                        borderColor: "#E5E5E5",
                                                    }}
                                                    labelStyle={{
                                                        marginLeft: 5,
                                                        flexDirection: "row",
                                                    }}
                                                    textStyle={{
                                                        fontSize: 14,
                                                        fontFamily: 'NunitoSans-Regular',

                                                        marginLeft: 5,
                                                        paddingVertical: 10,
                                                        flexDirection: "row",
                                                    }}
                                                    selectedItemContainerStyle={{
                                                        flexDirection: "row",
                                                    }}

                                                    showArrowIcon={true}
                                                    ArrowDownIconComponent={({ style }) => (
                                                        <Ionicon
                                                            size={25}
                                                            color={Colors.fieldtTxtColor}
                                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                            name="chevron-down-outline"
                                                        />
                                                    )}
                                                    ArrowUpIconComponent={({ style }) => (
                                                        <Ionicon
                                                            size={25}
                                                            color={Colors.fieldtTxtColor}
                                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                            name="chevron-up-outline"
                                                        />
                                                    )}

                                                    showTickIcon={true}
                                                    TickIconComponent={({ press }) => (
                                                        <Ionicon
                                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                            color={
                                                                press ? Colors.fieldtBgColor : Colors.primaryColor
                                                            }
                                                            name={'md-checkbox'}
                                                            size={25}
                                                        />
                                                    )}
                                                    placeholderStyle={{
                                                        color: Colors.fieldtTxtColor,
                                                        // marginTop: 15,
                                                    }}
                                                    dropDownDirection="BOTTOM"
                                                    onSelectItem={(items) => {
                                                        setWhatsappCreditType(items.value);
                                                    }}
                                                    value={whatsappCreditType}
                                                    items={WHATSAPP_CREDIT_TYPE_DROPDOWN_LIST}
                                                    // multiple={true}
                                                    // multipleText={`${scOrderTypes.length} type(s) selected`}
                                                    open={openTC}
                                                    setOpen={setOpenTC}
                                                />
                                            </View>
                                        </View>

                                        <View
                                            style={{
                                                width: "100%",
                                                alignItems: "center",
                                                marginVertical: 20,
                                                zIndex: -1000,
                                                // borderWidth: 1,
                                            }}
                                        >
                                            <TouchableOpacity
                                                style={{
                                                    justifyContent: "center",
                                                    flexDirection: "row",
                                                    borderWidth: 1,
                                                    borderColor: Colors.primaryColor,
                                                    backgroundColor: "#4E9F7D",
                                                    borderRadius: 5,
                                                    width: 120,
                                                    paddingHorizontal: 10,
                                                    height: switchMerchant ? 35 : 40,
                                                    alignItems: "center",
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                    zIndex: -4,
                                                }}
                                                disabled={isLoading}
                                                onPress={() => {
                                                    // updateOutletDetails();

                                                    var whatsappCreditPhoneParsed = whatsappCreditPhone
                                                        .replace("-", "")
                                                        .replace(" ", "");
                                                    if (!whatsappCreditPhoneParsed.startsWith("6")) {
                                                        whatsappCreditPhoneParsed = `6${whatsappCreditPhoneParsed}`;
                                                    }

                                                    if (!whatsappCreditPhoneParsed && !/^\d+$/.test(whatsappCreditPhoneParsed)) {
                                                        alert("Invalid or empty phone for OTP.");
                                                        return;
                                                    }

                                                    if (!whatsappCreditPhone) {
                                                        alert("Invalid or empty phone for OTP.");
                                                        return;
                                                    }

                                                    setShowMpsChannelModal();
                                                }}
                                            >
                                                <Text
                                                    style={{
                                                        color: Colors.whiteColor,
                                                        //marginLeft: 5,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: "NunitoSans-Bold",
                                                    }}
                                                >
                                                    {isLoading ? "LOADING..." : "TOP UP"}
                                                </Text>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                </ScrollView>
                            </View>
                        </View>
                    </ScrollView>
                </ScrollView>
            </View>

            <Modal
                style={{ flex: 1 }}
                visible={showMpsChannelModal}
                transparent={true}
                animationType="fade"
            >
                <View
                    style={{
                        backgroundColor: "rgba(0,0,0,0.5)",
                        flex: 1,
                        justifyContent: "center",
                        alignItems: "center",
                    }}
                >
                    <View
                        style={[
                            styles.confirmBox,
                            {
                                // width: switchMerchant ? '80%' : '40%',
                                // height: switchMerchant ? '80%' : '70%',
                                width: switchMerchant
                                    ? windowWidth * 0.8
                                    : // : windowWidth * 0.4,
                                    Dimensions.get("screen").width * 0.4,
                                height: switchMerchant
                                    ? windowHeight * 0.8
                                    : // : windowHeight * 0.7,
                                    Dimensions.get("screen").height * 0.7,
                                borderRadius: 10,
                            },
                        ]}
                    >
                        <TouchableOpacity
                            onPress={() => {
                                setShowMpsChannelModal(false);
                            }}
                        >
                            <View
                                style={{
                                    alignSelf: "flex-end",
                                    padding: 13,
                                }}
                            >
                                <Close name="closecircle" size={28} />
                            </View>
                        </TouchableOpacity>
                        <View>
                            <Text
                                style={{
                                    textAlign: "center",
                                    fontSize: 16,
                                    marginBottom: 10,
                                    fontFamily: "NunitoSans-Bold",
                                }}
                            >
                                Choose your payment method
                            </Text>
                        </View>
                        <View
                            style={{
                                marginTop: 15,
                                justifyContent: "center",
                                alignItems: "center",
                                width: "100%",
                                alignContent: "center",
                                // marginBottom: "10%",
                                height: "75%",
                            }}
                        >
                            {!isLoading ? (
                                <FlatList
                                    data={MPS_CHANNEL_LIST}
                                    renderItem={renderMpsChannelList}
                                    keyExtractor={(item, index) => String(index)}
                                    contentContainerStyle={{
                                        paddingHorizontal: 24,
                                    }}
                                />
                            ) : (
                                <ActivityIndicator color={Colors.primaryColor} size={"large"} />
                            )}
                        </View>
                    </View>
                </View>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#ffffff",
        flexDirection: "row",
        fontFamily: "NunitoSans-Regular",
    },
    iosStyle: {
        paddingHorizontal: 30,
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    headerLogo1: {
        width: "100%",
        height: "100%",
    },
    list: {
        paddingVertical: 20,
        paddingHorizontal: 30,
        flexDirection: "row",
        alignItems: "center",
    },
    listItem: {
        fontFamily: "NunitoSans-Regular",
        marginLeft: 20,
        color: Colors.descriptionColor,
        fontSize: 16,
    },
    sidebar: {
        width: Dimensions.get("screen").width * Styles.sideBarWidth,
        // shadowColor: "#000",
        // shadowOffset: {
        //   width: 0,
        //   height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    content: {
        padding: 30,
        width: Dimensions.get("screen").width * (1 - Styles.sideBarWidth),
        // backgroundColor: 'lightgrey',
        backgroundColor: Colors.highlightColor,
        height: Dimensions.get("screen").height * 1,
    },
    textInput: {
        fontFamily: "NunitoSans-Regular",
        width: 300,
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        marginRight: 0,
        flex: 1,
        flexDirection: "row",
    },
    textInputLocation: {
        fontFamily: "NunitoSans-Regular",
        width: 300,
        height: 100,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 10,
        marginRight: 10,
    },
    textInput8: {
        fontFamily: "NunitoSans-Regular",
        width: 75,
        height: 50,
        flex: 1,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
    },
    textInput9: {
        fontFamily: "NunitoSans-Regular",
        width: 110,
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        flexDirection: "row",
        justifyContent: "center",
    },
    textInput10: {
        fontFamily: "NunitoSans-Regular",
        width: 200,
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        flexDirection: "row",
        justifyContent: "center",
    },
    textInput1: {
        fontFamily: "NunitoSans-Regular",
        width: 250,
        height: 40,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginBottom: 10,
    },
    textInput2: {
        fontFamily: "NunitoSans-Regular",
        width: 300,
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginRight: 30,
    },
    textInput3: {
        fontFamily: "NunitoSans-Regular",
        width: "85%",
        height: 50,
        backgroundColor: Colors.fieldtBgColor,
        marginTop: 10,
        marginBottom: 10,
        borderRadius: 10,
        alignSelf: "center",
        paddingHorizontal: 10,
    },
    textInput4: {
        width: "85%",
        height: 70,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        marginTop: 10,
        marginBottom: 10,
        borderRadius: 10,
    },
    textInput5: {
        fontFamily: "NunitoSans-Regular",
        width: 300,
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 50,
    },
    textInput6: {
        fontFamily: "NunitoSans-Regular",
        width: "80 %",
        padding: 16,
        backgroundColor: Colors.fieldtBgColor,
        marginRight: 30,
        borderRadius: 10,
        alignSelf: "center",
    },
    textInput7: {
        fontFamily: "NunitoSans-Regular",
        width: 300,
        height: 80,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 7,
        marginRight: 30,
    },
    button: {
        backgroundColor: Colors.primaryColor,
        width: 150,
        padding: 8,
        borderRadius: 10,
        alignItems: "center",
        alignSelf: "center",
        marginBottom: 20,
    },
    button1: {
        width: "15%",
        padding: 8,
        borderRadius: 10,
        alignItems: "center",
        alignSelf: "center",
        marginBottom: 20,
    },
    button2: {
        backgroundColor: Colors.primaryColor,
        width: "60%",
        padding: 8,
        borderRadius: 10,
        alignItems: "center",
        marginLeft: "2%",
    },
    button3: {
        backgroundColor: Colors.primaryColor,
        width: "30%",
        height: 50,
        borderRadius: 10,
        alignItems: "center",
        alignSelf: "center",
        marginBottom: 30,
    },
    textSize: {
        fontSize: 19,
        fontFamily: "NunitoSans-SemiBold",
    },
    viewContainer: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        flex: 0,
        width: "100%",
        marginBottom: 15,
    },
    openHourContainer: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        flex: 1,
        marginBottom: 15,
        width: "100%",
    },
    addButtonView: {
        flexDirection: "row",
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        width: 200,
        height: 40,
        alignItems: "center",
    },
    addButtonView1: {
        flexDirection: "row",
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        width: 150,
        height: 40,
        alignItems: "center",
    },
    addNewView: {
        flexDirection: "row",
        justifyContent: "flex-start",
        marginBottom: 65,
        marginTop: 7,
        width: "83%",
        alignSelf: "flex-end",
    },
    addNewView1: {
        flexDirection: "row",
        justifyContent: "center",
        marginBottom: 10,
        alignItems: "center",
    },
    merchantDisplayView: {
        flexDirection: "row",
        flex: 1,
        marginLeft: "17%",
    },
    shiftView: {
        flexDirection: "row",
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 50,
        width: 200,
        height: 60,
        alignItems: "center",
        marginTop: 30,
        alignSelf: "center",
        justifyContent: "center",
    },
    shiftText: {
        // marginLeft: '15%',
        color: Colors.primaryColor,
        fontFamily: "NunitoSans-SemiBold",
        fontSize: 25,
    },
    closeView: {
        flexDirection: "row",
        borderRadius: 5,
        borderColor: Colors.primaryColor,
        borderWidth: 1,
        width: 200,
        height: 40,
        alignItems: "center",
        marginTop: 30,
        alignSelf: "center",
    },
    taxView: {
        flexDirection: "row",
        borderWidth: 2,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        width: 150,
        height: 40,
        alignItems: "center",
        marginTop: 20,
        alignSelf: "flex-end",
    },
    sectionView: {
        flexDirection: "row",
        borderRadius: 5,
        padding: 16,
        alignItems: "center",
    },
    receiptView: {
        flexDirection: "row",
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        borderRadius: 5,
        width: 200,
        height: 40,
        alignItems: "center",
        alignSelf: "flex-end",
    },
    pinBtn: {
        backgroundColor: Colors.fieldtBgColor,
        width: 70,
        height: 70,
        marginBottom: 16,
        alignContent: "center",
        justifyContent: "center",
        alignItems: "center",
        borderRadius: 10,
    },
    pinNo: {
        fontSize: 20,
        fontWeight: "bold",
    },
    confirmBox: {
        width: "30%",
        height: "30%",
        borderRadius: 30,
        backgroundColor: Colors.whiteColor,
    },
    logoTxt: {
        color: Colors.descriptionColor,
        fontSize: 20,
        letterSpacing: 7,
        marginTop: 10,
        alignSelf: "center",
        marginBottom: 40,
    },

    description: {
        paddingVertical: 5,
        fontSize: 13,
        color: Colors.descriptionColor,
        fontFamily: "NunitoSans-Regular",
        // fontWeight: '400',
        marginBottom: 2,
    },
    price: {
        paddingVertical: 5,
        fontSize: 13,
        alignSelf: "flex-end",
        fontFamily: "NunitoSans-Regular",
        color: Colors.descriptionColor,
        // fontWeight: "400",
        marginBottom: 2,
    },
    total: {
        // paddingVertical: 5,
        fontSize: 16,
        // fontWeight: "700",
        // marginTop: 5,
        fontFamily: "NunitoSans-Bold",
    },
    totalPrice: {
        color: Colors.primaryColor,
        // paddingVertical: 5,
        fontSize: 16,
        // fontWeight: "700",
        fontFamily: "NunitoSans-Bold",
        // marginTop: 5,
    },
    confirmBox: {
        width: "60%",
        height: "33%",
        borderRadius: 30,
        backgroundColor: Colors.whiteColor,
    },
    headerLeftStyle: {
        width: useWindowDimensions.width * 0.17,
        justifyContent: "center",
        alignItems: "center",
    },
});
export default SettingCreditWhatsappScreen;
