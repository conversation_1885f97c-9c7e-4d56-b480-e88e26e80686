import React, {
    Component,
    useReducer,
    useState,
    useEffect,
    useRef,
    useCallback,
} from "react";
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    Alert,
    TouchableOpacity,
    Dimensions,
    Platform,
    Switch,
    Modal,
    KeyboardAvoidingView,
    TextInput,
    ActivityIndicator,
    Picker,
    useWindowDimensions,
    Animated,
} from "react-native";
import Colors from "../constant/Colors";
import SideBar from "./SideBar";
import Icon from "react-native-vector-icons/Feather";
import Ionicon from "react-native-vector-icons/Ionicons";
import AntDesign from "react-native-vector-icons/AntDesign";
import Entypo from "react-native-vector-icons/Entypo";
import { ReactComponent as GCalendar } from "../assets/svg/GCalendar.svg";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import EvilIcons from "react-native-vector-icons/EvilIcons";
import { FlatList } from "react-native-gesture-handler";
import API from "../constant/API";
import ApiClient from "../util/ApiClient";
import Styles from "../constant/Styles";
import * as User from "../util/User";
import AsyncStorage from "@react-native-async-storage/async-storage";
// import CheckBox from 'react-native-check-box';
import moment from "moment";
import DateTimePickerModal from "react-native-modal-datetime-picker";
// import { isTablet } from 'react-native-device-detection';
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { CommonStore } from "../store/commonStore";
import { OutletStore } from "../store/outletStore";
import { MerchantStore } from "../store/merchantStore";
import { UserStore } from "../store/userStore";
import { ReactComponent as Upload } from "../assets/svg/Upload.svg";
import { ReactComponent as Download } from "../assets/svg/Download.svg";
import { ReactComponent as ArrowLeft } from "../assets/svg/ArrowLeft.svg";
import { ReactComponent as ArrowRight } from "../assets/svg/ArrowRight.svg";
import {
    convertArrayToCSV,
    generateEmailReport,
    sortReportDataList,
    sliceUnicodeStringV2WithDots,
    getTransformForScreenInsideNavigation,
    getTransformForModalInsideNavigation,
} from "../util/common";
import {
    EMAIL_REPORT_TYPE,
    REPORT_SORT_FIELD_TYPE,
    TABLE_PAGE_SIZE_DROPDOWN_LIST,
    ORDER_TYPE,
    USER_ORDER_ACTION_PARSED,
    EXPAND_TAB_TYPE,
} from "../constant/common";
// import RNFetchBlob from 'rn-fetch-blob';
// import { useKeyboard } from '../hooks';
import XLSX from "xlsx";
import { CSVLink } from "react-csv";
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
// import RNPickerSelect from 'react-native-picker-select';
import AsyncImage from "../components/asyncImage";
import Feather from "react-native-vector-icons/Feather";
// import Tooltip from 'react-native-walkthrough-tooltip';

// import firestore from '@react-native-firebase/firestore';
import firebase from "firebase";
import { Collections } from "../constant/firebase";
import Select from "react-select";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";
import { useFocusEffect } from "@react-navigation/native";
// import Ionicons from 'react-native-vector-icons/Ionicons';
import DropDownPicker from 'react-native-dropdown-picker';
// import UserIdleWrapper from '../components/userIdleWrapper';
// import DropDownPicker from 'react-native-dropdown-picker';
import { isMobile } from "../util/common";
import TopBar from './TopBar';


const { nanoid } = require("nanoid");
// const RNFS = require('react-native-fs');

const EmployeeTimeSheet = (props) => {
    //port til aug 11 changes
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    // const [isMounted, setIsMounted] = useState(true);

    // useFocusEffect(
    //     useCallback(() => {
    //         setIsMounted(true);
    //         return () => {
    //             setIsMounted(false);
    //         };
    //     }, [])
    // );

    ///////////////////////////////////////////////////////////

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    // const [keyboardHeight] = useKeyboard();
    const [visible, setVisible] = useState(false);
    const [perPage, setPerPage] = useState(10);
    const [pageCount, setPageCount] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [currentDetailsPage, setCurrentDetailsPage] = useState(1);
    const [switchMerchant, setSwitchMerchant] = useState(false);
    const [pageReturn, setPageReturn] = useState(1);
    const [search, setSearch] = useState("");

    const [loading, setLoading] = useState(false);

    const [pushPagingToTop, setPushPagingToTop] = useState(false);

    const [showDateTimePicker, setShowDateTimePicker] = useState(false);
    const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
    const [rev_date, setRev_date] = useState(
        moment().subtract(6, "days").startOf("day")
    );
    const [rev_date1, setRev_date1] = useState(
        moment().endOf(Date.now()).endOf("day")
    );

    const userName = UserStore.useState((s) => s.name);

    const [exportEmail, setExportEmail] = useState("");

    const [showDetails, setShowDetails] = useState(false);

    const [exportModalVisibility, setExportModalVisibility] = useState(false);

    const merchantId = UserStore.useState((s) => s.merchantId);
    const isLoading = CommonStore.useState((s) => s.isLoading);
    const [isCsv, setIsCsv] = useState(false);
    const [isExcel, setIsExcel] = useState(false);

    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView
    );

    const allOutletsEmployees = OutletStore.useState(
        (s) => s.allOutletsEmployees
    );
    const employeeClockDict = OutletStore.useState((s) => s.employeeClockDict);

    const [allOutletsEmployeesClock, setAllOutletsEmployeesClock] = useState([]);
    const [allOutletsEmployeesDetails, setAllOutletsEmployeesDetails] = useState(
        []
    );

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);
    // const allOutlets = MerchantStore.useState((s) => s.allOutlets);

    const [openPage, setOpenPage] = useState(false);

    const [isSidebarOpen, setIsSidebarOpen] = useState(true);

    // const toggleSidebar = () => {
    //     setIsSidebarOpen(!isSidebarOpen);
    // };
    const simpliFiedLayout = CommonStore.useState((s) => s.simpliFiedLayout);
    const sidebarClose = () => {
        setIsSidebarOpen(false);
        Animated.timing(sidebarXValue, {
          toValue: -100,
          duration: 200,
          useNativeDriver: true,
        }).start();
    
        Animated.timing(contentXValue, {
          toValue: simpliFiedLayout? 3: 10,
          duration: 200,
          useNativeDriver: true,
        }).start();
      };
      const sidebarOpen = () => {
        setIsSidebarOpen(true);
        Animated.timing(sidebarXValue, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }).start();
        Animated.timing(contentXValue, {
          toValue: simpliFiedLayout? 103: 110,
          duration: 200,
          useNativeDriver: true,
        }).start();
      };
    
      const sidebarXValue = useRef(new Animated.Value(0)).current;
      const contentXValue = useRef(new Animated.Value(simpliFiedLayout? 103: 110)).current;
    useEffect(() => {
        if (showDetails && allOutletsEmployeesDetails) {
            setPageReturn(currentPage);
            // console.log('currentPage value is');
            // console.log(currentPage);
            setCurrentDetailsPage(1);
            setPageCount(Math.ceil(allOutletsEmployeesDetails.length / perPage));
        }
    }, [showDetails, allOutletsEmployeesDetails, perPage]);

    useEffect(async () => {
        // const userActionSnapshot = await firestore()
        //   .collection(Collections.UserAction)
        //   .get();

        // const userAction = userActionSnapshot.docs.map(doc => doc.data());

        const tempAllOutletsEmployeesClock = allOutletsEmployees.map((employee) => {
            // const employeeAction = employeeClockDict.reduce(
            //   (prev, curr) => {
            //     if (curr.userId == employee.firebaseUid) {
            //       return curr.actions;
            //     }
            //     return [];
            //   },
            //   []
            // );

            var employeeClock = [];

            if (
                employeeClockDict[employee.firebaseUid] &&
                employeeClockDict[employee.firebaseUid].clockRecords &&
                employeeClockDict[employee.firebaseUid].clockRecords.length > 0
            ) {
                var filteredClockRecords = employeeClockDict[
                    employee.firebaseUid
                ].clockRecords.filter((record) =>
                    moment(record.clockInTime, "x").isBetween(rev_date, rev_date1)
                );

                employeeClock = [...filteredClockRecords];
            }

            return {
                ...employee,
                detailsList: employeeClock
                    .map((clock) => ({
                        ...clock,
                        day: clock.clockInTime
                            ? moment(clock.clockInTime).format("dddd")
                            : "N/A",
                        duration:
                            clock.clockInTime && clock.clockOutTime
                                ? (
                                    moment(clock.clockOutTime).diff(
                                        clock.clockInTime,
                                        "minute"
                                    ) / 60
                                ).toFixed(2)
                                : "0.00",
                    }))
                    .reverse(),
            };
        });

        // console.log('allOutletsEmployeesClock', tempAllOutletsEmployeesClock);
        setAllOutletsEmployeesClock(tempAllOutletsEmployeesClock);

        setPageCount(Math.ceil(tempAllOutletsEmployeesClock.length / perPage));
    }, [allOutletsEmployees, employeeClockDict, rev_date, rev_date1]);

    const setState = () => { };

    const currOutletShiftStatus = OutletStore.useState(
        (s) => s.currOutletShiftStatus
    );

    const allOutlets = MerchantStore.useState((s) => s.allOutlets);
    const currOutletId = MerchantStore.useState((s) => s.currOutletId);
    // const [outletDropdownList, setOutletDropdownList] = useState([]);
    // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

    // var outletNames = [];

    // for (var i = 0; i < allOutlets.length; i++) {
    //     for (var j = 0; j < selectedOutletList.length; j++) {
    //         if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
    //             outletNames.push(allOutlets[i].name);
    //             break;
    //         }
    //     }
    // }

    // useEffect(() => {
    //     setOutletDropdownList(
    //         allOutlets.map((item) => {
    //             return { label: item.name, value: item.uniqueId };
    //         })
    //     );
    // }, [allOutlets]);

    var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
        label: sliceUnicodeStringV2WithDots(outlet.name, 20),
        value: outlet.uniqueId,
    }));

    const [openO, setOpenO] = useState(false);

    // useEffect(() => {
    //     CommonStore.update((s) => {
    //         s.outletSelectDropdownView = () => {
    //             return (
    //                 <View
    //                     style={{
    //                         flexDirection: "row",
    //                         alignItems: "center",
    //                         borderRadius: 8,
    //                         width: 200,
    //                         backgroundColor: "white",
    //                     }}
    //                 >
    //                     {currOutletId.length > 0 &&
    //                         allOutlets.find((item) => item.uniqueId === currOutletId) ? (
    //                         <MultiSelect
    //                             clearable={false}
    //                             singleSelect={true}
    //                             defaultValue={currOutletId}
    //                             placeholder={"Choose Outlet"}
    //                             onChange={(value) => {
    //                                 if (value) { // if choose the same option again, value = ''
    //                                     MerchantStore.update((s) => {
    //                                         s.currOutletId = value;
    //                                         s.currOutlet =
    //                                             allOutlets.find(
    //                                                 (outlet) => outlet.uniqueId === value
    //                                             ) || {};
    //                                     });
    //                                 }

    //                                 CommonStore.update((s) => {
    //                                     s.shiftClosedModal = false;
    //                                 });
    //                             }}
    //                             options={targetOutletDropdownListTemp}
    //                             className="msl-varsHEADER"
    //                         />
    //                     ) : (
    //                         <ActivityIndicator size={"small"} color={Colors.whiteColor} />
    //                     )}

    //                     <DropDownPicker
    //                         style={{
    //                             backgroundColor: Colors.fieldtBgColor,
    //                             width: 200,
    //                             height: 40,
    //                             borderRadius: 10,
    //                             borderWidth: 1,
    //                             borderColor: "#E5E5E5",
    //                             flexDirection: "row",
    //                         }}
    //                         dropDownContainerStyle={{
    //                             width: 200,
    //                             backgroundColor: Colors.fieldtBgColor,
    //                             borderColor: "#E5E5E5",
    //                         }}
    //                         labelStyle={{
    //                             marginLeft: 5,
    //                             flexDirection: "row",
    //                         }}
    //                         textStyle={{
    //                             fontSize: 14,
    //                             fontFamily: 'NunitoSans-Regular',

    //                             marginLeft: 5,
    //                             paddingVertical: 10,
    //                             flexDirection: "row",
    //                         }}
    //                         selectedItemContainerStyle={{
    //                             flexDirection: "row",
    //                         }}

    //                         showArrowIcon={true}
    //                         ArrowDownIconComponent={({ style }) => (
    //                             <Ionicon
    //                                 size={25}
    //                                 color={Colors.fieldtTxtColor}
    //                                 style={{ paddingHorizontal: 5, marginTop: 5 }}
    //                                 name="chevron-down-outline"
    //                             />
    //                         )}
    //                         ArrowUpIconComponent={({ style }) => (
    //                             <Ionicon
    //                                 size={25}
    //                                 color={Colors.fieldtTxtColor}
    //                                 style={{ paddingHorizontal: 5, marginTop: 5 }}
    //                                 name="chevron-up-outline"
    //                             />
    //                         )}

    //                         showTickIcon={true}
    //                         TickIconComponent={({ press }) => (
    //                             <Ionicon
    //                                 style={{ paddingHorizontal: 5, marginTop: 5 }}
    //                                 color={
    //                                     press ? Colors.fieldtBgColor : Colors.primaryColor
    //                                 }
    //                                 name={'md-checkbox'}
    //                                 size={25}
    //                             />
    //                         )}
    //                         placeholderStyle={{
    //                             color: Colors.fieldtTxtColor,
    //                             // marginTop: 15,
    //                         }}
    //                         dropDownDirection="BOTTOM"
    //                         placeholder="Choose Outlet"
    //                         items={targetOutletDropdownListTemp}
    //                         value={currOutletId}
    //                         onSelectItem={(item) => {
    //                             if (item) { // if choose the same option again, value = ''
    //                                 MerchantStore.update((s) => {
    //                                     s.currOutletId = item.value;
    //                                     s.currOutlet =
    //                                         allOutlets.find(
    //                                             (outlet) => outlet.uniqueId === item.value
    //                                         ) || {};
    //                                 });
    //                             }

    //                             CommonStore.update((s) => {
    //                                 s.shiftClosedModal = false;
    //                             });
    //                         }}
    //                         open={openO}
    //                         setOpen={setOpenO}
    //                     />
    //                 </View>
    //             );
    //         };
    //     });
    // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

    navigation.setOptions({
        headerLeft: () => (
            <View
                style={[
                    styles.headerLeftStyle,
                    {
                        width: windowWidth * 0.17,
                    },
                ]}
            >
                <img src={headerLogo} width={124} height={26} />
                {/* <Image
              style={{
                width: 124,
                height: 26,
              }}
              resizeMode="contain"
              source={require('../assets/image/logo.png')}
            /> */}
            </View>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        justifyContent: "center",
                        alignItems: "center",
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        //width:  "55%",
                    },
                    Dimensions.get("screen").width <= 768
                        ? { right: Dimensions.get("screen").width * 0.12 }
                        : {},
                ]}
            >
                <Text
                    style={{
                        fontSize: 24,
                        // lineHeight: 25,
                        textAlign: "center",
                        fontFamily: "NunitoSans-Bold",
                        color: Colors.whiteColor,
                        opacity: 1,
                    }}
                >
                    Orders Channel Report
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                {/* {console.log('edward test')} */}
                {/* {console.log(outletSelectDropdownView)} */}
                {outletSelectDropdownView && outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: "white",
                        width: 0.5,
                        height: Dimensions.get("screen").height * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                        // borderWidth: 1
                    }}
                ></View>
                <TouchableOpacity
                    onPress={() => {
                        navigation.navigate("General Settings - KooDoo Manager");
                    }}
                    style={{ flexDirection: "row", alignItems: "center" }}
                >
                    <Text
                        style={{
                            fontFamily: "NunitoSans-SemiBold",
                            fontSize: 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            //backgroundColor: 'red',
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: "white",
                        }}
                    >
                        <img
                            src={personicon}
                            width={windowHeight * 0.035}
                            height={windowHeight * 0.035}
                        />
                        {/* <Image
                  style={{
                    width: windowHeight * 0.05,
                  height: windowHeight * 0.05,
                    alignSelf: 'center',
                  }}
                  source={require('../assets/image/profile-pic.jpg')}
                /> */}
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    navigation.setOptions({
        headerLeft: () => (
            <View
                style={[
                    styles.headerLeftStyle,
                    {
                        width: windowWidth * 0.17,
                    },
                ]}
            >
                <img src={headerLogo} width={124} height={26} />
                {/* <Image
              style={{
                width: 124,
                height: 26,
              }}
              resizeMode="contain"
              source={require('../assets/image/logo.png')}
            /> */}
            </View>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        justifyContent: "center",
                        alignItems: "center",
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        //width:  "55%",
                    },
                    Dimensions.get("screen").width <= 768
                        ? { right: Dimensions.get("screen").width * 0.12 }
                        : {},
                ]}
            >
                <Text
                    style={{
                        fontSize: 24,
                        // lineHeight: 25,
                        textAlign: "center",
                        fontFamily: "NunitoSans-Bold",
                        color: Colors.whiteColor,
                        opacity: 1,
                    }}
                >
                    Employee Timesheet
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                {/* {console.log('edward test')} */}
                {/* {console.log(outletSelectDropdownView)} */}
                {outletSelectDropdownView && outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: "white",
                        width: 0.5,
                        height: Dimensions.get("screen").height * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                        // borderWidth: 1
                    }}
                ></View>
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate("General Settings - KooDoo Manager");
                        }
                    }}
                    style={{ flexDirection: "row", alignItems: "center" }}
                >
                    <Text
                        style={{
                            fontFamily: "NunitoSans-SemiBold",
                            fontSize: 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            //backgroundColor: 'red',
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: "white",
                        }}
                    >
                        <img
                            src={personicon}
                            width={windowHeight * 0.035}
                            height={windowHeight * 0.035}
                        />
                        {/* <Image
                  style={{
                    width: windowHeight * 0.05,
                  height: windowHeight * 0.05,
                    alignSelf: 'center',
                  }}
                  source={require('../assets/image/profile-pic.jpg')}
                /> */}
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    const nextPage = () => {
        setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
    };

    const prevPage = () => {
        setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
    };

    const nextDetailsPage = () => {
        setCurrentDetailsPage(
            currentDetailsPage + 1 > pageCount
                ? currentDetailsPage
                : currentDetailsPage + 1
        );
    };

    const prevDetailsPage = () => {
        setCurrentDetailsPage(
            currentDetailsPage - 1 < 1 ? currentDetailsPage : currentDetailsPage - 1
        );
    };

    const renderItem = ({ item, index }) => {
        return (
            <TouchableOpacity
                onPress={() => {
                    setShowDetails(true);
                    setAllOutletsEmployeesDetails(item.detailsList);
                }}
                style={{
                    backgroundColor:
                        (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
                    paddingVertical: 10,
                    paddingHorizontal: 3,
                    paddingLeft: 1,
                    borderColor: "#BDBDBD",
                    borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                    borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                }}
            >
                <View style={{ flexDirection: "row" }}>
                    <Text
                        style={{
                            width: simpliFiedLayout? '20%':"5%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {index + 1}
                    </Text>
                    <Text
                        style={{
                            width: simpliFiedLayout? '40%':"25%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {item.name}
                    </Text>
                    <Text
                        style={{
                            width: simpliFiedLayout? '40%':"25%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {item.detailsList.length}
                    </Text>
                </View>
            </TouchableOpacity>
        );
    };

    const renderItemDetails = ({ item, index }) => {
        return (
            <View
                style={{
                    backgroundColor:
                        (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
                    paddingVertical: 10,
                    borderColor: "#BDBDBD",
                    borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                    borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                }}
            >
                <View style={{ flexDirection: "row" }}>
                    {!simpliFiedLayout &&(
                    <Text
                        style={{
                            width: "6%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontFamily: "NunitoSans-Regular",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {index + 1}
                    </Text>
                    )}
                    <Text
                        style={{
                            width: simpliFiedLayout? '25%':"23.5%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontWeight: "500",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {item.day}
                    </Text>
                    <Text
                        style={{
                            width: simpliFiedLayout? '25%':"23.5%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontWeight: "500",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {item.clockInTime
                            ? moment(item.clockInTime, "x").format("DD MMM YY hh:mm A")
                            : "N/A"}
                    </Text>
                    <Text
                        style={{
                            width: simpliFiedLayout? '25%':"23.5%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontWeight: "500",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {item.clockOutTime
                            ? moment(item.clockOutTime, "x").format("DD MMM YY hh:mm A")
                            : "N/A"}
                    </Text>
                    <Text
                        style={{
                            width: simpliFiedLayout? '25%':"23.5%",
                            fontSize: switchMerchant ? 10 : 13,
                            fontWeight: "500",
                            textAlign: "left",
                            paddingLeft: 10,
                        }}
                    >
                        {item.duration}
                    </Text>
                </View>
            </View>
        );
    };

    const convertDataToExcelFormat = () => {
        var excelData = [];

        if (!showDetails) {
            for (var i = 0; i < allOutletsEmployeesClock.length; i++) {
                for (var j = 0; j < allOutletsEmployeesClock[i].detailsList.length; j++) {
                    var excelOutletName = allOutlets.find(outlet => outlet.uniqueId === allOutletsEmployeesClock[i].outletId);

                    var excelRow = {
                        'Date': `${moment(rev_date).format('DD MMM yyyy')}-${moment(rev_date1).format('DD MMM yyyy')}`,
                        'Day': allOutletsEmployeesClock[i].detailsList[j].day ? allOutletsEmployeesClock[i].detailsList[j].day : 'N/A',
                        'Employee Name': allOutletsEmployeesClock[i].name,
                        'Role': allOutletsEmployeesClock[i].role,
                        'Clock In': allOutletsEmployeesClock[i].detailsList[j].clockInTime ? moment(allOutletsEmployeesClock[i].detailsList[j].clockInTime, 'x').format('DD MMM YY hh:mm A') : 'N/A',
                        'Clock Out': allOutletsEmployeesClock[i].detailsList[j].clockOutTime ? moment(allOutletsEmployeesClock[i].detailsList[j].clockOutTime, 'x').format('DD MMM YY hh:mm A') : 'N/A',
                        'Duration': allOutletsEmployeesClock[i].detailsList[j].duration ? allOutletsEmployeesClock[i].detailsList[j].duration : 'N/A',
                        // 'Outlet': allOutlets.find(outlet => outlet.uniqueId === allOutletsEmployeesClock[i].outletId).name,
                        'Outlet': excelOutletName ? excelOutletName.name : 'N/A',
                    };
                    excelData.push(excelRow);
                }
                // if (allOutletsEmployeesClock[i].detailsList.length === 0) {
                //     var excelRow = {
                //         'Date': `${moment(rev_date).format('DD MMM yyyy')}-${moment(rev_date1).format('DD MMM yyyy')}`,
                //         'Day': 'N/A',
                //         'Employee Name': allOutletsEmployeesClock[i].name,
                //         'Role': allOutletsEmployeesClock[i].role,
                //         'Clock In': 'N/A',
                //         'Clock Out': 'N/A',
                //         'Duration': 'N/A',
                //         'Outlet': allOutlets.find(outlet => outlet.uniqueId === allOutletsEmployeesClock[i].outletId).name,
                //     };
                //     excelData.push(excelRow);
                // }
            }
        } else {
            for (var i = 0; i < allOutletsEmployeesDetails.length; i++) {
                var excelRow = {
                    'Day': allOutletsEmployeesDetails[i].day,
                    'Time In': allOutletsEmployeesDetails[i].clockInTime ? moment(allOutletsEmployeesDetails[i].clockInTime, 'x').format('DD MMM YY hh:mm A') : 'N/A',
                    'Time Out': allOutletsEmployeesDetails[i].clockOutTime ? moment(allOutletsEmployeesDetails[i].clockOutTime, 'x').format('DD MMM YY hh:mm A') : 'N/A',
                    'Total Hours': allOutletsEmployeesDetails[i].duration,
                };

                excelData.push(excelRow);
            }
        }

        // console.log('excelData');
        // console.log(excelData);

        return excelData;
    };

    const convertDataToCSVFormat = () => {
        var csvData = [];

        if (!showDetails) {
            csvData.push(
                `Date,Day,Employee Name,Role,Clock In,Clock Out,Duration,Outlet`,
            );

            for (var i = 0; i < allOutletsEmployeesClock.length; i++) {
                for (var j = 0; j < allOutletsEmployeesClock[i].detailsList.length; j++) {
                    var csvRow = `${moment(rev_date).format('DD MMM yyyy')}-${moment(rev_date1).format('DD MMM yyyy')},${allOutletsEmployeesClock[i].detailsList[j].day ? allOutletsEmployeesClock[i].detailsList[j].day : 'N/A'},${allOutletsEmployeesClock[i].name},${allOutletsEmployeesClock[i].role},${allOutletsEmployeesClock[i].detailsList[j].clockInTime ? moment(allOutletsEmployeesClock[i].detailsList[j].clockInTime, 'x').format('DD MMM YY hh:mm A') : 'N/A'},${allOutletsEmployeesClock[i].detailsList[j].clockOutTime ? moment(allOutletsEmployeesClock[i].detailsList[j].clockOutTime, 'x').format('DD MMM YY hh:mm A') : 'N/A'},${allOutletsEmployeesClock[i].detailsList[j].duration ? allOutletsEmployeesClock[i].detailsList[j].duration : 'N/A'},${allOutlets.find(outlet => outlet.uniqueId === allOutletsEmployeesClock[i].outletId).name}`;

                    csvData.push(csvRow);
                }
                // if (allOutletsEmployeesClock[i].detailsList.length === 0) {
                //     var csvRow = `${moment(rev_date).format('DD MMM yyyy')}-${moment(rev_date1).format('DD MMM yyyy')},${allOutletsEmployeesClock[i].detailsList[j].day ? allOutletsEmployeesClock[i].detailsList[j].day : 'N/A'},${allOutletsEmployeesClock[i].name},${allOutletsEmployeesClock[i].role},${allOutletsEmployeesClock[i].detailsList[j].clockInTime ? moment(allOutletsEmployeesClock[i].detailsList[j].clockInTime, 'x').format('DD MMM YY hh:mm A') : 'N/A'},${allOutletsEmployeesClock[i].detailsList[j].clockOutTime ? moment(allOutletsEmployeesClock[i].detailsList[j].clockOutTime, 'x').format('DD MMM YY hh:mm A') : 'N/A'},${allOutletsEmployeesClock[i].detailsList[j].duration ? allOutletsEmployeesClock[i].detailsList[j].duration : 'N/A'},${allOutlets.find(outlet => outlet.uniqueId === allOutletsEmployeesClock[i].outletId).name}`;

                // csvData.push(csvRow);
                // }
            }
        } else {
            csvData.push(
                `Day,Time In,Time Out,Total Hours`,
            );
            for (var i = 0; i < allOutletsEmployeesDetails.length; i++) {
                var csvRow = `${allOutletsEmployeesDetails[i].day}',${allOutletsEmployeesDetails[i].clockInTime ? moment(allOutletsEmployeesDetails[i].clockInTime, 'x').format('DD MMM YY hh:mm A') : 'N/A'},${allOutletsEmployeesDetails[i].clockOutTime ? moment(allOutletsEmployeesDetails[i].clockOutTime, 'x').format('DD MMM YY hh:mm A') : 'N/A'},${allOutletsEmployeesDetails[i].duration}`;

                csvData.push(csvRow);
            }
        }

        // console.log('excelData');
        // console.log(excelData);

        return csvData.join('\r\n');
    };

    const handleExportExcel = () => {
        var wb = XLSX.utils.book_new(),
            ws = XLSX.utils.json_to_sheet(convertDataToExcelFormat());

        XLSX.utils.book_append_sheet(wb, ws, "EmployeeTimesheet");
        XLSX.writeFile(wb, "EmployeeTimesheet.xlsx");
    };

    const emailVariant = () => {
        const excelData = convertDataToExcelFormat();

        var body = {
            // data: CsvData,
            //data: convertArrayToCSV(todaySalesChart.dataSource.data),
            data: JSON.stringify(excelData),
            //data: convertDataToExcelFormat(),
            email: exportEmail,
        };

        ApiClient.POST(API.emailDashboard, body, false).then((result) => {
            if (result !== null) {
                window.confirm(
                    "Success",
                    "Email has been sent",
                    [{ text: "OK", onPress: () => { } }],
                    { cancelable: false }
                );
            }
        });

        setVisible(false);
    };

    var leftSpacing = "0%";

    if (windowWidth >= 1280) {
        leftSpacing = "10%";
    }

    const flatListRef = useRef();

    const filterItem = (item) => {
        if (search !== "") {
            if (item.name.toLowerCase().includes(search.toLowerCase())) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    };

    const filterItemDetails = (item) => {
        if (search !== "") {
            if (
                item.clockInTime &&
                moment(item.clockInTime, "x")
                    .format("DD MMM YYY hh:mma")
                    .toLowerCase()
                    .includes(search.toLowerCase())
            ) {
                return true;
            } else if (
                item.clockOutTime &&
                moment(item.clockOutTime, "x")
                    .format("DD MMM YYY hh:mma")
                    .toLowerCase()
                    .includes(search.toLowerCase())
            ) {
                return true;
            } else if (
                item.day &&
                item.day.toLowerCase().includes(search.toLowerCase())
            ) {
                return true;
            } else if (
                item.duration &&
                item.duration.toLowerCase().includes(search.toLowerCase())
            ) {
                return true;
            } else {
                return false;
            }
        } else {
            // check if there is data between the dates
            // return moment(item.clockInTime, 'x').isBetween(rev_date, rev_date1);

            return true;
        }
    };

    return (
        //<UserIdleWrapper disabled={!isMounted}>
        <View>
            {isMobile() && <TopBar navigation={navigation} />}
        <View
            style={[
                styles.container,
                {
                    height: windowHeight,
                    width: windowWidth,
                    ...getTransformForScreenInsideNavigation(),
                },
            ]}
        >
            
            <Animated.View
                style={[
                styles.sidebar,
                {
                    transform: [{ translateX: sidebarXValue }],
                    flex: 0.8,
                    //zIndex: 100,
                    ...isMobile() && {
                    //width: windowWidth,
                    //flex: 2.7,
                    flex: 1,

                    },
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    zIndex: 2,
                    height: '100%',
                    width: isMobile() ? windowWidth * 0.23 : {},
                    marginTop: 50,
                    //backgroundColor: 'blue',
                }
                ]}
            >
            <View style={{ flex: 0.8 }}>
                <SideBar navigation={navigation} selectedTab={0} />
            </View>
            </Animated.View>
            

            <View style={{ height: windowHeight, flex: 9 }}>
                <View
                style={{
                    width: isMobile() ? windowWidth * 0.23 : 123,
                    height:70,

                    //backgroundColor: 'red',

                    zIndex: 3,
                    // backgroundColor: isSidebarOpen ? Colors.whiteColor : {},
                    // marginLeft: isSidebarOpen ? 0 : 0,
                }}
                >
                
                    {isSidebarOpen ?
                    <Animated.View
                    style={{
                        transform: [{ translateX: sidebarXValue }],
                        backgroundColor: Colors.whiteColor,
                        height: '100%'
                      }}
                    >
                        <TouchableOpacity
                            style={{ marginTop: 5, }}
                            onPress={sidebarClose}
                        >
                            <MaterialIcons name='keyboard-capslock' color={Colors.primaryColor} size={isMobile() ? 35 : 30} style={{ alignSelf: 'center', transform: [{ rotate: '270deg' }], marginTop: 7, marginLeft: -3 }} />
                        </TouchableOpacity>
                    </Animated.View>
                    :
                    <TouchableOpacity
                        style={{ marginTop: 5, }}
                        onPress={sidebarOpen}
                    >
                        <View style={{
                            flexDirection: 'row',
                            marginLeft: 10,
                            marginVertical: 10,
                            justifyContent: 'center',
                            flexDirection: 'row',
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: '#4E9F7D',
                            borderRadius: 5,
                            //width: 160,
                            height: switchMerchant ? 35 : 40,
                            width: windowWidth * 0.4,
                            alignItems: 'center',
                            shadowOffset: {
                            width: 0,
                            height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,

                        }}>
                            <MaterialIcons name='keyboard-capslock' color={Colors.whiteColor} size={isMobile() ? 25 : 30} style={{ alignSelf: 'flex-start', transform: [{ rotate: '90deg' }], marginTop: 5 }} />
                            <Text style={{
                            color: Colors.whiteColor,
                            marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                            }}
                            >
                            MORE PAGES
                            </Text>
                        </View>
                    </TouchableOpacity>
                    }
                
                </View>
                <Animated.ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{ 
                        transform: [{ translateX: contentXValue}],
                        //width: windowWidth * 0.9 
                        // marginLeft: simpliFiedLayout? 
                        // (isSidebarOpen
                        //     ? (isMobile() ? '25%' : '7%')
                        //     : 5)
                        // : 
                        //     (isSidebarOpen
                        //     ? (isMobile() ? '12%' : '7%')
                        //     : -40
                        // ),
                    }}
                    contentContainerStyle={{
                        paddingBottom: windowHeight * 0.1,
                        backgroundColor: Colors.highlightColor,
                    }}
                >
                    <Modal
                        style={{}}
                        visible={exportModalVisibility}
                        supportedOrientations={["portrait", "landscape"]}
                        transparent={true}
                        animationType={"fade"}
                    >
                        <View
                            style={{
                                flex: 1,
                                backgroundColor: Colors.modalBgColor,
                                alignItems: "center",
                                justifyContent: "center",
                            }}
                        >
                            <View
                                style={{
                                    // height: windowWidth * 0.08,
                                    height: Dimensions.get("screen").width * 0.08,
                                    // width: windowWidth * 0.18,
                                    width: Dimensions.get("screen").width * 0.18,
                                    backgroundColor: Colors.whiteColor,
                                    borderRadius: 12,
                                    // padding: windowWidth * 0.03,
                                    padding: Dimensions.get("screen").width * 0.03,
                                    alignItems: "center",
                                    justifyContent: "center",

                                    ...getTransformForModalInsideNavigation(),
                                }}
                            >
                                <TouchableOpacity
                                    disabled={isLoading}
                                    style={{
                                        position: "absolute",
                                        // right: windowWidth * 0.015,
                                        right: Dimensions.get("screen").width * 0.015,
                                        // top: windowWidth * 0.01,
                                        top: Dimensions.get("screen").width * 0.01,

                                        elevation: 1000,
                                        zIndex: 1000,
                                    }}
                                    onPress={() => {
                                        setExportModalVisibility(false);
                                    }}
                                >
                                    <AntDesign
                                        name="closecircle"
                                        size={switchMerchant ? 15 : 25}
                                        color={Colors.fieldtTxtColor}
                                    />
                                </TouchableOpacity>
                                <View
                                    style={{
                                        alignItems: "center",
                                        top: "20%",
                                        position: "absolute",
                                    }}
                                >
                                    <Text
                                        style={{
                                            fontFamily: "NunitoSans-Bold",
                                            textAlign: "center",
                                            fontSize: switchMerchant ? 16 : 24,
                                        }}
                                    >
                                        Download Report
                                    </Text>
                                </View>
                                <View style={{ top: switchMerchant ? "14%" : "10%" }}>
                                    {/* <Text
                                    style={{
                                        fontSize: switchMerchant ? 10 : 20,
                                        fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    Email Address:
                                </Text>
                                <TextInput
                                    underlineColorAndroid={Colors.fieldtBgColor}
                                    style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: switchMerchant ? 240 : 370,
                                        height: switchMerchant ? 35 : 50,
                                        borderRadius: 5,
                                        padding: 5,
                                        marginVertical: 5,
                                        borderWidth: 1,
                                        borderColor: '#E5E5E5',
                                        paddingLeft: 10,
                                        fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    autoCapitalize='none'
                                    placeholderStyle={{ padding: 5 }}
                                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                    placeholder="Enter your email"
                                    onChangeText={(text) => {
                                        setExportEmail(text);
                                    }}
                                    value={exportEmail}
                                />
                                <Text
                                    style={{
                                        fontSize: switchMerchant ? 10 : 20,
                                        fontFamily: 'NunitoSans-Bold',
                                        marginTop: 15,
                                    }}>
                                    Send As:
                                </Text> */}

                                    <View
                                        style={{
                                            alignItems: "center",
                                            justifyContent: "center",
                                            flexDirection: "row",
                                            marginTop: 30,
                                        }}
                                    >
                                        <TouchableOpacity
                                            disabled={isLoading}
                                            style={{
                                                justifyContent: "center",
                                                flexDirection: "row",
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: "#4E9F7D",
                                                borderRadius: 5,
                                                width: switchMerchant ? 100 : 100,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 35 : 40,
                                                alignItems: "center",
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                                marginRight: 15,
                                            }}
                                            onPress={() => {
                                                // if (exportEmail.length > 0) {
                                                //     CommonStore.update((s) => {
                                                //         s.isLoading = true;
                                                //     });

                                                //     setIsExcel(true);

                                                //     const excelData = convertDataToExcelFormat();

                                                //     generateEmailReport(
                                                //         EMAIL_REPORT_TYPE.EXCEL,
                                                //         excelData,
                                                //         'KooDoo Add-Ons Sales Report',
                                                //         'KooDoo Add-Ons Sales Report.xlsx',
                                                //         `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                                                //         exportEmail,
                                                //         'KooDoo Add-Ons Sales Report',
                                                //         'KooDoo Add-Ons Sales Report',
                                                //         () => {
                                                //             CommonStore.update((s) => {
                                                //                 s.isLoading = false;
                                                //             });

                                                //             setIsExcel(false);

                                                //            window.confirm(
                                                //                 'Success',
                                                //                 'Report will be sent to the email address shortly',
                                                //             );

                                                //             setExportModalVisibility(false);
                                                //         },
                                                //     );
                                                // } else {
                                                //    window.confirm('Info', 'Invalid email address');
                                                // }
                                                handleExportExcel();
                                            }}
                                        >
                                            {isLoading && isExcel ? (
                                                <ActivityIndicator
                                                    size={"small"}
                                                    color={Colors.whiteColor}
                                                />
                                            ) : (
                                                <Text
                                                    style={{
                                                        color: Colors.whiteColor,
                                                        //marginLeft: 5,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: "NunitoSans-Bold",
                                                    }}
                                                >
                                                    EXCEL
                                                </Text>
                                            )}
                                        </TouchableOpacity>

                                        {/* <TouchableOpacity
                                        disabled={isLoading}
                                        style={{
                                            justifyContent: 'center',
                                            flexDirection: 'row',
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            backgroundColor: '#4E9F7D',
                                            borderRadius: 5,
                                            width: switchMerchant ? 100 : 100,
                                            paddingHorizontal: 10,
                                            height: switchMerchant ? 35 : 40,
                                            alignItems: 'center',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                            zIndex: -1,
                                        }}
                                        onPress={() => {
                                            if (exportEmail.length > 0) {
                                                CommonStore.update((s) => {
                                                    s.isLoading = true;
                                                });

                                                setIsCsv(true);

                                                const csvData = convertArrayToCSV(allOutletsEmployeesClock);

                                                generateEmailReport(
                                                    EMAIL_REPORT_TYPE.CSV,
                                                    csvData,
                                                    'KooDoo Add-Ons Sales Report',
                                                    'KooDoo Add-Ons Sales Report.csv',
                                                    `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                                                    exportEmail,
                                                    'KooDoo Add-Ons Sales Report',
                                                    'KooDoo Add-Ons Sales Report',
                                                    () => {
                                                        CommonStore.update((s) => {
                                                            s.isLoading = false;
                                                        });

                                                        setIsCsv(false);

                                                        window.confirm(
                                                            'Success',
                                                            'Report will be sent to the email address shortly',
                                                        );

                                                        setExportModalVisibility(false);
                                                    },
                                                );
                                            } else {
                                                window.confirm('Info', 'Invalid email address');
                                            }
                                        }}>
                                        {isLoading && isCsv ? (
                                            <ActivityIndicator
                                                size={'small'}
                                                color={Colors.whiteColor}
                                            />
                                        ) : (
                                            <Text
                                                style={{
                                                    color: Colors.whiteColor,
                                                    //marginLeft: 5,
                                                    fontSize: switchMerchant ? 10 : 16,
                                                    fontFamily: 'NunitoSans-Bold',
                                                }}>
                                                CSV
                                            </Text>
                                        )}
                                    </TouchableOpacity> */}
                                        <CSVLink
                                            style={{
                                                justifyContent: "center",
                                                flexDirection: "row",
                                                borderWidth: 1,
                                                textDecoration: "none",
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: "#4E9F7D",
                                                borderRadius: 5,
                                                width: switchMerchant ? 100 : 100,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 35 : 40,
                                                alignItems: "center",
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                            }}
                                            data={convertDataToExcelFormat()}
                                            filename="EmployeeTimeSheet.csv"
                                        >
                                            <View
                                                style={{
                                                    width: "100%",
                                                    height: "100%",
                                                    alignContent: "center",
                                                    alignItems: "center",
                                                    alignSelf: "center",
                                                    justifyContent: "center",
                                                }}
                                            >
                                                <Text
                                                    style={{
                                                        color: Colors.whiteColor,
                                                        //marginLeft: 5,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: "NunitoSans-Bold",
                                                    }}
                                                >
                                                    CSV
                                                </Text>
                                            </View>
                                        </CSVLink>
                                    </View>
                                </View>
                            </View>
                        </View>
                    </Modal>

                    <DateTimePickerModal
                        isVisible={showDateTimePicker}
                        mode={"date"}
                        onConfirm={(text) => {
                            setRev_date(moment(text).startOf("day"));
                            setShowDateTimePicker(false);
                        }}
                        onCancel={() => {
                            setShowDateTimePicker(false);
                        }}
                        maximumDate={moment(rev_date1).toDate()}
                        date={moment(rev_date).toDate()}
                    />

                    <DateTimePickerModal
                        isVisible={showDateTimePicker1}
                        mode={"date"}
                        onConfirm={(text) => {
                            setRev_date1(moment(text).endOf("day"));
                            setShowDateTimePicker1(false);
                        }}
                        onCancel={() => {
                            setShowDateTimePicker1(false);
                        }}
                        minimumDate={moment(rev_date).toDate()}
                        date={moment(rev_date1).toDate()}
                    />

                    <Modal
                        supportedOrientations={["landscape", "portrait"]}
                        style={{ flex: 1 }}
                        visible={visible}
                        transparent={true}
                        animationType="slide"
                    >
                        <KeyboardAvoidingView
                            style={{
                                backgroundColor: "rgba(0,0,0,0.5)",
                                flex: 1,
                                justifyContent: "center",
                                alignItems: "center",
                                minHeight: windowHeight,
                            }}
                        >
                            <View style={[styles.confirmBox1, { ...getTransformForModalInsideNavigation(), }]}>
                                <Text
                                    style={{
                                        fontSize: 24,
                                        justifyContent: "center",
                                        alignSelf: "center",
                                        marginTop: 40,
                                        fontFamily: "NunitoSans-Bold",
                                    }}
                                >
                                    Enter your email
                                </Text>
                                <View
                                    style={{
                                        justifyContent: "center",
                                        alignSelf: "center",
                                        alignContent: "center",
                                        marginTop: 20,
                                        flexDirection: "row",
                                        width: "80%",
                                    }}
                                >
                                    <View
                                        style={{ justifyContent: "center", marginHorizontal: 5 }}
                                    >
                                        <Text
                                            style={{ color: Colors.descriptionColor, fontSize: 20 }}
                                        >
                                            email:
                                        </Text>
                                    </View>
                                    <TextInput
                                        underlineColorAndroid={Colors.fieldtBgColor}
                                        style={[styles.textInput8, { paddingLeft: 5 }]}
                                        placeholder="Enter your email"
                                        // style={{
                                        //     // paddingLeft: 1,
                                        // }}
                                        //defaultValue={extentionCharges}
                                        onChangeText={(text) => {
                                            // setState({ exportEmail: text });
                                            setExportEmail(text);
                                        }}
                                        placeholderTextColor={Platform.select({ ios: "#a9a9a9" })}
                                        value={exportEmail}
                                    />
                                </View>
                                <Text
                                    style={{
                                        fontSize: 20,
                                        fontFamily: "NunitoSans-Bold",
                                        marginTop: 25,
                                        justifyContent: "center",
                                        alignSelf: "center",
                                        alignContent: "center",
                                    }}
                                >
                                    Share As:
                                </Text>

                                {/* Share file using email */}
                                <View
                                    style={{
                                        justifyContent: "space-between",
                                        alignSelf: "center",
                                        marginTop: 10,
                                        flexDirection: "row",
                                        width: "80%",
                                    }}
                                >
                                    <TouchableOpacity
                                        style={[
                                            styles.modalSaveButton1,
                                            {
                                                zIndex: -1,
                                            },
                                        ]}
                                        onPress={() => { }}
                                    >
                                        <Text
                                            style={[
                                                styles.modalDescText,
                                                { color: Colors.primaryColor },
                                            ]}
                                        >
                                            Excel
                                        </Text>
                                    </TouchableOpacity>

                                    <TouchableOpacity
                                        style={[
                                            styles.modalSaveButton1,
                                            {
                                                zIndex: -1,
                                            },
                                        ]}
                                        onPress={() => { }}
                                    >
                                        <Text
                                            style={[
                                                styles.modalDescText,
                                                { color: Colors.primaryColor },
                                            ]}
                                        >
                                            CSV
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={[
                                            styles.modalSaveButton1,
                                            {
                                                zIndex: -1,
                                            },
                                        ]}
                                        onPress={() => { }}
                                    >
                                        <Text
                                            style={[
                                                styles.modalDescText,
                                                { color: Colors.primaryColor },
                                            ]}
                                        >
                                            PDF
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                                <View
                                    style={{
                                        alignSelf: "center",
                                        marginTop: 20,
                                        justifyContent: "center",
                                        alignItems: "center",
                                        // width: 260,
                                        // width: windowWidth * 0.2,
                                        width: Dimensions.get("screen").width * 0.2,
                                        height: 60,
                                        alignContent: "center",
                                        flexDirection: "row",
                                        marginTop: 40,
                                    }}
                                >
                                    <TouchableOpacity
                                        onPress={emailVariant}
                                        style={{
                                            backgroundColor: Colors.fieldtBgColor,
                                            width: "100%",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            alignContent: "center",
                                            height: 60,
                                            borderBottomLeftRadius: 10,
                                            borderRightWidth: StyleSheet.hairlineWidth,
                                            borderTopWidth: StyleSheet.hairlineWidth,
                                        }}
                                    >
                                        <Text
                                            style={{
                                                fontSize: 22,
                                                color: Colors.primaryColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                            }}
                                        >
                                            Email
                                        </Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        onPress={() => {
                                            // setState({ visible: false });
                                            setVisible(false);
                                        }}
                                        style={{
                                            backgroundColor: Colors.fieldtBgColor,
                                            width: "100%",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            alignContent: "center",
                                            height: 60,
                                            borderBottomRightRadius: 10,
                                            borderTopWidth: StyleSheet.hairlineWidth,
                                        }}
                                    >
                                        <Text
                                            style={{
                                                fontSize: 22,
                                                color: Colors.descriptionColor,
                                                fontFamily: "NunitoSans-SemiBold",
                                            }}
                                        >
                                            Cancel
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </KeyboardAvoidingView>
                    </Modal>
                    
                    <ScrollView horizontal={simpliFiedLayout? isSidebarOpen: true} showsHorizontalScrollIndicator={simpliFiedLayout? isSidebarOpen: true}>
                    <View
                        style={{
                            paddingVertical: 10,
                            //marginHorizontal: simpliFiedLayout? 0:30,
                        }}
                    >
                        {/* <View style={{ flex: 1 }}> */}
                            <View
                                style={{
                                    flexDirection: simpliFiedLayout? 'column':"row",
                                    justifyContent: simpliFiedLayout? 'flex-start':"space-between",
                                    alignItems: simpliFiedLayout? 'flex-start':"center",
                                    //marginHorizontal: simpliFiedLayout? 0:30,
                                    //marginTop: 20,
                                    //height: windowHeight * 0.1,
                                    width: simpliFiedLayout? windowWidth *0.95: isMobile()? windowWidth * 1.5: windowWidth *0.877,
                                    alignSelf: "center",
                                  }}
                            >
                                <Text
                                    style={{
                                        fontSize: switchMerchant ? 20 : 26,
                                        fontFamily: "NunitoSans-Bold",
                                    }}
                                >
                                    Employee Timesheet
                                </Text>
                                <View
                                    style={{
                                        flexDirection: simpliFiedLayout? 'column':"row",
                                    }}
                                >
                                    <TouchableOpacity
                                        style={{
                                            justifyContent: "center",
                                            flexDirection: "row",
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            backgroundColor: "#4E9F7D",
                                            borderRadius: 5,
                                            width: simpliFiedLayout?'60%': '35%',
                                            paddingHorizontal: 10,
                                            height: switchMerchant ? 35 : 40,
                                            alignItems: "center",
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                            zIndex: -1,
                                            marginRight: 10,
                                            ...simpliFiedLayout && {
                                                marginTop: 10,
                                            },
                                        }}
                                        onPress={() => {
                                            setExportModalVisibility(true);
                                        }}
                                    >
                                        <View
                                            style={{ 
                                                flexDirection: "row", 
                                                alignItems: "center",
                                             }}
                                        >
                                            <Icon
                                                name="download"
                                                size={switchMerchant ? 10 : 20}
                                                color={Colors.whiteColor}
                                            />
                                            <Text
                                                style={{
                                                    color: Colors.whiteColor,
                                                    marginLeft: 5,
                                                    fontSize: switchMerchant ? 10 : 16,
                                                    fontFamily: "NunitoSans-Bold",
                                                }}
                                            >
                                                DOWNLOAD
                                            </Text>
                                        </View>
                                    </TouchableOpacity>

                                    <View
                                        style={[
                                            {
                                                height: switchMerchant ? 35 : 40,
                                            },
                                        ]}
                                    >
                                        <View
                                            style={{
                                                width: switchMerchant ? 200 : 250,
                                                height: switchMerchant ? 35 : 40,
                                                backgroundColor: "white",
                                                borderRadius: 5,
                                                flexDirection: "row",
                                                alignContent: "center",
                                                alignItems: "center",

                                                shadowColor: "#000",
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 3,
                                                borderWidth: 1,
                                                borderColor: "#E5E5E5",
                                                ...simpliFiedLayout && {
                                                    marginTop: 10,
                                                },
                                            }}
                                        >
                                            <Icon
                                                name="search"
                                                size={switchMerchant ? 13 : 18}
                                                color={Colors.primaryColor}
                                                style={{ marginLeft: 15 }}
                                            />
                                            <TextInput
                                                editable={!loading}
                                                underlineColorAndroid={Colors.whiteColor}
                                                style={{
                                                    width: switchMerchant ? 180 : 220,
                                                    fontSize: switchMerchant ? 10 : 15,
                                                    fontFamily: "NunitoSans-Regular",
                                                    paddingLeft: 5,
                                                    height: 45,
                                                }}
                                                placeholderTextColor={Platform.select({
                                                    ios: "#a9a9a9",
                                                })}
                                                clearButtonMode="while-editing"
                                                placeholder=" Search"
                                                onChangeText={(text) => {
                                                    setSearch(text);
                                                }}
                                                value={search}
                                            />
                                        </View>
                                    </View>
                                </View>
                            </View>
                            <View
                                style={{
                                    flexDirection: "row",
                                    justifyContent: showDetails? "space-between": 'flex-start',
                                    alignItems: simpliFiedLayout? 'flex-start':"center",
                                    //marginHorizontal: simpliFiedLayout? 0:30,
                                    marginTop: 30,
                                    width: simpliFiedLayout? windowWidth *0.95: isMobile()? windowWidth * 1.5: windowWidth *0.877,
                                    alignSelf: 'center',
                                    zIndex: 9999,
                                }}
                            >
                                {showDetails &&(
                                <TouchableOpacity
                                    style={[
                                        {
                                            justifyContent: "center",
                                            flexDirection: "row",
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            backgroundColor: "#4E9F7D",
                                            borderRadius: 5,
                                            //width: 160,
                                            paddingHorizontal: 10,
                                            height: switchMerchant ? 35 : 40,
                                            alignItems: "center",
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                            zIndex: -1,

                                            opacity: !showDetails ? 0 : 100,
                                        },
                                    ]}
                                    onPress={() => {
                                        setShowDetails(false);
                                        setPageCount(
                                            Math.ceil(allOutletsEmployeesClock.length / perPage)
                                        );
                                        setCurrentPage(pageReturn);
                                        // console.log('Returning to page');
                                        // console.log(pageReturn);
                                    }}
                                    disabled={!showDetails}
                                >
                                    <AntDesign
                                        name="arrowleft"
                                        size={switchMerchant ? 10 : 20}
                                        color={Colors.whiteColor}
                                        style={{}}
                                    />
                                    <Text
                                        style={{
                                            color: Colors.whiteColor,
                                            marginLeft: 5,
                                            fontSize: switchMerchant ? 10 : 16,
                                            fontFamily: "NunitoSans-Bold",
                                        }}
                                    >
                                        Summary
                                    </Text>
                                </TouchableOpacity>
                                )}

                                <View style={{ flexDirection: "row" }}>
                                    <View
                                        style={[
                                            {
                                                paddingHorizontal: 15,
                                                flexDirection: "row",
                                                alignItems: "center",
                                                borderRadius: 10,
                                                paddingVertical: 10,
                                                justifyContent: "center",
                                                backgroundColor: Colors.whiteColor,
                                                shadowOpacity: 0,
                                                shadowColor: "#000",
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                            },
                                        ]}
                                    >
                                        <View
                                            style={{ alignSelf: "center", marginRight: 5 }}
                                            onPress={() => {
                                                setState({
                                                    pickerMode: "date",
                                                    showDateTimePicker: true,
                                                });
                                            }}
                                        >
                                            <GCalendar
                                                width={switchMerchant ? 15 : 20}
                                                height={switchMerchant ? 15 : 20}
                                            />
                                        </View>

                                        <DatePicker
                                            selected={rev_date.toDate()}
                                            onChange={(date) => {
                                                setRev_date(moment(date));
                                            }}
                                            maxDate={moment(rev_date1).toDate()}
                                            date={moment(rev_date).toDate()}
                                        />

                                        <Text
                                            style={
                                                switchMerchant
                                                    ? { fontSize: 10, fontFamily: "NunitoSans-Regular" }
                                                    : { fontFamily: "NunitoSans-Regular" }
                                            }
                                        >
                                            -
                                        </Text>

                                        <DatePicker
                                            selected={rev_date1.toDate()}
                                            onChange={(date) => {
                                                setRev_date1(moment(date));
                                            }}
                                            minDate={moment(rev_date).toDate()}
                                            date={moment(rev_date1).toDate()}
                                        />
                                    </View>
                                </View>
                            </View>

                            <View>
                                <View
                                    style={{
                                        backgroundColor: Colors.whiteColor,
                                        width: simpliFiedLayout? windowWidth *0.95: isMobile()? windowWidth * 1.5: windowWidth *0.877,
                                        height: 450,
                                        marginTop: 10,
                                        marginBottom: 30,
                                        //marginHorizontal: simpliFiedLayout? 0:30,
                                        alignSelf: 'center',
                                        borderRadius: 5,
                                        shadowOpacity: 0,
                                        shadowColor: "#000",
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 3,
                                    }}
                                >
                                    {!showDetails ? (
                                        <View style={{ marginTop: 10, flexDirection: "row" }}>
                                            <View
                                                style={{
                                                    flexDirection: "row",
                                                    width: simpliFiedLayout? '20%':"5%",
                                                    borderRightWidth: 1,
                                                    borderRightColor: "lightgrey",
                                                    alignItems: "center",
                                                    justifyContent: "flex-start",
                                                    paddingLeft: 10,
                                                }}
                                            >
                                                <View style={{ flexDirection: "row" }}>
                                                    <View style={{ flexDirection: "column" }}>
                                                        <Text
                                                            numberOfLines={2}
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 13,
                                                                fontFamily: "NunitoSans-Bold",
                                                                textAlign: "left",
                                                            }}
                                                        >
                                                            {"No.\n"}
                                                        </Text>
                                                    </View>
                                                </View>
                                            </View>
                                            <View
                                                style={{
                                                    flexDirection: "row",
                                                    width: simpliFiedLayout? '40%':"25%",
                                                    borderRightWidth: 1,
                                                    borderRightColor: "lightgrey",
                                                    alignItems: "center",
                                                    justifyContent: "flex-start",
                                                    padding: 10,
                                                }}
                                            >
                                                <TouchableOpacity
                                                    onPress={() => {
                                                        // if (
                                                        //   currReportSummarySort ===
                                                        //   REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_ASC
                                                        // ) {
                                                        //   setCurrReportSummarySort(
                                                        //     REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_DESC,
                                                        //   );
                                                        // } else {
                                                        //   setCurrReportSummarySort(
                                                        //     REPORT_SORT_FIELD_TYPE.ADD_ON_NAME_ASC,
                                                        //   );
                                                        // }
                                                    }}
                                                >
                                                    <View style={{ flexDirection: "row" }}>
                                                        <View style={{ flexDirection: "column" }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: "NunitoSans-Bold",
                                                                    textAlign: "left",
                                                                }}
                                                            >
                                                                {"Employee Name\n"}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                            <View
                                                style={{
                                                    flexDirection: "row",
                                                    width: simpliFiedLayout? '40%':"25%",
                                                    borderRightWidth: 1,
                                                    borderRightColor: "lightgrey",
                                                    alignItems: "center",
                                                    justifyContent: "flex-start",
                                                    padding: 10,
                                                }}
                                            >
                                                <TouchableOpacity onPress={() => { }}>
                                                    <View style={{ flexDirection: "row" }}>
                                                        <View style={{ flexDirection: "column" }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: "NunitoSans-Bold",
                                                                }}
                                                            >
                                                                {"Clock In/Out Times\n"}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                    ) : (
                                        <View style={{ marginTop: 10, flexDirection: "row" }}>
                                            {!simpliFiedLayout &&(
                                            <View
                                                style={{
                                                    flexDirection: "row",
                                                    width: "6%",
                                                    borderRightWidth: 1,
                                                    borderRightColor: "lightgrey",
                                                    alignItems: "center",
                                                    justifyContent: "flex-start",
                                                    paddingLeft: 10,
                                                }}
                                            >
                                                <View style={{ flexDirection: "row" }}>
                                                    <View style={{ flexDirection: "column" }}>
                                                        <Text
                                                            numberOfLines={2}
                                                            style={{
                                                                fontSize: switchMerchant ? 10 : 13,
                                                                fontFamily: "NunitoSans-Bold",
                                                                textAlign: "left",
                                                            }}
                                                        >
                                                            {"No.\n"}
                                                        </Text>
                                                    </View>
                                                </View>
                                            </View>
                                            )}
                                            <View
                                                style={{
                                                    flexDirection: "row",
                                                    width: simpliFiedLayout? '25%':"23.5%",
                                                    borderRightWidth: 1,
                                                    borderRightColor: "lightgrey",
                                                    alignItems: "center",
                                                    justifyContent: "flex-start",
                                                    paddingLeft: 10,
                                                }}
                                            >
                                                <TouchableOpacity onPress={() => { }}>
                                                    <View style={{ flexDirection: "row" }}>
                                                        <View style={{ flexDirection: "column" }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: "NunitoSans-Bold",
                                                                }}
                                                            >
                                                                {"Day\n"}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                            <View
                                                style={{
                                                    flexDirection: "row",
                                                    width: simpliFiedLayout? '25%':"23.5%",
                                                    borderRightWidth: 1,
                                                    borderRightColor: "lightgrey",
                                                    alignItems: "center",
                                                    justifyContent: "flex-start",
                                                    paddingLeft: 10,
                                                }}
                                            >
                                                <TouchableOpacity onPress={() => { }}>
                                                    <View style={{ flexDirection: "row" }}>
                                                        <View style={{ flexDirection: "column" }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: "NunitoSans-Bold",
                                                                }}
                                                            >
                                                                {"Time In\n"}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>

                                            <View
                                                style={{
                                                    flexDirection: "row",
                                                    width: simpliFiedLayout? '25%':"23.5%",
                                                    borderRightWidth: 1,
                                                    borderRightColor: "lightgrey",
                                                    alignItems: "center",
                                                    justifyContent: "flex-start",
                                                    paddingLeft: 10,
                                                }}
                                            >
                                                <TouchableOpacity onPress={() => { }}>
                                                    <View style={{ flexDirection: "row" }}>
                                                        <View style={{ flexDirection: "column" }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: "NunitoSans-Bold",
                                                                }}
                                                            >
                                                                {"Time Out\n"}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>

                                            <View
                                                style={{
                                                    flexDirection: "row",
                                                    width: simpliFiedLayout? '25%':"23.5%",
                                                    borderRightWidth: 1,
                                                    borderRightColor: "lightgrey",
                                                    alignItems: "center",
                                                    justifyContent: "flex-start",
                                                    padding: 10,
                                                }}
                                            >
                                                <TouchableOpacity onPress={() => { }}>
                                                    <View style={{ flexDirection: "row" }}>
                                                        <View style={{ flexDirection: "column" }}>
                                                            <Text
                                                                numberOfLines={2}
                                                                style={{
                                                                    fontSize: switchMerchant ? 10 : 13,
                                                                    fontFamily: "NunitoSans-Bold",
                                                                }}
                                                            >
                                                                {"Total Hours\n"}
                                                            </Text>
                                                        </View>
                                                    </View>
                                                </TouchableOpacity>
                                            </View>
                                        </View>
                                    )}

                                    {!showDetails ? (
                                        <>
                                            {allOutletsEmployeesClock.filter((item) => {
                                                return filterItem(item);
                                            }).length > 0 ? (
                                                <FlatList
                                                    showsVerticalScrollIndicator={false}
                                                    ref={flatListRef}
                                                    data={allOutletsEmployeesClock
                                                        .filter((item) => {
                                                            return filterItem(item);
                                                        })
                                                        .slice(
                                                            (currentPage - 1) * perPage,
                                                            currentPage * perPage
                                                        )}
                                                    renderItem={renderItem}
                                                    keyExtractor={(item, index) => String(index)}
                                                    style={{ marginTop: 10 }}
                                                />
                                            ) : (
                                                <View
                                                    style={{
                                                        height: windowHeight * 0.4,
                                                    }}
                                                >
                                                    <View
                                                        style={{
                                                            alignItems: "center",
                                                            justifyContent: "center",
                                                            height: "100%",
                                                        }}
                                                    >
                                                        <Text style={{ color: Colors.descriptionColor }}>
                                                            - No Data Available -
                                                        </Text>
                                                    </View>
                                                </View>
                                            )}
                                        </>
                                    ) : (
                                        <>
                                            {allOutletsEmployeesDetails.filter((item) => {
                                                return filterItemDetails(item);
                                            }).length > 0 ? (
                                                <FlatList
                                                    showsVerticalScrollIndicator={false}
                                                    ref={flatListRef}
                                                    data={allOutletsEmployeesDetails
                                                        .filter((item) => {
                                                            return filterItemDetails(item);
                                                        })
                                                        .slice(
                                                            (currentDetailsPage - 1) * perPage,
                                                            currentDetailsPage * perPage
                                                        )}
                                                    renderItem={renderItemDetails}
                                                    keyExtractor={(item, index) => String(index)}
                                                    style={{ marginTop: 10 }}
                                                />
                                            ) : (
                                                <View
                                                    style={{
                                                        height: windowHeight * 0.4,
                                                    }}
                                                >
                                                    <View
                                                        style={{
                                                            alignItems: "center",
                                                            justifyContent: "center",
                                                            height: "100%",
                                                        }}
                                                    >
                                                        <Text style={{ color: Colors.descriptionColor }}>
                                                            - No Data Available -
                                                        </Text>
                                                    </View>
                                                </View>
                                            )}
                                        </>
                                    )}
                                </View>

                                {!showDetails ? (
                                    <View
                                    style ={{
                                        flexDirection: 'row',
                                        width: simpliFiedLayout? windowWidth*0.95: isMobile()? windowWidth * 1.5: windowWidth *0.877,
                                        alignItems: 'center',
                                        alignSelf: 'center',
                                        justifyContent: 'flex-end',
                                        // ...isMobile() && {
                                        //     width: '100%',
                                        //   },
                                        ...simpliFiedLayout && {
                                            alignSelf: 'flex-start',
                                            justifyContent: 'flex-start',
                                            alignItems: 'flex-start',
                                            flexDirection: 'column',
                                            marginBottom: 25,
                                            marginLeft: 15,
                                          }
                                    }}>
                                        <View
                                            style={{ flexDirection: 'row', alignItems: 'center', width: simpliFiedLayout? '100%': {}, }}
                                        >
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    fontFamily: "NunitoSans-Bold",
                                                    marginRight: "1%",
                                                }}
                                            >
                                                Items Showed
                                            </Text>
                                            <View
                                                style={{
                                                    width: Platform.OS === "ios" ? 65 : "13%", //65,
                                                    height: switchMerchant ? 20 : 35,
                                                    backgroundColor: Colors.whiteColor,
                                                    borderRadius: 10,
                                                    justifyContent: "center",
                                                    paddingHorizontal: Platform.OS === "ios" ? 0 : 0,
                                                    //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                                                    // paddingTop: '-60%',
                                                    borderWidth: 1,
                                                    borderColor: "#E5E5E5",
                                                    marginRight: "1%",
                                                    ...simpliFiedLayout && {
                                                        width: 65,
                                                      }
                                                }}
                                            >
                                                <DropDownPicker
                                                    style={{
                                                        backgroundColor: Colors.fieldtBgColor,
                                                        width: '100%',
                                                        height: 40,
                                                        borderRadius: 10,
                                                        borderWidth: 1,
                                                        borderColor: "#E5E5E5",
                                                        flexDirection: "row",
                                                    }}
                                                    dropDownContainerStyle={{
                                                        width: '100%',
                                                        backgroundColor: Colors.fieldtBgColor,
                                                        borderColor: "#E5E5E5",
                                                    }}
                                                    labelStyle={{
                                                        marginLeft: 5,
                                                        flexDirection: "row",
                                                    }}
                                                    textStyle={{
                                                        fontSize: 14,
                                                        fontFamily: 'NunitoSans-Regular',

                                                        marginLeft: 5,
                                                        paddingVertical: 10,
                                                        flexDirection: "row",
                                                    }}
                                                    selectedItemContainerStyle={{
                                                        flexDirection: "row",
                                                    }}

                                                    showArrowIcon={true}
                                                    ArrowDownIconComponent={({ style }) => (
                                                        <Ionicon
                                                            size={25}
                                                            color={Colors.fieldtTxtColor}
                                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                            name="chevron-down-outline"
                                                        />
                                                    )}
                                                    ArrowUpIconComponent={({ style }) => (
                                                        <Ionicon
                                                            size={25}
                                                            color={Colors.fieldtTxtColor}
                                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                            name="chevron-up-outline"
                                                        />
                                                    )}

                                                    showTickIcon={true}
                                                    TickIconComponent={({ press }) => (
                                                        <Ionicon
                                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                            color={
                                                                press ? Colors.fieldtBgColor : Colors.primaryColor
                                                            }
                                                            name={'md-checkbox'}
                                                            size={25}
                                                        />
                                                    )}
                                                    placeholder={'Select a Type'}
                                                    placeholderStyle={{
                                                        color: Colors.fieldtTxtColor,
                                                        // marginTop: 15,
                                                    }}
                                                    // searchable
                                                    // searchableStyle={{
                                                    //   paddingHorizontal: windowWidth * 0.0079,
                                                    // }}
                                                    value={perPage}
                                                    items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                                                        label: 'All',
                                                        value: !showDetails
                                                            ? allOutletsEmployeesClock.length
                                                            : allOutletsEmployeesDetails.length,
                                                    })}
                                                    // multiple={true}
                                                    // multipleText={`${item.tagIdList.length} Tag(s)`}
                                                    onSelectItem={(item) => {
                                                        setPerPage(item.value);
                                                        // var currentPageTemp =
                                                        //   text.length > 0 ? parseInt(text) : 1;

                                                        // setCurrentPage(
                                                        //   currentPageTemp > pageCount
                                                        //     ? pageCount
                                                        //     : currentPageTemp < 1
                                                        //       ? 1
                                                        //       : currentPageTemp,
                                                        // );
                                                    }}
                                                    open={openPage}
                                                    setOpen={setOpenPage}
                                                    dropDownDirection="TOP"
                                                />
                                            </View>
                                        </View>
                                        <View style = {{flexDirection: 'row', alignItems: 'center', marginVertical: 10, width: simpliFiedLayout? '100%': {}, }}>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    fontFamily: "NunitoSans-Bold",
                                                    marginRight: "1%",
                                                }}
                                            >
                                                Page
                                            </Text>
                                            <View
                                                style={{
                                                    width: switchMerchant ? 65 : 70,
                                                    height: switchMerchant ? 20 : 35,
                                                    backgroundColor: Colors.whiteColor,
                                                    borderRadius: 10,
                                                    justifyContent: "center",
                                                    paddingHorizontal: 22,
                                                    borderWidth: 1,
                                                    borderColor: "#E5E5E5",
                                                }}
                                            >
                                                {console.log("currentPage")}
                                                {console.log(currentPage)}

                                                <TextInput
                                                    onChangeText={(text) => {
                                                        var currentPageTemp =
                                                            text.length > 0 ? parseInt(text) : 1;

                                                        setCurrentPage(
                                                            currentPageTemp > pageCount
                                                                ? pageCount
                                                                : currentPageTemp < 1
                                                                    ? 1
                                                                    : currentPageTemp
                                                        );
                                                    }}
                                                    placeholder={
                                                        pageCount !== 0 ? currentPage.toString() : "0"
                                                    }
                                                    placeholderTextColor={Platform.select({
                                                        ios: "#a9a9a9",
                                                    })}
                                                    style={{
                                                        color: "black",
                                                        fontSize: switchMerchant ? 10 : 14,
                                                        fontFamily: "NunitoSans-Regular",
                                                        marginTop: Platform.OS === "ios" ? 0 : -15,
                                                        marginBottom: Platform.OS === "ios" ? 0 : -15,
                                                        textAlign: "center",
                                                        width: "100%",
                                                    }}
                                                    value={pageCount !== 0 ? currentPage.toString() : "0"}
                                                    defaultValue={
                                                        pageCount !== 0 ? currentPage.toString() : "0"
                                                    }
                                                    keyboardType={"numeric"}
                                                    onFocus={() => {
                                                        setPushPagingToTop(true);
                                                    }}
                                                />
                                            </View>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    fontFamily: "NunitoSans-Bold",
                                                    marginLeft: "1%",
                                                    marginRight: "1%",
                                                }}
                                            >
                                                of {pageCount}
                                            </Text>
                                            <TouchableOpacity
                                                style={{
                                                    width: switchMerchant ? 30 : 45,
                                                    height: switchMerchant ? 20 : 28,
                                                    backgroundColor: Colors.primaryColor,
                                                    alignItems: "center",
                                                    justifyContent: "center",
                                                }}
                                                onPress={() => {
                                                    prevPage();
                                                }}
                                            >
                                                <ArrowLeft color={Colors.whiteColor} />
                                            </TouchableOpacity>
                                            <TouchableOpacity
                                                style={{
                                                    width: switchMerchant ? 30 : 45,
                                                    height: switchMerchant ? 20 : 28,
                                                    backgroundColor: Colors.primaryColor,
                                                    alignItems: "center",
                                                    justifyContent: "center",
                                                }}
                                                onPress={() => {
                                                    nextPage();
                                                }}
                                            >
                                                <ArrowRight color={Colors.whiteColor} />
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                ) : (
                                    <View
                                    style ={{
                                        flexDirection: 'row',
                                        width: simpliFiedLayout? windowWidth*0.95: isMobile()? windowWidth * 1.5: windowWidth *0.877,
                                        alignItems: 'center',
                                        alignSelf: 'center',
                                        justifyContent: 'flex-end',
                                        // ...isMobile() && {
                                        //     width: '100%',
                                        //   },
                                        ...simpliFiedLayout && {
                                            alignSelf: 'flex-start',
                                            justifyContent: 'flex-start',
                                            alignItems: 'flex-start',
                                            flexDirection: 'column',
                                            marginBottom: 25,
                                            marginLeft: 15,
                                          }
                                    }}>
                                        <View
                                            style={{ flexDirection: 'row', alignItems: 'center', width: simpliFiedLayout? '100%': {}, }}
                                        >
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    fontFamily: "NunitoSans-Bold",
                                                    marginRight: "1%",
                                                }}
                                            >
                                                Items Showed
                                            </Text>
                                            <View
                                                style={{
                                                    width: Platform.OS === "ios" ? 65 : "13%", //65,
                                                    height: switchMerchant ? 20 : 35,
                                                    backgroundColor: Colors.whiteColor,
                                                    borderRadius: 10,
                                                    justifyContent: "center",
                                                    paddingHorizontal: Platform.OS === "ios" ? 0 : 0,
                                                    //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                                                    // paddingTop: '-60%',
                                                    borderWidth: 1,
                                                    borderColor: "#E5E5E5",
                                                    marginRight: "1%",
                                                    ...simpliFiedLayout && {
                                                        width: 65,
                                                      }
                                                }}
                                            >
                                                <DropDownPicker
                                                    style={{
                                                        backgroundColor: Colors.fieldtBgColor,
                                                        width: '100%',
                                                        height: 40,
                                                        borderRadius: 10,
                                                        borderWidth: 1,
                                                        borderColor: "#E5E5E5",
                                                        flexDirection: "row",
                                                    }}
                                                    dropDownContainerStyle={{
                                                        width: '100%',
                                                        backgroundColor: Colors.fieldtBgColor,
                                                        borderColor: "#E5E5E5",
                                                    }}
                                                    labelStyle={{
                                                        marginLeft: 5,
                                                        flexDirection: "row",
                                                    }}
                                                    textStyle={{
                                                        fontSize: 14,
                                                        fontFamily: 'NunitoSans-Regular',

                                                        marginLeft: 5,
                                                        paddingVertical: 10,
                                                        flexDirection: "row",
                                                    }}
                                                    selectedItemContainerStyle={{
                                                        flexDirection: "row",
                                                    }}

                                                    showArrowIcon={true}
                                                    ArrowDownIconComponent={({ style }) => (
                                                        <Ionicon
                                                            size={25}
                                                            color={Colors.fieldtTxtColor}
                                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                            name="chevron-down-outline"
                                                        />
                                                    )}
                                                    ArrowUpIconComponent={({ style }) => (
                                                        <Ionicon
                                                            size={25}
                                                            color={Colors.fieldtTxtColor}
                                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                            name="chevron-up-outline"
                                                        />
                                                    )}

                                                    showTickIcon={true}
                                                    TickIconComponent={({ press }) => (
                                                        <Ionicon
                                                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                                                            color={
                                                                press ? Colors.fieldtBgColor : Colors.primaryColor
                                                            }
                                                            name={'md-checkbox'}
                                                            size={25}
                                                        />
                                                    )}
                                                    placeholder={'Select a Type'}
                                                    placeholderStyle={{
                                                        color: Colors.fieldtTxtColor,
                                                        // marginTop: 15,
                                                    }}
                                                    // searchable
                                                    // searchableStyle={{
                                                    //   paddingHorizontal: windowWidth * 0.0079,
                                                    // }}
                                                    value={perPage}
                                                    items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                                                        label: 'All',
                                                        value: !showDetails
                                                            ? allOutletsEmployeesClock.length
                                                            : allOutletsEmployeesDetails.length,
                                                    })}
                                                    // multiple={true}
                                                    // multipleText={`${item.tagIdList.length} Tag(s)`}
                                                    onSelectItem={(item) => {
                                                        setPerPage(item.value);
                                                        // var currentPageTemp =
                                                        //   text.length > 0 ? parseInt(text) : 1;

                                                        // setCurrentPage(
                                                        //   currentPageTemp > pageCount
                                                        //     ? pageCount
                                                        //     : currentPageTemp < 1
                                                        //       ? 1
                                                        //       : currentPageTemp,
                                                        // );
                                                    }}
                                                    open={openPage}
                                                    setOpen={setOpenPage}
                                                    dropDownDirection="TOP"
                                                />
                                            </View>
                                        </View>
                                        <View style = {{flexDirection: 'row', alignItems: 'center', marginVertical: 10, width: simpliFiedLayout? '100%': {}, }}>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    fontFamily: "NunitoSans-Bold",
                                                    marginRight: "1%",
                                                }}
                                            >
                                                Page
                                            </Text>
                                            <View
                                                style={{
                                                    width: switchMerchant ? 65 : 70,
                                                    height: switchMerchant ? 20 : 35,
                                                    backgroundColor: Colors.whiteColor,
                                                    borderRadius: 10,
                                                    justifyContent: "center",
                                                    paddingHorizontal: 22,
                                                    borderWidth: 1,
                                                    borderColor: "#E5E5E5",
                                                }}
                                            >
                                                {console.log("currentDetailsPage")}
                                                {console.log(currentDetailsPage)}

                                                <TextInput
                                                    onChangeText={(text) => {
                                                        var currentPageTemp =
                                                            text.length > 0 ? parseInt(text) : 1;
                                                        // console.log('currentDetailsPage pending');
                                                        // console.log(
                                                        //     currentPageTemp > pageCount
                                                        //         ? pageCount
                                                        //         : currentPageTemp < 1
                                                        //             ? 1
                                                        //             : currentPageTemp,
                                                        // );
                                                        setCurrentDetailsPage(
                                                            currentPageTemp > pageCount
                                                                ? pageCount
                                                                : currentPageTemp < 1
                                                                    ? 1
                                                                    : currentPageTemp
                                                        );
                                                    }}
                                                    placeholder={
                                                        pageCount !== 0
                                                            ? currentDetailsPage.toString()
                                                            : "0"
                                                    }
                                                    placeholderTextColor={Platform.select({
                                                        ios: "#a9a9a9",
                                                    })}
                                                    style={{
                                                        color: "black",
                                                        fontSize: switchMerchant ? 10 : 14,
                                                        fontFamily: "NunitoSans-Regular",
                                                        marginTop: Platform.OS === "ios" ? 0 : -15,
                                                        marginBottom: Platform.OS === "ios" ? 0 : -15,
                                                        textAlign: "center",
                                                        width: "100%",
                                                    }}
                                                    value={
                                                        pageCount !== 0
                                                            ? currentDetailsPage.toString()
                                                            : "0"
                                                    }
                                                    defaultValue={
                                                        pageCount !== 0
                                                            ? currentDetailsPage.toString()
                                                            : "0"
                                                    }
                                                    keyboardType={"numeric"}
                                                    onFocus={() => {
                                                        setPushPagingToTop(true);
                                                    }}
                                                />
                                            </View>
                                            <Text
                                                style={{
                                                    fontSize: switchMerchant ? 10 : 14,
                                                    fontFamily: "NunitoSans-Bold",
                                                    marginLeft: "1%",
                                                    marginRight: "1%",
                                                }}
                                            >
                                                of {pageCount}
                                            </Text>
                                            <TouchableOpacity
                                                style={{
                                                    width: switchMerchant ? 30 : 45,
                                                    height: switchMerchant ? 20 : 28,
                                                    backgroundColor: Colors.primaryColor,
                                                    alignItems: "center",
                                                    justifyContent: "center",
                                                }}
                                                onPress={() => {
                                                    prevDetailsPage();
                                                }}
                                            >
                                                <ArrowLeft color={Colors.whiteColor} />
                                            </TouchableOpacity>
                                            <TouchableOpacity
                                                style={{
                                                    width: switchMerchant ? 30 : 45,
                                                    height: switchMerchant ? 20 : 28,
                                                    backgroundColor: Colors.primaryColor,
                                                    alignItems: "center",
                                                    justifyContent: "center",
                                                }}
                                                onPress={() => {
                                                    nextDetailsPage();
                                                }}
                                            >
                                                <ArrowRight color={Colors.whiteColor} />
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                )}
                            </View>
                        {/* </View> */}
                    </View>
                    </ScrollView>
                </Animated.ScrollView>
            </View>
        </View>
        </View>
        //</UserIdleWrapper >
        
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: "row",
    },
    sidebar: {
        // width: Dimensions.get("window").width * Styles.sideBarWidth,
        // shadowColor: "#000",
        // shadowOffset: {
        //     width: 0,
        //     height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    content: {
        padding: 20,
        width: Dimensions.get("window").width * (1 - Styles.sideBarWidth),
        backgroundColor: Colors.highlightColor,
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    confirmBox: {
        // width: '30%',
        // height: '30%',
        // borderRadius: 30,
        // backgroundColor: Colors.whiteColor,
        width: Dimensions.get("window").width * 0.4,
        height: Dimensions.get("window").height * 0.3,
        borderRadius: 12,
        backgroundColor: Colors.whiteColor,
        justifyContent: "space-between",
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: "center",
        justifyContent: "center",
    },
    modalView: {
        height: Dimensions.get("window").width * 0.2,
        width: Dimensions.get("window").width * 0.3,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Dimensions.get("window").width * 0.03,
        alignItems: "center",
        justifyContent: "center",
    },
    closeButton: {
        position: "absolute",
        right: Dimensions.get("window").width * 0.02,
        top: Dimensions.get("window").width * 0.02,

        elevation: 1000,
        zIndex: 1000,
    },
    modalTitle: {
        alignItems: "center",
        top: "20%",
        position: "absolute",
    },
    modalBody: {
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
    },
    modalTitleText: {
        fontFamily: "NunitoSans-Bold",
        textAlign: "center",
        fontSize: 20,
    },
    modalDescText: {
        fontFamily: "NunitoSans-SemiBold",
        fontSize: 18,
        color: Colors.fieldtTxtColor,
    },
    modalBodyText: {
        flex: 1,
        fontFamily: "NunitoSans-SemiBold",
        fontSize: 25,
        width: "20%",
    },
    modalSaveButton: {
        width: Dimensions.get("window").width * 0.15,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,

        marginVertical: 10,
    },
    modalSaveButton1: {
        width: Dimensions.get("window").width * 0.1,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: "center",
        justifyContent: "center",
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,

        marginVertical: 10,
    },
    confirmBox1: {
        width: Dimensions.get("window").width * 0.4,
        height: Dimensions.get("window").height * 0.4,
        borderRadius: 12,
        backgroundColor: Colors.whiteColor,
        justifyContent: "space-between",
    },
    submitText: {
        height:
            Platform.OS == "ios"
                ? Dimensions.get("window").height * 0.06
                : Dimensions.get("window").height * 0.07,
        paddingVertical: 5,
        paddingHorizontal: 20,
        flexDirection: "row",
        color: "#4cd964",
        textAlign: "center",
        borderRadius: 10,
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        justifyContent: "center",
        alignContent: "center",
        alignItems: "center",
        marginRight: 10,
    },
    headerLeftStyle: {
        width: useWindowDimensions.width * 0.17,
        justifyContent: "center",
        alignItems: "center",
    },
});
export default EmployeeTimeSheet;
