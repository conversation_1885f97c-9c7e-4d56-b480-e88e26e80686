import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  TouchableOpacity,
  Dimensions,
  Switch,
  Platform,
  Modal,
  KeyboardAvoidingView,
  TextInput,
  ActivityIndicator,
  Picker,
  useWindowDimensions,
  Animated,
} from "react-native";
import Colors from "../constant/Colors";
import SideBar from "./SideBar";
import Icon from "react-native-vector-icons/Feather";
import Ionicon from "react-native-vector-icons/Ionicons";
import AntDesign from "react-native-vector-icons/AntDesign";
import Entypo from "react-native-vector-icons/Entypo";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";

import { ReactComponent as ArrowLeft } from "../assets/svg/ArrowLeft.svg";
import { ReactComponent as ArrowRight } from "../assets/svg/ArrowRight.svg";
import { ReactComponent as FastFoodOutLine } from "../assets/svg/FastFoodOutLine.svg";
import EvilIcons from "react-native-vector-icons/EvilIcons";
import { ReactComponent as GCalendar } from "../assets/svg/GCalendar.svg";
import { FlatList } from "react-native-gesture-handler";
import API from "../constant/API";
import ApiClient from "../util/ApiClient";
import * as User from "../util/User";
import AsyncStorage from "@react-native-async-storage/async-storage";
// import CheckBox from 'react-native-check-box';
import moment, { months } from "moment";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import Styles from "../constant/Styles";
// import { isTablet } from 'react-native-device-detection';
import { OutletStore } from "../store/outletStore";
import { MerchantStore } from "../store/merchantStore";
import {
  EMAIL_REPORT_TYPE,
  ORDER_TYPE,
  ORDER_TYPE_PARSED,
  REPORT_SORT_FIELD_TYPE,
  TABLE_PAGE_SIZE_DROPDOWN_LIST,
  EXPAND_TAB_TYPE,
  PRODUCT_PRICE_TYPE,
  UNIT_TYPE_SHORT,
  APP_TYPE,
  ORDER_TYPE_SUB
} from "../constant/common";
import { UserStore } from "../store/userStore";
// import Upload from '../assets/svg/Upload';
// import Download from '../assets/svg/Download';
import {
  convertArrayToCSV,
  generateEmailReport,
  sortReportDataList,
  sliceUnicodeStringV2WithDots,
  getOrderDiscountInfo,
  getOrderDiscountInfoInclOrderBased,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  getCartItemPriceWithoutAddOn,
  getAddOnChoiceQuantity,
  getAddOnChoicePrice,
} from "../util/common";
// import RNFetchBlob from 'rn-fetch-blob';
import XLSX from "xlsx";
// import { useKeyboard } from '../hooks';
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
import { CommonStore } from "../store/commonStore";

// import RNPickerSelect from 'react-native-picker-select';
import AsyncImage from "../components/asyncImage";
import Feather from "react-native-vector-icons/Feather";
// import Tooltip from 'react-native-walkthrough-tooltip';
// import { useFocusEffect } from '@react-navigation/native';
// import UserIdleWrapper from '../components/userIdleWrapper';
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "../constant/datePicker.css";
import { CSVLink } from "react-csv";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";
import Select from "react-select";
import { Collections } from '../constant/firebase';
import firebase from "firebase";
import DropDownPicker from "react-native-dropdown-picker";
import TopBar from "./TopBar";
import { isMobile } from '../util/common';
import { SvgFromXml } from "react-native-svg";

const { nanoid } = require("nanoid");
// const RNFS = require('react-native-fs');
const ReportSalesTransaction = (props) => {
  //port til 23 june 2023 changes
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  // const [isMounted, setIsMounted] = useState(true);

  // useFocusEffect(
  //     useCallback(() => {
  //         setIsMounted(true);
  //         return () => {
  //             setIsMounted(false);
  //         };
  //     }, [])
  // );

  ///////////////////////////////////////////////////////////

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView
  );
  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  // navigation.dangerouslyGetParent().setOptions({
  //     tabBarVisible: false,
  // });

  const [oriList, setOriList] = useState([]);
  const [list, setList] = useState([]);
  const [page, setPage] = useState(0);
  const [name, setName] = useState("Orders Channel");
  const [visible, setVisible] = useState(false);
  const [visible1, setVisible1] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [isChecked1, setIsChecked1] = useState(false);
  const [endDate, setEndDate] = useState(new Date());
  const [startDate, setStartDate] = useState(new Date());
  const [offset, setOffset] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [pageCount, setPageCount] = useState(0);
  const [detailsPageCount, setDetailsPageCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentDetailsPage, setCurrentDetailsPage] = useState(1);
  const [pageReturn, setPageReturn] = useState(1);
  const [day, setDay] = useState(false);
  const [pick, setPick] = useState(null);
  const [pick1, setPick1] = useState(null);
  const [search, setSearch] = useState("");
  const [list1, setList1] = useState(true);
  const [searchList, setSearchList] = useState(false);
  const [lists, setLists] = useState([]);

  const [loading, setLoading] = useState(false);
  const [pushPagingToTop, setPushPagingToTop] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState(false);

  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
  const [pickerMode, setPickerMode] = useState("datetime");
  const [rev_date, setRev_date] = useState(
    moment().subtract(6, "days").startOf("day")
  );
  const [rev_date1, setRev_date1] = useState(
    moment().endOf(Date.now()).endOf("day")
  );

  const reportOutletShifts = OutletStore.useState((s) => s.reportOutletShifts);
  const reportDisplayType = OutletStore.useState((s) => s.reportDisplayType);
  const historyStartDate = CommonStore.useState(s => s.historyStartDate);
  const historyEndDate = CommonStore.useState(s => s.historyEndDate);

  const [transactionTypeSales, setTransactionTypeSales] = useState([]);

  const [expandDetailsDict, setExpandDetailsDict] = useState({});
  const crmUsers = OutletStore.useState((s) => s.crmUsers);

  // 2023-04-14
  const payoutTransactions = OutletStore.useState(s => s.payoutTransactions.filter(p => p.v >= '3')); // only check for v3
  const payoutTransactionsExtend = OutletStore.useState(s => s.payoutTransactionsExtend.filter(p => p.v >= '3')); // only check for v3

  const ptTimestamp = OutletStore.useState(s => s.ptTimestamp);
  const pteTimestamp = OutletStore.useState(s => s.pteTimestamp);

  const allOutletsUserOrdersDoneRaw = OutletStore.useState((s) => s.allOutletsUserOrdersDone,);
  const [allOutletsUserOrdersDone, setAllOutletsUserOrdersDone] = useState([]);

  // const allOutletsUserOrdersDone = OutletStore.useState(
  //   (s) => s.allOutletsUserOrdersDone,
  // );

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);

  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);

  const [exportEmail, setExportEmail] = useState("");
  const [CsvData, setCsvData] = useState([]);

  const [showDetails, setShowDetails] = useState(false);
  const [transactionTypeSalesDetails, setTransactionTypeSalesDetails] =
    useState([]);

  const [selectedItemSummary, setSelectedItemSummary] = useState({});

  const [exportModalVisibility, setExportModalVisibility] = useState(false);

  const [currReportSummarySort, setCurrReportSummarySort] = useState("");
  const [currReportDetailsSort, setCurrReportDetailsSort] = useState("");

  const merchantId = UserStore.useState((s) => s.merchantId);
  const isLoading = CommonStore.useState((s) => s.isLoading);
  const [isCsv, setIsCsv] = useState(false);
  const [isExcel, setIsExcel] = useState(false);

  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus
  );

  const [filterAppType, setFilterAppType] = useState([APP_TYPE.WEB_ORDER, APP_TYPE.MERCHANT, APP_TYPE.USER, APP_TYPE.WAITER]);
  const [openFA, setOpenFA] = useState(false);
  const [openPage, setOpenPage] = useState(false);
  const [openOS, setOpenOS] = useState(false);
  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets
  const selectedOutletList = CommonStore.useState((s) => s.reportOutletIdList);
  const isMasterAccount = UserStore.useState((s) => s.isMasterAccount);
  const [outletDropdownList, setOutletDropdownList] = useState([]);
  const [selectedOutletId, setSelectedOutletId] = useState("");

  const simpliFiedLayout = CommonStore.useState((s) => s.simpliFiedLayout);

  // const [outletDropdownList, setOutletDropdownList] = useState([]);
  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  // var outletNames = [];

  // for (var i = 0; i < allOutlets.length; i++) {
  //     for (var j = 0; j < selectedOutletList.length; j++) {
  //         if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
  //             outletNames.push(allOutlets[i].name);
  //             break;
  //         }
  //     }
  // }

  // useEffect(() => {
  //     setOutletDropdownList(
  //         allOutlets.map((item) => {
  //             return { label: item.name, value: item.uniqueId };
  //         })
  //     );
  // }, [allOutlets]);

  var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));

  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.outletSelectDropdownView = () => {
  //       return (
  //         <View
  //           style={{
  //             flexDirection: "row",
  //             alignItems: "center",
  //             borderRadius: 8,
  //             width: 200,
  //             backgroundColor: "white",
  //           }}
  //         >
  //           {currOutletId.length > 0 &&
  //             allOutlets.find((item) => item.uniqueId === currOutletId) ? (
  //             <MultiSelect
  //               clearable={false}
  //               singleSelect={true}
  //               defaultValue={currOutletId}
  //               placeholder={"Choose Outlet"}
  //               onChange={(value) => {
  //                 if (value) { // if choose the same option again, value = ''
  //                   MerchantStore.update((s) => {
  //                     s.currOutletId = value;
  //                     s.currOutlet =
  //                       allOutlets.find(
  //                         (outlet) => outlet.uniqueId === value
  //                       ) || {};
  //                   });
  //                 }

  //                 CommonStore.update((s) => {
  //                   s.shiftClosedModal = false;
  //                 });
  //               }}
  //               options={targetOutletDropdownListTemp}
  //               className="msl-varsHEADER"
  //             />
  //           ) : (
  //             <ActivityIndicator size={"small"} color={Colors.whiteColor} />
  //           )}

  //           {/* <Select

  //                           placeholder={"Choose Outlet"}
  //                           onChange={(items) => {
  //                               setSelectedOutletList(items);
  //                           }}
  //                           options={outletDropdownList}
  //                           isMulti
  //                       /> */}
  //         </View>
  //       );
  //     };
  //   });
  // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
        style={{
          width: 124,
          height: 26,
        }}
        resizeMode="contain"
        source={require('../assets/image/logo.png')}
      /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Orders Channel Report
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo Manager")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
            style={{
              width: windowHeight * 0.05,
            height: windowHeight * 0.05,
              alignSelf: 'center',
            }}
            source={require('../assets/image/profile-pic.jpg')}
          /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  useEffect(() => {
    if (currOutletId !== "" && allOutlets.length > 0) {
      var transactionTypeSalesDict = {
        [ORDER_TYPE.DELIVERY]: {
          summaryId: nanoid(),
          orderType: ORDER_TYPE_PARSED[ORDER_TYPE.DELIVERY],
          totalTransactions: 0,
          totalSales: 0,
          totalDiscount: 0,
          discount: 0,
          tax: 0,
          serviceCharge: 0,
          gp: 0,
          netSales: 0,
          averageNetSales: 0,
          detailsList: [],
        },
        [ORDER_TYPE.PICKUP]: {
          summaryId: nanoid(),
          orderType: ORDER_TYPE_PARSED[ORDER_TYPE.PICKUP],
          totalTransactions: 0,
          totalSales: 0,
          totalDiscount: 0,
          discount: 0,
          tax: 0,
          serviceCharge: 0,
          gp: 0,
          netSales: 0,
          averageNetSales: 0,
          detailsList: [],
        },
        [ORDER_TYPE.DINEIN]: {
          summaryId: nanoid(),
          orderType: ORDER_TYPE_PARSED[ORDER_TYPE.DINEIN],
          totalTransactions: 0,
          totalSales: 0,
          totalDiscount: 0,
          discount: 0,
          tax: 0,
          serviceCharge: 0,
          gp: 0,
          netSales: 0,
          averageNetSales: 0,
          detailsList: [],
        },
        [ORDER_TYPE.PRE_ORDER]: {
          summaryId: nanoid(),
          orderType: ORDER_TYPE_PARSED[ORDER_TYPE.PRE_ORDER],
          totalSales: 0,
          totalTransactions: 0,
          totalDiscount: 0,
          discount: 0,
          tax: 0,
          serviceCharge: 0,
          gp: 0,
          netSales: 0,
          averageNetSales: 0,
          detailsList: [],
        },
        [ORDER_TYPE_SUB.OTHER_DELIVERY]: {
          summaryId: nanoid(),
          orderType: ORDER_TYPE_SUB.OTHER_DELIVERY,
          totalSales: 0,
          totalTransactions: 0,
          totalDiscount: 0,
          discount: 0,
          tax: 0,
          serviceCharge: 0,
          gp: 0,
          netSales: 0,
          averageNetSales: 0,
          detailsList: [],
        },
        //[ORDER_TYPE.TAKEAWAY]: {
        //  summaryId: nanoid(),
        //  orderType: ORDER_TYPE_PARSED[ORDER_TYPE.TAKEAWAY],
        //  totalSales: 0,
        //  totalTransactions: 0,
        //  totalDiscount: 0,
        //  discount: 0,
        //  tax: 0,
        //  serviceCharge: 0,
        //  gp: 0,
        // netSales: 0,
        //  averageNetSales: 0,
        //  detailsList: [],
        //},
      };

      for (var i = 0; i < allOutletsUserOrdersDone.length; i++) {
        if (
          (
            isMasterAccount && (selectedOutletList.includes(allOutletsUserOrdersDone[i].outletId))
            ||
            (!isMasterAccount && allOutletsUserOrdersDone[i].outletId === currOutletId)
          ) &&
          (moment(historyStartDate).isSameOrBefore(
            allOutletsUserOrdersDone[i].createdAt,
          ) &&
            moment(historyEndDate).isAfter(
              allOutletsUserOrdersDone[i].createdAt
            )) &&
          moment(historyEndDate).isAfter(
            allOutletsUserOrdersDone[i].createdAt
          ) &&
          allOutletsUserOrdersDone[i].finalPrice > 0
          // &&
          // (filterAppType.includes(allOutletsUserOrdersDone[i].appType) || filterAppType.length === 0)
        ) {
          if (allOutletsUserOrdersDone[i].orderType === ORDER_TYPE.PICKUP && allOutletsUserOrdersDone[i].orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY) {
            transactionTypeSalesDict[allOutletsUserOrdersDone[i].orderTypeSub].totalTransactions += 1

            transactionTypeSalesDict[
              allOutletsUserOrdersDone[i].orderTypeSub
            ].totalSales += (allOutletsUserOrdersDone[i].finalPriceBefore
              ? allOutletsUserOrdersDone[i].finalPriceBefore
              : allOutletsUserOrdersDone[i].finalPrice) + getOrderDiscountInfo(allOutletsUserOrdersDone[i]);

            transactionTypeSalesDict[
              allOutletsUserOrdersDone[i].orderTypeSub
            ].totalDiscount += getOrderDiscountInfoInclOrderBased(allOutletsUserOrdersDone[i]);

            // transactionTypeSalesDict[allOutletsUserOrdersDone[i].orderType].discount += allOutletsUserOrdersDone[i].discount / allOutletsUserOrdersDone[i].totalPrice * 100;
            transactionTypeSalesDict[
              allOutletsUserOrdersDone[i].orderTypeSub
            ].tax += allOutletsUserOrdersDone[i].tax;

            transactionTypeSalesDict[
              allOutletsUserOrdersDone[i].orderTypeSub
            ].serviceCharge += allOutletsUserOrdersDone[i].sc || 0;

            transactionTypeSalesDict[
              allOutletsUserOrdersDone[i].orderTypeSub
            ].gp += 0;

            transactionTypeSalesDict[
              allOutletsUserOrdersDone[i].orderTypeSub
            ].netSales +=
              (allOutletsUserOrdersDone[i].finalPriceBefore
                ? allOutletsUserOrdersDone[i].finalPriceBefore
                : allOutletsUserOrdersDone[i].finalPrice) -
              allOutletsUserOrdersDone[i].tax -
              (allOutletsUserOrdersDone[i].sc
                ? allOutletsUserOrdersDone[i].sc
                : 0);

            const calculatedDiscount = getOrderDiscountInfoInclOrderBased(allOutletsUserOrdersDone[i]);

            transactionTypeSalesDict[
              allOutletsUserOrdersDone[i].orderTypeSub
            ].detailsList.push({
              ...allOutletsUserOrdersDone[i],
              discountPercentage: parseFloat(
                isFinite(
                  calculatedDiscount /
                  // allOutletsUserOrdersDone[i].totalPrice,
                  (allOutletsUserOrdersDone[i].finalPrice +
                    calculatedDiscount),
                )
                  ? (calculatedDiscount /
                    // allOutletsUserOrdersDone[i].totalPrice) *
                    (allOutletsUserOrdersDone[i].finalPrice +
                      calculatedDiscount)) *
                  100
                  : 0,
              ),
            });
          }
          else {
            if (transactionTypeSalesDict) {
            if (transactionTypeSalesDict[allOutletsUserOrdersDone[i].orderType]) {
              transactionTypeSalesDict[
                allOutletsUserOrdersDone[i].orderType
              ].totalTransactions += 1;

              transactionTypeSalesDict[
                allOutletsUserOrdersDone[i].orderType
              ].totalSales += (allOutletsUserOrdersDone[i].finalPriceBefore
                ? allOutletsUserOrdersDone[i].finalPriceBefore
                : allOutletsUserOrdersDone[i].finalPrice) + getOrderDiscountInfo(allOutletsUserOrdersDone[i]);

              transactionTypeSalesDict[
                allOutletsUserOrdersDone[i].orderType
              ].totalDiscount += getOrderDiscountInfoInclOrderBased(allOutletsUserOrdersDone[i]);

              // transactionTypeSalesDict[allOutletsUserOrdersDone[i].orderType].discount += allOutletsUserOrdersDone[i].discount / allOutletsUserOrdersDone[i].totalPrice * 100;
              transactionTypeSalesDict[
                allOutletsUserOrdersDone[i].orderType
              ].tax += allOutletsUserOrdersDone[i].tax;

              transactionTypeSalesDict[
                allOutletsUserOrdersDone[i].orderType
              ].serviceCharge += allOutletsUserOrdersDone[i].sc || 0;

              transactionTypeSalesDict[
                allOutletsUserOrdersDone[i].orderType
              ].gp += 0;

              transactionTypeSalesDict[
                allOutletsUserOrdersDone[i].orderType
              ].netSales +=
                (allOutletsUserOrdersDone[i].finalPriceBefore
                  ? allOutletsUserOrdersDone[i].finalPriceBefore
                  : allOutletsUserOrdersDone[i].finalPrice) -
                allOutletsUserOrdersDone[i].tax -
                (allOutletsUserOrdersDone[i].sc
                  ? allOutletsUserOrdersDone[i].sc
                  : 0);

              const calculatedDiscount = getOrderDiscountInfoInclOrderBased(allOutletsUserOrdersDone[i]);

              // transactionTypeSalesDict[allOutletsUserOrdersDone[i].orderType].detailsList.push(allOutletsUserOrdersDone[i]);
              transactionTypeSalesDict[
                allOutletsUserOrdersDone[i].orderType
              ].detailsList.push({
                ...allOutletsUserOrdersDone[i],
                discountPercentage: parseFloat(
                  isFinite(
                    calculatedDiscount /
                    // allOutletsUserOrdersDone[i].totalPrice,
                    (allOutletsUserOrdersDone[i].finalPrice +
                      calculatedDiscount),
                  )
                    ? (calculatedDiscount /
                      // allOutletsUserOrdersDone[i].totalPrice) *
                      (allOutletsUserOrdersDone[i].finalPrice +
                        calculatedDiscount)) *
                    100
                    : 0,
                ),
              });
            }
          }
        }
        /*  transactionTypeSalesDict[
                   allOutletsUserOrdersDone[i].orderType
                 ].netSales =
                   Math.ceil(
                     transactionTypeSalesDict[allOutletsUserOrdersDone[i].orderType]
                       .netSales *
                     20 -
                     0.05,
                   ) / 20; */
      }}

      transactionTypeSalesDict[ORDER_TYPE.DELIVERY].discount = isFinite(
        (transactionTypeSalesDict[ORDER_TYPE.DELIVERY].totalDiscount /
          transactionTypeSalesDict[ORDER_TYPE.DELIVERY].totalSales) *
        100
      )
        ? (transactionTypeSalesDict[ORDER_TYPE.DELIVERY].totalDiscount /
          transactionTypeSalesDict[ORDER_TYPE.DELIVERY].totalSales) *
        100
        : 0;

      transactionTypeSalesDict[ORDER_TYPE.DELIVERY].averageNetSales = isFinite(
        transactionTypeSalesDict[ORDER_TYPE.DELIVERY].netSales /
        transactionTypeSalesDict[ORDER_TYPE.DELIVERY].totalTransactions
      )
        ? transactionTypeSalesDict[ORDER_TYPE.DELIVERY].netSales /
        transactionTypeSalesDict[ORDER_TYPE.DELIVERY].totalTransactions
        : 0;

      transactionTypeSalesDict[ORDER_TYPE.PICKUP].discount = isFinite(
        (transactionTypeSalesDict[ORDER_TYPE.PICKUP].totalDiscount /
          transactionTypeSalesDict[ORDER_TYPE.PICKUP].totalSales) *
        100
      )
        ? (transactionTypeSalesDict[ORDER_TYPE.PICKUP].totalDiscount /
          transactionTypeSalesDict[ORDER_TYPE.PICKUP].totalSales) *
        100
        : 0;

      transactionTypeSalesDict[ORDER_TYPE.PICKUP].averageNetSales = isFinite(
        transactionTypeSalesDict[ORDER_TYPE.PICKUP].netSales /
        transactionTypeSalesDict[ORDER_TYPE.PICKUP].totalTransactions
      )
        ? transactionTypeSalesDict[ORDER_TYPE.PICKUP].netSales /
        transactionTypeSalesDict[ORDER_TYPE.PICKUP].totalTransactions
        : 0;

      transactionTypeSalesDict[ORDER_TYPE.DINEIN].discount = isFinite(
        (transactionTypeSalesDict[ORDER_TYPE.DINEIN].totalDiscount /
          transactionTypeSalesDict[ORDER_TYPE.DINEIN].totalSales) *
        100
      )
        ? (transactionTypeSalesDict[ORDER_TYPE.DINEIN].totalDiscount /
          transactionTypeSalesDict[ORDER_TYPE.DINEIN].totalSales) *
        100
        : 0;

      transactionTypeSalesDict[ORDER_TYPE.DINEIN].averageNetSales = isFinite(
        transactionTypeSalesDict[ORDER_TYPE.DINEIN].netSales /
        transactionTypeSalesDict[ORDER_TYPE.DINEIN].totalTransactions
      )
        ? transactionTypeSalesDict[ORDER_TYPE.DINEIN].netSales /
        transactionTypeSalesDict[ORDER_TYPE.DINEIN].totalTransactions
        : 0;

      transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].discount = isFinite(
        (transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].totalDiscount /
          transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].totalSales) *
        100
      )
        ? (transactionTypeSalesDict[ORDER_TYPE.DELIVERY].totalDiscount /
          transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].totalSales) *
        100
        : 0;

      transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].averageNetSales = isFinite(
        transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].netSales /
        transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].totalTransactions
      )
        ? transactionTypeSalesDict[ORDER_TYPE.DELIVERY].netSales /
        transactionTypeSalesDict[ORDER_TYPE.PRE_ORDER].totalTransactions
        : 0;

      transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].discount = isFinite(
        (transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].totalDiscount /
          transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].totalSales) *
        100,
      )
        ? (transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].totalDiscount /
          transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].totalSales) *
        100
        : 0;

      transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].averageNetSales = isFinite(
        transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].netSales /
        transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].totalTransactions,
      )
        ? transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].netSales /
        transactionTypeSalesDict[ORDER_TYPE_SUB.OTHER_DELIVERY].totalTransactions
        : 0;
      const transactionTypeSalesTemp = Object.entries(
        transactionTypeSalesDict
      ).map(([key, value]) => ({ ...value }));

      const dummyData = ["Download Test"];

      setState({ CsvData: dummyData });

      setTransactionTypeSales(transactionTypeSalesTemp);

      //setCurrentPage(1);
      setPageCount(Math.ceil(transactionTypeSalesTemp.length / perPage));

      setShowDetails(false);
    }
  }, [
    allOutletsUserOrdersDone,
    currOutletId,
    allOutlets,
    historyStartDate,
    historyEndDate,
    perPage,
    // filterAppType,
    selectedOutletList,
    isMasterAccount,
  ]);

  useEffect(() => {
    setOutletDropdownList(
      allOutlets.map((item) => ({
        label: item.name,
        value: item.uniqueId,
      })),
    );
    if (selectedOutletId === "" && allOutlets.length > 0 && currOutletId) {
      setSelectedOutletId(currOutletId);

      // setSelectedOutletList([currOutletId]);
      CommonStore.update((s) => {
        s.reportOutletIdList = [currOutletId];
      })
    }
  }, [allOutlets, currOutletId]);

  useEffect(() => {
    if (showDetails && selectedItemSummary.detailsList) {
      setTransactionTypeSalesDetails(selectedItemSummary.detailsList);

      setPageReturn(currentPage);
      // console.log('currentPage value is');
      // console.log(currentPage);
      setCurrentDetailsPage(1);

      setPageCount(Math.ceil(selectedItemSummary.detailsList.length / perPage));
    }
  }, [showDetails, selectedItemSummary, perPage, filterAppType,]);

  useEffect(() => {
    var allOutletsUserOrdersDoneTemp = [];

    var currDateTime = moment().valueOf();

    if (
      // global.payoutTransactions.length > 0
      true
    ) {
      for (var j = 0; j < global.payoutTransactions.length; j++) {
        allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneTemp.concat(
          (filterAppType && filterAppType.length > 0)
            ?
            (global.payoutTransactions[j].userOrdersFigures ? global.payoutTransactions[j].userOrdersFigures : [])
            :
            (global.payoutTransactions[j].userOrdersFigures ? global.payoutTransactions[j].userOrdersFigures : []).filter((item) =>
              //filterChartItems(item, appliedChartFilterQueriesLineChart),
              (filterAppType.includes(item.appType))
            )
        );
      }

      for (var j = 0; j < global.payoutTransactionsExtend.length; j++) {
        allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneTemp.concat(
          (filterAppType && filterAppType.length > 0)
            ?
            (global.payoutTransactionsExtend[j].userOrdersFigures ? global.payoutTransactionsExtend[j].userOrdersFigures : [])
            :
            (global.payoutTransactionsExtend[j].userOrdersFigures ? global.payoutTransactionsExtend[j].userOrdersFigures : []).filter((item) =>
              //filterChartItems(item, appliedChartFilterQueriesLineChart),
              (filterAppType.includes(item.appType))
            )
        );
      }

      const startTime = moment().set({ hour: 0, minute: 0, second: 0 }); // Set the start time to 12:00am
      const endTime = moment().set({ hour: 5, minute: 55, second: 0 }); // Set the end time to 05:55am

      for (var i = 0; i < allOutletsUserOrdersDoneRaw.length; i++) {
        if (
          moment(allOutletsUserOrdersDoneRaw[i].createdAt).isSame(currDateTime, 'day')
          ||
          (
            moment(currDateTime).isBetween(startTime, endTime)
            &&
            moment(currDateTime).add(-1, 'day').isSame(allOutletsUserOrdersDoneRaw[i].createdAt, 'day')
          )
        ) {
          if (filterAppType.includes(allOutletsUserOrdersDoneRaw[i].appType)) {
            if (!allOutletsUserOrdersDoneTemp.find(order => order.uniqueId === allOutletsUserOrdersDoneRaw[i].uniqueId)) {
              allOutletsUserOrdersDoneTemp.push(allOutletsUserOrdersDoneRaw[i]);
            }
          }
        }
      }
    }
    else {
      allOutletsUserOrdersDoneTemp = allOutletsUserOrdersDoneRaw.filter((item) =>
        (filterAppType.includes(item.appType))
      );
    }

    setAllOutletsUserOrdersDone(allOutletsUserOrdersDoneTemp);
  }, [
    allOutletsUserOrdersDoneRaw,

    // payoutTransactions,
    // payoutTransactionsExtend,

    ptTimestamp,
    pteTimestamp,

    //isMounted,
    reportOutletShifts,
    reportDisplayType,

    filterAppType,
  ]);


  // componentDidMount = () => {
  //     moment()
  // }

  const setState = () => { };

  const searchBarItem = () => {
    ApiClient.GET(
      `${API.salesByTransactionChannelSearchBar +
      1
      }&queryName=${search
      }&startDate=${startDate
      }&endDate=${endDate}`,
    ).then((result) => {
      setState({ lists: result });
    });
  };

  /* const email = () => {
          var body = {
              email: '<EMAIL>',
              data: list
          }
          ApiClient.POST(API.emailReportPdf, body, false).then((result) => {
              try {
                  if (result != null) {
                      window.confirm(
                          'Congratulation!',
                          'You Have Successfull',
                          [
                              {
                                  text: 'OK',
                                  onPress: () => { setState({ visible1: false }) },
                              },
                          ],
                          { cancelable: false },
                      );
                  }
              } catch (error) {
                  window.confirm('Failed', [{ text: 'OK', onPress: () => { } }], {
                      cancelable: false,
                  });
              }
          })
      }
  
      const download = () => {
          var body = {
              data: list
          }
          ApiClient.POST(API.generateReportPDF, body, false).then((result) => {
              try {
                  if (result != null) {
                      window.confirm(
                          'Congratulation!',
                          'You Have Successfull',
                          [
                              {
                                  text: 'OK',
                                  onPress: () => { setState({ visible: false }) },
                              },
                          ],
                          { cancelable: false },
                      );
                  }
              } catch (error) {
                  window.confirm('Failed', [{ text: 'OK', onPress: () => { } }], {
                      cancelable: false,
                  });
              }
          })
      } */

  const add = async () => {
    if (page + 1 < pageCount) {
      await setState({ page: page + 1, currentPage: currentPage + 1 });
      // console.log(page);
      var e = page;
      next(e);
    }
  };

  const next = (e) => {
    const offset = e * perPage; //10
    setState({ offset });
    loadMoreData();
  };

  const less = async () => {
    if (page > 0) {
      await setState({ page: page - 1, currentPage: currentPage - 1 });
      // console.log(page);
      var y = page;
      pre(y);
    }
  };

  const pre = (y) => {
    const offset = y * perPage;
    setState({ offset });
    loadMoreData();
  };

  const nextPage = () => {
    setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
  };

  const prevPage = () => {
    setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
  };

  const nextDetailsPage = () => {
    setCurrentDetailsPage(
      currentDetailsPage + 1 > pageCount
        ? currentDetailsPage
        : currentDetailsPage + 1
    );
  };

  const prevDetailsPage = () => {
    setCurrentDetailsPage(
      currentDetailsPage - 1 < 1 ? currentDetailsPage : currentDetailsPage - 1
    );
  };

  const loadMoreData = () => {
    const data = oriList;
    const slice = data.slice(offset, offset + perPage); //30
    setState({ list: slice, pageCount: Math.ceil(data.length / perPage) });
  };

  // moment = async () => {
  //     const today = new Date();
  //     const day = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
  //     await setState({ startDate: moment(day).format('YYYY-MM-DD'), endDate: moment(today).format('YYYY-MM-DD') })
  //     getDetail()
  // }

  const getDetail = () => {
    ApiClient.GET(
      `${API.salesByTransactionCategory +
      1
      }&startDate=${startDate
      }&endDate=${endDate}`,
    ).then((result) => {
      var data = result;
      var slice = data.slice(offset, offset + perPage);
      setState({
        list: slice,
        oriList: data,
        pageCount: Math.ceil(data.length / perPage),
      });
    });
  };

  const decimal = (value) => {
    return value.toFixed(2);
  };

  const renderSearchItem = ({ item, index }) =>
    (index + 1) % 2 == 0 ? (
      <View style={{ backgroundColor: Colors.whiteColor, padding: 12 }}>
        <View style={{ flexDirection: "row" }}>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {item.category == null || item.category == ""
              ? "DELIVERY"
              : item.category}
          </Text>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.finalPrice).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {item.count}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.discount).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.discount).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.tax).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.serviceCharge).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 1,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.gp).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.netSale).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.averageNetSale).toFixed(2)}
          </Text>
        </View>
      </View>
    ) : (
      <View style={{ backgroundColor: Colors.fieldtBgColor, padding: 12 }}>
        <View style={{ flexDirection: "row" }}>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {item.category == null || item.category == ""
              ? "DELIVERY"
              : item.category}
          </Text>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.finalPrice).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {item.count}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.discount).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.discount).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.tax).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.serviceCharge).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 1,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.gp).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 2,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.netSale).toFixed(2)}
          </Text>
          <Text
            style={{
              flex: 3,
              fontSize: 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "center",
            }}
          >
            {parseFloat(item.averageNetSale)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
          </Text>
        </View>
      </View>
    );

  const onItemSummaryClicked = (item) => {
    // setTransactionTypeSalesDetails(item.detailsList);
    setSelectedItemSummary(item);
    setShowDetails(true);

    // setCurrentPage(1);
    // setPageCount(Math.ceil(item.detailsList.length / perPage));

    // console.log('item.detailsList');
    // console.log(item.detailsList);
  };

  const renderItem = ({ item, index }) => (
    <TouchableOpacity
      onPress={() => onItemSummaryClicked(item)}
      style={{
        backgroundColor:
          (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
        paddingVertical: 10,
        //paddingHorizontal: 3,
        //paddingLeft: 1,
        borderColor: "#BDBDBD",
        borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
        borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
      }}
    >
      <View style={{ flexDirection: "row" }}>
        {!simpliFiedLayout && (
          <Text
            style={{
              width: "6%",
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "left",
              paddingLeft: 10,
            }}
          >
            {index + 1}
          </Text>
        )}
        <Text
          style={{
            width: simpliFiedLayout ? '40%' : "18%",
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: "NunitoSans-Regular",
            textAlign: "left",
            paddingLeft: 10,
          }}
        >
          {item.orderType}
        </Text>
        <Text
          style={{
            width: simpliFiedLayout ? '30%' : "10%",
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: "NunitoSans-Regular",
            textAlign: "left",
            paddingLeft: 10,
          }}
        >
          {item.totalTransactions}
        </Text>
        {!simpliFiedLayout && (
          <Text
            style={{
              width: "10%",
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "left",
              paddingLeft: 10,
            }}
          >
            {item.totalSales.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
          </Text>
        )}
        {!simpliFiedLayout && (
          <Text
            style={{
              width: "8%",
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "left",
              paddingLeft: 10,
            }}
          >
            {item.totalDiscount
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
          </Text>
        )}
        {!simpliFiedLayout && (
          <Text
            style={{
              width: "8%",
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "left",
              paddingLeft: 10,
            }}
          >
            {(item.totalDiscount != 0
              ? (item.totalDiscount / item.totalSales) * 100
              : 0
            ).toFixed(2)}
          </Text>
        )}
        {!simpliFiedLayout && (
          <Text
            style={{
              width: "8%",
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "left",
              paddingLeft: 10,
            }}
          >
            {(item.tax || 0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
          </Text>
        )}
        {!simpliFiedLayout && (
          <Text
            style={{
              width: "10%",
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "left",
              paddingLeft: 10,
            }}
          >
            {item.serviceCharge
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
          </Text>
        )}
        {!simpliFiedLayout && (
          <Text
            style={{
              width: "10%",
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "left",
              paddingLeft: 10,
            }}
          >
            {(item.salesReturn || 0)
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
          </Text>
        )}
        {/* <Text style={{ flex: 1, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'left' }}>{item.gp}</Text> */}
        <Text
          style={{
            width: simpliFiedLayout ? '30%' : "12%",
            fontSize: switchMerchant ? 10 : 13,
            fontFamily: "NunitoSans-Regular",
            textAlign: "right",
            paddingRight: 20,
          }}
        >
          {item.netSales.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
        </Text>
        {/* <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'left' }}>{item.averageNetSales.toFixed(2)}</Text> */}
        {/* <Text
            style={{
              flex: 1.5,
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: 'NunitoSans-Regular',
              textAlign: 'right',
              paddingRight: 20,
            }}>
            {item.averageNetSales
              .toFixed(2)
              .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text> */}
      </View>
    </TouchableOpacity>
  );

  const onClickItemDetails = async (item) => {
    const userOrderSnapshot = await firebase.firestore()
      .collection(Collections.UserOrder)
      .where('uniqueId', '==', item.uniqueId)
      .limit(1)
      .get();

    var userOrder = null;
    if (!userOrderSnapshot.empty) {
      userOrder = userOrderSnapshot.docs[0].data();
    }

    if (userOrder) {
      setExpandDetailsDict({
        ...expandDetailsDict,
        [item.uniqueId]: expandDetailsDict[item.uniqueId] ? false : userOrder,
      });
    }
  };

  const renderItemDetails = ({ item, index }) => {
    var record = null;
    if (item && expandDetailsDict[item.uniqueId] && expandDetailsDict[item.uniqueId].uniqueId) {
      record = expandDetailsDict[item.uniqueId];
    }
    ///////////////////////////

    // console.log('order id');
    // console.log(item.orderId);

    // calculate longest

    var longestStr = 5;

    for (var i = 0; i < item.cartItems.length; i++) {
      const cartItemPriceWIthoutAddOn = getCartItemPriceWithoutAddOn(item.cartItems[i]);

      if (cartItemPriceWIthoutAddOn.toFixed(0).length > longestStr) {
        longestStr = cartItemPriceWIthoutAddOn.toFixed(0).length;
      }

      for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
        if (
          item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length > longestStr
        ) {
          longestStr = item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length;
        }
      }
    }

    if (item.totalPrice.toFixed(0).length > longestStr) {
      longestStr = item.totalPrice.toFixed(0).length;
    }

    if (item.discount.toFixed(0).length > longestStr) {
      longestStr = item.discount.toFixed(0).length;
    }

    if (item.tax.toFixed(0).length > longestStr) {
      longestStr = item.tax.toFixed(0).length;
    }

    if (item.finalPrice.toFixed(0).length > longestStr) {
      longestStr = item.finalPrice.toFixed(0).length;
    }

    // console.log(longestStr);

    ///////////////////////////

    // calculate spacing

    // var cartItemPriceWIthoutAddOnSpacingList = [];
    // var addOnsSpacingList = [];

    // for (var i = 0; i < item.cartItems.length; i++) {
    //   const cartItemPriceWIthoutAddOn =
    //     item.cartItems[i].price -
    //     item.cartItems[i].addOns.reduce(
    //       (accum, addOn) => accum + addOn.prices[0],
    //       0,
    //     );

    //   cartItemPriceWIthoutAddOnSpacingList.push(
    //     Math.max(longestStr - cartItemPriceWIthoutAddOn.toFixed(0).length, 0) +
    //     1,
    //   );

    //   for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
    //     addOnsSpacingList.push(
    //       Math.max(
    //         longestStr -
    //         item.cartItems[i].addOns[j].prices[0].toFixed(0).length,
    //         0,
    //       ) + 1,
    //     );
    //   }
    // }

    var totalPriceSpacing =
      Math.max(longestStr - item.totalPrice.toFixed(0).length, 0) + 1;
    var discountSpacing =
      Math.max(longestStr - item.discount.toFixed(0).length, 0) + 1;
    var taxSpacing = Math.max(longestStr - item.tax.toFixed(0).length, 0) + 1;
    var finalPriceSpacing =
      Math.max(longestStr - item.finalPrice.toFixed(0).length, 0) + 1;

    ///////////////////////////
    return (
      <TouchableOpacity
        onPress={() => onClickItemDetails(item)}
        style={{
          backgroundColor:
            (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
          paddingVertical: 10,
          //paddingHorizontal: 3,
          //paddingLeft: 1,
          borderColor: "#BDBDBD",
          borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
        }}
      >
        <View style={{ flexDirection: "row" }}>
          {!simpliFiedLayout && (
            <Text
              style={{
                width: "6%",
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: "NunitoSans-Regular",
                textAlign: "left",
                paddingLeft: 10,
              }}
            >
              {index + 1}
            </Text>
          )}
          <Text
            style={{
              width: simpliFiedLayout ? '30%' : "12%",
              fontSize: switchMerchant ? 10 : 13,
              fontFamily: "NunitoSans-Regular",
              textAlign: "left",
              paddingLeft: 10,
            }}
          >
            {ORDER_TYPE_PARSED[item.orderType]}
          </Text>

          <View style={{ width: simpliFiedLayout ? '40%' : "18%" }}>
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: "NunitoSans-Regular",
                textAlign: "left",
                paddingLeft: 10,
              }}
            >
              {moment(item.createdAt).format("DD MMM YY hh:mm A")}
            </Text>
          </View>
          {!simpliFiedLayout && (
            <Text
              style={{
                width: "10%",
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: "NunitoSans-Regular",
                textAlign: "left",
                paddingLeft: 10,
              }}
            >
              {((item.finalPriceBefore ? item.finalPriceBefore : item.finalPrice) + getOrderDiscountInfo(item))
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
            </Text>
          )}
          {!simpliFiedLayout && (
            <Text
              style={{
                width: "8%",
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: "NunitoSans-Regular",
                textAlign: "left",
                paddingLeft: 10,
              }}
            >
              {(getOrderDiscountInfoInclOrderBased(item)).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
            </Text>
          )}
          {!simpliFiedLayout && (
            <Text
              style={{
                width: "8%",
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: "NunitoSans-Regular",
                textAlign: "left",
                paddingLeft: 10,
              }}
            >
              {parseFloat(item.discountPercentage)
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
            </Text>
          )}
          {!simpliFiedLayout && (
            <Text
              style={{
                width: "8%",
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: "NunitoSans-Regular",
                textAlign: "left",
                paddingLeft: 10,
              }}
            >
              {item.tax.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
            </Text>
          )}
          {!simpliFiedLayout && (
            <Text
              style={{
                width: "9%",
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: "NunitoSans-Regular",
                textAlign: "left",
                paddingLeft: 10,
              }}
            >
              {parseFloat(item.sc || 0)
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
            </Text>
          )}
          {!simpliFiedLayout && (
            <Text
              style={{
                width: "9%",
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: "NunitoSans-Regular",
                textAlign: "left",
                paddingLeft: 10,
              }}
            >
              {(item.salesReturn || 0)
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
            </Text>
          )}
          {/* <Text style={{ flex: 1.6, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'left' }}>{item.discountPercentage ? item.discountPercentage.toFixed(2) : '0.00'}</Text> */}

          {/* <Text style={{ flex: 2, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'left' }}>{item.serviceCharge.toFixed(2)}</Text> */}
          {/* <Text style={{ flex: 1, fontSize: 13, fontFamily: 'NunitoSans-Regular', textAlign: 'left' }}>{(0).toFixed(2)}</Text> */}
          <View
            style={{
              width: simpliFiedLayout ? '30%' : "12%",
              flexDirection: "row",
              justifyContent: "space-between",
              paddingLeft: 10,
              paddingRight: switchMerchant ? "2.1%" : "1.8%",
            }}
          >
            <Text style={{}} />
            <Text
              style={{
                fontSize: switchMerchant ? 10 : 13,
                fontFamily: "NunitoSans-Regular",
              }}
            >
              {/* <Text style={{
                            opacity: 0,
                            ...Platform.OS === 'android' && {
                                color: 'transparent',
                            },
                        }}>
                            {'0'.repeat((finalPriceSpacing * 0.6) + (item.finalPrice.toFixed(0).length === 1 ? 1 : 0))}
                        </Text> */}
              {(item.finalPriceBefore != 0 || item.finalPrice != 0
                ? (item.finalPriceBefore
                  ? item.finalPriceBefore
                  : item.finalPrice) -
                item.tax -
                (item.sc || 0)
                : 0
              )
                .toFixed(2)
                .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
            </Text>
          </View>
        </View>

        {expandDetailsDict[item.uniqueId] && !simpliFiedLayout && (
          <View
            style={{
              minheight: windowHeight * 0.35,
              marginTop: 30,
              paddingBottom: 20,
            }}
          >
            {record.cartItems.map((cartItem, index) => {
              const cartItemPriceWIthoutAddOn = getCartItemPriceWithoutAddOn(cartItem);

              return (
                <View
                  style={{
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <View
                    style={{
                      width: "100%",
                      alignItems: "flex-start",
                      flexDirection: "row",
                      marginBottom: Platform.OS == "ios" ? 10 : 10,
                      minHeight: 80,
                      //backgroundColor: 'yellow',
                    }}
                  >
                    <View
                      style={{
                        flexDirection: "row",
                        width: "100%",
                        //backgroundColor: 'blue',
                      }}
                    >
                      {index == 0 ? (
                        <View
                          style={{
                            marginHorizontal: 1,
                            width: Platform.OS == "ios" ? "8%" : "8%",
                            //justifyContent: 'center',
                            alignItems: "center",
                            //backgroundColor: 'blue',
                          }}
                        >
                          <TouchableOpacity
                            style={{
                              alignItems: "center",
                              marginTop: 0,
                            }}
                            onPress={() => {
                              var crmUser = null;

                              if (record.crmUserId !== undefined) {
                                for (var i = 0; i < crmUsers.length; i++) {
                                  if (record.crmUserId === crmUsers[i].uniqueId) {
                                    crmUser = crmUsers[i];
                                    break;
                                  }
                                }
                              }

                              if (!crmUser) {
                                for (var i = 0; i < crmUsers.length; i++) {
                                  if (record.userId === crmUsers[i].firebaseUid) {
                                    crmUser = crmUsers[i];
                                    break;
                                  }
                                }
                              }

                              if (crmUser) {
                                CommonStore.update(
                                  (s) => {
                                    s.selectedCustomerEdit = crmUser;
                                    // s.selectedCustomerEdit = userReservations[item.userId] && crmUsers[item.userId] ? crmUsers[item.userId] : null ;

                                    s.routeParams = {
                                      pageFrom: "Reservation",
                                    };
                                  },
                                  () => {
                                    navigation.navigate(
                                      "Customer - KooDoo Manager"
                                    );
                                  }
                                );
                              }
                            }}
                          >
                            {/* <Image
                                                            style={{
                                                                width: switchMerchant ? 30 : 60,
                                                                height: switchMerchant ? 30 : 60,
                                                            }}
                                                            resizeMode="contain"
                                                            source={require('../assets/image/default-profile.png')}
                                                        /> */}
                            <img src={personicon} width={simpliFiedLayout ? 20 : 60} height={simpliFiedLayout ? 20 : 60} />

                            <View
                              style={{
                                alignItems: "center",
                                justifyContent: "center",
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontFamily: "NunitoSans-Bold",
                                    marginTop: 0,
                                    fontSize: simpliFiedLayout ? 10 : 13,
                                    textAlign: "center",
                                  },
                                  switchMerchant
                                    ? {
                                      fontFamily: "NunitoSans-Bold",
                                      marginTop: 0,
                                      fontSize: 10,
                                      textAlign: "center",
                                    }
                                    : {},
                                ]}
                                numberOfLines={1}
                              >
                                {record.userName ? record.userName : "Guest"}
                              </Text>
                            </View>
                          </TouchableOpacity>
                        </View>
                      ) : (
                        <View
                          style={{
                            marginHorizontal: 1,
                            width: Platform.OS == "ios" ? "8%" : "8%",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        />
                      )}

                      <View
                        style={{
                          // flex: 0.3,
                          width: "5%",
                          //justifyContent: 'center',
                          alignItems: "center",
                          //backgroundColor: 'red',
                          //paddingLeft: '1.2%',
                        }}
                      >
                        <Text
                          style={[
                            {
                              fontFamily: "NunitoSans-Bold",
                              fontSize: simpliFiedLayout ? 10 : 13,
                            },
                            switchMerchant
                              ? {
                                fontFamily: "NunitoSans-Bold",
                                fontSize: 10,
                              }
                              : {},
                          ]}
                        >
                          {index + 1}.
                        </Text>
                      </View>

                      <View
                        style={{
                          //flex: 0.5,
                          width: "10%",
                          //backgroundColor: 'green',
                          alignItems: "center",
                        }}
                      >
                        {cartItem.image ? (
                          <AsyncImage
                            source={{ uri: cartItem.image }}
                            // item={cartItem}
                            style={{
                              width: switchMerchant ? 30 : simpliFiedLayout ? 40 : 60,
                              height: switchMerchant ? 30 : simpliFiedLayout ? 40 : 60,
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              borderRadius: 5,
                            }}
                          />
                        ) : (
                          <View
                            style={{
                              justifyContent: "center",
                              alignItems: "center",
                              width: switchMerchant ? 30 : simpliFiedLayout ? 40 : 60,
                              height: switchMerchant ? 30 : simpliFiedLayout ? 40 : 60,
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              borderRadius: 5,
                            }}
                          >
                            <Ionicon
                              name="fast-food-outline"
                              size={switchMerchant ? 25 : 35}
                            />
                          </View>
                        )}
                      </View>
                      <View style={{ width: simpliFiedLayout ? '55%' : "75%" }}>
                        <View
                          style={{
                            marginLeft: Platform.OS == "ios" ? 14 : 14,
                            marginBottom: 10,
                            //backgroundColor: 'blue',
                            width: "100%",
                            flexDirection: "row",
                          }}
                        >
                          <View style={{ width: "69%" }}>
                            <Text
                              style={[
                                {
                                  fontFamily: "NunitoSans-Bold",
                                  fontSize: 13,
                                },
                                switchMerchant
                                  ? {
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 10,
                                  }
                                  : {},
                              ]}
                            >
                              {cartItem.name}{cartItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[cartItem.unitType]})` : ""}
                            </Text>
                          </View>

                          <View
                            style={{
                              width: "13%",
                            }}
                          >
                            <View
                              style={{
                                alignItems: "center",
                                //backgroundColor: 'yellow',
                              }}
                            >
                              <Text
                                style={[
                                  {
                                    fontFamily: "NunitoSans-Bold",
                                    fontSize: 13,
                                  },
                                  // Platform.OS === 'android'
                                  //   ? {
                                  //       width: '200%',
                                  //     }
                                  //   : {},
                                  switchMerchant
                                    ? {
                                      fontFamily: "NunitoSans-Bold",
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                x{cartItem.quantity}
                              </Text>
                            </View>
                          </View>
                          <View
                            style={{
                              flexDirection: "row",
                              justifyContent: "space-between",
                              width: "18.75%",
                            }}
                          >
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                  }
                                  : { fontSize: 13 }
                              }
                            >
                              RM
                            </Text>
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    paddingRight: 20,
                                    fontFamily: "NunitoSans-Regular",
                                  }
                                  : {
                                    fontSize: 13,
                                    paddingRight: 20,
                                    fontFamily: "NunitoSans-Regular",
                                  }
                              }
                            >
                              {cartItemPriceWIthoutAddOn
                                .toFixed(2)
                                .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
                            </Text>
                          </View>
                        </View>

                        {cartItem.remarks && cartItem.remarks.length > 0 ? (
                          <View
                            style={{
                              alignItems: "center",
                              flexDirection: "row",
                              marginLeft: Platform.OS == "ios" ? 14 : 14,
                            }}
                          >
                            <View style={{ justifyContent: "center" }}>
                              <Text
                                style={[
                                  {
                                    fontFamily: "NunitoSans-SemiBold",
                                    fontSize: 13,
                                  },
                                  switchMerchant
                                    ? {
                                      fontFamily: "NunitoSans-SemiBold",
                                      fontSize: 10,
                                    }
                                    : {},
                                ]}
                              >
                                {cartItem.remarks}
                              </Text>
                            </View>
                          </View>
                        ) : (
                          <></>
                        )}

                        {cartItem.addOns.map((addOnChoice, i) => {
                          return (
                            <View
                              style={{
                                flexDirection: "row",
                                // marginLeft: -5,
                                width: "100%",
                              }}
                            >
                              <View
                                style={{
                                  width: "69%",
                                  flexDirection: "row",
                                  marginLeft: Platform.OS == "ios" ? 14 : 14,
                                }}
                              >
                                <Text
                                  style={[
                                    {
                                      fontFamily: "NunitoSans-Bold",
                                      fontSize: 13,
                                      color: Colors.descriptionColor,
                                      width: "25%",
                                      // marginLeft: 5,
                                    },
                                    switchMerchant
                                      ? {
                                        fontFamily: "NunitoSans-Bold",
                                        fontSize: 10,
                                        color: Colors.descriptionColor,
                                        width: "25%",
                                      }
                                      : {},
                                  ]}
                                >
                                  {`${addOnChoice.name}:`}
                                </Text>
                                <Text
                                  style={[
                                    {
                                      fontFamily: "NunitoSans-Bold",
                                      fontSize: 13,
                                      color: Colors.descriptionColor,
                                      width: "75%",
                                      // marginLeft: 5,
                                    },
                                    switchMerchant
                                      ? {
                                        fontFamily: "NunitoSans-Bold",
                                        fontSize: 10,
                                        color: Colors.descriptionColor,
                                        width: "75%",
                                      }
                                      : {},
                                  ]}
                                >
                                  {`${addOnChoice.choiceNames[0]}`}
                                </Text>
                              </View>

                              <View
                                style={[
                                  {
                                    width: "13%",
                                    flexDirection: "row",
                                    justifyContent: "center",
                                    //backgroundColor: 'blue',
                                  },
                                  switchMerchant
                                    ? {
                                      width: "13%",
                                      flexDirection: "row",
                                      justifyContent: "center",
                                    }
                                    : {},
                                ]}
                              >
                                <Text
                                  style={[
                                    {
                                      fontFamily: "NunitoSans-Bold",
                                      fontSize: 13,
                                      color: Colors.descriptionColor,
                                      width: "28%",
                                      // right: 38,
                                      //backgroundColor: 'green',
                                      textAlign: "center",
                                    },
                                    switchMerchant
                                      ? {
                                        fontFamily: "NunitoSans-Bold",
                                        fontSize: 10,
                                        color: Colors.descriptionColor,
                                        width: "28%",
                                        textAlign: "center",
                                      }
                                      : {},
                                  ]}
                                >
                                  {`${addOnChoice.quantities
                                    ? `x${getAddOnChoiceQuantity(addOnChoice, cartItem)}`
                                    : ''
                                    }`}
                                </Text>
                              </View>

                              <View
                                style={{
                                  flexDirection: "row",
                                  justifyContent: "space-between",
                                  width: "18.75%",
                                  alignItems: "center",
                                }}
                              >
                                <Text
                                  style={[
                                    switchMerchant
                                      ? {
                                        color: Colors.descriptionColor,
                                        fontSize: 10,
                                      }
                                      : {
                                        color: Colors.descriptionColor,
                                        fontSize: 13,
                                      },
                                  ]}
                                >
                                  RM
                                </Text>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        color: Colors.descriptionColor,
                                        paddingRight: 20,
                                        fontSize: 10,
                                        fontFamily: "NunitoSans-Regular",
                                      }
                                      : {
                                        color: Colors.descriptionColor,
                                        paddingRight: 20,
                                        fontSize: 13,
                                        fontFamily: "NunitoSans-Regular",
                                      }
                                  }
                                >
                                  {(getAddOnChoicePrice(addOnChoice, cartItem))
                                    .toFixed(2)
                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
                                </Text>
                              </View>
                            </View>
                          );
                        })}
                      </View>
                    </View>
                  </View>
                  <View style={{ flexDirection: "row", width: "100%" }}>
                    <View style={{ width: simpliFiedLayout ? '55%' : "70%" }} />
                    <View style={{ width: 15 }} />
                    {index === record.cartItems.length - 1 ? (
                      <View
                        style={{
                          flexDirection: "row",
                          //backgroundColor: 'yellow',
                          width: "28.65%",
                        }}
                      >
                        <View
                          style={{
                            justifyContent: "center",
                            width: "100%",
                          }}
                        >
                          <View
                            style={{
                              flexDirection: "row",
                            }}
                          >
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: simpliFiedLayout ? '65%' : "50.9%",
                                    fontFamily: "Nunitosans-Bold",
                                  }
                                  : {
                                    fontSize: 13,
                                    width: simpliFiedLayout ? '65%' : "50.9%",
                                    fontFamily: "Nunitosans-Bold",
                                  }
                              }
                            >
                              Subtotal:
                            </Text>
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                width: simpliFiedLayout ? '35%' : "49.1%",
                              }}
                            >
                              <Text
                                style={
                                  switchMerchant
                                    ? { fontSize: 10 }
                                    : { fontSize: 13 }
                                }
                              >
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: simpliFiedLayout ? 0 : 20,
                                      fontFamily: "NunitoSans-Regular",
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: simpliFiedLayout ? 0 : 20,
                                      fontFamily: "NunitoSans-Regular",
                                    }
                                }
                              >
                                {((record.isRefundOrder && record.finalPrice <= 0)
                                  ? 0
                                  :
                                  // record.totalPrice +
                                  getOrderDiscountInfo(record)
                                )
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
                              </Text>
                            </View>
                          </View>

                          {cartItem.orderType === ORDER_TYPE.DELIVERY ? (
                            <View
                              style={{
                                flexDirection: "row",
                              }}
                            >
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      width: simpliFiedLayout ? '65%' : "50.9%",
                                      fontFamily: "Nunitosans-Bold",
                                    }
                                    : {
                                      fontSize: 13,
                                      width: simpliFiedLayout ? '65%' : "50.9%",
                                      fontFamily: "Nunitosans-Bold",
                                    }
                                }
                              >
                                Delivery Fee:
                              </Text>
                              <View
                                style={{
                                  flexDirection: "row",
                                  justifyContent: "space-between",
                                  width: simpliFiedLayout ? '35%' : "49.1%",
                                }}
                              >
                                <Text
                                  style={
                                    switchMerchant
                                      ? { fontSize: 10 }
                                      : { fontSize: 13 }
                                  }
                                >
                                  RM
                                </Text>
                                <Text
                                  style={
                                    switchMerchant
                                      ? {
                                        fontSize: 10,
                                        paddingRight: simpliFiedLayout ? 0 : 20,
                                        fontFamily: "NunitoSans-Regular",
                                      }
                                      : {
                                        fontSize: 13,
                                        paddingRight: simpliFiedLayout ? 0 : 20,
                                        fontFamily: "NunitoSans-Regular",
                                      }
                                  }
                                >
                                  {record.deliveryFee
                                    .toFixed(2)
                                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
                                </Text>
                              </View>
                            </View>
                          ) : (
                            <></>
                          )}

                          <View
                            style={{
                              flexDirection: "row",
                            }}
                          >
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: simpliFiedLayout ? '65%' : "50.9%",
                                    fontFamily: "Nunitosans-Bold",
                                  }
                                  : {
                                    fontSize: 13,
                                    width: simpliFiedLayout ? '65%' : "50.9%",
                                    fontFamily: "Nunitosans-Bold",
                                  }
                              }
                            >
                              Discount:
                            </Text>
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                width: simpliFiedLayout ? '35%' : "49.1%",
                              }}
                            >
                              <Text
                                style={
                                  switchMerchant
                                    ? { fontSize: 10 }
                                    : { fontSize: 13 }
                                }
                              >
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: simpliFiedLayout ? 0 : 20,
                                      fontFamily: "NunitoSans-Regular",
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: simpliFiedLayout ? 0 : 20,
                                      fontFamily: "NunitoSans-Regular",
                                    }
                                }
                              >

                                {((record.isRefundOrder && record.finalPrice <= 0)
                                  ? 0
                                  :
                                  //record.discount +
                                  getOrderDiscountInfoInclOrderBased(record)
                                )
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: "row",
                            }}
                          >
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: simpliFiedLayout ? '65%' : "50.9%",
                                    fontFamily: "Nunitosans-Bold",
                                  }
                                  : {
                                    fontSize: 13,
                                    width: simpliFiedLayout ? '65%' : "50.9%",
                                    fontFamily: "Nunitosans-Bold",
                                  }
                              }
                            >
                              Tax:
                            </Text>
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                width: simpliFiedLayout ? '35%' : "49.1%",
                              }}
                            >
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: simpliFiedLayout ? 0 : 20,
                                    }
                                    : { fontSize: 13, paddingRight: simpliFiedLayout ? 0 : 20 }
                                }
                              >
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: simpliFiedLayout ? 0 : 20,
                                      fontFamily: "NunitoSans-Regular",
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: simpliFiedLayout ? 0 : 20,
                                      fontFamily: "NunitoSans-Regular",
                                    }
                                }
                              >
                                {record.tax
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: "row",
                            }}
                          >
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: simpliFiedLayout ? '65%' : "50.85%",
                                    fontFamily: "Nunitosans-Bold",
                                  }
                                  : {
                                    fontSize: 13,
                                    width: simpliFiedLayout ? '65%' : "50.85%",
                                    fontFamily: "Nunitosans-Bold",
                                  }
                              }
                            >
                              Service Charge:
                            </Text>
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                width: simpliFiedLayout ? '35%' : switchMerchant ? "49.15%" : "49.1%",
                              }}
                            >
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                    }
                                    : { fontSize: 13 }
                                }
                              >
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: simpliFiedLayout ? 0 : 20,
                                      fontFamily: "NunitoSans-Regular",
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: simpliFiedLayout ? 0 : 20,
                                      fontFamily: "NunitoSans-Regular",
                                    }
                                }
                              >
                                {(record.sc || 0)
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
                              </Text>
                            </View>
                          </View>
                          <View
                            style={{
                              flexDirection: "row",
                            }}
                          >
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: simpliFiedLayout ? '65%' : "50.9%",
                                    fontFamily: "Nunitosans-Bold",
                                  }
                                  : {
                                    fontSize: 13,
                                    width: simpliFiedLayout ? '65%' : "50.9%",
                                    fontFamily: "Nunitosans-Bold",
                                  }
                              }
                            >
                              Rounding:
                            </Text>
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                width: simpliFiedLayout ? '35%' : "49.1%",
                              }}
                            >
                              <Text
                                style={
                                  switchMerchant
                                    ? { fontSize: 10 }
                                    : { fontSize: 13 }
                                }
                              >
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: simpliFiedLayout ? 0 : 20,
                                      fontFamily: "NunitoSans-Regular",
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: simpliFiedLayout ? 0 : 20,
                                      fontFamily: "NunitoSans-Regular",
                                    }
                                }
                              >
                                {(record.finalPrice
                                  ? record.finalPrice - record.finalPriceBefore
                                  : 0
                                )
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
                              </Text>
                            </View>
                          </View>

                          <View
                            style={{
                              flexDirection: "row",
                            }}
                          >
                            <Text
                              style={
                                switchMerchant
                                  ? {
                                    fontSize: 10,
                                    width: simpliFiedLayout ? '65%' : "50.9%",
                                    fontFamily: "Nunitosans-Bold",
                                  }
                                  : {
                                    fontSize: 13,
                                    width: simpliFiedLayout ? '65%' : "50.9%",
                                    fontFamily: "Nunitosans-Bold",
                                  }
                              }
                            >
                              Total:
                            </Text>
                            <View
                              style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                                width: simpliFiedLayout ? '35%' : "49.1%",
                              }}
                            >
                              <Text
                                style={
                                  switchMerchant
                                    ? { fontSize: 10 }
                                    : { fontSize: 13 }
                                }
                              >
                                RM
                              </Text>
                              <Text
                                style={
                                  switchMerchant
                                    ? {
                                      fontSize: 10,
                                      paddingRight: simpliFiedLayout ? 0 : 20,
                                      fontFamily: "NunitoSans-Regular",
                                    }
                                    : {
                                      fontSize: 13,
                                      paddingRight: simpliFiedLayout ? 0 : 20,
                                      fontFamily: "NunitoSans-Regular",
                                    }
                                }
                              >
                                {record.finalPrice
                                  .toFixed(2)
                                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")}
                              </Text>
                            </View>
                          </View>
                        </View>
                      </View>
                    ) : (
                      <></>
                    )}
                  </View>

                  {/* <View style={{alignItems:'flex-end'}}>
                        <View style={{ flexDirection: 'row' }}>
                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>Subtotal: {(Math.ceil(cartItem.price * 20-0.05) /20).toFixed(2)}</Text>
                        </View>
                      </View> */}
                  {/* {(cartItem.remarks && cartItem.remarks.length > 0) ?
                      <View style={{ alignItems: 'center', flexDirection: 'row' }}>
                        
                        <View style={{ flex: 1, justifyContent: 'center', }}>
                          <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 15 }}>{cartItem.remarks}</Text>
                        </View>
                        
                      </View>
                      : <></>
                    } */}
                </View>
              );
            })}
          </View>
        ) //: null
        }
      </TouchableOpacity>
    );
  };

  // const downloadCsv = () => {
  //     //if (productSales && productSales.dataSource && productSales.dataSource.data) {
  //     //const csvData = convertArrayToCSV(productSales.dataSource.data);
  //     const csvData = convertArrayToCSV(CsvData);

  //     const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir
  //         }/koodoo-report-Product-Sales${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
  //     // console.log('PATH', pathToWrite);
  //     RNFetchBlob.fs
  //         .writeFile(pathToWrite, csvData, 'utf8')
  //         .then(() => {
  //             // console.log(`wrote file ${pathToWrite}`);
  //             // wrote file /storage/emulated/0/Download/data.csv
  //             window.confirm(
  //                 'Success',
  //                 `Send to ${pathToWrite}`,
  //                 [{ text: 'OK', onPress: () => { } }],
  //                 { cancelable: false },
  //             );
  //         })
  //         .catch((error) => console.error(error));
  //     //}
  // };

  const convertDataToExcelFormat = () => {
    var excelData = [];

    if (!showDetails) {
      for (var i = 0; i < transactionTypeSales.length; i++) {
        var excelRow = {
          'Transaction Category': transactionTypeSales[i].orderType,
          'Order (Qty)': transactionTypeSales[i].totalTransactions,
          'Sales (RM)': +parseFloat(
            transactionTypeSales[i].totalSales,
          ).toFixed(2),
          'Disc (RM)': +parseFloat(
            transactionTypeSales[i].totalDiscount,
          ).toFixed(2),
          'Disc (%)': +parseFloat(transactionTypeSales[i].totalDiscount != 0
            ? (transactionTypeSales[i].totalDiscount / transactionTypeSales[i].totalSales) * 100
            : 0
          ).toFixed(
            2,
          ),
          'Tax (RM)': +parseFloat(transactionTypeSales[i].tax).toFixed(2),
          'Service Charge (RM)': +parseFloat(
            transactionTypeSales[i].serviceCharge,
          ).toFixed(2),
          'Sales Return (RM)': +parseFloat(transactionTypeSales[i].salesReturn || 0).toFixed(2),
          //'GP (%)': parseFloat(transactionTypeSales[i].gp).toFixed(2),
          'Net Sales (RM)': +parseFloat(
            transactionTypeSales[i].netSales,
          ).toFixed(2),
          // 'Average Net Sales (RM)': +parseFloat(
          //   transactionTypeSales[i].averageNetSales,
          // ).toFixed(2),
        };

        excelData.push(excelRow);
      }
    } else {
      for (var i = 0; i < transactionTypeSalesDetails.length; i++) {
        const calculatedDiscount = getOrderDiscountInfoInclOrderBased(transactionTypeSalesDetails[i]);

        var excelRow = {
          'Transaction Category':
            ORDER_TYPE_PARSED[transactionTypeSalesDetails[i].orderType],
          'Transaction Date': moment(
            transactionTypeSalesDetails[i].createdAt,
          ).format('DD MMM YY hh:mm A'),
          'Sales (RM)': +parseFloat((transactionTypeSalesDetails[i].finalPriceBefore ? transactionTypeSalesDetails[i].finalPriceBefore : transactionTypeSalesDetails[i].finalPrice) + getOrderDiscountInfo(transactionTypeSalesDetails[i])).toFixed(2),
          'Disc (RM)': +parseFloat(
            getOrderDiscountInfoInclOrderBased(transactionTypeSalesDetails[i]),
          ).toFixed(2),
          'Disc (%)': +parseFloat(transactionTypeSalesDetails[i].discountPercentage).toFixed(2),
          'Tax (RM)': +parseFloat(transactionTypeSalesDetails[i].tax).toFixed(
            2,
          ),
          'Service Charge (RM)': +parseFloat(transactionTypeSalesDetails[i].sc || 0)
            .toFixed(2),
          'Sales Return (RM)': +parseFloat(transactionTypeSalesDetails[i].salesReturn || 0),
          //'Tax (RM)': parseFloat(0).toFixed(2),
          //'GP (%)': parseFloat(0).toFixed(2),
          'Net Sales (RM)': +parseFloat(transactionTypeSalesDetails[i].finalPriceBefore != 0 || transactionTypeSalesDetails[i].finalPrice != 0
            ? (transactionTypeSalesDetails[i].finalPriceBefore
              ? transactionTypeSalesDetails[i].finalPriceBefore
              : transactionTypeSalesDetails[i].finalPrice) -
            transactionTypeSalesDetails[i].tax -
            (transactionTypeSalesDetails[i].sc || 0)
            : 0
          ).toFixed(2),
        };

        excelData.push(excelRow);
      }
    }

    // console.log('excelData');
    // console.log(excelData);

    return excelData;
  };
  const handleExportExcel = () => {
    var wb = XLSX.utils.book_new(),
      ws = XLSX.utils.json_to_sheet(convertDataToExcelFormat());

    XLSX.utils.book_append_sheet(wb, ws, "ReportSalesTransaction");
    XLSX.writeFile(wb, "ReportSalesTransaction.xlsx");
  };

  const convertDataToCSVFormat = () => {
    var csvData = [];

    if (!showDetails) {
      csvData.push(
        `Transaction Category,Order (Qty),Sales (RM),Disc (RM),Disc (%),Tax (RM),Service Charge (RM),Sales Return (RM),Net Sales (RM)`,
      );

      for (var i = 0; i < transactionTypeSales.length; i++) {
        var csvRow = `${transactionTypeSales[i].orderType},${transactionTypeSales[i].totalTransactions
          },${transactionTypeSales[i].totalSales.toFixed(2)},${+parseFloat(
            transactionTypeSales[i].totalDiscount,
          ).toFixed(2)},${(transactionTypeSales[i].totalDiscount != 0
            ? (transactionTypeSales[i].totalDiscount /
              transactionTypeSales[i].totalSales) *
            100
            : 0
          ).toFixed(2)},${(transactionTypeSales[i].tax || 0).toFixed(
            2,
          )},${transactionTypeSales[i].serviceCharge.toFixed(2)},${(
            transactionTypeSales[i].salesReturn || 0
          ).toFixed(2)},${transactionTypeSales[i].netSales.toFixed(2)}`;

        csvData.push(csvRow);
      }
    } else {
      csvData.push(
        `Transaction Category,Transaction Date,Sales (RM),Disc (RM),Disc (%),Tax (RM),Service Charge (RM),Sales Return (RM),Net Sales (RM)`,
      );

      for (var i = 0; i < transactionTypeSalesDetails.length; i++) {
        var csvRow = `${ORDER_TYPE_PARSED[transactionTypeSalesDetails[i].orderType]
          },${moment(transactionTypeSalesDetails[i].createdAt).format(
            'DD MMM YY hh:mm A',
          )},${(transactionTypeSalesDetails[i].finalPriceBefore
            ? transactionTypeSalesDetails[i].finalPriceBefore
            : transactionTypeSalesDetails[i].finalPrice + getOrderDiscountInfo(transactionTypeSalesDetails[i])
          ).toFixed(2)},${+parseFloat(
            getOrderDiscountInfoInclOrderBased(transactionTypeSalesDetails[i]),
          ).toFixed(2)},${+parseFloat(
            transactionTypeSalesDetails[i].discountPercentage,
          ).toFixed(2)},${(transactionTypeSalesDetails[i].tax || 0).toFixed(
            2,
          )},${(transactionTypeSalesDetails[i].sc || 0).toFixed(2)},${(
            transactionTypeSalesDetails[i].salesReturn || 0
          ).toFixed(2)},${(transactionTypeSalesDetails[i].finalPriceBefore != 0 ||
            transactionTypeSalesDetails[i].finalPrice != 0
            ? (transactionTypeSalesDetails[i].finalPriceBefore
              ? transactionTypeSalesDetails[i].finalPriceBefore
              : transactionTypeSalesDetails[i].finalPrice) -
            transactionTypeSalesDetails[i].tax -
            (transactionTypeSalesDetails[i].sc || 0)
            : 0
          ).toFixed(2)}`;

        csvData.push(csvRow);
      }
    }

    // console.log('excelData');
    // console.log(excelData);

    return csvData.join('\r\n');
  };

  // const downloadExcel = () => {
  //     const excelData = convertDataToExcelFormat();

  //     var excelFile = `${Platform.OS === 'ios'
  //         ? RNFS.DocumentDirectoryPath
  //         : RNFS.DownloadDirectoryPath
  //         }/koodoo-report-Product-Sales${moment().format(
  //             'YYYY-MM-DD-HH-mm-ss',
  //         )}.xlsx`;
  //     var excelWorkSheet = XLSX.utils.json_to_sheet(excelData);
  //     var excelWorkBook = XLSX.utils.book_new();
  //     XLSX.utils.book_append_sheet(
  //         excelWorkBook,
  //         excelWorkSheet,
  //         'Product Sales Report',
  //     );

  //     const workBookData = XLSX.write(excelWorkBook, {
  //         type: 'binary',
  //         bookType: 'xlsx',
  //     });

  //     RNFS.writeFile(excelFile, workBookData, 'ascii')
  //         .then((success) => {
  //             // console.log(`wrote file ${excelFile}`);

  //             window.confirm(
  //                 'Success',
  //                 `Send to ${excelFile}`,
  //                 [{ text: 'OK', onPress: () => { } }],
  //                 { cancelable: false },
  //             );
  //         })
  //         .catch((err) => {
  //             // console.log(err.message);
  //         });

  //     // XLSX.writeFileAsync(excelFile, excelWorkBook, () => {
  //     //     window.confirm(
  //     //         'Success',
  //     //         `Send to ${excelFile}`,
  //     //         [{ text: 'OK', onPress: () => { } }],
  //     //         { cancelable: false },
  //     //     );
  //     // });

  //     // const csvData = convertArrayToCSV(CsvData);

  //     // const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir}/koodoo-report-Product-Sales${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
  //     // // console.log("PATH", excelFile);
  //     // RNFetchBlob.fs
  //     //     .writeFile(excelFile, excelWorkBook, 'utf8')
  //     //     .then(() => {
  //     //         // console.log(`wrote file ${excelFile}`);
  //     //         window.confirm(
  //     //             'Success',
  //     //             `Send to ${excelFile}`,
  //     //             [{ text: 'OK', onPress: () => { } }],
  //     //             { cancelable: false },
  //     //         );
  //     //     })
  //     //     .catch(error => console.error(error));
  // };

  // const emailTransaction = () => {
  //     var body = {
  //         data: CsvData,
  //         //data: convertArrayToCSV(productSales.dataSource.data),
  //         data: convertArrayToCSV(CsvData),
  //         email: exportEmail,
  //     };

  //     ApiClient.POST(API.emailDashboard, body, false).then((result) => {
  //         if (result !== null) {
  //             window.confirm(
  //                 'Success',
  //                 'Email sent to your inbox',
  //                 [{ text: 'OK', onPress: () => { } }],
  //                 { cancelable: false },
  //             );
  //         }
  //     });

  //     setVisible(false);
  // };

  const emailTransaction = () => {
    const excelData = convertDataToExcelFormat();

    var body = {
      // data: CsvData,
      //data: convertArrayToCSV(todaySalesChart.dataSource.data),
      data: JSON.stringify(excelData),
      //data: convertDataToExcelFormat(),
      email: exportEmail,
    };

    ApiClient.POST(API.emailDashboard, body, false).then((result) => {
      if (result !== null) {
        window.confirm(
          "Success",
          "Email has been sent",
          [{ text: "OK", onPress: () => { } }],
          { cancelable: false }
        );
      }
    });

    setVisible(false);
  };

  var leftSpacing = "0%";

  if (windowWidth >= 1280) {
    leftSpacing = "-4%";
  }

  const leftSpacingScale = {
    marginLeft: leftSpacing,
  };

  const flatListRef = useRef();

  const ScrollToTop = () => {
    flatListRef.current.scrollToOffset({ animated: true, offset: 0 });
  };

  const ScrollToBottom = () => {
    flatListRef.current.scrollToOffset({ animated: true, offset: 100 });
  };
  
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [dimensions, setDimensions] = useState({
    window: Dimensions.get('window')
  });

  // ScreenSize Listener
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setDimensions({ window });
      
      if (!isSidebarOpen) {
        const newSidebarWidth = calculateSidebarWidth(window.width);
        sidebarXValue.setValue(-newSidebarWidth * 1.2);
      }
    });
    
    return () => subscription.remove();
  }, [isSidebarOpen]);

  const calculateSidebarWidth = (width) => {
    const isLandscapeMode = width > Dimensions.get('window').height;
    
    if (isLandscapeMode) {
      return isMobile() ? width * 0.23 : width * 0.08;
    } else {
      return isMobile() ? width * 0.23 : width * 0.08;
    }
  };

  const sidebarClose = () => {
    setIsSidebarOpen(false);
    Animated.timing(sidebarXValue, {
      toValue: -sidebarWidth,
      duration: 200,
      useNativeDriver: true,
    }).start();

    Animated.timing(contentXValue, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const sidebarOpen = () => {
    setIsSidebarOpen(true);
    Animated.timing(sidebarXValue, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
    
    Animated.timing(contentXValue, {
      toValue: sidebarWidth * 0.55,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const sidebarWidth = useMemo(() => {
    return calculateSidebarWidth(dimensions.window.width);
  }, [dimensions.window.width]);
  
  const sidebarXValue = useRef(new Animated.Value(
    isSidebarOpen ? 0 : -calculateSidebarWidth(Dimensions.get('window').width) * 1.2
  )).current;
  const contentXValue = useRef(new Animated.Value(0)).current;

  return (
    // <View style={styles.container}>
    //     <View style={styles.sidebar}>
    //<UserIdleWrapper disabled={!isMounted}>
    <View>
      {isMobile() && <TopBar navigation={navigation} />}
      <View
        style={[
          styles.container,
          {
            height: windowHeight,
            width: windowWidth,
          },
        ]}
      >
        
        <Animated.View
          style={[
            styles.sidebar,
            {
              transform: [{ translateX: sidebarXValue }],
              flex: 0.8,
              ...isMobile() && {
                flex: 0.9,
              },
              position: 'absolute',
              top: 0,
              left: 0,
              zIndex: 2,
              height: '100%',
              width: sidebarWidth,
              marginTop: 50,

            }
          ]}
        >
          <SideBar navigation={navigation} selectedTab={0} isSidebarOpen={isSidebarOpen} /*toggleSidebar={toggleSidebar} */ />
        </Animated.View>
       
        <View style={[{ height: windowHeight, flex: 9, },
        {
          ...isMobile() && {
            flex: 3,
            //backgroundColor:'yellow',
          }
        },
        ]}>
          <View
            style={{
              width: isMobile() ? windowWidth * 0.23 : 123,
              height: 70,
              //marginBottom: -25,
              zIndex: 3,
              // backgroundColor: isSidebarOpen ? Colors.whiteColor : {},
              // marginLeft: isSidebarOpen ? 0 : 0,
            }}
          >
            
              {isSidebarOpen ?
                <Animated.View
                  style={{
                    transform: [{ translateX: sidebarXValue }],
                    backgroundColor: Colors.whiteColor,
                    height: '100%',
                    width: sidebarWidth,
                  }}
                >
                  <TouchableOpacity
                    style={{ marginTop: 5, }}
                    onPress={sidebarClose}
                  >
                    <MaterialIcons name='keyboard-capslock' color={Colors.primaryColor} size={isMobile() ? 35 : 30} style={{ alignSelf: 'center', transform: [{ rotate: '270deg' }], marginTop: 7, marginLeft: -3}}/>
                  </TouchableOpacity>
                </Animated.View>
                :
                <TouchableOpacity
                  style={{ marginTop: 5, }}
                  onPress={sidebarOpen}
                >
                  <View style={{
                    flexDirection: 'row',
                    marginLeft: 10,
                    marginVertical: 10,
                    justifyContent: 'center',
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: '#4E9F7D',
                    borderRadius: 5,
                    //width: 160,
                    height: switchMerchant ? 35 : 40,
                    width: windowWidth * 0.4,
                    alignItems: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,

                  }}>
                    <MaterialIcons name='keyboard-capslock' color={Colors.whiteColor} size={isMobile() ? 25 : 30} style={{ alignSelf: 'flex-start', transform: [{ rotate: '90deg' }], marginTop: 5 }} />
                    <Text style={{
                      color: Colors.whiteColor,
                      marginLeft: 5,
                      fontSize: switchMerchant ? 10 : 16,
                      fontFamily: 'NunitoSans-Bold',
                    }}
                    >
                      MORE PAGES
                    </Text>
                  </View>
                </TouchableOpacity>
              }
            
          </View>

          <Animated.ScrollView
            showsVerticalScrollIndicator={false}
            style={{
              transform: [{ translateX: contentXValue}],
              ...isMobile() && {
              },
              // marginLeft: isSidebarOpen
              //   ? (isMobile() ? '20%' : '7%')
              //   : -10,
            }}
            contentContainerStyle={{
              paddingBottom: windowHeight * 0.05,
            }}>
            <ScrollView horizontal={simpliFiedLayout? isSidebarOpen: true} showsHorizontalScrollIndicator={simpliFiedLayout? isSidebarOpen: true}>
              <Modal
                style={{}}
                visible={exportModalVisibility}
                supportedOrientations={["portrait", "landscape"]}
                transparent={true}
                animationType={"fade"}
              >
                <View
                  style={{
                    flex: 1,
                    backgroundColor: Colors.modalBgColor,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <View
                    style={{
                      height: Dimensions.get("screen").width * 0.1,
                      width: Dimensions.get("screen").width * 0.18,
                      backgroundColor: Colors.whiteColor,
                      borderRadius: 12,
                      padding: Dimensions.get("screen").width * 0.03,
                      alignItems: "center",
                      justifyContent: "center",
                      ...isMobile() && {
                        height: Dimensions.get("screen").width * 0.35,
                        width: Dimensions.get("screen").width * 0.65,
                      }
                    }}
                  >
                    <TouchableOpacity
                      disabled={isLoading}
                      style={{
                        position: "absolute",
                        right: Dimensions.get("screen").width * 0.008,
                        top: Dimensions.get("screen").width * 0.008,

                        elevation: 1000,
                        zIndex: 1000,
                        ...isMobile() && {
                          right: Dimensions.get("screen").width * 0.015,
                          top: Dimensions.get("screen").width * 0.012,
                        }
                      }}
                      onPress={() => {
                        setExportModalVisibility(false);
                      }}
                    >
                      <AntDesign
                        name="closecircle"
                        size={switchMerchant ? 15 : 25}
                        color={Colors.fieldtTxtColor}
                      />
                    </TouchableOpacity>
                    <View
                      style={{
                        alignItems: "center",
                        top: "20%",
                        position: "absolute",
                      }}
                    >
                      <Text
                        style={{
                          fontFamily: "NunitoSans-Bold",
                          textAlign: "center",
                          fontSize: switchMerchant ? 16 : 24,
                        }}
                      >
                        Download Report
                      </Text>
                    </View>
                    <View style={{ top: switchMerchant ? "14%" : "10%" }}>
                      {/* <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 20,
                                            fontFamily: 'NunitoSans-Bold',
                                        }}>
                                        Email Address:
                                    </Text>
                                    <TextInput
                                        underlineColorAndroid={Colors.fieldtBgColor}
                                        style={{
                                            backgroundColor: Colors.fieldtBgColor,
                                            width: switchMerchant ? 240 : 370,
                                            height: switchMerchant ? 35 : 50,
                                            borderRadius: 5,
                                            padding: 5,
                                            marginVertical: 5,
                                            borderWidth: 1,
                                            borderColor: '#E5E5E5',
                                            paddingLeft: 10,
                                            fontSize: switchMerchant ? 10 : 14,
                                        }}
                                        autoCapitalize='none'
                                        placeholderStyle={{ padding: 5 }}
                                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                        placeholder="Enter your email"
                                        onChangeText={(text) => {
                                            setExportEmail(text);
                                        }}
                                        value={exportEmail}
                                    />
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 20,
                                            fontFamily: 'NunitoSans-Bold',
                                            marginTop: 15,
                                        }}>
                                        Send As:
                                    </Text> */}

                      <View
                        style={{
                          alignItems: "center",
                          justifyContent: "center",
                          flexDirection: "row",
                          marginTop: 30,
                        }}
                      >
                        <TouchableOpacity
                          disabled={isLoading}
                          style={{
                            justifyContent: "center",
                            flexDirection: "row",
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: "#4E9F7D",
                            borderRadius: 5,
                            width: switchMerchant ? 100 : 100,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: "center",
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginRight: 15,
                          }}
                          onPress={() => {
                            // if (exportEmail.length > 0) {
                            //     CommonStore.update((s) => {
                            //         s.isLoading = true;
                            //     });

                            //     setIsExcel(true);

                            //     const excelData = convertDataToExcelFormat();

                            //     generateEmailReport(
                            //         EMAIL_REPORT_TYPE.EXCEL,
                            //         excelData,
                            //         'KooDoo Transaction Sales Report',
                            //         'KooDoo Transaction Sales Report.xlsx',
                            //         `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                            //         exportEmail,
                            //         'KooDoo Transaction Sales Report',
                            //         'KooDoo Transaction Sales Report',
                            //         () => {
                            //             CommonStore.update((s) => {
                            //                 s.isLoading = false;
                            //             });

                            //             setIsExcel(false);

                            //             window.confirm(
                            //                 'Success',
                            //                 'Report will be sent to the email address shortly',
                            //             );

                            //             setExportModalVisibility(false);
                            //         },
                            //     );
                            // } else {
                            //     window.confirm('Info', 'Invalid email address');
                            // }
                            handleExportExcel();
                          }}
                        >
                          {isLoading && isExcel ? (
                            <ActivityIndicator
                              size={"small"}
                              color={Colors.whiteColor}
                            />
                          ) : (
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: "NunitoSans-Bold",
                              }}
                            >
                              EXCEL
                            </Text>
                          )}
                        </TouchableOpacity>

                        {/* <TouchableOpacity
                                            disabled={isLoading}
                                            style={{
                                                justifyContent: 'center',
                                                flexDirection: 'row',
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: '#4E9F7D',
                                                borderRadius: 5,
                                                width: switchMerchant ? 100 : 100,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 35 : 40,
                                                alignItems: 'center',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                            }}
                                            onPress={() => {
                                                if (exportEmail.length > 0) {
                                                    CommonStore.update((s) => {
                                                        s.isLoading = true;
                                                    });

                                                    setIsCsv(true);

                                                    //const csvData = convertArrayToCSV(transactionTypeSales);
                                                    const csvData = convertDataToCSVFormat();

                                                    generateEmailReport(
                                                        EMAIL_REPORT_TYPE.CSV,
                                                        csvData,
                                                        'KooDoo Transaction Sales Report',
                                                        'KooDoo Transaction Sales Report.csv',
                                                        `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                                                        exportEmail,
                                                        'KooDoo Transaction Sales Report',
                                                        'KooDoo Transaction Sales Report',
                                                        () => {
                                                            CommonStore.update((s) => {
                                                                s.isLoading = false;
                                                            });

                                                            setIsCsv(false);

                                                            window.confirm(
                                                                'Success',
                                                                'Report will be sent to the email address shortly',
                                                            );

                                                            setExportModalVisibility(false);
                                                        },
                                                    );
                                                } else {
                                                   window.confirm('Info', 'Invalid email address');
                                                }
                                            }}>
                                            {isLoading && isCsv ? (
                                                <ActivityIndicator
                                                    size={'small'}
                                                    color={Colors.whiteColor}
                                                />
                                            ) : (
                                                <Text
                                                    style={{
                                                        color: Colors.whiteColor,
                                                        //marginLeft: 5,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: 'NunitoSans-Bold',
                                                    }}>
                                                    CSV
                                                </Text>
                                            )}
                                        </TouchableOpacity> */}
                        <CSVLink
                          style={{
                            justifyContent: "center",
                            flexDirection: "row",
                            borderWidth: 1,
                            textDecoration: "none",
                            borderColor: Colors.primaryColor,
                            backgroundColor: "#4E9F7D",
                            borderRadius: 5,
                            width: switchMerchant ? 100 : 100,
                            paddingHorizontal: 10,
                            height: switchMerchant ? 35 : 40,
                            alignItems: "center",
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                          }}
                          data={convertDataToCSVFormat()}
                          filename="ReportSalesTransaction.csv"
                        >
                          <View
                            style={{
                              width: "100%",
                              height: "100%",
                              alignContent: "center",
                              alignItems: "center",
                              alignSelf: "center",
                              justifyContent: "center",
                            }}
                          >
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: "NunitoSans-Bold",
                              }}
                            >
                              CSV
                            </Text>
                          </View>
                        </CSVLink>

                        {/* <TouchableOpacity
                                            style={[styles.modalSaveButton, {
                                                zIndex: -1
                                            }]}
                                            onPress={() => { downloadPDF() }}>
                                            <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>PDF</Text>
                                        </TouchableOpacity> */}
                      </View>
                    </View>
                  </View>
                </View>
              </Modal>

              <DateTimePickerModal
                isVisible={showDateTimePicker}
                mode={"date"}
                onConfirm={(text) => {
                  setRev_date(moment(text).startOf("day"));
                  setShowDateTimePicker(false);
                }}
                onCancel={() => {
                  setShowDateTimePicker(false);
                }}
                maximumDate={moment(rev_date1).toDate()}
                date={moment(rev_date).toDate()}
              />

              <DateTimePickerModal
                isVisible={showDateTimePicker1}
                mode={"date"}
                onConfirm={(text) => {
                  setRev_date1(moment(text).endOf("day"));
                  setShowDateTimePicker1(false);
                }}
                onCancel={() => {
                  setShowDateTimePicker1(false);
                }}
                minimumDate={moment(rev_date).toDate()}
                date={moment(rev_date1).toDate()}
              />

              {/* <Modal
                style={{ flex: 1 }}
                visible={visible}
                transparent={true}
                animationType="slide">

                <KeyboardAvoidingView
                    behavior="padding"
                    style={{
                        backgroundColor: 'rgba(0,0,0,0.5)',
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        minHeight: windowHeight,
                    }}>
                    <View style={styles.confirmBox}>

                        <Text style={{ fontSize: 24, justifyContent: "center", alignSelf: "center", marginTop: 40, fontFamily: 'NunitoSans-Bold' }}>Enter your email</Text>
                        <View style={{ justifyContent: "center", alignSelf: "center", alignContent: 'center', marginTop: 20, flexDirection: 'row', width: '80%' }}>
                            <View style={{ justifyContent: 'center', marginHorizontal: 5 }}>
                                <Text style={{ color: Colors.descriptionColor, fontSize: 20, }}>
                                    email:
                                    </Text>
                            </View>
                            <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={styles.textInput8}
                                placeholder="Enter your email"
                                // style={{
                                //     // paddingLeft: 1,
                                // }}                                        
                                //defaultValue={extentionCharges}
                                onChangeText={(text) => {
                                    // setState({ exportEmail: text });
                                    setExportEmail(text);
                                }}
                                value={exportEmail}
                            />

                <View style={{flexDirection:'row', marginTop: '13.5%'}}>
                                <TouchableOpacity
                                    onPress={emailTransaction}
                                    style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: '50%',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        alignContent: 'center',
                                        height: 60,
                                        borderBottomLeftRadius: 30,
                                        borderRightWidth: StyleSheet.hairlineWidth,
                                        borderTopWidth: StyleSheet.hairlineWidth
                                    }}>
                                    <Text style={{ fontSize: 22, color: Colors.primaryColor, fontFamily: 'NunitoSans-SemiBold' }}>
                                        Email
                                    </Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => {
                                        // setState({ visible: false });
                                        setVisible(false);
                                    }}
                                    style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: '50%',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        alignContent: 'center',
                                        height: 60,
                                        borderBottomRightRadius: 30,
                                        borderTopWidth: StyleSheet.hairlineWidth
                                    }}>
                                    <Text style={{ fontSize: 22, color: Colors.descriptionColor, fontFamily: 'NunitoSans-SemiBold' }}>
                                        Cancel
                                            </Text>
                                </TouchableOpacity>
                            </View>
                        </View>

                    </View>
                </KeyboardAvoidingView>
            </Modal> */}

              {/* <View style={[styles.content, {
                //top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 1 : 0,
            }]}> */}
              <View
                style={{
                  padding: 20,
                  // width: windowWidth - 140,
                  width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
                  backgroundColor: Colors.highlightColor,

                  //position: 'relative',
                  //backgroundColor: isSidebarOpen ? 'red' : 'blue',
                  //position: isSidebarOpen ? {} : 'absolute',
                  //marginLeft: isSidebarOpen ? 0 : -90,
                  //zIndex: isSidebarOpen ? 1 : 100000,

                  ...isMobile() && {
                    //backgroundColor: 'red',
                    width: simpliFiedLayout ? windowWidth *1.05 : windowWidth * 3,
                    marginLeft: isSidebarOpen ? 10 : 5,
                  }
                }}>

                <View
                  style={{
                    height: simpliFiedLayout ? 'auto' : 70,
                    // flexDirection: 'row',
                    // justifyContent: 'space-between',
                    flexDirection: simpliFiedLayout ? 'column' : 'row',
                    alignSelf: simpliFiedLayout ? 'flex-start' : 'center',
                    alignItems: simpliFiedLayout ? 'flex-start' : 'center',
                    //backgroundColor: '#ffffff',
                    justifyContent: simpliFiedLayout ? 'flex-start' : 'space-between',
                    //padding: 18,
                    //marginTop: 5,
                    width: windowWidth * 0.87,
                    paddingLeft: 1,
                    zIndex: 2,
                    ...isMobile() && {
                      width: '100%',
                      //backgroundColor:'purple',
                    },
                  }}>
                  <Text
                    style={{
                      fontSize: switchMerchant ? 20 : 26,
                      fontFamily: "NunitoSans-Bold",
                    }}
                  >
                    Sales By: {name}
                  </Text>
                  <View
                    style={{
                      flexDirection: simpliFiedLayout ? 'column' : "row",
                    }}
                  >
                    <View >
                    {/* <View >
                      <DropDownPicker
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: 210,
                          height: 40,
                          borderRadius: 10,
                          borderWidth: 1,
                          borderColor: "#E5E5E5",
                          flexDirection: "row",
                        }}
                        dropDownContainerStyle={{
                          width: 210,
                          backgroundColor: Colors.fieldtBgColor,
                          borderColor: "#E5E5E5",
                        }}
                        labelStyle={{
                          marginLeft: 5,
                          flexDirection: "row",
                        }}
                        textStyle={{
                          fontSize: 14,
                          fontFamily: 'NunitoSans-Regular',

                          marginLeft: 5,
                          paddingVertical: 10,
                          flexDirection: "row",
                        }}
                        selectedItemContainerStyle={{
                          flexDirection: "row",
                        }}

                        showArrowIcon={true}
                        ArrowDownIconComponent={({ style }) => (
                          <Ionicon
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            name="chevron-down-outline"
                          />
                        )}
                        ArrowUpIconComponent={({ style }) => (
                          <Ionicon
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            name="chevron-up-outline"
                          />
                        )}

                        showTickIcon={true}
                        TickIconComponent={({ press }) => (
                          <Ionicon
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            color={
                              press ? Colors.fieldtBgColor : Colors.primaryColor
                            }
                            name={'md-checkbox'}
                            size={25}
                          />
                        )}

                        placeholder="Choose Outlet"
                        multipleText={`${selectedOutletList ? selectedOutletList.length : '0'} outlet(s) selected`}
                        placeholderStyle={{
                          color: Colors.fieldtTxtColor,
                          // marginTop: 15,
                        }}
                        // multipleText={'%d outlet(s) selected'}
                        items={outletDropdownList}
                        value={selectedOutletList}
                        multiple={true}
                        open={openOS}
                        setOpen={setOpenOS}
                        onSelectItem={(items) => {
                          // setSelectedOutletList(items.map(item => item.value))
                          CommonStore.update((s) => {
                            s.reportOutletIdList = items.map(item => item.value)
                          })
                        }}
                        dropDownDirection="BOTTOM"
                      />
                    </View>*/}
                    </View> 
                    <View
                      style={[
                        {
                          flexDirection: "row",
                          alignItems: "center",
                          borderRadius: 10,
                          marginRight: 10,
                          // marginLeft: 10,
                          zIndex: 5,
                          marginTop: simpliFiedLayout ? 10 : 0,
                        },
                      ]}>
                      <DropDownPicker
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: 210,
                          height: 40,
                          borderRadius: 10,
                          borderWidth: 1,
                          borderColor: "#E5E5E5",
                          flexDirection: "row",
                        }}
                        dropDownContainerStyle={{
                          width: 210,
                          backgroundColor: Colors.fieldtBgColor,
                          borderColor: "#E5E5E5",
                        }}
                        labelStyle={{
                          marginLeft: 5,
                          flexDirection: "row",
                        }}
                        textStyle={{
                          fontSize: 14,
                          fontFamily: 'NunitoSans-Regular',

                          marginLeft: 5,
                          paddingVertical: 10,
                          flexDirection: "row",
                        }}
                        selectedItemContainerStyle={{
                          flexDirection: "row",
                        }}

                        showArrowIcon={true}
                        ArrowDownIconComponent={({ style }) => (
                          <Ionicon
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            name="chevron-down-outline"
                          />
                        )}
                        ArrowUpIconComponent={({ style }) => (
                          <Ionicon
                            size={25}
                            color={Colors.fieldtTxtColor}
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            name="chevron-up-outline"
                          />
                        )}

                        showTickIcon={true}
                        TickIconComponent={({ press }) => (
                          <Ionicon
                            style={{ paddingHorizontal: 5, marginTop: 5 }}
                            color={
                              press ? Colors.fieldtBgColor : Colors.primaryColor
                            }
                            name={'md-checkbox'}
                            size={25}
                          />
                        )}
                        placeholder={'Select a Type'}
                        placeholderStyle={{
                          color: Colors.fieldtTxtColor,
                          // marginTop: 15,
                        }}
                        // searchable
                        // searchableStyle={{
                        //   paddingHorizontal: windowWidth * 0.0079,
                        // }}
                        value={filterAppType}
                        items={[
                          // { label: "All", value: "ALL" },
                          { label: "Merchant App Order", value: APP_TYPE.MERCHANT },
                          { label: "Waiter App Order", value: APP_TYPE.WAITER },
                          { label: "User App Order", value: APP_TYPE.USER },
                          { label: "QR Order", value: APP_TYPE.WEB_ORDER },
                        ]}
                        multiple={true}
                        multipleText={`${filterAppType.length} App Type(s)`}
                        onSelectItem={(items) => {
                          setFilterAppType(items.map(item => item.value))
                        }}
                        open={openFA}
                        setOpen={setOpenFA}
                        dropDownDirection="BOTTOM"
                      />
                    </View>
                    <TouchableOpacity
                      style={{
                        justifyContent: "center",
                        flexDirection: "row",
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: "#4E9F7D",
                        borderRadius: 5,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: "center",
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginRight: 10,
                        marginTop: simpliFiedLayout ? 10 : 0,
                        width: 140,
                      }}
                      onPress={() => {
                        setExportModalVisibility(true);
                      }}
                    >
                      <View
                        style={{ flexDirection: "row", alignItems: "center" }}
                      >
                        <Icon
                          name="download"
                          size={switchMerchant ? 10 : 20}
                          color={Colors.whiteColor}
                        />
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: "NunitoSans-Bold",
                          }}
                        >
                          DOWNLOAD
                        </Text>
                      </View>
                    </TouchableOpacity>


                    <View
                      style={{
                        width: switchMerchant ? 200 : 250,
                        height: switchMerchant ? 35 : 40,
                        backgroundColor: "white",
                        borderRadius: 5,
                        // marginLeft: '53%',
                        flexDirection: "row",
                        alignContent: "center",
                        alignItems: "center",
                        marginTop: simpliFiedLayout ? 10 : 0,
                        //marginRight: windowWidth * Styles.sideBarWidth,

                        shadowColor: "#000",
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                        borderWidth: 1,
                        borderColor: "#E5E5E5",
                      }}
                    >
                      <Icon
                        name="search"
                        size={switchMerchant ? 13 : 18}
                        color={Colors.primaryColor}
                        style={{ marginLeft: 15 }}
                      />
                      <TextInput
                        editable={!loading}
                        underlineColorAndroid={Colors.whiteColor}
                        style={{
                          width: switchMerchant ? 180 : 220,
                          fontSize: switchMerchant ? 10 : 15,
                          fontFamily: "NunitoSans-Regular",
                          paddingLeft: 5,
                          height: 45,
                        }}
                        placeholderTextColor={Platform.select({
                          ios: "#a9a9a9",
                        })}
                        clearButtonMode="while-editing"
                        placeholder=" Search"
                        onChangeText={(text) => {
                          setSearch(text);
                          // setList1(false);
                          // setSearchList(true);
                        }}
                        value={search}
                      />
                    </View>

                  </View>
                </View>


                <View
                  style={[
                    {
                      // marginRight: 10,
                      // paddingLeft: 15,
                      paddingHorizontal: 15,
                      flexDirection: "row",
                      alignItems: simpliFiedLayout ? 'flex-start' : "center",
                      borderRadius: 10,
                      paddingVertical: 10,
                      justifyContent: simpliFiedLayout ? 'flex-start' : "flex-end",
                      backgroundColor: Colors.whiteColor,
                      shadowOpacity: 0,
                      shadowColor: "#000",
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                      marginTop: simpliFiedLayout ? 15 : 30,
                      width: switchMerchant ? 200 : 250,
                      alignSelf: simpliFiedLayout ? 'flex-start' : 'flex-end',
                    },
                  ]}
                >
                  <View
                    style={{ alignSelf: "center", marginRight: 5 }}
                    onPress={() => {
                      setState({
                        pickerMode: "date",
                        showDateTimePicker: true,
                      });
                    }}
                  >
                    {/* <EvilIcons name="calendar" size={25} color={Colors.primaryColor} /> */}
                    <GCalendar
                      width={switchMerchant ? 15 : 20}
                      height={switchMerchant ? 15 : 20}
                    />
                  </View>

                  <DatePicker
                    selected={moment(historyStartDate).toDate()}
                    onChange={(date) => {
                      // setRev_date(moment(text).startOf('day'));
                      CommonStore.update(s => {
                        s.historyStartDate = moment(date).startOf('day');
                      });
                    }}
                    maxDate={moment(historyEndDate).toDate()}
                  />

                  <Text
                    style={
                      switchMerchant
                        ? { fontSize: 10, fontFamily: "NunitoSans-Regular", marginHorizontal: 10 }
                        : { fontFamily: "NunitoSans-Regular", marginHorizontal: 10 }
                    }
                  >
                    -
                  </Text>

                  <DatePicker
                    selected={moment(historyEndDate).toDate()}
                    onChange={(date) => {
                      // setRev_date1(moment(text).endOf('day'));
                      CommonStore.update(s => {
                        s.historyEndDate = moment(date).endOf('day');
                      });
                    }}
                    minDate={moment(historyStartDate).toDate()}
                  />
                </View>

                {/* <Text style={{ fontSize: 16, marginTop: 10, color: Colors.descriptionColor }}>Date last updated on {moment().format('LLLL')}</Text> */}
                {showDetails? (
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "flex-start",
                    alignSelf: "flex-start",
                    //backgroundColor: '#ffffff',
                    justifyContent: "flex-start",
                    //padding: 18,
                    //marginTop: 5,
                    width: Dimensions.get("screen").width * 0.87,
                    zIndex: -1,
                  }}
                >
                  {/* <View
                            style={[{
                                // flex: 1,
                                // alignContent: 'flex-end',
                                // marginBottom: 10,
                                // flexDirection: 'row',
                                // marginRight: '-40%',
                                // marginLeft: 310,
                                // backgroundColor: 'red',
                                // alignItems: 'flex-end',
                                // right: '-50%',
                                width: '45%',
                                height: 40,

                            }, !isTablet ? {
                                marginLeft: 0,
                            } : {}]}>
                            <View style={{
                                width: 250,
                                height: 40,
                                backgroundColor: 'white',
                                borderRadius: 10,
                                // marginLeft: '53%',
                                flexDirection: 'row',
                                alignContent: 'center',
                                alignItems: 'center',

                                //marginRight: windowWidth * Styles.sideBarWidth,

                                position: 'absolute',
                                //right: '17%',

                                shadowColor: '#000',
                                shadowOffset: {
                                    width: 0,
                                    height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 3,
                            }}>
                                <Icon name="search" size={18} color={Colors.fieldtTxtColor} style={{ marginLeft: 15 }} />
                                <TextInput
                                    editable={!loading}
                                    underlineColorAndroid={Colors.whiteColor}
                                    style={{
                                        width: 250,
                                        fontSize: 15,
                                        fontFamily: 'NunitoSans-Regular',
                                    }}
                                    clearButtonMode="while-editing"
                                    placeholder=" Search"
                                    onChangeText={(text) => {
                                        setSearch(text);
                                        // setList1(false);
                                        // setSearchList(true);
                                    }}
                                    value={search}
                                />
                            </View>
                        </View> */}

                  {/* <TouchableOpacity style={{ marginRight: 10, width: 230, flexDirection: 'row', alignItems: 'center', paddingLeft: 15, borderRadius: 10, height: 40, backgroundColor: Colors.whiteColor }}
                                    onPress={() => { changeClick() }}>
                                    <TouchableOpacity style={{ alignSelf: "center" }} onPress={() => { setState({ pickerMode: 'date', showDateTimePicker: true }) }}>
                                        <EvilIcons name="calendar" size={25} color={Colors.primaryColor} />
                                    </TouchableOpacity>
                                    <Text style={{ fontFamily: "NunitoSans-Regular" }}>{moment(rev_date).format("DD MMM yyyy")} - {moment(rev_date1).format("DD MMM yyyy")}</Text>
                                </TouchableOpacity> */}

                  <TouchableOpacity
                    style={[
                      {
                        justifyContent: "center",
                        flexDirection: "row",
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: "#4E9F7D",
                        borderRadius: 5,
                        marginTop: simpliFiedLayout ? 10 : 0,
                        marginBottom: 10,
                        //width: 160,
                        paddingHorizontal: 10,
                        height: switchMerchant ? 35 : 40,
                        alignItems: "center",
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,

                        opacity: !showDetails ? 0 : 100,
                        //marginTop: 10,
                      },
                    ]}
                    onPress={() => {
                      setShowDetails(false);
                      setCurrentPage(pageReturn);
                      // console.log('Returning to page');
                      // console.log(pageReturn);
                      setPageCount(
                        Math.ceil(transactionTypeSales.length / perPage)
                      );
                      setCurrReportSummarySort("");
                      setCurrReportDetailsSort("");
                    }}
                    disabled={!showDetails}
                  >
                    <AntDesign
                      name="arrowleft"
                      size={switchMerchant ? 10 : 20}
                      color={Colors.whiteColor}
                      style={
                        {
                          // top: -1,
                          //marginRight: -5,
                        }
                      }
                    />
                    <Text
                      style={{
                        color: Colors.whiteColor,
                        marginLeft: 5,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: "NunitoSans-Bold",
                        // marginBottom: Platform.OS === 'ios' ? 0 : 2
                      }}
                    >
                      Summary
                    </Text>
                  </TouchableOpacity>

                  <View style={{ flexDirection: "row" }}>


                    {/* <TouchableOpacity
                                style={{
                                    paddingHorizontal: 15, backgroundColor: Colors.whiteColor, height: 40, width: 120, alignItems: 'center', borderRadius: 7, flexDirection: 'row', justifyContent: 'center', shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                }}
                                onPress={() => {
                                    // setState({
                                    //     visible: true
                                    // })
                                    setVisible(true);
                                }}
                            >
                                <Upload width={15} height={15} />
                                <Text style={{ fontFamily: "NunitoSans-Regular", fontSize: 13, marginLeft: 12 }}>Email</Text>
                            </TouchableOpacity> */}
                  </View>

                  {/* <View style={{ flex: 4 }}>
                            <TouchableOpacity>
                                <View style={{ width: '92%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                    <AntDesign name='search1' size={25} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                    <TextInput
                                        editable={!loading}
                                        underlineColorAndroid={Colors.whiteColor}
                                        style={{ width: '82%' }}
                                        clearButtonMode="while-editing"
                                        placeholder=" Search"
                                        onChangeText={(text) => {
                                            setState({
                                                search: text.trim(),
                                                list1: false,
                                                searchList: true,
                                            });
                                        }}
                                        value={search}
                                    //onSubmitEditing={searchBarItem()}
                                    />
                                </View>
                            </TouchableOpacity>
                        </View>
                        <View style={{ flex: 6, flexDirection: 'row', justifyContent: 'flex-end' }}>
                            <View style={{ width: '40%' }}>
                                <TouchableOpacity style={{ width: '100%' }} onPress={() => { setState({ day: !day }) }}>
                                    <View style={{ width: '100%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                        <EvilIcons name='calendar' size={30} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                        <View style={{ justifyContent: 'center', flex: 2 }}>
                                            <Text style={{ color: Colors.descriptionColor, marginLeft: '2%', fontSize: 12 }}>{moment(startDate).format('DD MMM YYYY')} - {moment(endDate).format('DD MMM YYYY')} </Text>
                                        </View>
                                    </View>
                                </TouchableOpacity>
                                <DateTimePickerModal
                                    isVisible={showDateTimePicker}
                                    mode={pickerMode}
                                    onConfirm={(text) => {
                                        if (pick == 1) {
                                            var date_ob = new Date(text);
                                            let date = ("0" + date_ob.getDate()).slice(-2);
                                            let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
                                            let year = date_ob.getFullYear();
                                            setState({ startDate: year + "-" + month + "-" + date })
                                        } else {
                                            var date_ob = new Date(text);
                                            let date = ("0" + date_ob.getDate()).slice(-2);
                                            let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
                                            let year = date_ob.getFullYear();
                                            setState({ endDate: year + "-" + month + "-" + date })
                                        }

                                        setState({ showDateTimePicker: false })
                                    }}
                                    onCancel={() => {
                                        setState({ showDateTimePicker: false })
                                    }}
                                />
                                {day ?
                                    <View style={{ position: 'absolute', width: "100%", backgroundColor: Colors.whiteColor, marginTop: '20%', zIndex: 6000 }}>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.primaryColor }} onPress={() => { moment() }}>
                                            <Text style={{ color: Colors.whiteColor }}>Today</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).subtract(1, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>Yesterday</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).subtract(7, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>Last 7 days</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).subtract(30, 'days')).format('YYYY-MM-DD'), endDate: moment(new Date()).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>Last 30 days</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(new Date()).startOf("month")).format('YYYY-MM-DD'), endDate: moment(moment(new Date()).endOf("month")).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>This month</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }} onPress={() => { setState({ startDate: moment(moment(moment(new Date()).startOf("month")).subtract(1, 'month')).format('YYYY-MM-DD'), endDate: moment(moment(moment(new Date()).endOf("month")).subtract(1, 'month')).format('YYYY-MM-DD') }) }}>
                                            <Text style={{ color: "#828282" }}>Last month</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={{ padding: 10, backgroundColor: Colors.whiteColor }}>
                                            <Text style={{ color: "#828282" }}>Custom range</Text>
                                        </TouchableOpacity>
                                        <View style={{ flexDirection: 'row' }}>
                                            <View style={{ flex: 1, marginLeft: 25 }}>
                                                <Text style={{ color: "#828282" }}>From</Text>
                                            </View>
                                            <View style={{ flex: 1 }}>
                                                <Text style={{ color: "#828282" }}>To</Text>
                                            </View>
                                        </View>
                                        <View style={{ flexDirection: 'row' }}>
                                            <TouchableOpacity style={{ width: "38%", marginLeft: 25, borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.fieldtBgColor }}
                                                onPress={() => { setState({ pick: 1, pick1: 0, pickerMode: 'date', showDateTimePicker: true }) }}>
                                                <Text style={{ fontSize: 12 }}>{moment(startDate).format("DD MMM yyyy")}</Text>
                                            </TouchableOpacity>
                                            <View style={{ width: "8%" }}>
                                            </View>
                                            <TouchableOpacity style={{ width: "38%", borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.fieldtBgColor }}
                                                onPress={() => { setState({ pick: 0, pick1: 1, pickerMode: 'date', showDateTimePicker: true }) }}>
                                                <Text style={{ fontSize: 12 }}>{moment(endDate).format("DD MMM yyyy")}</Text>
                                            </TouchableOpacity>
                                        </View>
                                        <View style={{ flexDirection: 'row', marginTop: 20 }}>
                                            <TouchableOpacity style={{ width: "38%", marginLeft: 15, borderWidth: 1, alignItems: "center", borderColor: "#919191", backgroundColor: Colors.whiteColor, height: 30, borderRadius: 5, justifyContent: "center", alignItems: 'center' }}
                                                onPress={() => { setState({ day: false }) }}>
                                                <Text style={{ fontSize: 15, color: "#919191" }}>Cancel</Text>
                                            </TouchableOpacity>
                                            <View style={{ width: "8%" }}>
                                            </View>
                                            <TouchableOpacity style={{ width: "38%", borderWidth: 1, alignItems: "center", borderColor: Colors.primaryColor, backgroundColor: Colors.primaryColor, height: 30, borderRadius: 5, justifyContent: "center", alignItems: 'center' }}
                                                onPress={() => { setState({ day: false }), getDetail() }}>
                                                <Text style={{ fontSize: 15, color: Colors.whiteColor }}>Apply</Text>
                                            </TouchableOpacity>
                                        </View>
                                        <View style={{ height: 20 }}>
                                        </View>
                                    </View>
                                    : null}
                            </View>
                            <View style={{ width: '4%' }}></View>
                            <TouchableOpacity style={{ width: '20%' }} onPress={() => { setState({ visible: true }); }}>
                                <View style={{ width: '100%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                    <AntDesign name='download' size={20} color={Colors.primaryColor} style={{ marginLeft: '5%' }} />
                                    <View style={{ justifyContent: 'center', flex: 2 }}>
                                        <Text style={{ color: Colors.descriptionColor, marginLeft: '5%', fontSize: 15 }}>Download</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>
                            <View style={{ width: '4%' }}></View>
                            <TouchableOpacity style={{ width: '20%' }} onPress={() => { setState({ visible1: true }); }}>
                                <View style={{ width: '100%', height: 50, backgroundColor: Colors.whiteColor, borderRadius: 10, elevation: 2, alignItems: 'center', flexDirection: 'row' }}>
                                    <AntDesign name='upload' size={20} color={Colors.primaryColor} style={{ marginLeft: '5%', flex: 1 }} />
                                    <View style={{ justifyContent: 'center', flex: 2 }}>
                                        <Text style={{ color: Colors.descriptionColor, marginLeft: '2%', fontSize: 15 }}>Email</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>
                        </View> */}
                </View>): (
                    <></>
                  )}

                <View style={{ width: "100%", zIndex: -2 }}>
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      width: windowWidth * 0.87,
                      height:
                        Platform.OS == 'android'
                          ? windowHeight * 0.52
                          : windowHeight * 0.66,
                      marginHorizontal: 30,
                      marginBottom: 10,
                      alignSelf: 'center',
                      borderRadius: 5,
                      shadowOpacity: 0,
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,

                      zIndex: -10,

                      ...!showDetails && {
                        marginTop: 15,
                      },
                      ...isMobile() && {
                        width: '100%',
                        alignSelf: 'flex-start',
                        marginLeft: 0,
                      }
                    }}>
                    {/* <View style={{ height: '89%', position: 'absolute', justifyContent: 'space-between', zIndex: showDetails && transactionTypeSalesDetails.length > 0 ? 10 : -2, marginVertical: 0, marginTop: 70, alignSelf: 'center' }}>
                                <TouchableOpacity
                                    onPress={() => {
                                        ScrollToTop();
                                    }}
                                    style={{ alignSelf: 'center', marginTop: '8%', zIndex: 10 }}>
                                    <AntDesign name={'upcircle'} size={23} color={Colors.primaryColor} style={{ opacity: showDetails && transactionTypeSalesDetails.length > 0 ? 0.4 : 0 }} />
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => {
                                        ScrollToBottom();
                                    }}
                                    style={{ alignSelf: 'center', marginTop: '42%', zIndex: 10 }}>
                                    <AntDesign name={'downcircle'} size={23} color={Colors.primaryColor} style={{ opacity: showDetails && transactionTypeSalesDetails.length > 0 ? 0.4 : 0 }} />
                                </TouchableOpacity>
                            </View> */}
                    {!showDetails ? (
                      <View style={{ marginTop: 10, flexDirection: "row", zIndex: -1 }}>
                        {!simpliFiedLayout && (
                          <View
                            style={{
                              flexDirection: "row",
                              width: "6%",
                              borderRightWidth: 1,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                              paddingLeft: 10,
                            }}
                          >

                            <View style={{ flexDirection: "row" }}>
                              <View style={{ flexDirection: "column" }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: "NunitoSans-Bold",
                                    textAlign: "left",
                                  }}
                                >
                                  {"No.\n"}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}
                                ></Text>
                              </View>
                              <View style={{ marginLeft: "3%" }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color="transparent"
                                ></Entypo>

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color="transparent"
                                ></Entypo>
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }}
                                ></Text>
                              </View>
                            </View>

                            {/* <View style={{ marginLeft: '3%' }}>
                                                <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC)}>
                                                    <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>

                                                <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC)}>
                                                    <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>
                                            </View> */}
                          </View>
                        )}
                        <View
                          style={{
                            flexDirection: "row",
                            width: simpliFiedLayout ? '40%' : "18%",
                            borderRightWidth: 1,
                            borderRightColor: "lightgrey",
                            alignItems: "center",
                            justifyContent: "flex-start",
                            paddingLeft: 10,
                          }}
                        >
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_DESC
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_ASC
                                );
                              }
                            }}
                          >
                            <View style={{ flexDirection: "row" }}>
                              <View style={{ flexDirection: "column" }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: "NunitoSans-Bold",
                                    textAlign: "left",
                                  }}
                                >
                                  {"Transaction\nCategory"}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}
                                ></Text>
                              </View>
                              <View style={{ marginLeft: "3%" }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  }
                                ></Entypo>

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  }
                                ></Entypo>
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }}
                                ></Text>
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View
                          style={{
                            flexDirection: "row",
                            width: simpliFiedLayout ? '30%' : "10%",
                            borderRightWidth: 1,
                            borderRightColor: "lightgrey",
                            alignItems: "center",
                            justifyContent: "flex-start",
                            paddingLeft: 10,
                          }}
                        >
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.TOTAL_TRANSACTION_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_TRANSACTION_DESC
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.TOTAL_TRANSACTION_ASC
                                );
                              }
                            }}
                          >
                            <View style={{ flexDirection: "row" }}>
                              <View style={{ flexDirection: "column" }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: "NunitoSans-Bold",
                                    textAlign: "center",
                                  }}
                                >
                                  {"Order\n"}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}
                                >
                                  Qty
                                </Text>
                              </View>
                              <View style={{ marginLeft: "3%" }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TOTAL_TRANSACTION_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  }
                                ></Entypo>

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportSummarySort ===
                                      REPORT_SORT_FIELD_TYPE.TOTAL_TRANSACTION_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  }
                                ></Entypo>
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }}
                                ></Text>
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {!simpliFiedLayout && (
                          <View
                            style={{
                              flexDirection: "row",
                              width: "10%",
                              borderRightWidth: 1,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                              paddingLeft: 10,
                            }}
                          >
                            <TouchableOpacity
                              onPress={() => {
                                if (
                                  currReportSummarySort ===
                                  REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC
                                ) {
                                  setCurrReportSummarySort(
                                    REPORT_SORT_FIELD_TYPE.TOTAL_SALES_DESC
                                  );
                                } else {
                                  setCurrReportSummarySort(
                                    REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC
                                  );
                                }
                              }}
                            >
                              <View style={{ flexDirection: "row" }}>
                                <View style={{ flexDirection: "column" }}>
                                  <Text
                                    numberOfLines={2}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 13,
                                      fontFamily: "NunitoSans-Bold",
                                    }}
                                  >
                                    {"Sales\n"}
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  >
                                    RM
                                  </Text>
                                </View>

                                <View
                                  style={{
                                    marginLeft: "3%",
                                    justifyContent: "space-between",
                                  }}
                                >
                                  <View>
                                    <Entypo
                                      name="triangle-up"
                                      size={switchMerchant ? 7 : 14}
                                      color={
                                        currReportSummarySort ===
                                          REPORT_SORT_FIELD_TYPE.TOTAL_SALES_ASC
                                          ? Colors.secondaryColor
                                          : Colors.descriptionColor
                                      }
                                    ></Entypo>

                                    <Entypo
                                      name="triangle-down"
                                      size={switchMerchant ? 7 : 14}
                                      color={
                                        currReportSummarySort ===
                                          REPORT_SORT_FIELD_TYPE.TOTAL_SALES_DESC
                                          ? Colors.secondaryColor
                                          : Colors.descriptionColor
                                      }
                                    ></Entypo>
                                  </View>
                                  {/* <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text> */}
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                        )}
                        {!simpliFiedLayout && (
                          <View
                            style={{
                              flexDirection: "row",
                              width: "8%",
                              borderRightWidth: 1,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                              paddingLeft: 10,
                            }}
                          >
                            <TouchableOpacity
                              onPress={() => {
                                if (
                                  currReportSummarySort ===
                                  REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC
                                ) {
                                  setCurrReportSummarySort(
                                    REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_DESC
                                  );
                                } else {
                                  setCurrReportSummarySort(
                                    REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC
                                  );
                                }
                              }}
                            >
                              <View style={{ flexDirection: "row" }}>
                                <View style={{ flexDirection: "column" }}>
                                  <Text
                                    numberOfLines={2}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 13,
                                      fontFamily: "NunitoSans-Bold",
                                      textAlign: "center",
                                    }}
                                  >
                                    {"Disc\n"}
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  >
                                    RM
                                  </Text>
                                </View>
                                <View style={{ marginLeft: "3%" }}>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.TOTAL_DISCOUNT_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  ></Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                        )}
                        {!simpliFiedLayout && (
                          <View
                            style={{
                              flexDirection: "row",
                              width: "8%",
                              borderRightWidth: 1,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                              paddingLeft: 10,
                            }}
                          >
                            <TouchableOpacity
                              onPress={() => {
                                if (
                                  currReportSummarySort ===
                                  REPORT_SORT_FIELD_TYPE.DISCOUNT_ASC
                                ) {
                                  setCurrReportSummarySort(
                                    REPORT_SORT_FIELD_TYPE.DISCOUNT_DESC
                                  );
                                } else {
                                  setCurrReportSummarySort(
                                    REPORT_SORT_FIELD_TYPE.DISCOUNT_ASC
                                  );
                                }
                              }}
                            >
                              <View style={{ flexDirection: "row" }}>
                                <View style={{ flexDirection: "column" }}>
                                  <Text
                                    numberOfLines={2}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 13,
                                      fontFamily: "NunitoSans-Bold",
                                    }}
                                  >
                                    {"Disc\n"}
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  >
                                    %
                                  </Text>
                                </View>
                                <View style={{ marginLeft: "3%" }}>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.DISCOUNT_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.DISCOUNT_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  ></Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                        )}
                        {!simpliFiedLayout && (
                          <View
                            style={{
                              flexDirection: "row",
                              width: "8%",
                              borderRightWidth: 1,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                              paddingLeft: 10,
                            }}
                          >
                            <TouchableOpacity
                              onPress={() => {
                                if (
                                  currReportSummarySort ===
                                  REPORT_SORT_FIELD_TYPE.TAX_ASC
                                ) {
                                  setCurrReportSummarySort(
                                    REPORT_SORT_FIELD_TYPE.TAX_DESC
                                  );
                                } else {
                                  setCurrReportSummarySort(
                                    REPORT_SORT_FIELD_TYPE.TAX_ASC
                                  );
                                }
                              }}
                            >
                              <View style={{ flexDirection: "row" }}>
                                <View style={{ flexDirection: "column" }}>
                                  <Text
                                    numberOfLines={2}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 13,
                                      fontFamily: "NunitoSans-Bold",
                                    }}
                                  >
                                    {"Tax\n"}
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  >
                                    RM
                                  </Text>
                                </View>
                                <View style={{ marginLeft: "3%" }}>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.TAX_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.TAX_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  ></Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                        )}
                        {!simpliFiedLayout && (
                          <View
                            style={{
                              flexDirection: "row",
                              width: "10%",
                              borderRightWidth: 1,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                              paddingLeft: 10,
                            }}
                          >
                            <TouchableOpacity
                              onPress={() => {
                                if (
                                  currReportSummarySort ===
                                  REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_ASC
                                ) {
                                  setCurrReportSummarySort(
                                    REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_DESC
                                  );
                                } else {
                                  setCurrReportSummarySort(
                                    REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_ASC
                                  );
                                }
                              }}
                            >
                              <View style={{ flexDirection: "row" }}>
                                <View style={{ flexDirection: "column" }}>
                                  <Text
                                    numberOfLines={2}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 13,
                                      fontFamily: "NunitoSans-Bold",
                                    }}
                                  >
                                    {"Service\nCharge"}
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  >
                                    RM
                                  </Text>
                                </View>
                                <View style={{ marginLeft: "3%" }}>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.SERVICE_CHARGE_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  ></Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                        )}
                        {/* <View style={{ flexDirection: 'row', flex: 1, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                            <View style={{ flexDirection: 'column' }}>
                                                <TouchableOpacity onPress={() => {
                                                    if (currReportSummarySort === REPORT_SORT_FIELD_TYPE.GP_ASC) {
                                                        setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.GP_DESC)
                                                    }
                                                    else {
                                                        setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.GP_ASC)
                                                    }
                                                }}>
                                                    <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>GP</Text>
                                                    <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                                </TouchableOpacity>
                                            </View>
                                            <View style={{ marginLeft: '3%' }}>
                                                <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.GP_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>

                                                <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.GP_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </View>
                                        </View> */}
                        {!simpliFiedLayout && (
                          <View
                            style={{
                              flexDirection: "row",
                              width: "10%",
                              borderRightWidth: 1,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                              paddingLeft: 10,
                            }}
                          >
                            <TouchableOpacity
                              onPress={() => {
                                if (
                                  currReportDetailsSort ===
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                                ) {
                                  setCurrReportDetailsSort(
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_DESC
                                  );
                                } else {
                                  setCurrReportDetailsSort(
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                                  );
                                }
                              }}
                            >
                              <View style={{ flexDirection: "row" }}>
                                <View style={{ flexDirection: "column" }}>
                                  <Text
                                    numberOfLines={2}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 13,
                                      fontFamily: "NunitoSans-Bold",
                                    }}
                                  >
                                    {"Sales\nReturn"}
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  >
                                    RM
                                  </Text>
                                </View>
                                <View style={{ marginLeft: "3%" }}>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  ></Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                        )}
                        <View
                          style={{
                            flexDirection: "row",
                            width: simpliFiedLayout ? '30%' : "12%",
                            //borderRightWidth: 1,
                            //borderRightColor: 'lightgrey',
                            alignItems: "center",
                            justifyContent: "flex-start",
                            paddingLeft: 10,
                          }}
                        >
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportSummarySort ===
                                REPORT_SORT_FIELD_TYPE.NET_SALES_ASC
                              ) {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.NET_SALES_DESC
                                );
                              } else {
                                setCurrReportSummarySort(
                                  REPORT_SORT_FIELD_TYPE.NET_SALES_ASC
                                );
                              }
                            }}
                          >
                            <View style={{ flexDirection: "row" }}>
                              <View style={{ flexDirection: "column" }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: "NunitoSans-Bold",
                                  }}
                                >
                                  {"Net Sales\n"}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}
                                >
                                  RM
                                </Text>
                              </View>
                              <View
                                style={{
                                  marginLeft: "3%",
                                  justifyContent: "space-between",
                                }}
                              >
                                <View>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.NET_SALES_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportSummarySort ===
                                        REPORT_SORT_FIELD_TYPE.NET_SALES_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>
                                </View>
                                {/* <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text> */}
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {/* <View
                        style={{
                          flexDirection: 'row',
                          flex: 1.8,
                          borderRightWidth: 0,
                          borderRightColor: 'lightgrey',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingLeft: 10,
                        }}>
                        <TouchableOpacity
                          onPress={() => {
                            if (
                              currReportSummarySort ===
                              REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_ASC
                            ) {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_DESC,
                              );
                            } else {
                              setCurrReportSummarySort(
                                REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_ASC,
                              );
                            }
                          }}>
                          <View style={{ flexDirection: 'row' }}>
                            <View style={{ flexDirection: 'column' }}>
                              <Text
                                numberOfLines={2}
                                style={{
                                  fontSize: switchMerchant ? 10 : 13,
                                  fontFamily: 'NunitoSans-Bold',
                                  textAlign: 'left',
                                }}>
                                {'Avg\nNet Sales'}
                              </Text>
                              <Text
                                style={{
                                  fontSize: switchMerchant ? 8 : 10,
                                  color: Colors.descriptionColor,
                                }}>
                                RM
                              </Text>
                            </View>
                            <View style={{ marginLeft: '3%' }}>
                              <Entypo
                                name="triangle-up"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_ASC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                }></Entypo>

                              <Entypo
                                name="triangle-down"
                                size={switchMerchant ? 7 : 14}
                                color={
                                  currReportSummarySort ===
                                    REPORT_SORT_FIELD_TYPE.AVERAGE_NET_SALES_DESC
                                    ? Colors.secondaryColor
                                    : Colors.descriptionColor
                                }></Entypo>
                              <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text>
                            </View>
                          </View>
                        </TouchableOpacity>
                      </View> */}
                      </View>
                    ) : (
                      <View style={{ marginTop: 10, flexDirection: "row", zIndex: -1 }}>
                        {!simpliFiedLayout && (
                          <View
                            style={{
                              flexDirection: "row",
                              width: "6%",
                              borderRightWidth: 1,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                              paddingLeft: 10,
                            }}
                          >
                            <View style={{ flexDirection: "row" }}>
                              <View style={{ flexDirection: "column" }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: "NunitoSans-Bold",
                                    textAlign: "left",
                                  }}
                                >
                                  {"No.\n"}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}
                                ></Text>
                              </View>
                              <View style={{ marginLeft: "3%" }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color="transparent"
                                ></Entypo>

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color="transparent"
                                ></Entypo>
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }}
                                ></Text>
                              </View>
                            </View>
                            {/* <View style={{ marginLeft: '3%' }}>
                                                <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC)}>
                                                    <Entypo name='triangle-up' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>

                                                <TouchableOpacity onPress={() => setCurrReportSummarySort(REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC)}>
                                                    <Entypo name='triangle-down' size={14} color={currReportSummarySort === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>
                                            </View> */}
                          </View>
                        )}
                        <View
                          style={{
                            flexDirection: "row",
                            width: simpliFiedLayout ? '30%' : "12%",
                            borderRightWidth: 1,
                            borderRightColor: "lightgrey",
                            alignItems: "center",
                            justifyContent: "flex-start",
                            paddingLeft: 10,
                          }}
                        >
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_DESC
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_ASC
                                );
                              }
                            }}
                          >
                            <View style={{ flexDirection: "row" }}>
                              <View style={{ flexDirection: "column" }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: "NunitoSans-Bold",
                                    textAlign: "left",
                                  }}
                                >
                                  {"Transaction\nCategory"}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}
                                ></Text>
                              </View>
                              <View style={{ marginLeft: "3%" }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  }
                                ></Entypo>

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  }
                                ></Entypo>
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }}
                                ></Text>
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>

                        <View
                          style={{
                            flexDirection: "row",
                            width: simpliFiedLayout ? '40%' : "18%",
                            borderRightWidth: 1,
                            borderRightColor: "lightgrey",
                            alignItems: "center",
                            justifyContent: "flex-start",
                            paddingLeft: 10,
                          }}
                        >
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_DESC
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC
                                );
                              }
                            }}
                          >
                            <View style={{ flexDirection: "row" }}>
                              <View style={{ flexDirection: "column" }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: "NunitoSans-Bold",
                                    textAlign: "left",
                                  }}
                                >
                                  {"Transaction\nDate & Time"}
                                </Text>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 8 : 10,
                                    color: Colors.descriptionColor,
                                  }}
                                ></Text>
                              </View>
                              <View style={{ marginLeft: "3%" }}>
                                <Entypo
                                  name="triangle-up"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_ASC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  }
                                ></Entypo>

                                <Entypo
                                  name="triangle-down"
                                  size={switchMerchant ? 7 : 14}
                                  color={
                                    currReportDetailsSort ===
                                      REPORT_SORT_FIELD_TYPE.USER_ORDER_DATE_TIME_DESC
                                      ? Colors.secondaryColor
                                      : Colors.descriptionColor
                                  }
                                ></Entypo>
                                <Text
                                  style={{
                                    fontSize: 10,
                                    color: Colors.descriptionColor,
                                  }}
                                ></Text>
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {!simpliFiedLayout && (
                          <View
                            style={{
                              flexDirection: "row",
                              width: "10%",
                              borderRightWidth: 1,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                              paddingLeft: 10,
                            }}
                          >
                            <TouchableOpacity
                              onPress={() => {
                                if (
                                  currReportDetailsSort ===
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC
                                ) {
                                  setCurrReportDetailsSort(
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_DESC
                                  );
                                } else {
                                  setCurrReportDetailsSort(
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC
                                  );
                                }
                              }}
                            >
                              <View style={{ flexDirection: "row" }}>
                                <View style={{ flexDirection: "column" }}>
                                  <Text
                                    numberOfLines={2}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 13,
                                      fontFamily: "NunitoSans-Bold",
                                    }}
                                  >
                                    {"Sales\n"}
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  >
                                    RM
                                  </Text>
                                </View>
                                <View
                                  style={{
                                    marginLeft: "3%",
                                    justifyContent: "space-between",
                                  }}
                                >
                                  <View>
                                    <Entypo
                                      name="triangle-up"
                                      size={switchMerchant ? 7 : 14}
                                      color={
                                        currReportDetailsSort ===
                                          REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_ASC
                                          ? Colors.secondaryColor
                                          : Colors.descriptionColor
                                      }
                                    ></Entypo>

                                    <Entypo
                                      name="triangle-down"
                                      size={switchMerchant ? 7 : 14}
                                      color={
                                        currReportDetailsSort ===
                                          REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_DESC
                                          ? Colors.secondaryColor
                                          : Colors.descriptionColor
                                      }
                                    ></Entypo>
                                  </View>
                                  {/* <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text> */}
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                        )}
                        {/* <View style={{ flexDirection: 'row', flex: 2.7, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                    <View style={{ flexDirection: 'column' }}>
                                        <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Transaction Time</Text>
                                        <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text>
                                    </View>
                                </View> */}
                        {!simpliFiedLayout && (
                          <View
                            style={{
                              flexDirection: "row",
                              width: "8%",
                              borderRightWidth: 1,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                              paddingLeft: 10,
                            }}
                          >
                            <TouchableOpacity
                              onPress={() => {
                                if (
                                  currReportDetailsSort ===
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC
                                ) {
                                  setCurrReportDetailsSort(
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_DESC
                                  );
                                } else {
                                  setCurrReportDetailsSort(
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC
                                  );
                                }
                              }}
                            >
                              <View style={{ flexDirection: "row" }}>
                                <View style={{ flexDirection: "column" }}>
                                  <Text
                                    numberOfLines={2}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 13,
                                      fontFamily: "NunitoSans-Bold",
                                    }}
                                  >
                                    {"Disc\n"}
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  >
                                    RM
                                  </Text>
                                </View>
                                <View style={{ marginLeft: "3%" }}>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_TOTAL_DISCOUNT_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  ></Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                        )}
                        {!simpliFiedLayout && (
                          <View
                            style={{
                              flexDirection: "row",
                              width: "8%",
                              borderRightWidth: 1,
                              borderRightColor: "lightgrey",
                              alignItems: "center",
                              justifyContent: "flex-start",
                              paddingLeft: 10,
                            }}
                          >
                            <TouchableOpacity
                              onPress={() => {
                                if (
                                  currReportDetailsSort ===
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                                ) {
                                  setCurrReportDetailsSort(
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC
                                  );
                                } else {
                                  setCurrReportDetailsSort(
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                                  );
                                }
                              }}
                            >
                              <View style={{ flexDirection: "row" }}>
                                <View style={{ flexDirection: "column" }}>
                                  <Text
                                    numberOfLines={2}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 13,
                                      fontFamily: "NunitoSans-Bold",
                                    }}
                                  >
                                    {"Disc\n"}
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  >
                                    %
                                  </Text>
                                </View>
                                <View style={{ marginLeft: "3%" }}>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  ></Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                        )}
                        {!simpliFiedLayout && (
                          <View
                            style={{
                              flexDirection: "row",
                              width: "8%",
                              alignItems: "center",
                              justifyContent: "flex-start",
                              paddingLeft: 10,
                              borderRightWidth: 1,
                              borderRightColor: "lightgrey",
                            }}
                          >
                            <TouchableOpacity
                              onPress={() => {
                                if (
                                  currReportDetailsSort ===
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC
                                ) {
                                  setCurrReportDetailsSort(
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_DESC
                                  );
                                } else {
                                  setCurrReportDetailsSort(
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC
                                  );
                                }
                              }}
                            >
                              <View style={{ flexDirection: "row" }}>
                                <View style={{ flexDirection: "column" }}>
                                  <Text
                                    numberOfLines={2}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 13,
                                      fontFamily: "NunitoSans-Bold",
                                    }}
                                  >
                                    {"Tax\n"}
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  >
                                    RM
                                  </Text>
                                </View>
                                <View style={{ marginLeft: "3%" }}>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_TAX_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  ></Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                        )}
                        {/* <View style={{ flexDirection: 'row', flex: 1.6, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                            <View style={{ flexDirection: 'column' }}>
                                                <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Disc</Text>
                                                <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                            </View>
                                            <View style={{ marginLeft: '3%' }}>
                                                <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC)}>
                                                    <Entypo name='triangle-up' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>

                                                <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC)}>
                                                    <Entypo name='triangle-down' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_DISCOUNT_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>
                                            </View>
                                        </View> */}
                        {!simpliFiedLayout && (
                          <View
                            style={{
                              flexDirection: "row",
                              width: "9%",
                              alignItems: "center",
                              justifyContent: "flex-start",
                              paddingLeft: 10,
                              borderRightWidth: 1,
                              borderRightColor: "lightgrey",
                            }}
                          >
                            <TouchableOpacity
                              onPress={() => {
                                if (
                                  currReportDetailsSort ===
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC
                                ) {
                                  setCurrReportDetailsSort(
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_DESC
                                  );
                                } else {
                                  setCurrReportDetailsSort(
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC
                                  );
                                }
                              }}
                            >
                              <View style={{ flexDirection: "row" }}>
                                <View style={{ flexDirection: "column" }}>
                                  <Text
                                    numberOfLines={2}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 13,
                                      fontFamily: "NunitoSans-Bold",
                                    }}
                                  >
                                    {"Service\nCharge"}
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  >
                                    RM
                                  </Text>
                                </View>

                                <View style={{ marginLeft: "3%" }}>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  ></Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                        )}
                        {!simpliFiedLayout && (
                          <View
                            style={{
                              flexDirection: "row",
                              width: "9%",
                              // borderRightWidth: 1,
                              // borderRightColor: 'lightgrey',
                              alignItems: "center",
                              justifyContent: "flex-start",
                              paddingLeft: 10,
                            }}
                          >
                            <TouchableOpacity
                              onPress={() => {
                                if (
                                  currReportDetailsSort ===
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                                ) {
                                  setCurrReportDetailsSort(
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_DESC
                                  );
                                } else {
                                  setCurrReportDetailsSort(
                                    REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                                  );
                                }
                              }}
                            >
                              <View style={{ flexDirection: "row" }}>
                                <View style={{ flexDirection: "column" }}>
                                  <Text
                                    numberOfLines={2}
                                    style={{
                                      fontSize: switchMerchant ? 10 : 13,
                                      fontFamily: "NunitoSans-Bold",
                                    }}
                                  >
                                    {"Sales\nReturn"}
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  >
                                    RM
                                  </Text>
                                </View>
                                <View style={{ marginLeft: "3%" }}>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_SALES_RETURN_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>
                                  <Text
                                    style={{
                                      fontSize: 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  ></Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </View>
                        )}
                        {/* <View style={{ flexDirection: 'row', flex: 2, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                            <View style={{ flexDirection: 'column' }}>
                                                <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>Service Charge</Text>
                                                <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>RM</Text>
                                            </View>

                                            <View style={{ marginLeft: '3%' }}>
                                                <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC)}>
                                                    <Entypo name='triangle-up' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>

                                                <TouchableOpacity onPress={() => setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_DESC)}>
                                                    <Entypo name='triangle-down' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_SERVICE_CHARGE_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                                </TouchableOpacity>
                                            </View>
                                        </View> */}
                        {/* <View style={{ flexDirection: 'row', flex: 1, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                            <View style={{ flexDirection: 'column' }}>
                                                <TouchableOpacity onPress={() => {
                                                    if (currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_ASC) {
                                                        setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_DESC)
                                                    }
                                                    else {
                                                        setCurrReportDetailsSort(REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_ASC)
                                                    }
                                                }}>
                                                    <Text numberOfLines={2} style={{ fontSize: 13, fontFamily: 'NunitoSans-Bold' }}>GP</Text>
                                                    <Text style={{ fontSize: 10, color: Colors.descriptionColor }}>%</Text>
                                                </TouchableOpacity>
                                            </View>
                                            <View style={{ marginLeft: '3%' }}>
                                                <Entypo name='triangle-up' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_ASC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>

                                                <Entypo name='triangle-down' size={14} color={currReportDetailsSort === REPORT_SORT_FIELD_TYPE.USER_ORDER_GP_DESC ? Colors.secondaryColor : Colors.descriptionColor}></Entypo>
                                            </View>
                                        </View> */}
                        <View
                          style={{
                            flexDirection: "row",
                            width: simpliFiedLayout ? '30%' : "12%",
                            borderRightWidth: 0,
                            borderRightColor: "lightgrey",
                            alignItems: "center",
                            justifyContent: "flex-start",
                            paddingLeft: 10,
                            borderLeftWidth: 1,
                            borderLeftColor: "lightgrey",
                          }}
                        >
                          <TouchableOpacity
                            onPress={() => {
                              if (
                                currReportDetailsSort ===
                                REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC
                              ) {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_DESC
                                );
                              } else {
                                setCurrReportDetailsSort(
                                  REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC
                                );
                              }
                            }}
                          >
                            <View style={{ flexDirection: "row" }}>
                              <View style={{ flexDirection: "column" }}>
                                <Text
                                  numberOfLines={2}
                                  style={{
                                    fontSize: switchMerchant ? 10 : 13,
                                    fontFamily: "NunitoSans-Bold",
                                  }}
                                >
                                  {"Net Sales\n"}
                                </Text>
                                <View style={{ flexDirection: "row" }}>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.descriptionColor,
                                    }}
                                  >
                                    RM
                                  </Text>
                                  <Text
                                    style={{
                                      fontSize: switchMerchant ? 8 : 10,
                                      color: Colors.primaryColor,
                                    }}
                                  >
                                    {" "}
                                    *incl tax
                                  </Text>
                                </View>
                              </View>
                              <View
                                style={{
                                  marginLeft: "3%",
                                  justifyContent: "space-between",
                                }}
                              >
                                <View>
                                  <Entypo
                                    name="triangle-up"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_ASC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>

                                  <Entypo
                                    name="triangle-down"
                                    size={switchMerchant ? 7 : 14}
                                    color={
                                      currReportDetailsSort ===
                                        REPORT_SORT_FIELD_TYPE.USER_ORDER_NET_SALES_DESC
                                        ? Colors.secondaryColor
                                        : Colors.descriptionColor
                                    }
                                  ></Entypo>
                                </View>
                                {/* <Text
                                style={{
                                  fontSize: 10,
                                  color: Colors.descriptionColor,
                                }}></Text> */}
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                        {/* <View style={{ flexDirection: 'row', flex: 3, borderRightWidth: 1, borderRightColor: 'lightgrey', alignItems: 'center', justifyContent: 'center' }}>
                                            <View>
                                                <Text style={{ fontSize: 13, fontFamily: 'NunitoSans-Regular' }}>Average Net Sales</Text>
                                                <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text>
                                            </View>
                                        </View> */}
                      </View>
                    )}

                    {!showDetails ? (
                      <>
                        {transactionTypeSales.length > 0 ? (
                          <FlatList
                            showsVerticalScrollIndicator={false}
                            ref={flatListRef}
                            data={sortReportDataList(
                              transactionTypeSales.filter((item) => {
                                if (search !== "") {
                                  if (
                                    item.orderType
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.totalTransactions
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.totalSales
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.totalDiscount
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.discount
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.tax
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.serviceCharge
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.netSales
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.averageNetSales
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else {
                                    return false;
                                  }
                                } else {
                                  return true;
                                }
                              }),
                              currReportSummarySort
                            ).slice(
                              (currentPage - 1) * perPage,
                              currentPage * perPage
                            )}
                            // extraData={transactionTypeSales}
                            renderItem={renderItem}
                            keyExtractor={(item, index) => String(index)}
                            style={{ marginTop: 10 }}
                          />
                        ) : (
                          <View
                            style={{
                              height: windowHeight * 0.4,
                            }}>
                            <View
                              style={{
                                alignItems: "center",
                                justifyContent: "center",
                                height: "100%",
                              }}
                            >
                              <Text style={{ color: Colors.descriptionColor }}>
                                - No Data Available -
                              </Text>
                            </View>
                          </View>
                        )}
                      </>
                    ) : (
                      <>
                        {transactionTypeSalesDetails.length > 0 ? (
                          <FlatList
                            showsVerticalScrollIndicator={false}
                            ref={flatListRef}
                            data={sortReportDataList(
                              transactionTypeSalesDetails,
                              currReportDetailsSort
                            )
                              .filter((item) => {
                                if (search !== "") {
                                  if (
                                    item.orderType
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.totalPrice
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    getOrderDiscountInfoInclOrderBased(item)
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    item.tax
                                      .toFixed(2)
                                      .toString()
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else if (
                                    moment(item.createdAt)
                                      .format("DD MMM YYY hh:mma")
                                      .toLowerCase()
                                      .includes(search.toLowerCase())
                                  ) {
                                    return true;
                                  } else {
                                    return false;
                                  }
                                } else {
                                  return true;
                                }
                              })
                              .slice(
                                (currentDetailsPage - 1) * perPage,
                                currentDetailsPage * perPage
                              )}
                            // extraData={transactionTypeSales}
                            renderItem={renderItemDetails}
                            keyExtractor={(item, index) => String(index)}
                            style={{ marginTop: 10 }}
                          />
                        ) : (
                          <View
                            style={{
                              height: windowHeight * 0.4,
                            }}>
                            <View
                              style={{
                                alignItems: "center",
                                justifyContent: "center",
                                height: "100%",
                              }}
                            >
                              <Text style={{ color: Colors.descriptionColor }}>
                                - No Data Available -
                              </Text>
                            </View>
                          </View>
                        )}
                      </>
                    )}

                    {/* {searchList ? (

                                <FlatList
                                    data={lists}
                                    extraData={lists}
                                    renderItem={renderSearchItem}
                                    keyExtractor={(item, index) => String(index)}
                                />

                            ) : null} */}
                  </View>

                  {!showDetails ? (
                    <View
                      style={{
                        flexDirection: "row",
                        marginTop: 10,
                        width: windowWidth * 0.87,
                        alignItems: "center",
                        alignSelf: "center",
                        marginBottom: 15,
                        justifyContent: simpliFiedLayout ? 'flex-start' : "flex-end",
                        // backgroundColor: pushPagingToTop && keyboardHeight > 0 ? Colors.highlightColor : null,
                        // borderWidth: pushPagingToTop && keyboardHeight > 0 ? 1 : 0,
                        // borderColor: pushPagingToTop && keyboardHeight > 0 ? '#E5E5E5' : null,
                        // shadowOffset: {
                        //     width: 0,
                        //     height: 1,
                        // },
                        // shadowOpacity: 0.22,
                        // shadowRadius: 3.22,
                        // elevation: 1,
                        ...isMobile() && {
                          width: '100%',
                        },

                        ...simpliFiedLayout && {
                          alignSelf: 'flex-start',
                          justifyContent: 'flex-start',
                          alignItems: 'flex-start',
                          flexDirection: 'column',
                          marginBottom: 25,
                        }
                      }}
                    >
                      <View style={{ flexDirection: 'row', alignItems: 'center', width: '100%', }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: "NunitoSans-Bold",
                            marginRight: "1%",
                          }}
                        >
                          Items Showed
                        </Text>
                        <View
                          style={{
                            width: Platform.OS === "ios" ? 65 : "13%", //65,
                            height: switchMerchant ? 20 : 35,
                            backgroundColor: Colors.whiteColor,
                            borderRadius: 10,
                            justifyContent: "center",
                            paddingHorizontal: Platform.OS === "ios" ? 0 : 0,
                            //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                            // paddingTop: '-60%',
                            borderWidth: 1,
                            borderColor: "#E5E5E5",
                            marginRight: "1%",

                            ...simpliFiedLayout && {
                              width: 65,
                            }
                          }}
                        >
                          <DropDownPicker
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              width: '100%',
                              height: 40,
                              borderRadius: 10,
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              flexDirection: "row",
                            }}
                            dropDownContainerStyle={{
                              width: '100%',
                              backgroundColor: Colors.fieldtBgColor,
                              borderColor: "#E5E5E5",
                            }}
                            labelStyle={{
                              marginLeft: 5,
                              flexDirection: "row",
                            }}
                            textStyle={{
                              fontSize: 14,
                              fontFamily: 'NunitoSans-Regular',

                              marginLeft: 5,
                              paddingVertical: 10,
                              flexDirection: "row",
                            }}
                            selectedItemContainerStyle={{
                              flexDirection: "row",
                            }}

                            showArrowIcon={true}
                            ArrowDownIconComponent={({ style }) => (
                              <Ionicon
                                size={25}
                                color={Colors.fieldtTxtColor}
                                style={{ paddingHorizontal: 5, marginTop:  5 }}
                                name="chevron-down-outline"
                              />
                            )}
                            ArrowUpIconComponent={({ style }) => (
                              <Ionicon
                                size={25}
                                color={Colors.fieldtTxtColor}
                                style={{ paddingHorizontal: 5, marginTop:  5 }}
                                name="chevron-up-outline"
                              />
                            )}

                            showTickIcon={true}
                            TickIconComponent={({ press }) => (
                              <Ionicon
                                style={{ paddingHorizontal: 5, marginTop: 5 }}
                                color={
                                  press ? Colors.fieldtBgColor : Colors.primaryColor
                                }
                                name={'md-checkbox'}
                                size={25}
                              />
                            )}
                            placeholder={'Select a Type'}
                            placeholderStyle={{
                              color: Colors.fieldtTxtColor,
                              // marginTop: 15,
                            }}
                            // searchable
                            // searchableStyle={{
                            //   paddingHorizontal: windowWidth * 0.0079,
                            // }}
                            value={perPage}
                            items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                              label: 'All',
                              value: !showDetails
                                ? transactionTypeSales.length
                                : transactionTypeSalesDetails.length,
                            })}
                            // multiple={true}
                            // multipleText={`${item.tagIdList.length} Tag(s)`}
                            onSelectItem={(item) => {
                              setPerPage(item.value);
                              // var currentPageTemp =
                              //   text.length > 0 ? parseInt(text) : 1;

                              // setCurrentPage(
                              //   currentPageTemp > pageCount
                              //     ? pageCount
                              //     : currentPageTemp < 1
                              //       ? 1
                              //       : currentPageTemp,
                              // );
                            }}
                            open={openPage}
                            setOpen={setOpenPage}
                            dropDownDirection="TOP"
                          />
                        </View>
                      </View>

                      <View style={{ flexDirection: 'row', alignItems: 'center', marginVertical: 10, width: '100%', }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: "NunitoSans-Bold",
                            marginRight: "1%",
                          }}
                        >
                          Page
                        </Text>
                        <View
                          style={{
                            width: switchMerchant ? 65 : 70,
                            height: switchMerchant ? 20 : 35,
                            backgroundColor: Colors.whiteColor,
                            borderRadius: 10,
                            justifyContent: "center",
                            paddingHorizontal: 22,
                            borderWidth: 1,
                            borderColor: "#E5E5E5",
                          }}
                        >
                          {console.log("currentPage")}
                          {console.log(currentPage)}

                          <TextInput
                            onChangeText={(text) => {
                              var currentPageTemp =
                                text.length > 0 ? parseInt(text) : 1;

                              setCurrentPage(
                                currentPageTemp > pageCount
                                  ? pageCount
                                  : currentPageTemp < 1
                                    ? 1
                                    : currentPageTemp
                              );
                            }}
                            placeholderTextColor={Platform.select({
                              ios: "#a9a9a9",
                            })}
                            placeholder={
                              pageCount !== 0 ? currentPage.toString() : "0"
                            }
                            style={{
                              color: "black",
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: "NunitoSans-Regular",
                              marginTop: Platform.OS === "ios" ? 0 : -15,
                              marginBottom: Platform.OS === "ios" ? 0 : -15,
                              textAlign: 'center',
                              width: "100%",
                            }}
                            value={pageCount !== 0 ? currentPage.toString() : "0"}
                            defaultValue={
                              pageCount !== 0 ? currentPage.toString() : "0"
                            }
                            keyboardType={"numeric"}
                            onFocus={() => {
                              setPushPagingToTop(true);
                            }}
                          />
                        </View>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: "NunitoSans-Bold",
                            marginLeft: "1%",
                            marginRight: "1%",
                          }}
                        >
                          of {pageCount}
                        </Text>
                        <TouchableOpacity
                          style={{
                            width: switchMerchant ? 30 : 45,
                            height: switchMerchant ? 20 : 28,
                            backgroundColor: Colors.primaryColor,
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                          onPress={() => {
                            prevPage();
                          }}
                        >
                          <ArrowLeft color={Colors.whiteColor} />
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={{
                            width: switchMerchant ? 30 : 45,
                            height: switchMerchant ? 20 : 28,
                            backgroundColor: Colors.primaryColor,
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                          onPress={() => {
                            nextPage();
                          }}
                        >
                          <ArrowRight color={Colors.whiteColor} />
                        </TouchableOpacity>
                      </View>
                    </View>
                  ) : (
                    <View
                      style={{
                        flexDirection: "row",
                        marginTop: 10,
                        marginBottom: 15,
                        width: Dimensions.get("screen").width * 0.87,
                        alignItems: "center",
                        alignSelf: "center",
                        justifyContent: simpliFiedLayout ? 'flex-start' : "flex-end",

                        // backgroundColor: pushPagingToTop && keyboardHeight > 0 ? Colors.highlightColor : null,
                        // borderWidth: pushPagingToTop && keyboardHeight > 0 ? 1 : 0,
                        // borderColor: pushPagingToTop && keyboardHeight > 0 ? '#E5E5E5' : null,

                        // shadowOffset: {
                        //     width: 0,
                        //     height: 1,
                        // },
                        // shadowOpacity: 0.22,
                        // shadowRadius: 3.22,
                        // elevation: 1,
                        ...isMobile() && {
                          width: '100%',
                        },

                        ...simpliFiedLayout && {
                          alignSelf: 'flex-start',
                          justifyContent: 'flex-start',
                          alignItems: 'flex-start',
                          flexDirection: 'column',
                          marginBottom: 25,
                        }
                      }}
                    >
                      <View style={{ flexDirection: 'row', alignItems: 'center', width: '100%', }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: "NunitoSans-Bold",
                            marginRight: "1%",
                          }}
                        >
                          Items Showed
                        </Text>
                        <View
                          style={{
                            width: Platform.OS === "ios" ? 65 : "13%", //65,
                            height: switchMerchant ? 20 : 35,
                            backgroundColor: Colors.whiteColor,
                            borderRadius: 10,
                            justifyContent: "center",
                            paddingHorizontal: Platform.OS === "ios" ? 0 : 0,
                            //paddingLeft:switchMerchant ? '4.5%': Platform.OS === 'ios' ? '2%' : '4%',
                            // paddingTop: '-60%',
                            borderWidth: 1,
                            borderColor: "#E5E5E5",
                            marginRight: "1%",

                            ...simpliFiedLayout && {
                              width: 65,
                            }
                          }}
                        >
                          <DropDownPicker
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              width: '100%',
                              height: 40,
                              borderRadius: 10,
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              flexDirection: "row",
                            }}
                            dropDownContainerStyle={{
                              width: '100%',
                              backgroundColor: Colors.fieldtBgColor,
                              borderColor: "#E5E5E5",
                            }}
                            labelStyle={{
                              marginLeft: 5,
                              flexDirection: "row",
                            }}
                            textStyle={{
                              fontSize: 14,
                              fontFamily: 'NunitoSans-Regular',

                              marginLeft: 5,
                              paddingVertical: 10,
                              flexDirection: "row",
                            }}
                            selectedItemContainerStyle={{
                              flexDirection: "row",
                            }}

                            showArrowIcon={true}
                            ArrowDownIconComponent={({ style }) => (
                              <Ionicon
                                size={25}
                                color={Colors.fieldtTxtColor}
                                style={{ paddingHorizontal: 5, marginTop:  5 }}
                                name="chevron-down-outline"
                              />
                            )}
                            ArrowUpIconComponent={({ style }) => (
                              <Ionicon
                                size={25}
                                color={Colors.fieldtTxtColor}
                                style={{ paddingHorizontal: 5, marginTop:  5 }}
                                name="chevron-up-outline"
                              />
                            )}

                            showTickIcon={true}
                            TickIconComponent={({ press }) => (
                              <Ionicon
                                style={{ paddingHorizontal: 5, marginTop: 5 }}
                                color={
                                  press ? Colors.fieldtBgColor : Colors.primaryColor
                                }
                                name={'md-checkbox'}
                                size={25}
                              />
                            )}
                            placeholder={'Select a Type'}
                            placeholderStyle={{
                              color: Colors.fieldtTxtColor,
                              // marginTop: 15,
                            }}
                            // searchable
                            // searchableStyle={{
                            //   paddingHorizontal: windowWidth * 0.0079,
                            // }}
                            value={perPage}
                            items={TABLE_PAGE_SIZE_DROPDOWN_LIST.concat({
                              label: 'All',
                              value: !showDetails
                                ? transactionTypeSales.length
                                : transactionTypeSalesDetails.length,
                            })}
                            // multiple={true}
                            // multipleText={`${item.tagIdList.length} Tag(s)`}
                            onSelectItem={(item) => {
                              setPerPage(item.value);
                              // var currentPageTemp =
                              //   text.length > 0 ? parseInt(text) : 1;

                              // setCurrentPage(
                              //   currentPageTemp > pageCount
                              //     ? pageCount
                              //     : currentPageTemp < 1
                              //       ? 1
                              //       : currentPageTemp,
                              // );
                            }}
                            open={openPage}
                            setOpen={setOpenPage}
                            dropDownDirection="TOP"
                          />
                        </View>
                      </View>
                      <View style={{ flexDirection: 'row', alignItems: 'center', marginVertical: 10, width: '100%', }}>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: "NunitoSans-Bold",
                            marginRight: "1%",
                          }}
                        >
                          Page
                        </Text>
                        <View
                          style={{
                            width: switchMerchant ? 65 : 70,
                            height: switchMerchant ? 20 : 35,
                            backgroundColor: Colors.whiteColor,
                            borderRadius: 10,
                            justifyContent: "center",
                            paddingHorizontal: 22,
                            borderWidth: 1,
                            borderColor: "#E5E5E5",
                          }}
                        >
                          {console.log("currentDetailsPage")}
                          {console.log(currentDetailsPage)}

                          <TextInput
                            onChangeText={(text) => {
                              var currentPageTemp =
                                text.length > 0 ? parseInt(text) : 1;

                              setCurrentDetailsPage(
                                currentPageTemp > pageCount
                                  ? pageCount
                                  : currentPageTemp < 1
                                    ? 1
                                    : currentPageTemp
                              );
                            }}
                            placeholder={
                              pageCount !== 0
                                ? currentDetailsPage.toString()
                                : "0"
                            }
                            placeholderTextColor={Platform.select({
                              ios: "#a9a9a9",
                            })}
                            style={{
                              color: "black",
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: "NunitoSans-Regular",
                              marginTop: Platform.OS === "ios" ? 0 : -15,
                              marginBottom: Platform.OS === "ios" ? 0 : -15,
                              textAlign: 'center',
                              width: "100%",
                            }}
                            value={
                              pageCount !== 0
                                ? currentDetailsPage.toString()
                                : "0"
                            }
                            defaultValue={
                              pageCount !== 0
                                ? currentDetailsPage.toString()
                                : "0"
                            }
                            keyboardType={"numeric"}
                            onFocus={() => {
                              setPushPagingToTop(true);
                            }}
                          />
                        </View>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 14,
                            fontFamily: "NunitoSans-Bold",
                            marginLeft: "1%",
                            marginRight: "1%",
                          }}
                        >
                          of {pageCount}
                        </Text>
                        <TouchableOpacity
                          style={{
                            width: switchMerchant ? 30 : 45,
                            height: switchMerchant ? 20 : 28,
                            backgroundColor: Colors.primaryColor,
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                          onPress={() => {
                            prevDetailsPage();
                          }}
                        >
                          <ArrowLeft color={Colors.whiteColor} />
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={{
                            width: switchMerchant ? 30 : 45,
                            height: switchMerchant ? 20 : 28,
                            backgroundColor: Colors.primaryColor,
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                          onPress={() => {
                            nextDetailsPage();
                          }}
                        >
                          <ArrowRight color={Colors.whiteColor} />
                        </TouchableOpacity>
                      </View>
                    </View>
                  )}
                </View>


                <Modal
                  supportedOrientations={["landscape", "portrait"]}
                  style={{ flex: 1 }}
                  visible={visible}
                  transparent={true}
                  animationType="slide"
                >
                  <KeyboardAvoidingView
                    //behavior="padding"
                    style={{
                      backgroundColor: "rgba(0,0,0,0.5)",
                      flex: 1,
                      justifyContent: "center",
                      alignItems: "center",
                      minHeight: windowHeight,
                    }}
                  >
                    <View style={styles.confirmBox1}>
                      <Text
                        style={{
                          fontSize: 24,
                          justifyContent: "center",
                          alignSelf: "center",
                          marginTop: 40,
                          fontFamily: "NunitoSans-Bold",
                        }}
                      >
                        Enter your email
                      </Text>
                      <View
                        style={{
                          justifyContent: "center",
                          alignSelf: "center",
                          alignContent: "center",
                          marginTop: 20,
                          flexDirection: "row",
                          width: "80%",
                        }}
                      >
                        <View
                          style={{
                            justifyContent: "center",
                            marginHorizontal: 5,
                          }}
                        >
                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              fontSize: 20,
                            }}
                          >
                            email:
                          </Text>
                        </View>
                        <TextInput
                          underlineColorAndroid={Colors.fieldtBgColor}
                          style={[styles.textInput8, { paddingLeft: 5 }]}
                          placeholder="Enter your email"
                          // style={{
                          //     // paddingLeft: 1,
                          // }}
                          //defaultValue={extentionCharges}
                          onChangeText={(text) => {
                            // setState({ exportEmail: text });
                            setExportEmail(text);
                          }}
                          value={exportEmail}
                          placeholderTextColor={Platform.select({
                            ios: "#a9a9a9",
                          })}
                        />
                      </View>
                      <Text
                        style={{
                          fontSize: 20,
                          fontFamily: "NunitoSans-Bold",
                          marginTop: 25,
                          justifyContent: "center",
                          alignSelf: "center",
                          alignContent: "center",
                        }}
                      >
                        Share As:
                      </Text>

                      {/* Share file using email */}
                      <View
                        style={{
                          justifyContent: "space-between",
                          alignSelf: "center",
                          marginTop: 10,
                          flexDirection: "row",
                          width: "80%",
                        }}
                      >
                        <TouchableOpacity
                          style={[
                            styles.modalSaveButton1,
                            {
                              zIndex: -1,
                            },
                          ]}
                          onPress={() => { }}
                        >
                          <Text
                            style={[
                              styles.modalDescText,
                              { color: Colors.primaryColor },
                            ]}
                          >
                            Excel
                          </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={[
                            styles.modalSaveButton1,
                            {
                              zIndex: -1,
                            },
                          ]}
                          onPress={() => { }}
                        >
                          <Text
                            style={[
                              styles.modalDescText,
                              { color: Colors.primaryColor },
                            ]}
                          >
                            CSV
                          </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={[
                            styles.modalSaveButton1,
                            {
                              zIndex: -1,
                            },
                          ]}
                          onPress={() => { }}
                        >
                          <Text
                            style={[
                              styles.modalDescText,
                              { color: Colors.primaryColor },
                            ]}
                          >
                            PDF
                          </Text>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          alignSelf: "center",
                          marginTop: 20,
                          justifyContent: "center",
                          alignItems: "center",
                          // width: 260,
                          width: windowWidth * 0.2,
                          height: 60,
                          alignContent: "center",
                          flexDirection: "row",
                          marginTop: 40,
                        }}
                      >
                        <TouchableOpacity
                          onPress={emailTransaction}
                          style={{
                            backgroundColor: Colors.fieldtBgColor,
                            width: "100%",
                            justifyContent: "center",
                            alignItems: "center",
                            alignContent: "center",
                            height: 60,
                            borderBottomLeftRadius: 10,
                            borderRightWidth: StyleSheet.hairlineWidth,
                            borderTopWidth: StyleSheet.hairlineWidth,
                          }}
                        >
                          <Text
                            style={{
                              fontSize: 22,
                              color: Colors.primaryColor,
                              fontFamily: "NunitoSans-SemiBold",
                            }}
                          >
                            Email
                          </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          onPress={() => {
                            // setState({ visible: false });
                            setVisible(false);
                          }}
                          style={{
                            backgroundColor: Colors.fieldtBgColor,
                            width: "100%",
                            justifyContent: "center",
                            alignItems: "center",
                            alignContent: "center",
                            height: 60,
                            borderBottomRightRadius: 10,
                            borderTopWidth: StyleSheet.hairlineWidth,
                          }}
                        >
                          <Text
                            style={{
                              fontSize: 22,
                              color: Colors.descriptionColor,
                              fontFamily: "NunitoSans-SemiBold",
                            }}
                          >
                            Cancel
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </KeyboardAvoidingView>
                </Modal>
                <Modal
                  supportedOrientations={["landscape", "portrait"]}
                  style={{ flex: 1 }}
                  visible={visible1}
                  transparent={true}
                  animationType="slide"
                >
                  <View
                    style={{
                      backgroundColor: "rgba(0,0,0,0.5)",
                      flex: 1,
                      justifyContent: "center",
                      alignItems: "center",
                      minHeight: windowHeight,
                    }}
                  >
                    <View style={styles.confirmBox}>
                      <View
                        style={{
                          flex: 3,
                          borderBottomWidth: StyleSheet.hairlineWidth,
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      ></View>
                      <View style={{ flex: 1, flexDirection: "row" }}>
                        <TouchableOpacity
                          style={{
                            flex: 1,
                            borderRightWidth: StyleSheet.hairlineWidth,
                            justifyContent: "center",
                          }}
                          onPress={() => {
                            // email();
                          }}
                        >
                          <Text
                            style={{
                              color: Colors.primaryColor,
                              fontSize: 24,
                              fontWeight: "400",
                              textAlign: "center",
                            }}
                          >
                            Email
                          </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={{ flex: 1, justifyContent: "center" }}
                          onPress={() => {
                            setState({ visible1: !visible1 });
                          }}
                        >
                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              fontSize: 24,
                              fontWeight: "400",
                              textAlign: "center",
                            }}
                          >
                            Cancel
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </Modal>
              </View>
            </ScrollView>
          </Animated.ScrollView>
        </View>
      </View>
    </View>
    //</UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: "row",
  },
  sidebar: {
    width: Dimensions.get("window").width * Styles.sideBarWidth,

  },
  content: {
    padding: 20,
    width: Dimensions.get("window").width * (1 - Styles.sideBarWidth),
    backgroundColor: Colors.highlightColor,
    height: Dimensions.get("window").height,
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  confirmBox: {
    // width: '30%',
    // height: '30%',
    // borderRadius: 30,
    // backgroundColor: Colors.whiteColor,
    width: Dimensions.get("window").width * 0.4,
    height: Dimensions.get("window").height * 0.3,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
    justifyContent: "space-between",
  },

  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: "center",
    justifyContent: "center",
  },
  modalView: {
    height: Dimensions.get("window").width * 0.2,
    width: Dimensions.get("window").width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get("window").width * 0.03,
    alignItems: "center",
    justifyContent: "center",
  },
  closeButton: {
    position: "absolute",
    right: Dimensions.get("window").width * 0.02,
    top: Dimensions.get("window").width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: "center",
    top: "20%",
    position: "absolute",
  },
  modalBody: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  modalTitleText: {
    fontFamily: "NunitoSans-Bold",
    textAlign: "center",
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalBodyText: {
    flex: 1,
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 25,
    width: "20%",
  },
  modalSaveButton: {
    width: Dimensions.get("window").width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  modalSaveButton1: {
    width: Dimensions.get("window").width * 0.1,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  confirmBox1: {
    width: Dimensions.get("window").width * 0.4,
    height: Dimensions.get("window").height * 0.4,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
    justifyContent: "space-between",
  },
  submitText: {
    height:
      Platform.OS == "ios"
        ? Dimensions.get("window").height * 0.06
        : Dimensions.get("window").height * 0.07,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: "row",
    color: "#4cd964",
    textAlign: "center",
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  headerLeftStyle: {
    width: useWindowDimensions.width * 0.17,
    justifyContent: "center",
    alignItems: "center",
  },
});
export default ReportSalesTransaction;
