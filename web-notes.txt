keytool -genkeypair -keystore mykoodoo_merchant.pfx -storetype PKCS12 -storepass mykoodoo_merchant -alias KEYSTORE_ENTRY -keyalg RSA -keysize 2048 -validity 99999 -dname "CN=KooDoo Merchant, OU=KooDoo Merchant, O=KooDoo, L=Petaling Jaya, ST=Selangor, C=MY" -ext san=dns:mykoodoo.com,dns:localhost,ip:127.0.0.1
keytool -exportcert -keystore mykoodoo_merchant.pfx -storepass mykoodoo_merchant -alias KEYSTORE_ENTRY -rfc -file mykoodoo_merchant_public-certificate.pem
openssl pkcs12 -in mykoodoo_merchant.pfx -password pass:mykoodoo_merchant -nodes -nocerts -out mykoodoo_merchant_private-key.key
openssl rsa -in mykoodoo_merchant_private-key.key -pubout > mykoodoo_merchant_public-key.pub

chrome://inspect/#devices

http://*************:5400/web/new-order/b422c1d9-d30b-4de7-ad49-2e601d950919/508611ac-c167-4484-a68d-d33e71faf5aa/A5/4/VJC8tbKAsMfU5meM5iuZ8glgxo63

================================================================================================================
manager.mykoodoo.com

dev:
scp -i C:/Users/<USER>/Downloads/mykoodoo.pem -r D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-web/build/* ubuntu@**************:/home/<USER>/koodoo/manager-dev

https://mykoodoo.com/web/new-order/b422c1d9-d30b-4de7-ad49-2e601d950919/798598d7-4aed-4c26-bae9-d7b314819f62/L5L9/5/VJC8tbKAsMfU5meM5iuZ8glgxo63

prod:
scp -i C:/Users/<USER>/Downloads/mykoodoo.pem -r D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-web/build/* ubuntu@**************:/home/<USER>/koodoo/manager

================================================================================================================
mykoodoo.com/manager

dev:
scp -i C:/Users/<USER>/Downloads/mykoodoo.pem -r D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-web/build/* ubuntu@**************:/var/www/html/manager-dev

https://mykoodoo.com/web/new-order/b422c1d9-d30b-4de7-ad49-2e601d950919/798598d7-4aed-4c26-bae9-d7b314819f62/L5L9/5/VJC8tbKAsMfU5meM5iuZ8glgxo63

uat:
scp -i C:/Users/<USER>/Downloads/mykoodoo.pem -r D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-web/build/* ubuntu@**************:/var/www/html/manager-uat

prod:
scp -i C:/Users/<USER>/Downloads/mykoodoo.pem -r D:/Users/<USER>/Documents/GitHub/koodooapps/koodoo-merchant-web/build/* ubuntu@**************:/var/www/html/manager

==============================================================================

find /var/www/html/manager -name '*.*' -type f -mtime +90 -delete

==============================================================================
