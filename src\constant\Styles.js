import { Dimensions } from "react-native"
import Colors from '../constant/Colors';
// import { isTablet } from 'react-native-device-detection';

// const isTablet = true;

const width = Dimensions.get('window').width
const height = Dimensions.get('window').height
const button = {
    backgroundColor: Colors.primaryColor,
    padding: 20,  
    borderRadius: 10,
    alignItems: 'center',
    marginVertical : 20
}

// console.log('isTablet');
// console.log(isTablet);

// const sideBarWidth = 0.15;
const sideBarWidth = 0.08;

const rnPickerSelectStyle = {
    inputAndroidContainer: {
        height: 35,
        justifyContent: 'center',
        backgroundColor: '#fafafa',
        borderRadius: 4,

        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
    },
    inputAndroid: {
        backgroundColor: '#fafafa',
        color: 'black',
        fontFamily: 'NunitoSans-Regular',
        fontSize: 10,
        //width: 200
    },
    inputIOS: {
        backgroundColor: '#fafafa',
        color: 'black',
        fontFamily: 'NunitoSans-Regular',
        fontSize: 10,

        paddingLeft: 12,
    },
    viewContainer: {
        backgroundColor: '#fafafa',
        borderRadius: 4,
        height: 35,

        justifyContent: 'center',

        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
    }
};
 
const Styles = {
    width,
    height,
    button,
    sideBarWidth,
    rnPickerSelectStyle,
}

export default Styles
