export const SunmiPrinter = {};

export const PrinterImin = {};

export const NetPrinter = {
    closeConn: () => { },
    printTextAsync: () => { },
    connectPrinter: () => {
        return new Promise((resolve) => {
            resolve();
        });
    },
};

export const BLEPrinter = {};

export const SunmiBarcodeScanner = {};

export const RNFS = {
    readFile: () => {
        return new Promise((resolve) => {
            resolve();
        });
    },
};

export const RNQRGenerator = {};