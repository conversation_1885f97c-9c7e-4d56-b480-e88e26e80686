export const RAZER_CHANNEL_COMMISIONS_MYR_RATE = {
    credit: 2.4, // 2.4
    Credit: 2.4, // 2.4
    'CIL_Mastercard': 2.4,
    'CIL_Visa': 2.4,

    'TNG-EWALLET': 1.5, // 2024-04-22 - update to 1.5
    ShopeePay: 1.4,
    GrabPay: 1.4,
    BOOST: 1.6,
    'MB2U_QRPay-Push': 1.2,

    'Cash-711': 2.4,

    FPX: 2.4,
    FPX_MB2U: 2.4,
    MB2u: 2.4,
    FPX_CIMBCLICKS: 2.4,
    'CIMB-Clicks': 2.4,
    FPX_RHB: 2.4,
    'RHB-ONL': 2.4,
    FPX_PBB: 2.4,
    'PBeBank': 2.4,
    FPX_HLB: 2.4,
    'HLB-ONL': 2.4,
    FPX_BIMB: 2.4,
    FPX_AMB: 2.4,
    'AMB-W2W': 2.4,
    FPX_ABMB: 2.4,
    'ALB-ONL': 2.4,
    FPX_ABB: 2.4,
    'Affin-EPG': 2.4,
    FPX_BMMB: 2.4,
    FPX_BKRM: 2.4,
    FPX_BSN: 2.4,
    FPX_OCBC: 2.4,
    FPX_UOB: 2.4,
    FPX_HSBC: 2.4,
    FPX_SCB: 2.4,
    FPX_KFH: 2.4,
    'FPX-TPA': 2.4,
    FPX_AGROBANK: 2.4,

    FPX_B2B: 0,
    FPX_B2B_ABBM: 0,
    FPX_B2B_ABMB: 0,
    FPX_B2B_AGROBANK: 0,
    FPX_B2B_AMB: 0,
    FPX_B2B_BIMB: 0,
    FPX_B2B_BKRM: 0,
    FPX_B2B_BMMB: 0,
    FPX_B2B_BNP: 0,
    FPX_B2B_CIMB: 0,
    FPX_B2B_CITIBANK: 0,
    FPX_B2B_DEUTSCHE: 0,
    FPX_B2B_HLB: 0,
    FPX_B2B_HSBC: 0,
    FPX_B2B_KFH: 0,
    FPX_B2B_OCBC: 0,
    FPX_B2B_PBB: 0,    
    FPX_B2B_PBBE: 0,
    FPX_B2B_RHB: 0,
    FPX_B2B_SCB: 0,
    FPX_B2B_UOB: 0,
    FPX_B2B_UOBR: 0,
    FPX_M2E: 0,

    // 'Offline-Cash': 0,
    // 'Offline-Credit-Card': 0,
    // 'Offline-Bank-Transfer': 0,
    // 'Offline-Alipay': 0,
    // 'Offline-Boost': 0,
    // 'Offline-favePAY': 0,
    // 'Offline-TouchnGo-eWallet': 0,
    // 'Offline-WeChat-Pay': 0,
    // 'Offline-Credit-Amex': 0,
    // 'Offline-Bcard-Points': 0,
    // 'Offline-e-pay': 0,
    // 'Offline-Maybank-QRPAY': 0,
    // 'Offline-RAZER-CASH': 0,
    // 'Offline-RAZER-PAY': 0,
    // 'Offline-WEBCASH': 0,
    // 'Offline-Debit-Card': 0,
    // 'Offline-Credit-VISA': 0,
    // 'Offline-Credit-MasterCard': 0,
};