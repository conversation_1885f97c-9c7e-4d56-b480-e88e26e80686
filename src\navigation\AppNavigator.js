import React, { Component, useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  // useWindowDimensions,
  StyleSheet,
  Dimensions,
  useWindowDimensions,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator, useHeaderHeight } from '@react-navigation/stack';
// import TakeawayScreen from '../screen/TakeawayScreen.js.bak';
// import ErrorScreen from '../screen/ErrorScreen.takeaway';
// import PaymentSuccessScreen from '../screen/PaymentSuccessScreen';
// import OutletMenuScreen from '../screen/OutletMenuScreen.js.bak';
// import MenuItemDetailsScreen from '../screen/MenuItemDetailsScreen.js.bak';
// import CartScreen from '../screen/CartScreen.js.bak';
import { prefix } from '../constant/env';
import LoginScreen from '../screen/LoginScreen';
// import DashboardScreen from '../screen/DashboardScreen';
// import AllTransaction from '../screen/AllTransaction';
// import ProductScreen from '../screen/ProductScreen';
// import ProductCategoryScreen from '../screen/ProductCategoryScreen';
// import ProductAddScreen from '../screen/ProductAddScreen';
// import ProductMenuScreen from '../screen/ProductMenuScreen';
// import EmployeeScreen from '../screen/EmployeeScreen';
// import CrmScreen from '../screen/CrmScreen';
// import SegmentScreen from '../screen/SegmentScreen';
// import ReportSalesOvertime from '../screen/ReportSalesOvertime';
// import ReportSalesAov from '../screen/ReportSalesAov';
// import ReportSalesProduct from '../screen/ReportSalesProduct';
// import ReportSalesCategory from '../screen/ReportSalesCategory';
// import ReportCategoryProduct from '../screen/ReportCategoryProduct';
// import ReportSalesVariant from '../screen/ReportSalesVariant';
// import ReportSalesAddOns from '../screen/ReportSalesAddOns';
// import ReportSalesTransaction from '../screen/ReportSalesTransaction';
// import ReportSalesPaymentMethod from '../screen/ReportSalesPaymentMethod';
// import ReportSaleByShift from '../screen/ReportSaleByShift';
// import ReportSalesRepeat from '../screen/ReportSalesRepeat';
// import ReportShiftPayInOut from '../screen/ReportShiftPayInOut';
// import ReportPayout from '../screen/ReportPayout';
// import PreorderPackageScreen from '../screen/PreorderPackageScreen';
// import NewCustomer from '../screen/NewCustomer';
// import Guests from '../screen/Guests';
// import CreateGuests from '../screen/CreateGuests';
// import SettingsReservation from '../screen/SettingsReservationScreen';
// import SettingShiftScreen from '../screen/SettingShiftScreen';
// import SettingReceiptScreen from '../screen/SettingReceiptScreen';
// import SettingOrderScreen from '../screen/SettingOrderScreen';
// import SettingPaymentScreen from '../screen/SettingPaymentScreen';
// import EInvoiceScreen from '../screen/EInvoiceScreen';
// import SettingsWhatsappScreen from '../screen/SettingsWhatsappScreen';
// import NewLoyaltyCampaignScreen from '../screen/NewLoyaltyCampaignScreen';
// import SettingCredit from '../screen/SettingCredit';
// import LoyaltyStampScreen from '../screen/LoyaltyStampScreen';
// import VoucherReport from '../screen/VoucherReport';
// import SettingsScreen from '../screen/SettingsScreen';
// import VoucherScreen from '../screen/VoucherScreen';
// import TaggableVoucherListScreen from '../screen/TaggableVoucherListScreen';
// import TaggableVoucherReportScreen from '../screen/TaggableVoucherReport';
// import NewTaggableVoucherScreen from '../screen/NewTaggableVoucherScreen';
// import PromotionListScreen from '../screen/PromotionListScreen';
// import PromotionReport from '../screen/PromotionReport';
// import LoyaltyPointsRate from '../screen/LoyaltyPointsRate';
// import SettingSetCredit from '../screen/SettingSetCredit';
// import RedemptionRedeemedScreen from '../screen/RedemptionRedeemedScreen';
// import RedemptionExpiredScreen from '../screen/RedemptionExpiredScreen';
// import RedemptionScreen from '../screen/RedemptionScreen';
// import PurchaseOrderScreen from '../screen/PurchaseOrderScreen';
// import SettingLoyaltyScreen from '../screen/SettingLoyaltyScreen';
// import SettingRedemptionScreen from '../screen/SettingRedemptionScreen';
// import StockTakeScreen from '../screen/StockTakeScreen';
// import StockTransferScreen from '../screen/StockTransferScreen';
// import SupplierScreen from '../screen/SupplierScreen';
// import InventoryScreen from '../screen/InventoryScreen';
// import NewCampaignScreen from '../screen/NewCampaignScreen';
// import SettingPrinterScreen from '../screen/SettingPrinterScreen'
// import LoyaltyReport from '../screen/LoyaltyReport';
// import LoyaltySettingScreen from '../screen/LoyaltySettingScreen';
// import ReportActivityLog from '../screen/ReportActivityLog';
// import ReportSalesRefund from '../screen/ReportSalesRefund';
// import EmployeeTimeSheet from '../screen/EmployeeTimeSheet';
// import TopUpCreditReport from '../screen/TopUpCreditReport';
// import LoyaltySignUpCampaignScreen from '../screen/LoyaltySignUpCampaignScreen';
// import LoyaltyPayEarn from '../screen/LoyaltyPayEarn';
// import LoyaltyRewardRedemption from '../screen/LoyaltyRewardRedemption';
// import TopupCreditTypeScreen from '../screen/TopupCreditTypeScreen';
// import NewTopupCreditTypeScreen from '../screen/NewTopupCreditTypeScreen';
// import InventoryProductScreen from '../screen/InventoryProductScreen';
// import StockTakeProductScreen from '../screen/StockTakeProductScreen';
// import StockTransferProductScreen from '../screen/StockTransferProductScreen';
// import SupplierProductScreen from '../screen/SupplierProductScreen';
// import PurchaseOrderProductScreen from '../screen/PurchaseOrderProductScreen';
// import SettingMerchantPaymentScreen from '../screen/SettingMerchantPaymentScreen';
// import StockReturnProductScreen from '../screen/StockReturnProductScreen';
// import SettingCreditSMSScreen from '../screen/SettingCreditSMSScreen';
// import SettingCreditWhatsappScreen from '../screen/SettingCreditWhatsappScreen';
import Colors from '../constant/Colors';
// import VariantAddOnScreen from '../screen/VariantAddOnScreen';
// import TableScreen from '../screen/TableScreen';
// import OrderScreen from '../screen/OrderScreen';
// import TakeawayScreen from '../screen/TakeawayScreen';
// import OtherDeliveryScreen from '../screen/OtherDeliveryScreen';
// import HistoryScreen from '../screen/HistoryScreen';
// import UpsellingCampaignScreen from '../screen/UpsellingCampaignScreen';
// import UpsellingListScreen from '../screen/UpsellingListScreen';
// import ReservationAnalyticScreen from '../screen/ReservationAnalyticScreen';
// import SettingDepositScreen from '../screen/SettingDepositScreen';
// import ReportSalesOrderCount from '../screen/ReportSalesOrderCount';
// import KitchenScreen from '../screen/KitchenScreen';

// import DetailsScreen from '../screen/DetailsScreen';
// import CalendarScreen from '../screen/CalendarScreen';
// import ReportSalesRevisitCount from '../screen/ReportSalesRevisitCount';
// import ReportSalesUpselling from '../screen/ReportSalesUpselling';
// import ReportSalesUpsellingRevenue from '../screen/ReportSalesUpsellingRevenue';
import { isMobile } from '../util/common';
// import MoCartScreen from '../screen/MoCartScreen';
// import MenuOrderingScreen from '../screen/MenuOrderingScreen';
// import MoTableScreen from '../screen/MoTableScreen';
// import MoOutletMenuScreen from '../screen/MoOutletMenuScreen';
// import MoMenuItemDetailsScreen from '../screen/MoMenuItemDetailsScreen';

// import WorkOrderItemScreen from '../screen/WorkOrderItemList';
// import WorkOrderScreen from '../screen/WorkOrderList';
// import CompositerReportScreen from '../screen/CompositeReport';
// import CompositeReportScreen from '../screen/CompositeReport';
import HomePage from '../screen/HomePage';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

const config = {
  screens: {
    HomeStack: {
      path: `${prefix}`,
      initialRouteName: 'Login - KooDoo Manager',
      screens: {
        // 'KooDoo BackOffice': {
        //   path: '/',
        // },
        'Login - KooDoo Manager': {
          path: '/login',
        },
        'Error - KooDoo Manager': {
          path: '/error',
        },
        'Home Page - KooDoo Manager': {
          path: '/home',
        },
        'Dashboard - KooDoo Manager': {
          path: '/dashboard',
        },
        'All Transaction - KooDoo Manager': {
          path: '/all-transaction',
        },
        'Ordering - KooDoo Manager': {
          path: '/ordering',
        },
        'MoTable - KooDoo Manager': {
          path: '/motable',
        },
        'Product - KooDoo Manager': {
          path: '/product',
        },
        'Product Add - KooDoo Manager': {
          path: '/product-add',
        },
        'Product Category - KooDoo Manager': {
          path: '/product-category',
        },
        'Product Menu - KooDoo Manager': {
          path: '/product-menu',
        },
        'Employee List - KooDoo Manager': {
          path: '/employee-list',
        },
        'Promotion List - KooDoo Manager': {
          path: '/promotion-list'
        },
        'Promotion Report - KooDoo Manager': {
          path: '/promotion-report'
        },
        'Customer List - KooDoo Manager': {
          path: '/customers-list',
        },
        'Segment List - KooDoo Manager': {
          path: '/segment-list',
        },
        'Overview Report - KooDoo Manager': {
          path: '/overview-report'
        },
        'AOV Report - KooDoo Manager': {
          path: '/aov-report'
        },
        'Product Report - KooDoo Manager': {
          path: '/sales-by-product-report'
        },
        'Category Report - KooDoo Manager': {
          path: '/sales-by-category-report'
        },
        'Product Category Report - KooDoo Manager': {
          path: '/sales-by-product-category-report'
        },
        'Variant Report - KooDoo Manager': {
          path: '/sales-by-variant-report'
        },
        'Add Ons Report - KooDoo Manager': {
          path: '/sales-by-addons-report'
        },
        'Transaction Report - KooDoo Manager': {
          path: '/sales-by-transaction-report'
        },
        'Payment Method Report - KooDoo Manager': {
          path: '/sales-by-payment-method-report'
        },
        'Shift Report - KooDoo Manager': {
          path: '/sale-by-shift-report'
        },
        'ReportSalesRepeat': {
          path: '/report-sales-repeat'
        },
        'Pay In & Out Shift Report - KooDoo Manager': {
          path: '/sales-by-pay-in-n-out-shift-report'
        },
        'PayOut Report - KooDoo Manager': {
          path: '/payout-report'
        },
        'Preorder Package - KooDoo Manager': {
          path: '/preorder-package'
        },
        'Customer - KooDoo Manager': {
          path: '/customer'
        },
        'Settings Reservation - KooDoo Manager': {
          path: '/setting-reservation'
        },
        'Payment Setting - KooDoo Manager': {
          path: '/payment-settings'
        },
        'Order Settings - KooDoo Manager': {
          path: '/order-settings'
        },
        //old unused voucher page comment by 1/11
        // 'VoucherReport - KooDoo BackOffice': {
        //   path: '/voucherreport'
        // },
        // 'VoucherScreen - KooDoo BackOffice': {
        //   path: '/voucherscreen'
        // },
        'Voucher List - KooDoo Manager': {
          path: '/voucher-list'
        },
        'Voucher - KooDoo Manager': {
          path: '/voucher'
        },
        'Voucher Report - KooDoo Manager': {
          path: '/voucher-report'
        },
        'Receipt Settings - KooDoo Manager': {
          path: '/receipt-settings'
        },
        'Loyalty Campaign - KooDoo Manager': {
          path: '/loyalty-campaign'
        },
        'Loyalty Points - KooDoo Manager': {
          path: '/loyalty-point-list'
        },
        'Loyalty Stamps - KooDoo Manager': {
          path: '/loyalty-stamps-list'
        },
        'Guests - KooDoo Manager': {
          path: '/guests'
        },
        'Shift Settings - KooDoo Manager': {
          path: '/shift-settings'
        },
        'General Settings - KooDoo Manager': {
          path: '/general-settings'
        },
        'Redemption Package - KooDoo Manager': {
          path: '/redemption-package'
        },
        'Rate Settings - KooDoo Manager': {
          path: '/rate-settings'
        },
        'Redeemed Docket - KooDoo Manager': {
          path: '/redeemed-docket'
        },
        'Expired Docket - KooDoo Manager': {
          path: '/expired-docket'
        },
        'Active Docket - KooDoo Manager': {
          path: '/active-docket'
        },
        'Promotion - KooDoo Manager': {
          path: '/promotion'
        },
        'PurchaseOrder - KooDoo Manager': {
          path: '/purchaseorder'
        },
        'Setting Loyalty - KooDoo Manager': {
          path: '/setting-loyalty'
        },
        'Docket List - KooDoo Manager': {
          path: '/docket-list'
        },
        'StockTake - KooDoo Manager': {
          path: '/stocktake'
        },
        'Stock Transfer - KooDoo Manager': {
          path: '/stock-transfer'
        },
        'Supplier - KooDoo Manager': {
          path: 'supplier'
        },
        'Inventory - KooDoo Manager': {
          path: 'inventory'
        },
        'Printer Settings - KooDoo Manager': {
          path: 'printer-settings'
        },
        'Loyalty Report - KooDoo Manager': {
          path: 'loyalty-report'
        },
        'Loyalty Setting - KooDoo Manager': {
          path: 'loyalty-setting'
        },
        'Activity-Log - KooDoo Manager': {
          path: 'activity-log'
        },
        'Refund Report - KooDoo Manager': {
          path: 'sales-by-refund-report'
        },
        'Employee Timesheet - KooDoo Manager': {
          path: 'employee-timesheet'
        },
        'Pay & Earn - KooDoo Manager': {
          path: 'loyalty-pay-n-earn'
        },
        'Credit Type Report - KooDoo Manager': {
          path: 'credit-type-report'
        },
        'Sign Up Reward - KooDoo Manager': {
          path: 'sign-up-reward'
        },
        'Reward & Redemption - KooDoo Manager': {
          path: 'loyalty-reward-n-redemption'
        },
        'Credit Type List - KooDoo Manager': {
          path: 'credit-type-list'
        },
        'Credit Type - KooDoo Manager': {
          path: 'credit-type'
        },
        'StockTake Product - KooDoo Manager': {
          path: '/stocktake-product'
        },
        'Stock Transfer Product - KooDoo Manager': {
          path: '/stock-transfer-product'
        },
        'Supplier Product - KooDoo Manager': {
          path: 'supplier-product'
        },
        'Inventory Product - KooDoo Manager': {
          path: 'inventory-product'
        },
        'PurchaseOrder Product - KooDoo Manager': {
          path: '/purchaseorder-product'
        },
        'Stock Return Product - KooDoo Manager': {
          path: '/stock-return-product'
        },

        'Merchant Payment - KooDoo Manager': {
          path: '/mp-settings'
        },

        'SMS Credit - KooDoo Manager': {
          path: '/sms-credit-settings'
        },

        'Whatsapp Credit - KooDoo Manager': {
          path: '/whatsapp-credit-settings'
        },


        "Payment Success - KooDoo Manager": {
          path: "payment/:subdomain?/:amount?/:appCode?/:channel?/:checksum?/:mpSecuredVerfified?/:msgType?/:orderId?/:pInstruction?/:statusCode?/:txnId?",
        },

        'Table - KooDoo Manager': {
          path: '/table',
        },
        'Kitchen - KooDoo Manager': {
          path: '/kitchen',
        },
        'Order - KooDoo Manager': {
          path: '/order'
        },
        'Takeaway - KooDoo Manager': {
          path: '/takeaway'
        },
        'Other Delivery - KooDoo Manager': {
          path: '/other-delivery'
        },
        'History - KooDoo Manager': {
          path: '/history'
        },
        'Manage Reservation - KooDoo Manager': {
          path: '/manage-reservations'
        },
        'Calendar - KooDoo Manager': {
          path: '/calendar'
        },

        'Upselling List - KooDoo Manager': {
          path: '/upselling-list'
        },
        'Upselling Campaign - KooDoo Manager': {
          path: '/upselling-campaign'
        },
        'Reservation Analytic - KooDoo Manager': {
          path: '/reservation-analytic'
        },
        'Revisit Count Report - KooDoo Manager': {
          path: '/revisit-count-report'
        },
        'Order Count Report - KooDoo Manager': {
          path: '/order-count-report'
        },
        'Upselling Report - KooDoo Manager': {
          path: '/upselling-report'
        },
        'Upselling Revenue Report - KooDoo Manager': {
          path: '/upselling-revenue-report'
        },

        'Reservation Setting Deposit - KooDoo Manager': {
          path: '/reservation-setting-deposit'
        },
        'E Invoice - KooDoo BackOffice': {
          path: '/e-invoice'
        },
        'WhatsApp Settings - KooDoo BackOffice': {
          path: '/whatsapp-settings'
        },

        'Variant Add-on - KooDoo Manager': {
          path: '/variant-addon'
        },
        'Work Order - KooDoo BackOffice': {
          path: '/work-order'
        },
        'Work Order Item - KooDoo BackOffice': {
          path: '/work-order-item'
        },
        'Composite Report - KooDoo BackOffice': {
          path: '/composite-report'
        }
      },
    },

    // screens: {
    //   MainScreen: {
    //     path: 'qr/:outletId/:tableId/:tableCode/:waiterId?',
    //   },
    // },
    // NewOrderStack: {
    //   path: "/web/:outletId/:tableId/:tableCode/:tablePax/:waiterId",
    //   initialRouteName: "neworder",
    //   screens: {
    //     Login: {
    //       path: 'new-order',
    //     },
    //   },
    // },
    // NewOrderStack: {
    //   path: `${prefix}`,
    //   // initialRouteName: "NewOrder",
    //   screens: {
    //     NewOrder: {
    //       path: 'new-order/:outletId?/:tableId?/:tableCode?/:tablePax?/:waiterId?',
    //     },
    //     NewOrderGeneric: {
    //       path: 'new-order-generic/:outletId?/:tableId?/:tableCode?',
    //     },
    //     Takeaway: {
    //       path: 'takeaway',
    //     },
    //     Scan: {
    //       path: 'scan',
    //     },
    //     Error: {
    //       path: 'error',
    //     },
    //     PaymentSuccess: {
    //       path: 'payment/:amount?/:appCode?/:channel?/:checksum?/:mpSecuredVerfified?/:msgType?/:orderId?/:pInstruction?/:statusCode?/:txnId?',
    //     },
    //   },
    // },
    // OrderStack: {
    //   path: `${prefix}/outlet`,
    //   initialRouteName: "OutletMenu",
    //   screens: {
    //     OutletMenu: {
    //       path: 'menu',
    //     },
    //     MenuItemDetailsScreen: {
    //       path: 'menu/item',
    //     },
    //     CartScreen: {
    //       path: 'cart',
    //     },
    //   },
    // },
  },
};

const linking = {
  prefixes: [
    /* your linking prefixes */
  ],
  config: config,
};

// const NewOrderStack = ({ navigation }) => {
//   navigation.setOptions({ tabBarVisible: false });

//   return (
//     <Stack.Navigator
//       screenOptions={{
//         headerShown: false,
//       }}
//     >
//       <Stack.Screen
//         name='NewOrder'
//         component={NewOrderScreen}
//       />

//       <Stack.Screen
//         name='NewOrderGeneric'
//         component={NewOrderGenericScreen}
//       />

//       <Stack.Screen
//         name='Takeaway'
//         component={TakeawayScreen}
//       />

//       <Stack.Screen
//         name='PaymentSuccess'
//         component={PaymentSuccessScreen}
//       />

//       <Stack.Screen
//         name='Scan'
//         component={ScanScreen}
//       />

//       <Stack.Screen
//         name='Error'
//         component={ErrorScreen}
//       />
//     </Stack.Navigator>
//   );
// }

// const OrderStack = ({ navigation }) => {
//   navigation.setOptions({ tabBarVisible: false });

//   return (
//     <Stack.Navigator
//       screenOptions={{
//         // headerShown: false,
//       }}
//     >
//       <Stack.Screen
//         name='OutletMenu'
//         component={OutletMenuScreen}
//       />
//       <Stack.Screen
//         name='MenuItemDetailsScreen'
//         component={MenuItemDetailsScreen}
//       />
//       <Stack.Screen
//         name='CartScreen'
//         component={CartScreen}
//       />
//     </Stack.Navigator>
//   );
// }

const HomeStack = ({ navigation }) => {
  navigation.setOptions({ tabBarVisible: false });

  const { height: windowHeight, width: windowWidth } = useWindowDimensions();
  const headerOption = {
    headerTitleStyle: { color: Colors.whiteColor, marginLeft: windowWidth * 0.3, fontFamily: 'NunitoSans-Bold', fontSize: 32 },
    headerTintColor: Colors.darkBgColor,
    headerStyle: {
      backgroundColor: Colors.darkBgColor,
      elevation: 0,
      shadowOpacity: 0,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.22,
      shadowRadius: 3.22,
      elevation: 3,

      height: windowHeight * 0.08
    },
    tabBarVisible: false,
  };

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: isMobile() ? false : true, //change to true to shown
      }}
    >
      {/* <Stack.Screen
        name='KooDoo BackOffice'
        component={HomeScreen}
      /> */}

      <Stack.Screen
        name='Login - KooDoo Manager'
        component={LoginScreen}
      />

      {/* <Stack.Screen
        name='Error - KooDoo Manager'
        component={ErrorScreen}
      /> */}

      <Stack.Screen
        name='Home Page - KooDoo Manager'
        component={HomePage}
        options={headerOption}
      />

      {/* <Stack.Screen
        name='Dashboard - KooDoo Manager'
        component={DashboardScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='All Transaction - KooDoo Manager'
        component={AllTransaction}
        options={headerOption}
      /> */}

      {/* <Stack.Screen
        name="Ordering - KooDoo Manager"
        component={MenuOrderingScreen}
        options={{ headerShown: false }}
      />

      <Stack.Screen
        name="MoTable - KooDoo Manager"
        component={MoTableScreen}
        options={headerOption}
      />

      <Stack.Screen
        name="Outlet Menu - KooDoo Manager"
        component={MoOutletMenuScreen}
        options={headerOption}
      />

      <Stack.Screen
        name="Menu Item Details - KooDoo Manager"
        component={MoMenuItemDetailsScreen}
        options={headerOption}
      />

      <Stack.Screen
        name="Cart - KooDoo Manager"
        component={MoCartScreen}
        options={headerOption}
      /> */}

      {/* <Stack.Screen
        name='Product - KooDoo Manager'
        component={ProductScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Product Add - KooDoo Manager'
        component={ProductAddScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Product Category - KooDoo Manager'
        component={ProductCategoryScreen}
        options={headerOption}
      /> */}

      {/* <Stack.Screen
        name='Product Menu - KooDoo Manager'
        component={ProductMenuScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Employee List - KooDoo Manager'
        component={EmployeeScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Customer List - KooDoo Manager'
        component={CrmScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Promotion List - KooDoo Manager'
        component={PromotionListScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Promotion Report - KooDoo Manager'
        component={PromotionReport}
        options={headerOption}
      />

      <Stack.Screen
        name='Segment List - KooDoo Manager'
        component={SegmentScreen}
        options={headerOption}
      /> */}

      {/* <Stack.Screen
        name='Overview Report - KooDoo Manager'
        component={ReportSalesOvertime}
        options={headerOption}
      />
      <Stack.Screen
        name='AOV Report - KooDoo Manager'
        component={ReportSalesAov}
        options={headerOption}
      />
      <Stack.Screen
        name='Product Report - KooDoo Manager'
        component={ReportSalesProduct}
        options={headerOption}
      />
      <Stack.Screen
        name='Category Report - KooDoo Manager'
        component={ReportSalesCategory}
        options={headerOption}
      />
      <Stack.Screen
        name='Product Category Report - KooDoo Manager'
        component={ReportCategoryProduct}
        options={headerOption}
      />
      <Stack.Screen
        name='Variant Report - KooDoo Manager'
        component={ReportSalesVariant}
        options={headerOption}
      />
      <Stack.Screen
        name='Add Ons Report - KooDoo Manager'
        component={ReportSalesAddOns}
        options={headerOption}
      />
      <Stack.Screen
        name='Transaction Report - KooDoo Manager'
        component={ReportSalesTransaction}
        options={headerOption}
      />
      <Stack.Screen
        name='Payment Method Report - KooDoo Manager'
        component={ReportSalesPaymentMethod}
        options={headerOption}
      />
      <Stack.Screen
        name='Shift Report - KooDoo Manager'
        component={ReportSaleByShift}
        options={headerOption}
      />
      <Stack.Screen
        name='Repeat Sales Report - KooDoo Manager'
        component={ReportSalesRepeat}
        options={headerOption}
      />
      <Stack.Screen
        name='Pay In & Out Shift Report - KooDoo Manager'
        component={ReportShiftPayInOut}
        options={headerOption}
      />
      <Stack.Screen
        name='PayOut Report - KooDoo Manager'
        component={ReportPayout}
        options={headerOption}
      /> */}

      {/* <Stack.Screen
        name='Preorder Package - KooDoo Manager'
        component={PreorderPackageScreen}
        options={headerOption}
      /> */}
      {/* <Stack.Screen
        name='Customer - KooDoo Manager'
        component={NewCustomer}
        options={headerOption}
      /> */}
      {/* <Stack.Screen
        name='Guests - KooDoo Manager'
        component={Guests}
        options={headerOption}
      />
      <Stack.Screen
        name='Create Guests - KooDoo Manager'
        component={CreateGuests}
        options={headerOption}
      />
      <Stack.Screen
        name='Settings Reservation - KooDoo Manager'
        component={SettingsReservation}
        options={headerOption}
      />
      <Stack.Screen
        name='Receipt Settings - KooDoo Manager'
        component={SettingReceiptScreen}
        options={headerOption}
      />
      <Stack.Screen
        name="Order Settings - KooDoo Manager"
        component={SettingOrderScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Payment Setting - KooDoo Manager'
        component={SettingPaymentScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='E Invoice - KooDoo Manager'
        component={EInvoiceScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='WhatsApp Settings - KooDoo Manager'
        component={SettingsWhatsappScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='VoucherScreen - KooDoo Manager'
        component={VoucherScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Loyalty Campaign - KooDoo Manager'
        component={NewLoyaltyCampaignScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Loyalty Points - KooDoo Manager'
        component={SettingCredit}
        options={headerOption}
      />
      <Stack.Screen
        name='Loyalty Stamps - KooDoo Manager'
        component={LoyaltyStampScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='VoucherReport - KooDoo Manager'
        component={VoucherReport}
      />
      <Stack.Screen
        name='Voucher List - KooDoo Manager'
        component={TaggableVoucherListScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Voucher - KooDoo Manager'
        component={NewTaggableVoucherScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Voucher Report - KooDoo Manager'
        component={TaggableVoucherReportScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Shift Settings - KooDoo Manager'
        component={SettingShiftScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='General Settings - KooDoo Manager'
        component={SettingsScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Rate Settings - KooDoo Manager'
        component={LoyaltyPointsRate}
        options={headerOption}
      />
      <Stack.Screen
        name='Redemption Package - KooDoo Manager'
        component={SettingSetCredit}
        options={headerOption}
      />
      <Stack.Screen
        name='Redeemed Docket - KooDoo Manager'
        component={RedemptionRedeemedScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Expired Docket - KooDoo Manager'
        component={RedemptionExpiredScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Active Docket - KooDoo Manager'
        component={RedemptionScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Promotion - KooDoo Manager'
        component={NewCampaignScreen}
        options={headerOption}
      /> */}

      {/* <Stack.Screen
        name='PurchaseOrder - KooDoo Manager'
        component={PurchaseOrderScreen}
        options={headerOption}
      /> */}

      {/* <Stack.Screen
        name='Setting Loyalty - KooDoo Manager'
        component={SettingLoyaltyScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Docket List - KooDoo Manager'
        component={SettingRedemptionScreen}
        options={headerOption}
      /> */}

      {/* <Stack.Screen
        name='StockTake - KooDoo Manager'
        component={StockTakeScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Stock Transfer - KooDoo Manager'
        component={StockTransferScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Supplier - KooDoo Manager'
        component={SupplierScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Inventory - KooDoo Manager'
        component={InventoryScreen}
        options={headerOption}
      /> */}

      {/* <Stack.Screen
        name='Printer Settings - KooDoo Manager'
        component={SettingPrinterScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Loyalty Report - KooDoo Manager'
        component={LoyaltyReport}
        options={headerOption}
      />
      <Stack.Screen
        name='Loyalty Setting - KooDoo Manager'
        component={LoyaltySettingScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Activity-Log - KooDoo Manager'
        component={ReportActivityLog}
        options={headerOption}
      /> */}

      {/* <Stack.Screen
        name='Refund Report - KooDoo Manager'
        component={ReportSalesRefund}
        options={headerOption}
      /> */}

      {/* <Stack.Screen
        name='Employee Timesheet - KooDoo Manager'
        component={EmployeeTimeSheet}
        options={headerOption}
      />
      <Stack.Screen
        name='Pay & Earn - KooDoo Manager'
        component={LoyaltyPayEarn}
        options={headerOption}
      />
      <Stack.Screen
        name='Credit Type Report - KooDoo Manager'
        component={TopUpCreditReport}
        options={headerOption}
      />
      <Stack.Screen
        name='Sign Up Reward - KooDoo Manager'
        component={LoyaltySignUpCampaignScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Reward & Redemption - KooDoo Manager'
        component={LoyaltyRewardRedemption}
        options={headerOption}
      />
      <Stack.Screen
        name='Credit Type List - KooDoo Manager'
        component={TopupCreditTypeScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Credit Type - KooDoo Manager'
        component={NewTopupCreditTypeScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='StockTake Product - KooDoo Manager'
        component={StockTakeProductScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Stock Transfer Product - KooDoo Manager'
        component={StockTransferProductScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Supplier Product - KooDoo Manager'
        component={SupplierProductScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Inventory Product - KooDoo Manager'
        component={InventoryProductScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='PurchaseOrder Product - KooDoo Manager'
        component={PurchaseOrderProductScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Stock Return Product - KooDoo Manager'
        component={StockReturnProductScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Merchant Payment - KooDoo Manager'
        component={SettingMerchantPaymentScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='SMS Credit - KooDoo Manager'
        component={SettingCreditSMSScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Whatsapp Credit - KooDoo Manager'
        component={SettingCreditWhatsappScreen}
        options={headerOption}
      />


      <Stack.Screen
        name='Payment Success - KooDoo Manager'
        component={PaymentSuccessScreen}
      />

      <Stack.Screen
        name='Table - KooDoo Manager'
        component={TableScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Order - KooDoo Manager'
        component={OrderScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Kitchen - KooDoo Manager'
        component={KitchenScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Takeaway - KooDoo Manager'
        component={TakeawayScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Other Delivery - KooDoo Manager'
        component={OtherDeliveryScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='History - KooDoo Manager'
        component={HistoryScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Manage Reservation - KooDoo Manager'
        component={DetailsScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Calendar - KooDoo Manager'
        component={CalendarScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Upselling List - KooDoo Manager'
        component={UpsellingListScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Upselling Campaign - KooDoo Manager'
        component={UpsellingCampaignScreen}
        options={headerOption}
      />
      <Stack.Screen
        name='Reservation Analytic - KooDoo Manager'
        component={ReservationAnalyticScreen}
        options={headerOption}
      /> */}

      {/* <Stack.Screen
        name='Revisit Count Report - KooDoo Manager'
        component={ReportSalesRevisitCount}
        options={headerOption}
      />
      <Stack.Screen
        name='Order Count Report - KooDoo Manager'
        component={ReportSalesOrderCount}
        options={headerOption}
      />
      <Stack.Screen
        name='Upselling Report - KooDoo Manager'
        component={ReportSalesUpselling}
        options={headerOption}
      />

      <Stack.Screen
        name='Upselling Revenue Report - KooDoo Manager'
        component={ReportSalesUpsellingRevenue}
        options={headerOption}
      /> */}

      {/* <Stack.Screen
        name='Reservation Setting Deposit - KooDoo Manager'
        component={SettingDepositScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Variant Add-on - KooDoo Manager'
        component={VariantAddOnScreen}
        options={headerOption}
      /> */}

      {/* <Stack.Screen
        name='Work Order - KooDoo BackOffice'
        component={WorkOrderScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Work Order Item - KooDoo BackOffice'
        component={WorkOrderItemScreen}
        options={headerOption}
      />

      <Stack.Screen
        name='Composite Report - KooDoo BackOffice'
        component={CompositeReportScreen}
        options={headerOption}
      /> */}


    </Stack.Navigator >
  );
}

const AppNavigator = () => {
  return (
    <NavigationContainer
      // ref={navigationRef}
      // style={{
      //   backgroundColor: 'red',
      // }}      
      linking={linking} fallback={<Text>Loading...</Text>}>
      <Tab.Navigator tabBar={() => null}>
        <Tab.Screen
          name='HomeStack'
          component={HomeStack}
          screenOptions={({ route }) => ({
            tabBarVisible: null,
          })}
        />

        {/* <Tab.Screen
          name='NewOrderStack'
          component={NewOrderStack}
          screenOptions={({ route }) => ({
            tabBarVisible: null,
          })}
        />

        <Tab.Screen
          name='OrderStack'
          component={OrderStack}
          screenOptions={({ route }) => ({
            tabBarVisible: null,
          })}
        /> */}
      </Tab.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-evenly',
  },
  menuItemsCard: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  circleContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    padding: 10,
  },
});

export default AppNavigator;