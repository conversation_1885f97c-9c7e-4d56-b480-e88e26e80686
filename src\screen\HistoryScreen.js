import React, { Component, useReducer, useState, useEffect, useCallback, useRef } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    Alert,
    Dimensions,
    TouchableOpacity,
    Switch,
    FlatList,
    TextInput,
    Platform,
    useWindowDimensions,
    Modal,
    InteractionManager,
    ActivityIndicator,
} from 'react-native';
import Colors from '../constant/Colors';
import { ReactComponent as GCalendar } from "../assets/svg/GCalendar.svg";
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import MIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
//import Swipeable from 'react-native-gesture-handler/Swipeable';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as User from '../util/User';
import DropDownPicker from 'react-native-dropdown-picker';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import Styles from '../constant/Styles';
import moment from 'moment';
import IIcon from 'react-native-vector-icons/Ionicons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {
    getOrderDiscountInfo,
    getOrderDiscountInfoInclOrderBased,
    // isTablet,
    sendOrderReceiptEmail,
    sliceUnicodeStringV2WithDots,
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import {
    ORDER_TYPE,
    LALAMOVE_STATUS_PARSED,
    COURIER_CODE,
    COURIER_DROPDOWN_LIST,
    MRSPEEDY_STATUS_PARSED,
    PAYMENT_CHANNEL_NAME_PARSED,
    ROLE_TYPE,
    PRIVILEGES_NAME,
    REFUND_REASON_LIST,
    OFFLINE_PAYMENT_METHOD_TYPE,
    EXPAND_TAB_TYPE,
    OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
    ORDER_TYPE_SUB,
    KD_PRINT_VARIATION,
    UNIT_TYPE_SHORT,
    PRODUCT_PRICE_TYPE,
} from '../constant/common';
import { UserStore } from '../store/userStore';
import { CommonStore } from '../store/commonStore';
import { COURIER_INFO_DICT, USER_ORDER_STATUS } from '../constant/common';
import { openCashDrawer, printDocket, printDocketForKD, printKDSummaryCategoryWrapper, printUserOrder } from '../util/printer';
import AsyncImage from '../components/asyncImage';
import { PRINTER_USAGE_TYPE } from '../constant/printer';
import APILocal from '../util/apiLocalReplacers';
import { useNetInfo } from '@react-native-community/netinfo';
import { TripleDES } from 'crypto-js';
import AntDesign from 'react-native-vector-icons/AntDesign';
// import RNPickerSelect from 'react-native-picker-select';
// import CheckBox from '@react-native-community/checkbox';
import { setCartItem } from '../util/Cart';
import { NetPrinter } from 'react-native-thermal-receipt-printer-image-qr';
import { useFocusEffect } from '@react-navigation/native';
// import UserIdleWrapper from '../components/userIdleWrapper';
// import { storageMMKV } from '../util/storageMMKV';
// import { FlashList } from "@shopify/flash-list";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "../constant/datePicker.css";
import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import dineinicon from "../assets/image/dineinGrey.png";
import takeawayicon from "../assets/image/TakeawayBlack.png";
import guesticon from "../assets/image/profile-pic.jpg";
import { ReactComponent as SnedEmail } from '../assets/svg/SendEmail.svg';
import { ReactComponent as CreditcardMultiple } from '../assets/svg/CreditcardMultiple.svg';
import { ReactComponent as Close } from '../assets/svg/Close.svg';
import { ReactComponent as DeliveryDining } from '../assets/svg/DeliveryDining.svg';

import Ionicon from "react-native-vector-icons/Ionicons";

const HistoryScreen = React.memo((props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const netInfo = useNetInfo();

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    const [outletId, setOutletId] = useState(User.getOutletId());
    const [table, setTable] = useState([]);
    const [prepareTime, setPrepareTime] = useState([]);
    const [order, setOrder] = useState([]);
    const [expandOrder, setExpandOrder] = useState(false);
    const [sort, setSort] = useState(null);
    const [filter, setFilter] = useState(null);
    const [lastSort, setLastSort] = useState(null);
    const [lastFilter, setLastFilter] = useState(null);
    const [switchMerchant, setSwitchMerchant] = useState(false);
    const [controller, setController] = useState({});
    const [controller1, setController1] = useState({});

    const [expandViewDict, setExpandViewDict] = useState({});

    const [historyOrders, setHistoryOrders] = useState([]);
    const [search, setSearch] = useState('');

    const [filterType, setFilterType] = useState(1);

    const allOutletsUserOrdersDone = OutletStore.useState(
        (s) => s.allOutletsUserOrdersDone,
    );

    const crmUsers = OutletStore.useState((s) => s.crmUsers);

    const userName = UserStore.useState((s) => s.name);
    const userRole = UserStore.useState((s) => s.role);
    const merchantName = MerchantStore.useState((s) => s.name);
    const privileges_state = UserStore.useState((s) => s.privileges);

    ///////////Sorting////////
    const [sortSender, setSortSender] = useState();
    const [sortOrderID, setSortOrderID] = useState();
    const [sortDateTime, setSortDateTime] = useState();
    const [sortCustomerName, setSortCustomerName] = useState();
    const [sortPaymentMethod, setSortPaymentMethod] = useState();
    const [sortTotalPrice, setSortTotalPrice] = useState();

    const [refundOrder, setRefundOrder] = useState({});
    const [refundOrderId, setRefundOrderId] = useState('');
    const [undoRefundOrderId, setUndoRefundOrderId] = useState('');

    const [cartItemList, setCartItemList] = useState([]);

    //////////////////////////////////////////////////////////////

    const firebaseUid = UserStore.useState((s) => s.firebaseUid);

    const currOutletId = MerchantStore.useState((s) => s.currOutletId);
    const allOutlets = MerchantStore.useState((s) => s.allOutlets);

    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );

    // Testing calendar selection
    const [showDateTimePicker, setShowDateTimePicker] = useState(false);
    const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
    const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
    const [pickerMode, setPickerMode] = useState('datetime');

    // const [rev_date, setRev_date] = useState(
    //   moment().subtract(6, 'days').startOf('day').toDate(),
    // );
    // const [rev_date1, setRev_date1] = useState(
    //   moment().endOf('day').toDate(),
    // );

    const historyStartDate = CommonStore.useState(s => s.historyStartDate);
    const historyEndDate = CommonStore.useState(s => s.historyEndDate);

    const [refundModal, setRefundModal] = useState(false);

    const [undoRefundModal, setUndoRefundModal] = useState(false);

    const [refundReason, setRefundReason] = useState('');
    const [refundNote, setRefundNote] = useState('');

    const [selectedCartItemDict, setSelectedCartItemDict] = useState({});
    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    const [paymentMethodModal, setPaymentMethodModal] = useState(false);
    const [paymentMethodChannel, setPaymentMethodChannel] = useState('');
    const currOutlet = MerchantStore.useState((s) => s.currOutlet);
    const [paymentMethods, setPaymentMethods] = useState(
        OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
    );
    const [
        outletPaymentMethodsDropdownList,
        setOutletPaymentMethodsDropdownList,
    ] = useState([]);
    const outletPaymentMethods = OutletStore.useState(
        (s) => s.outletPaymentMethods,
    );

    //////////////////////////////////////////////////////////////

    // 2023-01-06 - Send receipt email support

    const [receiptEmailModal, setReceiptEmailModal] = useState(false);
    const [receiptEmailToSent, setReceiptEmailToSent] = useState('');
    const [receiptEmailOrder, setReceiptEmailOrder] = useState(null);

    //////////////////////////////////////////////////////////////

    const paymentMethodPickerRef = useRef(null);

    const [paymentMethodsDropdownList, setPaymentMethodsDropdownList] = useState([]);

    // 2022-09-13 - Optimize faster payment method list

    useEffect(() => {
        InteractionManager.runAfterInteractions(() => {
            var paymentMethodsDropdownListTemp = [];

            paymentMethodsDropdownListTemp = ((currOutlet && currOutlet.paymentMethodsHidden) ?
                paymentMethods.filter(method => !currOutlet.paymentMethodsHidden[method.value.name]).concat(outletPaymentMethodsDropdownList)
                : paymentMethods.concat(outletPaymentMethodsDropdownList)).map(item => ({
                    label: item.name,
                    value: item.value.channel ? item.value.channel : item.name,
                }));

            setPaymentMethodsDropdownList(paymentMethodsDropdownListTemp);
        });
    }, [currOutlet, paymentMethods, outletPaymentMethodsDropdownList]);

    //////////////////////////////////////////////////////////////

    const [privileges, setPrivileges] = useState([]);
    const role = UserStore.useState((s) => s.role);
    const pinNo = UserStore.useState(s => s.pinNo);

    const [openFilter, setOpenFilter] = useState(false);

    useEffect(async () => {
        // admin full access

        // const enteredPinNo = await AsyncStorage.getItem('enteredPinNo');
        // const enteredPinNo = storageMMKV.getString('enteredPinNo');

        if (role === ROLE_TYPE.ADMIN
            // && pinNo === enteredPinNo
        ) {
            setPrivileges([
                "EMPLOYEES",
                "OPERATION",
                "PRODUCT",
                "INVENTORY",
                "INVENTORY_COMPOSITE",
                "DOCKET",
                "VOUCHER",
                "PROMOTION",
                "CRM",
                "LOYALTY",
                "TRANSACTIONS",
                "REPORT",
                "RESERVATIONS",

                // for action
                'REFUND_ORDER',

                'SETTINGS',

                'QUEUE',

                'OPEN_CASH_DRAWER',

                'KDS',

                'UPSELLING',

                // for Kitchen

                'REJECT_ITEM',
                'CANCEL_ORDER',
                //'REFUND_tORDER',
            ]);
        } else {
            setPrivileges(privileges_state || []);
        }
    }, [role, privileges_state, pinNo]);

    //////////////////////////////////////////////////////////////

    useEffect(() => {
        InteractionManager.runAfterInteractions(() => {
            var filteredPaymentMethodDropdownList = (currOutlet.paymentMethodsHidden ?
                paymentMethods.filter(method => !currOutlet.paymentMethodsHidden[method.value.name]).concat(outletPaymentMethodsDropdownList)
                : paymentMethods.concat(outletPaymentMethodsDropdownList));

            if (filteredPaymentMethodDropdownList.length > 0) {
                setPaymentMethodChannel(filteredPaymentMethodDropdownList[0].value.channel);
            }
        });
    }, [currOutlet, paymentMethods]);

    //console.log('history orders');
    //console.log(historyOrders);

    useEffect(() => {
        InteractionManager.runAfterInteractions(() => {
            setOutletPaymentMethodsDropdownList(
                outletPaymentMethods.map((item) => ({
                    // label: item.name,
                    name: item.name,
                    // value: item.value,
                    // value: item.uniqueId,
                    value: item,
                    //image: item.image,
                    paymentMethodId: item.uniqueId,
                })),
            );
        });
    }, [outletPaymentMethods]);

    useEffect(() => {
        if (isMounted) {
            if (currOutletId !== '') {
                InteractionManager.runAfterInteractions(() => {
                    var historyOrdersTemp = allOutletsUserOrdersDone.filter((order) => {
                        // if (order.cartItems.find(item => item.itemName === 'Xtra Jus Mangga')) {
                        //   console.log('possible order');
                        //   console.log(order);
                        // }

                        // if (
                        //   order.outletId === currOutletId &&
                        //   moment(rev_date).isBefore(order.completedDate) &&
                        //   moment(rev_date1).isAfter(order.completedDate)
                        // ) {
                        //   return true;
                        // }
                        // else if (
                        //   order.outletId === currOutletId &&
                        //   moment(rev_date).isSame(order.completedDate, 'day')
                        // ) {
                        //   return true;
                        // } else {
                        //   return false;
                        // }

                        // // console.log('orderType & filterType');
                        // // console.log(order.orderType);
                        // // console.log(filterType);

                        // if (order.orderDate >= 1674549600000 && order.orderDate <= 1674550200000) {
                        //   console.log(order);
                        //   console.log('paused');
                        // }

                        if (
                            order.outletId === currOutletId &&
                            moment(historyStartDate).isBefore(order.completedDate) &&
                            moment(historyEndDate).isAfter(order.completedDate)
                        ) {
                            if (filterType === 1) {
                                return true;
                            }
                            else if (filterType === 2) {
                                if (order.orderType === ORDER_TYPE.DINEIN) {
                                    return true;
                                }
                            }
                            else if (filterType === 3) {
                                if (order.orderType === ORDER_TYPE.PICKUP || order.orderType === ORDER_TYPE.DELIVERY) {
                                    return true;
                                }
                            }

                            return false;
                        } else {
                            return false;
                        }
                    });

                    historyOrdersTemp.sort((a, b) => {
                        return b.createdAt - a.createdAt;
                    });

                    setHistoryOrders(historyOrdersTemp);
                });
            }

            // // console.log('takeAwayOrders');
            // // console.log(userOrders.filter(order => order.orderType !== ORDER_TYPE.DINEIN));
            //   for (var i = 0; i < allOutletsUserOrdersDone.length; i++) {
            //     if (allOutletsUserOrdersDone[i].outletId === currOutletId &&
            //         moment(rev_date).isSameOrBefore(allOutletsUserOrdersDone[i].completedDate) &&
            //         moment(rev_date1).isAfter(allOutletsUserOrdersDone[i].completedDate)) {
            //         allTransactionsTemp.push(allOutletsUserOrdersDone[i]);
            //     }
            // }

            // allTransactionsTemp.sort((a, b) => a.completedDate - b.completedDate);

            // setAllTransactions(allTransactionsTemp);
            // setCurrentPage(1);
            // setPageCount(Math.ceil(allTransactionsTemp.length / perPage));
        }
    }, [allOutletsUserOrdersDone, currOutletId, historyStartDate, historyEndDate, filterType, isMounted]);

    const setState = () => { };

    // navigation.dangerouslyGetParent().setOptions({
    //   tabBarVisible: false,
    // });

    const isLoading = CommonStore.useState((s) => s.isLoading);

    const currOutletShiftStatus = OutletStore.useState(
        (s) => s.currOutletShiftStatus
    );

    var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
        label: sliceUnicodeStringV2WithDots(outlet.name, 20),
        value: outlet.uniqueId,
    }));

    const [openO, setOpenO] = useState(false);

    useEffect(() => {
        CommonStore.update((s) => {
            s.outletSelectDropdownView = () => {
                return (
                    <View
                        style={{
                            flexDirection: "row",
                            alignItems: "center",
                            borderRadius: 8,
                            width: 200,
                            backgroundColor: "white",
                        }}
                    >
                        {currOutletId.length > 0 &&
                            allOutlets.find((item) => item.uniqueId === currOutletId) ? (
                            <DropDownPicker
                                style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: 200,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                }}
                                dropDownContainerStyle={{
                                    width: 200,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                }}
                                labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                }}
                                textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                }}
                                selectedItemContainerStyle={{
                                    flexDirection: "row",
                                }}

                                showArrowIcon={true}
                                ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-down-outline"
                                    />
                                )}
                                ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-up-outline"
                                    />
                                )}

                                showTickIcon={true}
                                TickIconComponent={({ press }) => (
                                    <Ionicon
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        color={
                                            press ? Colors.fieldtBgColor : Colors.primaryColor
                                        }
                                        name={'md-checkbox'}
                                        size={25}
                                    />
                                )}
                                placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                }}
                                dropDownDirection="BOTTOM"
                                placeholder="Choose Outlet"
                                items={targetOutletDropdownListTemp}
                                value={currOutletId}
                                onSelectItem={(item) => {
                                    if (item) { // if choose the same option again, value = ''
                                        MerchantStore.update((s) => {
                                            s.currOutletId = item.value;
                                            s.currOutlet =
                                                allOutlets.find(
                                                    (outlet) => outlet.uniqueId === item.value
                                                ) || {};
                                        });
                                    }

                                    CommonStore.update((s) => {
                                        s.shiftClosedModal = false;
                                    });
                                }}
                                open={openO}
                                setOpen={setOpenO}
                            />
                        ) : (
                            <ActivityIndicator size={"small"} color={Colors.whiteColor} />
                        )}

                        {/* <Select

                  placeholder={"Choose Outlet"}
                  onChange={(items) => {
                    setSelectedOutletList(items);
                  }}
                  options={outletDropdownList}
                  isMulti
                /> */}
                    </View>
                );
            };
        });
    }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

    navigation.setOptions({
        headerLeft: () => (
            <View
                style={[
                    styles.headerLeftStyle,
                    {
                        width: windowWidth * 0.17,
                    },
                ]}
            >
                <img src={headerLogo} width={124} height={26} />
                {/* <Image
            style={{
              width: 124,
              height: 26,
            }}
            resizeMode="contain"
            source={require('../assets/image/logo.png')}
          /> */}
            </View>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        justifyContent: "center",
                        alignItems: "center",
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        //width:  "55%",
                    },
                    Dimensions.get("screen").width <= 768
                        ? { right: Dimensions.get("screen").width * 0.12 }
                        : {},
                ]}
            >
                <Text
                    style={{
                        fontSize: 24,
                        // lineHeight: 25,
                        textAlign: "center",
                        fontFamily: "NunitoSans-Bold",
                        color: Colors.whiteColor,
                        opacity: 1,
                    }}
                >
                    History
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                {/* {console.log('edward test')} */}
                {/* {console.log(outletSelectDropdownView)} */}
                {outletSelectDropdownView && outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: "white",
                        width: 0.5,
                        height: Dimensions.get("screen").height * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                        // borderWidth: 1
                    }}
                ></View>
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate("General Settings - KooDoo Manager")
                        }
                    }}
                    style={{ flexDirection: "row", alignItems: "center" }}
                >
                    <Text
                        style={{
                            fontFamily: "NunitoSans-SemiBold",
                            fontSize: 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            //backgroundColor: 'red',
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: "white",
                        }}
                    >
                        <img
                            src={personicon}
                            width={windowHeight * 0.035}
                            height={windowHeight * 0.035}
                        />
                        {/* <Image
                style={{
                  width: windowHeight * 0.05,
                height: windowHeight * 0.05,
                  alignSelf: 'center',
                }}
                source={require('../assets/image/profile-pic.jpg')}
              /> */}
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    const getOrderHistory = () => {
        ApiClient.GET(API.getOutletOrder + User.getOutletId()).then((result) => {
            setState({ order: result, unfilteredOrder: result });
        });
    };

    // const sortingOrders = (param) => {
    //   if (param.value == 1) { //orderid
    //     setHistoryOrders(historyOrders.slice(0).sort((a, b) => b.orderId.localeCompare(a.orderId)));
    //   }
    //   if (param.value == 2) { //date time
    //     setHistoryOrders(historyOrders.slice(0).sort((a, b) => b.orderDate - a.orderDate));
    //   }
    //   if (param.value == 3) { //Name
    //     setHistoryOrders(historyOrders.slice(0).sort((a, b) => b.userName.localeCompare(a.userName)));
    //   }
    //   if (param.value == 4) { //Waiting Time
    //     setHistoryOrders(historyOrders.slice(0).sort((a, b) => (moment().valueOf() - b.estimatedPreparedDate) - (moment().valueOf() - a.estimatedPreparedDate)));
    //   }
    //   if (param.value == 5) { //Payment Method
    //     setHistoryOrders(historyOrders.slice(0).sort((a, b) => b.orderStatus.localeCompare(a.orderStatus)));
    //   }
    //   if (param.value == 6) { //Total
    //     setHistoryOrders(historyOrders.slice(0).sort((a, b) => b.finalPrice - a.finalPrice));
    //   }
    // }

    const updatePaymentMethod = async () => {
        var body = {
            orderId: refundOrderId,
            paymentMethodChannel: paymentMethodChannel ? paymentMethodChannel : OFFLINE_PAYMENT_METHOD_TYPE.CASH.channel,

            paymentMethodId: outletPaymentMethodsDropdownList.find(option => option.name === paymentMethodChannel) ? outletPaymentMethodsDropdownList.find(option => option.name === paymentMethodChannel).paymentMethodId : '',
        };

        // ApiClient.POST(API.updatePaymentMethod, body)
        APILocal.updatePaymentMethod({ body })
            .then((result) => {
                if (result && result.status === 'success') {
                    Alert.alert(
                        'Success',
                        'Payment method has been updated',
                    );
                } else {
                    Alert.alert(
                        'Error',
                        'Failed to update the payment method',
                    );
                }

                setPaymentMethodModal(false);
            })
            .catch((err) => {
                console.log(err);
            });
    }

    const sorting0 = () => {
        if (sortSender) {
            setHistoryOrders(historyOrders.slice(0).sort((a, b) => b.orderType.localeCompare(a.orderType)),);
        } else {
            setHistoryOrders(historyOrders.slice(0).sort((a, b) => a.orderType.localeCompare(b.orderType)),);
        }
    }

    const sorting = () => {
        if (sortOrderID) {
            ////////Sort Order ID
            setHistoryOrders(
                historyOrders
                    .slice(0)
                    .sort((a, b) => b.orderId.localeCompare(a.orderId)),
            );
            //setHistoryOrders(historyOrders.slice(0).sort((a, b) => b.orderId-a.orderId))
        } else {
            setHistoryOrders(
                historyOrders
                    .slice(0)
                    .sort((a, b) => a.orderId.localeCompare(b.orderId)),
            );
            //setHistoryOrders(historyOrders.slice(0).sort((a, b) => a.orderId-b.orderId))
        }
    };

    const sorting1 = () => {
        if (sortDateTime) {
            ////////
            setHistoryOrders(
                historyOrders.slice(0).sort((a, b) => b.createdAt - a.createdAt),
            );
        } else {
            setHistoryOrders(
                historyOrders.slice(0).sort((a, b) => a.createdAt - b.createdAt),
            );
        }
    };

    const sorting2 = () => {
        if (sortCustomerName) {
            ////////
            setHistoryOrders(
                historyOrders
                    .slice(0)
                    .sort((a, b) => b.userName.localeCompare(a.userName)),
            );
        } else {
            setHistoryOrders(
                historyOrders
                    .slice(0)
                    .sort((a, b) => a.userName.localeCompare(b.userName)),
            );
        }
    };

    const sorting3 = () => {
        if (sortPaymentMethod) {
            ////////
            setHistoryOrders(
                historyOrders
                    .slice(0)
                    .sort((a, b) => b.paymentMethod.localeCompare(a.paymentMethod)),
            );
        } else {
            setHistoryOrders(
                historyOrders
                    .slice(0)
                    .sort((a, b) => a.paymentMethod.localeCompare(b.paymentMethod)),
            );
        }
    };

    const sorting4 = () => {
        if (sortTotalPrice) {
            ////////
            setHistoryOrders(
                historyOrders.slice(0).sort((a, b) => b.finalPrice - a.finalPrice),
            );
        } else {
            setHistoryOrders(
                historyOrders.slice(0).sort((a, b) => a.finalPrice - b.finalPrice),
            );
        }
    };

    const filterOrders = (param) => {
        if (param == 1) {
            // All orders
            setHistoryOrders(
                allOutletsUserOrdersDone.filter(
                    (order) => order.outletId === currOutletId,
                ),
            );
        }

        if (param == 2) {
            // Dine in
            setHistoryOrders(
                allOutletsUserOrdersDone.filter(
                    (order) =>
                        order.orderType === ORDER_TYPE.DINEIN &&
                        order.outletId === currOutletId,
                ),
            );
        }

        if (param == 3) {
            // Delivery/Pick-up
            setHistoryOrders(
                allOutletsUserOrdersDone.filter(
                    (order) =>
                        order.orderType !== ORDER_TYPE.DINEIN &&
                        order.outletId === currOutletId,
                ),
            );
        }
    };

    const expandOrderFunc = (param) => {
        // if (expandOrder == false) {
        //   // return setState({ expandOrder: true }), param.expandOrder = true;
        //   // setExpandOrder(true);
        //   setExpandViewDict({
        //     ...expandViewDict,
        //     [param.uniqueId]: true,
        //   });
        //   expandViewDict;
        // } else {
        //   // return setState({ expandOrder: false }), param.expandOrder = false;
        //   // setExpandOrder(false);
        //   setExpandViewDict({
        //     ...expandViewDict,
        //     [param.uniqueId]: undefined,
        //   });
        // }

        if (!expandViewDict[param.uniqueId]) {
            // return setState({ expandOrder: true }), param.expandOrder = true;
            // setExpandOrder(true);
            setExpandViewDict({
                ...expandViewDict,
                [param.uniqueId]: true,
            });
        } else {
            // return setState({ expandOrder: false }), param.expandOrder = false;
            // setExpandOrder(false);
            setExpandViewDict({
                ...expandViewDict,
                [param.uniqueId]: false,
            });
        }
    };

    // const rightAction = (item, index) => {
    //   return (
    //     <View
    //       style={{
    //         justifyContent: 'center',
    //         alignItems: 'center',
    //         flexDirection: 'row',
    //       }}>
    //       {
    //         (
    //           (currOutlet && currOutlet.privileges &&
    //             currOutlet.privileges.includes(PRIVILEGES_NAME.REFUND_ORDER))
    //           && privileges && privileges.includes(PRIVILEGES_NAME.REFUND_ORDER))
    //           ?
    //           <>
    //             {
    //               !item.isRefundOrder
    //                 ?
    //                 <TouchableOpacity
    //                   onPress={async () => {
    //                     // var body = {
    //                     //   orderId: item.uniqueId,
    //                     // };       

    //                     if (
    //                       (item.paymentDetails &&
    //                         (
    //                           item.paymentDetails.txn_ID ||
    //                           item.paymentDetails.txnId
    //                         )
    //                       )
    //                     ) {
    //                       if (
    //                         typeof item.paymentDetails.txn_ID === 'number' ||
    //                         typeof item.paymentDetails.txnId === 'number'
    //                       ) {
    //                         Alert.alert('Info', `This order is not able to be refunded\n\nPlease send the following code to support team to check:\n\n${item.uniqueId}`);

    //                         return;
    //                       }
    //                     }

    //                     if (item.settlementDate === null) {
    //                       if (
    //                         (
    //                           moment().isBefore(moment().hour(5).minute(0))
    //                           &&
    //                           moment(item.createdAt).isBefore(moment().subtract(1, 'day').endOf('day'))
    //                         )
    //                         ||
    //                         (
    //                           moment(item.createdAt).isSameOrAfter(moment().startOf('day'))
    //                         )
    //                       ) {
    //                         var cartItemListTemp = [];

    //                         for (var i = 0; i < item.cartItems.length; i++) {
    //                           if (selectedCartItemDict[item.cartItems[i].itemId + item.cartItems[i].cartItemDate]) {
    //                             cartItemListTemp.push({
    //                               ...item.cartItems[i],
    //                               actionQuantity: selectedCartItemDict[item.cartItems[i].itemId + item.cartItems[i].cartItemDate].actionQuantity,
    //                             });
    //                           }
    //                         }

    //                         if (cartItemListTemp.length <= 0) {
    //                           Alert.alert('Info', 'Please select at least one item to proceed.');

    //                           return;
    //                         }

    //                         setCartItemList(cartItemListTemp);

    //                         setRefundOrder(item);

    //                         setRefundOrderId(item.uniqueId);

    //                         setRefundNote('');
    //                         setRefundReason(REFUND_REASON_LIST[0].value);

    //                         setRefundModal(true);

    //                         if (item.paymentDetails &&
    //                           item.paymentDetails.channel) {
    //                           if (item.paymentDetails.channel === OFFLINE_PAYMENT_METHOD_TYPE.CASH.channel) {
    //                             await openCashDrawer();
    //                           }
    //                         }
    //                         else {
    //                           // is offline/cash

    //                           // await openCashDrawer();
    //                         }
    //                       }
    //                       else {
    //                         Alert.alert('Info', 'Order(s) that placed one day before, only able to refund until the next day of 5:00 AM.');
    //                       }
    //                     }
    //                     else {
    //                       Alert.alert('Info', 'This order is already settled for the daily payout.');
    //                     }


    //                     /* (netInfo.isInternetReachable && netInfo.isConnected
    //                       ? ApiClient.POST(API.refundUserOrder, body)
    //                       : APILocal.refundUserOrder({
    //                         body: {
    //                           orderId: item.uniqueId,
    //                         },
    //                         uid: firebaseUid,
    //                       })
    //                     )
    //                       .then((result) => {
    //                         if (result && result.status === 'success') {
    //                           Alert.alert('Success', 'Order has been refunded');
    //                         } else {
    //                           Alert.alert('Error', 'Failed to refund order');
    //                         }
    //                       })
    //                       .catch((err) => {
    //                         // console.log(err);

    //                         Alert.alert('Error', 'Failed to refund order');
    //                       }); */
    //                   }}
    //                   style={[
    //                     {
    //                       height: '100%',
    //                       justifyContent: 'center',
    //                       alignItems: 'center',
    //                       backgroundColor: Colors.tabRed,
    //                       underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
    //                       width: 75,
    //                     },
    //                     switchMerchant
    //                       ? {
    //                         width: windowWidth * 0.08,
    //                       }
    //                       : {},
    //                   ]}>
    //                   {switchMerchant ? (
    //                     // <IIcon name="receipt-outline" size={10} color={Colors.whiteColor} />

    //                     <MaterialCommunityIcons
    //                       name="close"
    //                       size={10}
    //                       color={Colors.whiteColor}
    //                     // style={{ marginTop: 10 }}
    //                     />
    //                   ) : (
    //                     // <IIcon name="receipt-outline" size={40} color={Colors.whiteColor} />

    //                     <MaterialCommunityIcons
    //                       name="close"
    //                       size={40}
    //                       color={Colors.whiteColor}
    //                     // style={{ marginTop: 10 }}
    //                     />
    //                   )}

    //                   <Text
    //                     style={[
    //                       {
    //                         color: Colors.whiteColor,
    //                         fontSize: 12,
    //                         fontFamily: 'NunitoSans-Regular',
    //                         textAlign: 'center',
    //                         width: '80%',
    //                       },
    //                       switchMerchant
    //                         ? {
    //                           fontSize: 10,
    //                         }
    //                         : {},
    //                     ]}>
    //                     {'Refund\nOrder'}
    //                   </Text>
    //                 </TouchableOpacity>
    //                 :
    //                 <TouchableOpacity
    //                   onPress={async () => {

    //                     // if (item.paymentDetails &&
    //                     //   (
    //                     //     item.paymentDetails.txn_ID ||
    //                     //     item.paymentDetails.txnId
    //                     //   )) {
    //                     //     Alert.alert('Info', 'Paid first order is only able to submit the refund request once, and is unable to undo refund.');
    //                     // }
    //                     // else {

    //                     // }

    //                     if (item.settlementDate === null) {
    //                       if (
    //                         (
    //                           moment().isBefore(moment().hour(14).minute(0))
    //                           &&
    //                           moment(item.createdAt).isBefore(moment().subtract(1, 'day').endOf('day'))
    //                         )
    //                         ||
    //                         (
    //                           moment(item.createdAt).isSameOrAfter(moment().startOf('day'))
    //                         )
    //                       ) {
    //                         var cartItemListTemp = [];

    //                         for (var i = 0; i < item.cartItems.length; i++) {
    //                           if (selectedCartItemDict[item.cartItems[i].itemId + item.cartItems[i].cartItemDate]) {
    //                             cartItemListTemp.push({
    //                               ...item.cartItems[i],
    //                               actionQuantity: selectedCartItemDict[item.cartItems[i].itemId + item.cartItems[i].cartItemDate].actionQuantity,
    //                             });
    //                           }
    //                         }

    //                         if (cartItemListTemp.length <= 0) {
    //                           Alert.alert('Info', 'Please select at least one item to proceed.');

    //                           return;
    //                         }

    //                         setCartItemList(cartItemListTemp);

    //                         setRefundOrder(item);

    //                         setRefundOrderId(item.uniqueId);

    //                         setRefundNote(item.refundNote || '');

    //                         if (REFUND_REASON_LIST.find(dropdownItem => dropdownItem.value === item.refundReason)) {
    //                           setRefundReason(item.refundReason);
    //                         }
    //                         else {
    //                           setRefundReason(REFUND_REASON_LIST[0].value);
    //                         }

    //                         setUndoRefundModal(true);

    //                         if (item.paymentDetails &&
    //                           item.paymentDetails.channel) {
    //                           if (item.paymentDetails.channel === OFFLINE_PAYMENT_METHOD_TYPE.CASH.channel) {
    //                             await openCashDrawer();
    //                           }
    //                         }
    //                         else {
    //                           // is offline/cash

    //                           // await openCashDrawer();
    //                         }
    //                       }
    //                       else {
    //                         Alert.alert('Info', 'Order(s) that placed one day before, only able to be undo refund until the next day of 14:00 PM.');
    //                       }
    //                     }
    //                     else {
    //                       Alert.alert('Info', 'This order is already settled for the daily payout.');
    //                     }

    //                     // var body = {
    //                     //   orderId: item.uniqueId,
    //                     // };

    //                     // (netInfo.isInternetReachable && netInfo.isConnected
    //                     //   ? ApiClient.POST(API.undoRefundUserOrder, body)
    //                     //   : APILocal.undoRefundUserOrder({
    //                     //     body: {
    //                     //       orderId: item.uniqueId,
    //                     //     },
    //                     //     uid: firebaseUid,
    //                     //   })
    //                     // )
    //                     //   .then((result) => {
    //                     //     if (result && result.status === 'success') {
    //                     //       Alert.alert('Success', 'Order refund has been undone');
    //                     //     } else {
    //                     //       Alert.alert('Error', 'Failed to undo the refund order');
    //                     //     }
    //                     //   })
    //                     //   .catch((err) => {
    //                     //     // console.log(err);

    //                     //     Alert.alert('Error', 'Failed to undo the refund order');
    //                     //   });
    //                   }}
    //                   style={[
    //                     {
    //                       height: '100%',
    //                       justifyContent: 'center',
    //                       alignItems: 'center',
    //                       backgroundColor: Colors.primaryColor,
    //                       underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
    //                       width: 75,
    //                     },
    //                     switchMerchant
    //                       ? {
    //                         width: windowWidth * 0.08,
    //                       }
    //                       : {},
    //                   ]}>
    //                   {switchMerchant ? (
    //                     <EvilIcons name="undo" size={14} color={Colors.whiteColor} />
    //                   ) : (
    //                     <EvilIcons name="undo" size={54} color={Colors.whiteColor} />
    //                   )}

    //                   <Text
    //                     style={[
    //                       {
    //                         color: Colors.whiteColor,
    //                         fontSize: 12,
    //                         fontFamily: 'NunitoSans-Regular',
    //                         textAlign: 'center',
    //                         width: '80%',
    //                       },
    //                       switchMerchant
    //                         ? {
    //                           fontSize: 10,
    //                         }
    //                         : {},
    //                     ]}>
    //                     {'Undo\nRefund'}
    //                   </Text>
    //                 </TouchableOpacity>
    //             }
    //           </>
    //           :
    //           <></>
    //       }

    //       <TouchableOpacity
    //         onPress={async () => {
    //           Alert.alert('Info', 'Receipt has been added to print queue');

    //           // NetPrinter.closeConn(); // no need anymore

    //           await printUserOrder(
    //             {
    //               orderId: item.uniqueId,
    //               receiptNote: currOutlet.receiptNote || '',
    //             },
    //             false,
    //             [PRINTER_USAGE_TYPE.RECEIPT],
    //             // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
    //             false,
    //             false,
    //             false,
    //             netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
    //             // [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET]
    //             // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
    //           );

    //           // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
    //           //   await printUserOrder(
    //           //     {
    //           //       orderData: item,
    //           //     },
    //           //     false,
    //           //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
    //           //     false,
    //           //     false,
    //           //     false,
    //           //     { isInternetReachable: true, isConnected: true },
    //           //   );
    //           // }
    //           // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
    //           //   await printKDSummaryCategoryWrapper(
    //           //     {
    //           //       orderData: item,
    //           //     },
    //           //   );
    //           // }
    //           // else if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
    //           //   for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
    //           //     await printDocketForKD(
    //           //       {
    //           //         userOrder: item,
    //           //         cartItem: item.cartItems[bdIndex],
    //           //       },
    //           //       // true,
    //           //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
    //           //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
    //           //     );
    //           //   }
    //           // }

    //           // await openCashDrawer();

    //           // 2022-10-16 - No need print bd
    //           // if (item && item.cartItems) {
    //           //   for (var i = 0; i < item.cartItems.length; i++) {
    //           //     if (item.cartItems[i].isDocket) {
    //           //       // if (i > 0) {
    //           //       //   await waitForSeconds(1); // no need anymore
    //           //       // }

    //           //       // NetPrinter.closeConn(); // no need anymore

    //           //       await printDocket(
    //           //         item.cartItems[i],
    //           //         // true,
    //           //         [PRINTER_USAGE_TYPE.RECEIPT],
    //           //         // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
    //           //       );
    //           //     }
    //           //   }
    //           // }

    //           // await printUserOrder(
    //           //   {
    //           //     orderId: item.uniqueId,
    //           //   },
    //           //   true,
    //           //   [PRINTER_USAGE_TYPE.RECEIPT],
    //           //   false,
    //           //   true,
    //           //   true,
    //           // );            
    //         }}
    //         style={[
    //           {
    //             height: '100%',
    //             justifyContent: 'center',
    //             alignItems: 'center',
    //             backgroundColor: Colors.secondaryColor,
    //             underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
    //             width: 75,
    //           },
    //           switchMerchant
    //             ? {
    //               width: windowWidth * 0.08,
    //             }
    //             : {},
    //         ]}>
    //         {switchMerchant ? (
    //           <IIcon name="receipt-outline" size={10} color={Colors.whiteColor} />
    //         ) : (
    //           <IIcon name="receipt-outline" size={40} color={Colors.whiteColor} />
    //         )}

    //         <Text
    //           style={[
    //             {
    //               color: Colors.whiteColor,
    //               fontSize: 12,
    //               fontFamily: 'NunitoSans-Regular',
    //               textAlign: 'center',
    //               width: '80%',
    //             },
    //             switchMerchant
    //               ? {
    //                 fontSize: 10,
    //               }
    //               : {},
    //           ]}>
    //           {'Reprint\nReceipt'}
    //         </Text>
    //       </TouchableOpacity>

    //       <TouchableOpacity
    //         onPress={() => {
    //           setRefundOrderId(item.uniqueId);

    //           setPaymentMethodModal(true);
    //         }}
    //         style={{
    //           height: '100%',
    //           justifyContent: 'center',
    //           alignItems: 'center',
    //           backgroundColor: Colors.tabCyan,
    //           underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
    //           width: 75,
    //         }}>
    //         {switchMerchant ? (
    //           <MaterialCommunityIcons
    //             name="credit-card-multiple-outline"
    //             size={10}
    //             color={Colors.whiteColor}
    //             style={{ marginTop: 5 }}
    //           />
    //         ) : (
    //           <MaterialCommunityIcons
    //             name="credit-card-multiple-outline"
    //             size={40}
    //             color={Colors.whiteColor}
    //             style={{ marginTop: 5 }}
    //           />
    //         )}
    //         <Text
    //           style={[
    //             {
    //               color: Colors.whiteColor,
    //               fontSize: 12,
    //               fontFamily: 'NunitoSans-Regular',
    //               textAlign: 'center',
    //               width: '80%',
    //             },
    //             switchMerchant
    //               ? {
    //                 fontSize: 10,
    //               }
    //               : {},
    //           ]}>
    //           {`Payment\nMethod`}
    //         </Text>
    //       </TouchableOpacity>

    //       <TouchableOpacity
    //         onPress={() => {
    //           // setRefundOrderId(item.uniqueId);

    //           if (item.userPhone) {
    //             var foundUser = crmUsers.find(user => user.number === item.userPhone);
    //             if (foundUser && foundUser.email && !foundUser.email.startsWith('user-') && foundUser.email.length < 32) {
    //               setReceiptEmailToSent(foundUser.email);
    //             }
    //             else if (foundUser && foundUser.emailSecond && !foundUser.emailSecond.startsWith('user-') && foundUser.emailSecond.length < 32) {
    //               setReceiptEmailToSent(foundUser.emailSecond);
    //             }
    //             else {
    //               setReceiptEmailToSent('');
    //             }
    //           }

    //           setReceiptEmailOrder(item);
    //           setReceiptEmailModal(true);
    //         }}
    //         style={{
    //           height: '100%',
    //           justifyContent: 'center',
    //           alignItems: 'center',
    //           backgroundColor: Colors.primaryColor,
    //           underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
    //           width: 75,
    //         }}>
    //         {switchMerchant ? (
    //           <MaterialCommunityIcons
    //             name="email-send-outline"
    //             size={10}
    //             color={Colors.whiteColor}
    //             style={{ marginTop: 5 }}
    //           />
    //         ) : (
    //           <MaterialCommunityIcons
    //             name="email-send-outline"
    //             size={40}
    //             color={Colors.whiteColor}
    //             style={{ marginTop: 5 }}
    //           />
    //         )}
    //         <Text
    //           style={[
    //             {
    //               color: Colors.whiteColor,
    //               fontSize: 12,
    //               fontFamily: 'NunitoSans-Regular',
    //               textAlign: 'center',
    //               width: '80%',
    //             },
    //             switchMerchant
    //               ? {
    //                 fontSize: 10,
    //               }
    //               : {},
    //           ]}>
    //           {`Send\nReceipt`}
    //         </Text>
    //       </TouchableOpacity>

    //       {/* <TouchableOpacity
    //         // onPress={() => {
    //         //   cancelQueue(item.uniqueId);
    //         // }}
    //         style={{
    //           height: '100%',
    //           justifyContent: 'center',
    //           alignItems: 'center',
    //           backgroundColor: Colors.primaryColor,
    //           underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
    //           // paddingBottom: 6,
    //           width: 75,
    //         }}>

    //         <MIcon name="email-outline" size={40} color={Colors.whiteColor} />

    //         <Text style={{ color: Colors.whiteColor, fontSize: 12, fontFamily: 'NunitoSans-Regular', textAlign: 'center', width: '80%' }}>{'Email\nReceipt'}</Text>

    //       </TouchableOpacity> */}
    //     </View>
    //   );
    // };

    const renderOrder = ({ item, index }) => {
        const waitingTime =
            // (moment().valueOf() - item.estimatedPreparedDate) / (1000 * 60);
            //(moment().valueOf() - item.orderDate) / (1000 * 60);
            (item.completedDate - item.createdAt) / (1000 * 60);

        //var hrs = Math.floor(moment().diff(item.updatedAt, 'minutes') / 60);
        //var mins = Math.floor(moment().diff(item.updatedAt, 'minutes') % 60);

        var hrs = Math.floor(waitingTime / 60);
        var mins = Math.floor(waitingTime % 60);

        var courierStatusParsed = '';

        if (item.courierCode === COURIER_CODE.LALAMOVE) {
            courierStatusParsed = LALAMOVE_STATUS_PARSED[item.courierStatus];
        } else if (item.courierCode === COURIER_CODE.MRSPEEDY) {
            courierStatusParsed = MRSPEEDY_STATUS_PARSED[item.courierStatus];
        }

        ///////////////////////////

        // // console.log('order id');
        // // console.log(item.orderId, item.tableCode, item);

        // calculate longest

        var longestStr = Platform.OS === 'android' ? 3 : 5;

        // for (var i = 0; i < item.cartItems.length; i++) {
        //   const cartItemPriceWIthoutAddOn =
        //     item.cartItems[i].price -
        //     item.cartItems[i].addOns.reduce(
        //       (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
        //       0,
        //     );

        //   if (cartItemPriceWIthoutAddOn.toFixed(0).length > longestStr) {
        //     longestStr = cartItemPriceWIthoutAddOn.toFixed(0).length;
        //   }

        //   for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
        //     if (
        //       item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length > longestStr
        //     ) {
        //       longestStr = item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length;
        //     }
        //   }
        // }

        // if (item.totalPrice.toFixed(0).length > longestStr) {
        //   longestStr = item.totalPrice.toFixed(0).length;
        // }

        // if (item.discount.toFixed(0).length > longestStr) {
        //   longestStr = item.discount.toFixed(0).length;
        // }

        // if (item.tax.toFixed(0).length > longestStr) {
        //   longestStr = item.tax.toFixed(0).length;
        // }

        // if (item.finalPrice.toFixed(0).length > longestStr) {
        //   longestStr = item.finalPrice.toFixed(0).length;
        // }

        // // console.log(longestStr);

        ///////////////////////////

        // calculate spacing

        var cartItemPriceWIthoutAddOnSpacingList = [];
        var addOnsSpacingList = [];

        // for (var i = 0; i < item.cartItems.length; i++) {
        //   const cartItemPriceWIthoutAddOn =
        //     item.cartItems[i].price -
        //     item.cartItems[i].addOns.reduce(
        //       (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
        //       0,
        //     );

        //   cartItemPriceWIthoutAddOnSpacingList.push(
        //     Math.max(longestStr - cartItemPriceWIthoutAddOn.toFixed(0).length, 0) +
        //       1,
        //   );

        //   for (var j = 0; j < item.cartItems[i].addOns.length; j++) {
        //     addOnsSpacingList.push(
        //       Math.max(
        //         longestStr -
        //           item.cartItems[i].addOns[j].prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0).toFixed(0).length,
        //         0,
        //       ) + 1,
        //     );
        //   }
        // }

        // console.log('check for:');
        // console.log(item.uniqueId);

        // if (isNaN(item.totalPrice) || isNaN(item.discount) || isNaN(item.finalPrice) || isNaN(item.tax)
        //   || isNaN(item.totalPrice) || isNaN(item.finalPriceBefore) || isNaN(item.sc) || isNaN(item.deliveryFee)) {
        //   console.log('problem item here!');
        //   console.log(item);
        //   console.log('hit');

        //   return;
        // }
        // else {
        //   console.log('item ok');
        //   console.log(item.orderId);
        // }

        // var totalPriceSpacing =
        //   Math.max(longestStr - (!isNaN(item.totalPrice) ? item.totalPrice : 0).toFixed(0).length, 0) + 1;
        // var discountSpacing =
        //   Math.max(longestStr - (!isNaN(item.discount) ? item.discount : 0).toFixed(0).length, 0) + 1;
        // var taxSpacing = Math.max(longestStr - item.tax.toFixed(0).length, 0) + 1;
        // var finalPriceSpacing =
        //   Math.max(longestStr - (!isNaN(item.finalPrice) ? item.finalPrice : 0).toFixed(0).length, 0) + 1;

        ///////////////////////////

        return (
            <View style={{ padding: 3 }}>
                {/* <Swipeable
          renderRightActions={() => rightAction(item)}
          onSwipeableWillOpen={() => { }}> */}
                <TouchableOpacity
                    onPress={() => {
                        expandOrderFunc(item);

                        setSelectedCartItemDict({});

                        setCartItemList([]);
                    }}>
                    <View
                        style={{
                            elevation: 2,
                            borderRadius: 7,
                            backgroundColor: 'white',
                            // backgroundColor: 'blue',
                            // padding: Platform.OS == 'ios' ? 0 : 10,
                        }}>
                        <View
                            style={{
                                width: '100%',
                                flexDirection: 'row',
                                height: switchMerchant
                                    ? windowHeight * 0.2
                                    : item.appType === 'WEB_ORDER' ? windowHeight * 0.15
                                        : windowHeight * 0.1,
                                alignItems: 'center',
                                borderBottomColor: Colors.fieldtT,
                                borderBottomWidth:
                                    expandViewDict[item.uniqueId] == true
                                        ? StyleSheet.hairlineWidth
                                        : null,
                            }}>

                            {item.isRefundOrder == true ?
                                <View
                                    style={[{
                                        alignItems: 'flex-start',
                                        position: 'absolute',
                                        left: '1.25%',
                                    }]}>
                                    <FontAwesome name={'star'} size={switchMerchant ? 10 : 25} color={'#1260cc'} style={{}} />
                                </View>
                                :
                                <View
                                    style={[{
                                        alignItems: 'flex-start',
                                        position: 'absolute',
                                        left: '1.25%',
                                    }]} />
                            }

                            {/* <View style={{width: '2%'}}></View> */}
                            {/* Order Icon (Table/Sender/Bag) */}
                            <View
                                style={[
                                    switchMerchant
                                        ? {
                                            width: '9.9%',
                                        }
                                        : {
                                            width: '10.7%',
                                            //marginHorizontal: 0.5,
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            zIndex: 0,
                                            top: 5,
                                            // borderWidth: 1,
                                        },
                                ]}>
                                <View
                                    style={{
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        bottom:
                                            Platform.OS == 'ios'
                                                ? switchMerchant
                                                    ? 0
                                                    : 5
                                                : switchMerchant
                                                    ? 0
                                                    : 5,
                                    }}>
                                    {item.courierId ? (
                                        <>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                }}>
                                                <Image
                                                    style={[
                                                        { width: 60, height: 60 },
                                                        switchMerchant
                                                            ? {
                                                                width:
                                                                    windowWidth * 0.05,
                                                                height:
                                                                    windowHeight * 0.09,
                                                                //backgroundColor: 'red',
                                                            }
                                                            : {},
                                                    ]}
                                                    source={COURIER_INFO_DICT[item.courierCode].img}
                                                />
                                            </View>
                                        </>
                                    ) : item.orderType === ORDER_TYPE.DINEIN ? (
                                        <>
                                            <View
                                                style={[
                                                    {
                                                        width: 60,
                                                        height: 60,
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        // borderWidth: 1
                                                    },
                                                    switchMerchant
                                                        ? {
                                                            width: windowWidth * 0.057,
                                                            height: windowHeight * 0.1,
                                                            top: windowHeight * -0.008,
                                                        }
                                                        : {},
                                                ]}>
                                                <img
                                                    src={dineinicon}
                                                    width={30}
                                                    height={30}
                                                />

                                                {
                                                    item.tableCode
                                                        ?
                                                        <Text
                                                            style={[
                                                                {
                                                                    color: Colors.fontDark,
                                                                    fontSize: 16,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                    top: 2,
                                                                },
                                                                switchMerchant
                                                                    ? {
                                                                        fontSize: 10,
                                                                    }
                                                                    : {},
                                                            ]}
                                                            numberOfLines={1}>
                                                            {item.tableCode}
                                                        </Text>
                                                        :
                                                        <></>
                                                }
                                            </View>
                                        </>
                                    ) : (
                                        <View
                                            style={{
                                                width: 60,
                                                height: 60,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}>
                                            {
                                                item.orderTypeSub !== ORDER_TYPE_SUB.OTHER_DELIVERY
                                                    ?
                                                    <img
                                                        src={takeawayicon}
                                                        width={30}
                                                        height={30}
                                                    />
                                                    :
                                                    <DeliveryDining width={35} height={35} />
                                            }

                                        </View>
                                    )}
                                    {item.appType === 'WEB_ORDER' ?
                                        <View
                                            style={{
                                                width: 40,
                                                height: 40,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                            }}>
                                            <Ionicons name={'qr-code'} size={switchMerchant ? 20 : 25} />
                                        </View>
                                        : <></>}
                                </View>
                            </View>
                            <View
                                style={[
                                    {
                                        width: switchMerchant ? '10.5%' : '9.8%',
                                        paddingLeft: switchMerchant ? '1.3%' : 0,
                                        paddingTop: 0,
                                        //marginHorizontal: 0.5,
                                        alignItems: 'flex-start',
                                        // borderWidth: 1,
                                        //left: Platform.OS == 'ios' ? 10 : 0,
                                    },
                                ]}>
                                <Text
                                    style={[
                                        {
                                            fontSize: 16,
                                            fontFamily: 'NunitoSans-SemiBold',
                                            // textAlign: 'center',
                                            color: Colors.descriptionColor,
                                            //marginTop: -5,
                                            //marginLeft: 8,
                                            width: '100%',
                                            textAlign: 'left',
                                        },
                                        switchMerchant
                                            ? {
                                                fontSize: 10,
                                                //left: Platform.OS === 'ios' ? null : windowWidth * -0.015,
                                                // borderWidth: 1
                                                //left: windowWidth * -0.01,
                                            }
                                            : {
                                                // borderWidth: 1
                                            },
                                    ]}>
                                    {courierStatusParsed ? courierStatusParsed : 'N/A'}
                                </Text>
                            </View>

                            {/* <View style={{ width: '11%', marginHorizontal: 0.5, flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                  <View style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                    {item.courierId
                      ?
                      <>
                        <View style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}>
                          <Image style={{ width: 25, height: 25, borderRadius: 5 }}
                            source={COURIER_INFO_DICT[item.courierCode].img}
                          />
                          <Text style={{ fontSize: 16, fontFamily: 'NunitoSans-Bold', paddingLeft: 5, textAlign: 'center' }}>{COURIER_INFO_DICT[item.courierCode].name}</Text>
                        </View>
                      </>
                      :
                      item.tableCode ?
                        <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>{item.tableCode}</Text>
                        :
                        <Icon name={'shopping-bag'} size={25} color={Colors.fieldtTxtColor} style={{ padding: 4 }} />
                    }
                  </View>
                </View> */}
                            {/* <View style={{ flex: 1, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center', width: '10%' }}> */}
                            <View
                                style={[
                                    switchMerchant
                                        ? {
                                            width: '9.7%',
                                            marginHorizontal: 1,
                                            paddingLeft: '0.5%',
                                        }
                                        : {
                                            alignItems: 'flex-start',
                                            width: '9.7%',
                                            // borderWidth: 1,
                                            marginHorizontal: 1,
                                        },
                                ]}>
                                <Text
                                    style={[
                                        {
                                            color: Colors.fontDark,
                                            fontSize: 15,
                                            fontFamily: 'NunitoSans-Bold',
                                            width: '90%',

                                            // fontSize: 10,
                                        },
                                        switchMerchant
                                            ? {
                                                fontSize: 10,
                                                // left: windowWidth * -0.005,
                                            }
                                            : {},
                                    ]}>
                                    #{item.orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}
                                    {item.orderId}
                                    {/* {`\n${item.uniqueId}`} */}
                                </Text>
                            </View>
                            {/* <View style={{ flex: 1.5, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center', justifyContent: 'center', width: '13%', }}> */}
                            <View
                                style={[
                                    switchMerchant
                                        ? {
                                            width: '13.1%',
                                            paddingLeft: '0.2%',
                                        }
                                        : {
                                            alignItems: 'flex-start',
                                            width: '12.5%',
                                        },
                                ]}>
                                {/* date */}
                                <Text
                                    style={[
                                        {
                                            color: Colors.fontDark,
                                            fontSize: 16,
                                            fontFamily: 'NunitoSans-Bold',
                                        },
                                        switchMerchant
                                            ? {
                                                fontSize: 10,
                                                //left: windowWidth * 0.005,
                                            }
                                            : {},
                                    ]}>
                                    {item.createdAt
                                        ? moment(item.createdAt).format('DD MMM YYYY')
                                        : 'N/A'}
                                </Text>
                                <Text
                                    style={[
                                        {
                                            color: Colors.fontDark,
                                            fontSize: 16,
                                            fontFamily: 'NunitoSans-Bold',
                                            marginTop: 2,
                                        },
                                        switchMerchant
                                            ? {
                                                fontSize: 10,
                                                // left: windowWidth * 0.005,
                                            }
                                            : {},
                                    ]}>
                                    {item.createdAt ? moment(item.createdAt).format('hh:mm A') : 'N/A'}
                                </Text>
                                {/* time */}
                                {/* <Text style={{ color: Colors.fontDark, fontSize: 12, fontFamily: 'NunitoSans-Bold', marginTop: 2 }}>{' '}{item.completedDate ? moment(item.completedDate).format('LT') : null}</Text> */}
                            </View>
                            {/* Staff Name */}
                            {/* <View style={{ flex: 1, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center', width: '14%' }}> */}
                            <View
                                style={{
                                    alignItems: 'flex-start',
                                    width: switchMerchant ? '12.5%' : '12%',
                                    paddingLeft: switchMerchant ? '0.2%' : 0,
                                    // borderWidth: 1,
                                    marginHorizontal: 1,
                                }}>
                                <Text
                                    style={[
                                        {
                                            color: Colors.fontDark,
                                            fontSize: 16,
                                            fontFamily: 'NunitoSans-Bold',
                                        },
                                        switchMerchant
                                            ? {
                                                fontSize: 10,
                                                //left: windowWidth * -0.003,
                                            }
                                            : {},
                                    ]}>
                                    {item.waiterName ? item.waiterName : 'N/A'}
                                </Text>
                            </View>
                            {/* <View style={{ flex: 2, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center' }}>
            <View style={{ paddingVertical: 2, borderRadius: 3, backgroundColor: item.estimateTime < 15 ? Colors.tabGrey : (item.estimateTime >= 15 && item.estimateTime < 20) ? Colors.tabYellow : Colors.tabRed, paddingHorizontal: 20 }}>
              <Text style={{ color: Colors.whiteColor, fontSize: 15, fontFamily: 'NunitoSans-Regular' }}>{Math.trunc(item.estimateTime)}mins</Text>
            </View>
          </View> */}
                            {/* // {item.paymentMethod}
          <View style={{ flex: 2, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center' }}>
            <Text style={{ color: Colors.fontDark, fontSize: 16, fontFamily: 'NunitoSans-Bold' }}>
              {item.paymentDetails === null || !item.paymentDetails.channel ? 'On-site' : item.paymentDetails.channel}
              
            </Text>
          </View> */}
                            {/* {Waiting Time} */}
                            {/* <View style={{ flex: 1.5, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center', width: '15%' }}> */}
                            <View
                                style={[
                                    {
                                        alignItems: 'flex-start',
                                        width: '13%',
                                        marginHorizontal: 1,
                                        // borderWidth: 1
                                        //left: Platform.OS === 'android' ? '10%' : 0,
                                    },
                                    switchMerchant
                                        ? {
                                            width: '13.2%',
                                            marginHorizontal: 1,
                                        }
                                        : {},
                                ]}>
                                {item.preorderPackageId ? (
                                    <View
                                        style={{
                                            width: '100%',
                                            alignItems: 'flex-start',
                                            // borderWidth: 1,
                                        }}>
                                        <View
                                            style={{
                                                alignItems: 'center',
                                                paddingVertical: 2,
                                                borderRadius: 3,
                                                backgroundColor: Colors.secondaryColor,
                                                width: '90%',
                                                marginBottom: 6,
                                            }}>
                                            <Text
                                                style={[
                                                    {
                                                        color: Colors.blackColor,
                                                        fontSize: Platform.OS == 'ios' ? 14 : 16,
                                                        fontFamily: 'NunitoSans-Regular',
                                                    },
                                                    switchMerchant
                                                        ? {
                                                            fontSize: 10,
                                                        }
                                                        : {},
                                                ]}>
                                                {'Preorder'}
                                            </Text>
                                        </View>

                                        <Text
                                            style={[
                                                {
                                                    color: Colors.blackColor,
                                                    fontSize: Platform.OS == 'ios' ? 11 : 13,
                                                    fontFamily: 'NunitoSans-Regular',
                                                },
                                                switchMerchant
                                                    ? {
                                                        fontSize: 10,
                                                    }
                                                    : {},
                                            ]}>{`${moment(item.preorderCollectionDate).format(
                                                'DD/MM/YYYY',
                                            )} ${moment(item.preorderCollectionTime).format(
                                                'hh:mm A',
                                            )}`}</Text>
                                    </View>
                                ) : (
                                    <View
                                        style={[
                                            {
                                                alignItems: 'center',
                                                paddingVertical: 2,
                                                borderRadius: 3,
                                                backgroundColor:
                                                    waitingTime < 16
                                                        ? '#9e9e9e'
                                                        : waitingTime < 21
                                                            ? Colors.secondaryColor
                                                            : '#d90000',
                                                width: '90%',
                                                //right: 20,
                                            },
                                            switchMerchant
                                                ? {
                                                    // left: windowWidth * -0.017,
                                                }
                                                : {},
                                        ]}>
                                        <Text
                                            style={[
                                                {
                                                    color: Colors.whiteColor,
                                                    fontSize: Platform.OS == 'ios' ? 14 : 16,
                                                    fontFamily: 'NunitoSans-Regular',
                                                },
                                                switchMerchant
                                                    ? {
                                                        fontSize: 10,
                                                    }
                                                    : {},
                                            ]}>
                                            {/* {waitingTime > 60
                          ? 'OverTime'
                          : (waitingTime < 0 ? 0 : waitingTime.toFixed(0)) +
                          ' mins'} */}
                                            {/*moment().diff(waitingTime, 'minutes') < 60 ? `${mins} mins` : `${hrs}hrs:${mins}mins`*/}
                                            {waitingTime < 0 ? `Active ` : waitingTime < 60 ? `${mins} mins` : `${hrs}hrs:${mins}mins`}
                                        </Text>
                                    </View>
                                )}
                            </View>
                            {/* {Payment Status*/}
                            {/* <View style={{ flex: 2, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center', right: 30 }}> */}
                            <View
                                style={[
                                    {
                                        alignItems: 'flex-start',
                                        width: switchMerchant ? '16.3%' : '16.5%',
                                        marginHorizontal: 1,
                                        //backgroundColor: 'red',
                                        // borderWidth: 1
                                        //marginHorizontal: 0.5,
                                        //right: Platform.OS == 'ios' ? 10 : 0,
                                    },
                                ]}>
                                {item.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ? (
                                    <View
                                        style={{
                                            alignItems: 'center',
                                            paddingVertical: 2,
                                            borderRadius: 3,
                                            backgroundColor: Colors.secondaryColor,
                                            width: '70%',
                                        }}>
                                        <Text
                                            style={[
                                                {
                                                    color: Colors.whiteColor,
                                                    fontSize: Platform.OS == 'ios' ? 14 : 16,
                                                    fontFamily: 'NunitoSans-Regular',
                                                },
                                                switchMerchant
                                                    ? {
                                                        fontSize: 10,
                                                    }
                                                    : {},
                                            ]}>
                                            {item.paymentDetails && item.paymentDetails.channel
                                                ?

                                                PAYMENT_CHANNEL_NAME_PARSED[
                                                    item.paymentDetails.channel
                                                ]
                                                    ?
                                                    PAYMENT_CHANNEL_NAME_PARSED[
                                                    item.paymentDetails.channel
                                                    ]
                                                    : item.paymentDetails.channel
                                                :
                                                'N/A'
                                            }
                                        </Text>
                                    </View>
                                ) : (
                                    <View
                                        style={{
                                            alignItems: 'center',
                                            paddingVertical: 2,
                                            borderRadius: 3,
                                            backgroundColor: Colors.primaryColor,
                                            width: '70%',
                                        }}>
                                        <Text
                                            style={[
                                                {
                                                    color: Colors.whiteColor,
                                                    fontSize: Platform.OS == 'ios' ? 14 : 16,
                                                    fontFamily: 'NunitoSans-Regular',
                                                },
                                                switchMerchant
                                                    ? {
                                                        fontSize: 10,
                                                    }
                                                    : {},
                                            ]}>
                                            {item.paymentDetails && item.paymentDetails.channel
                                                ? PAYMENT_CHANNEL_NAME_PARSED[
                                                    item.paymentDetails.channel
                                                ]
                                                    ? PAYMENT_CHANNEL_NAME_PARSED[
                                                    item.paymentDetails.channel
                                                    ]
                                                    : item.paymentDetails.channel
                                                : 'Offline'}
                                        </Text>
                                    </View>
                                )}
                            </View>
                            {/* <View style={{ flex: 1, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center', right: 20 }}> */}
                            <View
                                style={[
                                    {
                                        marginHorizontal: switchMerchant ? 1 : 1,
                                        width: switchMerchant ? '9.95%' : '13%',
                                        alignItems: 'flex-start',
                                        justifyContent: 'space-between',
                                        flexDirection: 'row',
                                        // borderWidth: 1,
                                    },
                                ]}>
                                <Text
                                    style={{
                                        fontSize: switchMerchant ? 10 : 16,
                                        color: 'black',
                                    }}>
                                    RM
                                </Text>
                                <Text
                                    style={[
                                        Platform.OS === 'android' ? { position: 'relative' } : {},
                                        switchMerchant
                                            ? {
                                                fontSize: 10,
                                            }
                                            : { paddingRight: 20, fontSize: 16 },
                                    ]}>
                                    {(Math.round((!isNaN(item.finalPrice) ? item.finalPrice : 0) * 20) / 20)
                                        .toFixed(2)
                                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                                </Text>
                            </View>
                        </View>
                        <View
                            style={{
                                position: 'absolute',
                                top: Platform.OS == 'ios' ? 0 : 3,
                                right: Platform.OS == 'ios' ? 3 : 10,
                                alignItems: 'center',
                            }}>
                            {/* {switchMerchant ? (
                  <Icon name="chevron-left" size={10} color={Colors.tabGrey} />
                ) : (
                  <Icon name="chevron-left" size={30} color={Colors.tabGrey} />
                )} */}
                        </View>

                        {expandViewDict[item.uniqueId] == true ? (
                            <TouchableOpacity
                                style={{
                                    minheight: windowHeight * 0.35,
                                    marginTop: 30,
                                    paddingBottom: 20,
                                }}>

                                {/* Show cancelled items below */}
                                {item.cartItemsCancelled && item.cartItemsCancelled.length > 0 ? (
                                    item.cartItemsCancelled.map((cartItemsCancelled, index_inner) => {
                                        const cartItemCancelledPriceWithoutAddOn =
                                            cartItemsCancelled.price -
                                            cartItemsCancelled.addOns.reduce(
                                                (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
                                                0,
                                            );

                                        return (
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '100%',
                                                }}>
                                                <View
                                                    style={{
                                                        marginHorizontal: 1,
                                                        width: Platform.OS == 'ios' ? '8.7%' : '8.7%',
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                    }}
                                                />

                                                <View
                                                    style={{
                                                        width: '5%',
                                                        alignItems: 'center',
                                                    }}>
                                                    <Text
                                                        style={[
                                                            {
                                                                fontFamily: 'NunitoSans-Bold',
                                                                fontSize: 16,
                                                            },
                                                            switchMerchant
                                                                ? {
                                                                    fontSize: 10,
                                                                }
                                                                : {},
                                                        ]}>
                                                        {index_inner + 1}.
                                                    </Text>
                                                </View>

                                                <View
                                                    style={{
                                                        width: '10%',
                                                        alignItems: 'center',
                                                    }}>
                                                    {cartItemsCancelled.image ? (
                                                        <AsyncImage
                                                            source={{ uri: cartItemsCancelled.image }}
                                                            style={{
                                                                width: switchMerchant ? 30 : 60,
                                                                height: switchMerchant ? 30 : 60,
                                                                borderWidth: 1,
                                                                borderColor: '#E5E5E5',
                                                                borderRadius: 5,
                                                            }}
                                                        />
                                                    ) : (
                                                        <View
                                                            style={{
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                width: switchMerchant ? 30 : 60,
                                                                height: switchMerchant ? 30 : 60,
                                                                borderWidth: 1,
                                                                borderColor: '#E5E5E5',
                                                                borderRadius: 5,
                                                            }}>
                                                            <Ionicons
                                                                name="fast-food-outline"
                                                                size={switchMerchant ? 15 : 35}
                                                            />
                                                        </View>
                                                    )}
                                                </View>

                                                <View
                                                    style={{
                                                        width: '73.5%',
                                                        marginLeft: 8,
                                                    }}>
                                                    <View
                                                        style={{
                                                            marginBottom: 10,
                                                            width: '100%',
                                                            flexDirection: 'row',
                                                        }}>
                                                        <View style={{ width: '69.1%' }}>
                                                            <Text
                                                                style={[
                                                                    {
                                                                        fontFamily: 'NunitoSans-Bold',
                                                                        fontSize: 16,
                                                                        textDecorationLine: 'line-through'
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            fontSize: 10,
                                                                        }
                                                                        : {},
                                                                ]}>
                                                                {cartItemsCancelled.name}{cartItemsCancelled.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[cartItemsCancelled.unitType]})` : ''}
                                                            </Text>
                                                        </View>

                                                        <View
                                                            style={{
                                                                // backgroundColor: 'purple',
                                                                // flex: 1.5,
                                                                width: switchMerchant ? '14.2%' : '13%',
                                                                // borderWidth: 1,
                                                                //flexDirection: 'row',
                                                            }}>
                                                            <View
                                                                style={{
                                                                    // flex: 0.5,
                                                                    // width: '36%',
                                                                    // justifyContent: 'center',
                                                                    alignItems: 'center',
                                                                    // borderWidth: 1,
                                                                    // backgroundColor: 'yellow',
                                                                }}>
                                                                {/* <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>x{orderItems.quantity}</Text> */}
                                                                <Text
                                                                    style={[
                                                                        {
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                            fontSize: 16,
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    x{cartItemsCancelled.quantity}
                                                                </Text>
                                                            </View>
                                                        </View>
                                                        <View
                                                            style={{
                                                                flexDirection: 'row',
                                                                justifyContent: 'space-between',
                                                                width: switchMerchant ? '13.5%' : '17.65%',
                                                                alignItems: 'center',
                                                                marginHorizontal: 1,
                                                                // borderWidth: 1,
                                                            }}>
                                                            <Text
                                                                style={[
                                                                    {
                                                                        fontFamily: 'NunitoSans-Bold',
                                                                        fontSize: 16,
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            fontSize: 10,
                                                                        }
                                                                        : {},
                                                                ]}>
                                                                RM
                                                            </Text>
                                                            <Text
                                                                style={
                                                                    switchMerchant
                                                                        ? {
                                                                            fontSize: 10,
                                                                        }
                                                                        : { fontSize: 16, paddingRight: 20 }
                                                                }>
                                                                {cartItemCancelledPriceWithoutAddOn
                                                                    .toFixed(2)
                                                                    .replace(
                                                                        /(\d)(?=(\d{3})+(?!\d))/g,
                                                                        '$1,',
                                                                    )}
                                                            </Text>
                                                        </View>
                                                    </View>

                                                    {cartItemsCancelled.remarks &&
                                                        cartItemsCancelled.remarks.length > 0 ? (
                                                        <View
                                                            style={{
                                                                alignItems: 'center',
                                                                flexDirection: 'row',
                                                            }}>
                                                            <View style={{ justifyContent: 'center' }}>
                                                                <Text
                                                                    style={[
                                                                        {
                                                                            fontFamily: 'NunitoSans-SemiBold',
                                                                            fontSize: 16,
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {cartItemsCancelled.remarks}
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    ) : (
                                                        <></>
                                                    )}

                                                    {cartItemsCancelled.addOns.map((addOnChoice, i) => {
                                                        return (
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    // marginLeft: -5,
                                                                    width: '100%',
                                                                    alignItems: 'center',
                                                                }}>

                                                                <View
                                                                    style={{
                                                                        width: '69.1%',
                                                                        flexDirection: 'row',
                                                                    }}>
                                                                    <Text
                                                                        style={[
                                                                            {
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                                fontSize: 16,
                                                                                color: Colors.fieldtTxtColor,
                                                                                width: '25%',
                                                                                // marginLeft: 5,
                                                                            },
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : {},
                                                                        ]}>
                                                                        {`${addOnChoice.name}:`}
                                                                    </Text>
                                                                    <Text
                                                                        style={[
                                                                            {
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                                fontSize: 16,
                                                                                color: Colors.fieldtTxtColor,
                                                                                width: '75%',
                                                                                // marginLeft: 5,
                                                                            },
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : {},
                                                                        ]}>
                                                                        {`${addOnChoice.choiceNames[0]}`}
                                                                    </Text>
                                                                </View>

                                                                <View
                                                                    style={{
                                                                        width: switchMerchant ? '14.2%' : '13%',
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'center',
                                                                        // backgroundColor: 'blue',
                                                                    }}>
                                                                    <Text
                                                                        style={[
                                                                            {
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                                fontSize: 16,
                                                                                color: Colors.fieldtTxtColor,
                                                                                width: '28%',
                                                                                textAlign: 'center',
                                                                            },
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                    textAlign: 'center',
                                                                                }
                                                                                : {},
                                                                            !switchMerchant &&
                                                                                Platform.OS === 'android'
                                                                                ? {}
                                                                                : {},
                                                                        ]}>
                                                                        {`${addOnChoice.quantities
                                                                            ? `x${addOnChoice.quantities[0]}`
                                                                            : ''
                                                                            }`}
                                                                    </Text>
                                                                </View>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        width: switchMerchant
                                                                            ? '13.5%'
                                                                            : '17.65%',
                                                                        alignItems: 'center',
                                                                        marginHorizontal: 1,
                                                                        // borderWidth: 1,
                                                                    }}>
                                                                    <Text
                                                                        style={[
                                                                            switchMerchant
                                                                                ? { fontSize: 10 }
                                                                                : {
                                                                                    color: Colors.descriptionColor,
                                                                                    fontSize: 16,
                                                                                },
                                                                        ]}>
                                                                        RM
                                                                    </Text>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? { fontSize: 10 }
                                                                                : {
                                                                                    color: Colors.descriptionColor,
                                                                                    paddingRight: 20,
                                                                                    fontSize: 16,
                                                                                }
                                                                        }>
                                                                        {addOnChoice.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0)
                                                                            .toFixed(2)
                                                                            .replace(
                                                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                '$1,',
                                                                            )}
                                                                    </Text>
                                                                </View>
                                                            </View>
                                                        );
                                                    })}
                                                </View>
                                            </View>
                                        )
                                    })
                                ) : (
                                    <></>
                                )}

                                {/* Show cart item */}

                                {item.cartItems.map((cartItem, index) => {
                                    const cartItemPriceWIthoutAddOn =
                                        cartItem.price
                                        +
                                        (
                                            (cartItem.discount ? cartItem.discount : 0)
                                        )
                                        -
                                        cartItem.addOns.reduce(
                                            (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
                                            0,
                                        );

                                    return (
                                        <View
                                            style={{
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                //paddingHorizontal: 10,
                                                //backgroundColor: 'blue'
                                            }}>
                                            <View
                                                style={{
                                                    width: '100%',
                                                    alignItems: 'flex-start',
                                                    flexDirection: 'row',
                                                    marginBottom: Platform.OS == 'ios' ? 10 : 10,
                                                    minHeight: 80,
                                                }}>
                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        width: '100%',
                                                    }}>
                                                    {index == 0 ? (
                                                        <View
                                                            style={{
                                                                // flex: 0.75,
                                                                marginHorizontal: 1,
                                                                width: Platform.OS == 'ios' ? '8.7%' : '8.7%',
                                                                //justifyContent: 'center',
                                                                alignItems: 'center',
                                                                //marginLeft: 5,
                                                                // right: Platform.OS == 'ios' ? 10 : 20,

                                                                // backgroundColor: 'blue',
                                                            }}>
                                                            <TouchableOpacity
                                                                style={{
                                                                    alignItems: 'center',
                                                                    marginTop: '-10%',
                                                                }}
                                                                onPress={() => {
                                                                    var crmUser = null;
                                                                    // console.log(item);

                                                                    if (item.crmUserId !== undefined) {
                                                                        for (
                                                                            var i = 0;
                                                                            i < crmUsers.length;
                                                                            i++
                                                                        ) {
                                                                            if (
                                                                                item.crmUserId ===
                                                                                crmUsers[i].uniqueId
                                                                            ) {
                                                                                crmUser = crmUsers[i];
                                                                                break;
                                                                            }
                                                                        }
                                                                    }

                                                                    // // console.log(crmUser);

                                                                    if (crmUser === null) {
                                                                        for (
                                                                            var i = 0;
                                                                            i < crmUsers.length;
                                                                            i++
                                                                        ) {
                                                                            if (
                                                                                item.userId ===
                                                                                crmUsers[i].firebaseUid
                                                                            ) {
                                                                                crmUser = crmUsers[i];
                                                                                break;
                                                                            }
                                                                        }
                                                                    }

                                                                    if (crmUser) {
                                                                        CommonStore.update(
                                                                            (s) => {
                                                                                s.selectedCustomerEdit = crmUser;
                                                                                // s.selectedCustomerEdit = userReservations[item.userId] && crmUsers[item.userId] ? crmUsers[item.userId] : null ;

                                                                                s.routeParams = {
                                                                                    pageFrom: 'Reservation',
                                                                                };
                                                                            },
                                                                            () => {
                                                                                navigation.navigate('NewCustomer');
                                                                            },
                                                                        );
                                                                    }
                                                                }}
                                                            // onPress={() => {
                                                            //   CommonStore.update(s => {
                                                            //     s.selectedCustomerEdit = item;

                                                            //     s.timestamp = Date.now();
                                                            //   }, () => {
                                                            //     navigation.navigate('NewCustomer');
                                                            //   });
                                                            // }}
                                                            >
                                                                <img
                                                                    src={guesticon}
                                                                    width={60}
                                                                    height={60}
                                                                />

                                                                <View
                                                                    style={{
                                                                        alignItems: 'center',
                                                                        justifyContent: 'center',
                                                                    }}>
                                                                    <Text
                                                                        style={[
                                                                            {
                                                                                fontFamily: 'NunitoSans-Bold',
                                                                                marginTop: 0,
                                                                                fontSize: 16,
                                                                                textAlign: 'center',
                                                                            },
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : {},
                                                                        ]}
                                                                        numberOfLines={1}>
                                                                        {item.userName ? item.userName : 'Guest'}
                                                                    </Text>
                                                                </View>
                                                            </TouchableOpacity>
                                                        </View>
                                                    ) : (
                                                        <View
                                                            style={{
                                                                // flex: 0.75,
                                                                marginHorizontal: 1,
                                                                width: Platform.OS == 'ios' ? '8.7%' : '8.7%',
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                //marginLeft: 5,
                                                                // right: Platform.OS == 'ios' ? 10 : 20,
                                                            }}
                                                        />
                                                    )}

                                                    <View
                                                        style={{
                                                            // flex: 0.3,
                                                            width: '5%',
                                                            //justifyContent: 'center',
                                                            alignItems: 'center',
                                                            // backgroundColor: 'red',
                                                            //paddingLeft: '1.2%',
                                                            //marginLeft: 20,
                                                        }}>
                                                        <Text
                                                            style={[
                                                                {
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                    fontSize: 16,
                                                                },
                                                                switchMerchant
                                                                    ? {
                                                                        fontSize: 10,
                                                                    }
                                                                    : {},
                                                            ]}>
                                                            {index + 1}.
                                                        </Text>
                                                    </View>

                                                    <View
                                                        style={{
                                                            width: '5%',
                                                            // height: '100%',
                                                            //marginRight: 5,
                                                            paddingLeft: 8,
                                                            bottom: 4,
                                                        }}>
                                                        {/* checkbox need fix */}
                                                        <input
                                                            onChange={(value) => {
                                                                if (
                                                                    selectedCartItemDict[
                                                                    cartItem.itemId +
                                                                    cartItem.cartItemDate.toString()
                                                                    ]
                                                                ) {
                                                                    setSelectedCartItemDict({
                                                                        ...selectedCartItemDict,
                                                                        [cartItem.itemId + cartItem.cartItemDate.toString()]: false,
                                                                    });
                                                                } else {
                                                                    setSelectedCartItemDict({
                                                                        ...selectedCartItemDict,
                                                                        [cartItem.itemId + cartItem.cartItemDate.toString()]: {
                                                                            itemId: cartItem.itemId,
                                                                            cartItemDate: cartItem.cartItemDate,

                                                                            actionQuantity: cartItem.quantity,
                                                                        },
                                                                    });
                                                                }
                                                            }}
                                                            style={{
                                                                alignSelf: 'center',
                                                                borderRadius: 15,
                                                                paddingBottom: 3
                                                            }}
                                                            type={'checkbox'}
                                                            checked={
                                                                selectedCartItemDict[
                                                                cartItem.itemId +
                                                                cartItem.cartItemDate.toString()
                                                                ] !== false &&
                                                                selectedCartItemDict[
                                                                cartItem.itemId +
                                                                cartItem.cartItemDate.toString()
                                                                ] !== undefined
                                                            }
                                                        />

                                                        {/* <CheckBox
                                                            disabled={
                                                                (
                                                                    item.isRefundOrder
                                                                        ?
                                                                        (cartItem.isRefundItem ? false : true)
                                                                        :
                                                                        (cartItem.isRefundOrder ? true : false)
                                                                )
                                                            }
                                                            style={{
                                                                ...(Platform.OS === 'ios' && {
                                                                    width: 16,
                                                                    height: 16,
                                                                }),
                                                            }}
                                                            value={
                                                                selectedCartItemDict[
                                                                cartItem.itemId +
                                                                cartItem.cartItemDate.toString()
                                                                ] !== false &&
                                                                selectedCartItemDict[
                                                                cartItem.itemId +
                                                                cartItem.cartItemDate.toString()
                                                                ] !== undefined
                                                            }
                                                            onValueChange={(value) => {
                                                                if (
                                                                    selectedCartItemDict[
                                                                    cartItem.itemId +
                                                                    cartItem.cartItemDate.toString()
                                                                    ]
                                                                ) {
                                                                    setSelectedCartItemDict({
                                                                        ...selectedCartItemDict,
                                                                        [cartItem.itemId + cartItem.cartItemDate.toString()]: false,
                                                                    });
                                                                } else {
                                                                    setSelectedCartItemDict({
                                                                        ...selectedCartItemDict,
                                                                        [cartItem.itemId + cartItem.cartItemDate.toString()]: {
                                                                            itemId: cartItem.itemId,
                                                                            cartItemDate: cartItem.cartItemDate,

                                                                            actionQuantity: cartItem.quantity,
                                                                        },
                                                                    });
                                                                }
                                                            }}
                                                        /> */}
                                                    </View>

                                                    <View
                                                        style={{
                                                            //flex: 0.5,
                                                            width: '10%',
                                                            alignItems: 'center',
                                                        }}>
                                                        {cartItem.image ? (
                                                            <AsyncImage
                                                                source={{ uri: cartItem.image }}
                                                                // item={cartItem}
                                                                style={{
                                                                    width: switchMerchant ? 30 : 60,
                                                                    height: switchMerchant ? 30 : 60,
                                                                    borderWidth: 1,
                                                                    borderColor: '#E5E5E5',
                                                                    borderRadius: 5,
                                                                }}
                                                            />
                                                        ) : (
                                                            <View
                                                                style={{
                                                                    justifyContent: 'center',
                                                                    alignItems: 'center',
                                                                    width: switchMerchant ? 30 : 60,
                                                                    height: switchMerchant ? 30 : 60,
                                                                    borderWidth: 1,
                                                                    borderColor: '#E5E5E5',
                                                                    borderRadius: 5,
                                                                }}>
                                                                <Ionicons
                                                                    name="fast-food-outline"
                                                                    size={switchMerchant ? 15 : 35}
                                                                />
                                                            </View>
                                                        )}
                                                    </View>

                                                    <View
                                                        style={{
                                                            width: '68.5%',
                                                            marginLeft: 8,
                                                            //backgroundColor: 'red',
                                                        }}>
                                                        <View
                                                            style={{
                                                                //marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                                                marginBottom: 10,
                                                                //backgroundColor: 'blue',
                                                                width: '100%',
                                                                flexDirection: 'row',
                                                            }}>
                                                            <View style={{ width: '69.1%' }}>
                                                                <Text
                                                                    style={[
                                                                        {
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                            fontSize: 16,
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {cartItem.name}{cartItem.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[cartItem.unitType]})` : ''}
                                                                </Text>
                                                            </View>

                                                            <View
                                                                style={[{
                                                                    // backgroundColor: 'purple',
                                                                    // flex: 1.5,
                                                                    width: switchMerchant ? '14.2%' : '13%',
                                                                    // borderWidth: 1,
                                                                    //flexDirection: 'row',
                                                                }, switchMerchant ? {
                                                                    // paddingRight: 10,
                                                                    right: 10,

                                                                    // backgroundColor: 'red',
                                                                } : {}]}>
                                                                {
                                                                    selectedCartItemDict[cartItem.itemId + cartItem.cartItemDate]
                                                                        ?
                                                                        <View
                                                                            style={[{
                                                                                // width: '20%',
                                                                                flexDirection: 'row',
                                                                                // marginTop: Platform.OS === 'ios' ? '0%' : '1%',
                                                                            }, switchMerchant ? {
                                                                                // paddingRight: 10,
                                                                            } : {}]}>
                                                                            <TouchableOpacity
                                                                                onPress={() => {
                                                                                    // if (quantity - 1 > 0) {
                                                                                    //   lessqty(menuItem.id);
                                                                                    // }

                                                                                    // setQuantity(quantity - 1 >= 0 ? quantity - 1 : 0);

                                                                                    var quantityTemp = cartItem.quantity;

                                                                                    if (selectedCartItemDict[cartItem.itemId + cartItem.cartItemDate]) {
                                                                                        quantityTemp = selectedCartItemDict[cartItem.itemId + cartItem.cartItemDate].actionQuantity;
                                                                                    }

                                                                                    quantityTemp =
                                                                                        quantityTemp - 1 >= 1 ? quantityTemp - 1 : 1;

                                                                                    setSelectedCartItemDict({
                                                                                        ...selectedCartItemDict,
                                                                                        [cartItem.itemId + cartItem.cartItemDate.toString()]: {
                                                                                            itemId: cartItem.itemId,
                                                                                            cartItemDate: cartItem.cartItemDate,

                                                                                            actionQuantity: quantityTemp,
                                                                                        },
                                                                                    });
                                                                                }}>
                                                                                <View
                                                                                    style={[
                                                                                        styles.addBtn,
                                                                                        {
                                                                                            backgroundColor: Colors.descriptionColor,

                                                                                            width: 20,
                                                                                            height: 22,
                                                                                        },
                                                                                    ]}>
                                                                                    <Text
                                                                                        style={[
                                                                                            {
                                                                                                fontSize: 15,
                                                                                                fontWeight: '500',
                                                                                                color: Colors.whiteColor,
                                                                                                bottom: Platform.OS === 'ios' ? 0 : 2,
                                                                                            },
                                                                                            switchMerchant
                                                                                                ? {
                                                                                                    fontSize: 10,

                                                                                                    bottom: Platform.OS === 'ios' ? 0 : 1,
                                                                                                }
                                                                                                : {},
                                                                                        ]}>
                                                                                        -
                                                                                    </Text>
                                                                                </View>
                                                                            </TouchableOpacity>

                                                                            <View
                                                                                style={[
                                                                                    styles.addBtn,
                                                                                    {
                                                                                        backgroundColor: Colors.whiteColor,
                                                                                        borderWidth: StyleSheet.hairlineWidth,
                                                                                        borderColor: Colors.descriptionColor,
                                                                                        borderWidth: 1.5,

                                                                                        width: 25,
                                                                                        height: 22,
                                                                                    },
                                                                                ]}>
                                                                                <Text
                                                                                    style={[
                                                                                        {
                                                                                            fontSize: 13,
                                                                                            // fontWeight: "bold",
                                                                                            fontFamily: 'NunitoSans-Bold',
                                                                                            // color: Colors.primaryColor,
                                                                                            color: Colors.descriptionColor,
                                                                                            //top: -1,
                                                                                            bottom: Platform.OS === 'ios' ? 0 : 2,
                                                                                        },
                                                                                        switchMerchant
                                                                                            ? {
                                                                                                fontSize: 10,

                                                                                                bottom: Platform.OS === 'ios' ? 0 : 1,
                                                                                            }
                                                                                            : {},
                                                                                    ]}>
                                                                                    {/* {quantityFunc()} */}
                                                                                    {selectedCartItemDict[cartItem.itemId + cartItem.cartItemDate]
                                                                                        ?
                                                                                        selectedCartItemDict[cartItem.itemId + cartItem.cartItemDate].actionQuantity
                                                                                        :
                                                                                        cartItem.quantity}
                                                                                </Text>
                                                                            </View>

                                                                            <TouchableOpacity
                                                                                onPress={() => {
                                                                                    // addqty(menuItem.id);
                                                                                    // setQuantity(quantity + 1);

                                                                                    var quantityTemp = cartItem.quantity;

                                                                                    if (selectedCartItemDict[cartItem.itemId + cartItem.cartItemDate]) {
                                                                                        quantityTemp = selectedCartItemDict[cartItem.itemId + cartItem.cartItemDate].actionQuantity;
                                                                                    }

                                                                                    quantityTemp =
                                                                                        quantityTemp + 1 <= cartItem.quantity ? quantityTemp + 1 : cartItem.quantity;

                                                                                    setSelectedCartItemDict({
                                                                                        ...selectedCartItemDict,
                                                                                        [cartItem.itemId + cartItem.cartItemDate.toString()]: {
                                                                                            itemId: cartItem.itemId,
                                                                                            cartItemDate: cartItem.cartItemDate,

                                                                                            actionQuantity: quantityTemp,
                                                                                        },
                                                                                    });
                                                                                }}>
                                                                                <View
                                                                                    style={[
                                                                                        styles.addBtn,
                                                                                        {
                                                                                            backgroundColor: Colors.primaryColor,
                                                                                            left: -1,

                                                                                            width: 20,
                                                                                            height: 22,
                                                                                        },
                                                                                    ]}>
                                                                                    <Text
                                                                                        style={[
                                                                                            {
                                                                                                fontSize: 15,
                                                                                                fontWeight: '500',
                                                                                                color: Colors.whiteColor,
                                                                                                bottom: Platform.OS === 'ios' ? 0 : 2,
                                                                                            },
                                                                                            switchMerchant
                                                                                                ? {
                                                                                                    fontSize: 10,

                                                                                                    bottom: Platform.OS === 'ios' ? 0 : 1,
                                                                                                }
                                                                                                : {},
                                                                                        ]}>
                                                                                        +
                                                                                    </Text>
                                                                                </View>
                                                                            </TouchableOpacity>
                                                                        </View>
                                                                        :
                                                                        <View
                                                                            style={{
                                                                                // flex: 0.5,
                                                                                // width: '36%',
                                                                                // justifyContent: 'center',
                                                                                alignItems: 'center',
                                                                                // borderWidth: 1,
                                                                                // backgroundColor: 'yellow',
                                                                            }}>
                                                                            {/* <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>x{orderItems.quantity}</Text> */}
                                                                            <Text
                                                                                style={[
                                                                                    {
                                                                                        fontFamily: 'NunitoSans-Bold',
                                                                                        fontSize: 16,
                                                                                    },
                                                                                    switchMerchant
                                                                                        ? {
                                                                                            fontSize: 10,
                                                                                        }
                                                                                        : {},
                                                                                ]}>
                                                                                x{cartItem.quantity}
                                                                            </Text>
                                                                        </View>
                                                                }
                                                            </View>
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    justifyContent: 'space-between',
                                                                    width: switchMerchant ? '13.5%' : '17.65%',
                                                                    alignItems: 'center',
                                                                    marginHorizontal: 1,
                                                                    // borderWidth: 1,
                                                                }}>
                                                                <Text
                                                                    style={[
                                                                        {
                                                                            fontFamily: 'NunitoSans-Bold',
                                                                            fontSize: 16,
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    RM
                                                                </Text>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                            }
                                                                            : { fontSize: 16, paddingRight: 20 }
                                                                    }>
                                                                    {cartItemPriceWIthoutAddOn
                                                                        .toFixed(2)
                                                                        .replace(
                                                                            /(\d)(?=(\d{3})+(?!\d))/g,
                                                                            '$1,',
                                                                        )}
                                                                </Text>
                                                            </View>
                                                        </View>

                                                        {cartItem.remarks &&
                                                            cartItem.remarks.length > 0 ? (
                                                            <View
                                                                style={{
                                                                    alignItems: 'center',
                                                                    flexDirection: 'row',
                                                                    //marginLeft: Platform.OS == 'ios' ? 14 : 14,
                                                                }}>
                                                                <View style={{ justifyContent: 'center' }}>
                                                                    <Text
                                                                        style={[
                                                                            {
                                                                                fontFamily: 'NunitoSans-SemiBold',
                                                                                fontSize: 16,
                                                                            },
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : {},
                                                                        ]}>
                                                                        {cartItem.remarks}
                                                                    </Text>
                                                                </View>
                                                            </View>
                                                        ) : (
                                                            <></>
                                                        )}

                                                        {cartItem.addOns.map((addOnChoice, i) => {
                                                            return (
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        // marginLeft: -5,
                                                                        width: '100%',
                                                                        alignItems: 'center',
                                                                    }}>
                                                                    {/* <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.fieldtTxtColor }}>Add Ons: RM {addOnChoice.prices.reduce((accum, price) => accum + price, 0)}</Text> */}

                                                                    <View
                                                                        style={{
                                                                            width: '69.1%',
                                                                            flexDirection: 'row',
                                                                            // marginLeft:
                                                                            //   Platform.OS == 'ios' ? 14 : 14,
                                                                        }}>
                                                                        <Text
                                                                            style={[
                                                                                {
                                                                                    fontFamily: 'NunitoSans-Bold',
                                                                                    fontSize: 16,
                                                                                    color: Colors.fieldtTxtColor,
                                                                                    width: '25%',
                                                                                    // marginLeft: 5,
                                                                                },
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                    }
                                                                                    : {},
                                                                            ]}>
                                                                            {`${addOnChoice.name}:`}
                                                                        </Text>
                                                                        <Text
                                                                            style={[
                                                                                {
                                                                                    fontFamily: 'NunitoSans-Bold',
                                                                                    fontSize: 16,
                                                                                    color: Colors.fieldtTxtColor,
                                                                                    width: '75%',
                                                                                    // marginLeft: 5,
                                                                                },
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                    }
                                                                                    : {},
                                                                            ]}>
                                                                            {`${addOnChoice.choiceNames[0]}`}
                                                                        </Text>
                                                                    </View>

                                                                    <View
                                                                        style={{
                                                                            width: switchMerchant ? '14.2%' : '13%',
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'center',
                                                                            // backgroundColor: 'blue',
                                                                        }}>
                                                                        <Text
                                                                            style={[
                                                                                {
                                                                                    fontFamily: 'NunitoSans-Bold',
                                                                                    fontSize: 16,
                                                                                    color: Colors.fieldtTxtColor,
                                                                                    width: '28%',
                                                                                    // right: 38,
                                                                                    // backgroundColor: 'green',
                                                                                    textAlign: 'center',
                                                                                    // alignSelf: 'center',
                                                                                    // paddingRight:
                                                                                    //   Platform.OS == 'ios'
                                                                                    //     ? '2.5%'
                                                                                    //     : '1%',
                                                                                },
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                        // borderWidth: 1,
                                                                                        // paddingLeft: '7%',
                                                                                        textAlign: 'center',
                                                                                    }
                                                                                    : {},
                                                                                !switchMerchant &&
                                                                                    Platform.OS === 'android'
                                                                                    ? {}
                                                                                    : {},
                                                                            ]}>
                                                                            {`${addOnChoice.quantities
                                                                                ? `x${addOnChoice.quantities[0]}`
                                                                                : ''
                                                                                }`}
                                                                        </Text>
                                                                    </View>
                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'space-between',
                                                                            width: switchMerchant
                                                                                ? '13.5%'
                                                                                : '17.65%',
                                                                            alignItems: 'center',
                                                                            marginHorizontal: 1,
                                                                            // borderWidth: 1,
                                                                        }}>
                                                                        <Text
                                                                            style={[
                                                                                switchMerchant
                                                                                    ? { fontSize: 10 }
                                                                                    : {
                                                                                        color: Colors.descriptionColor,
                                                                                        fontSize: 16,
                                                                                    },
                                                                            ]}>
                                                                            RM
                                                                        </Text>
                                                                        <Text
                                                                            style={
                                                                                switchMerchant
                                                                                    ? { fontSize: 10 }
                                                                                    : {
                                                                                        color: Colors.descriptionColor,
                                                                                        paddingRight: 20,
                                                                                        fontSize: 16,
                                                                                    }
                                                                            }>
                                                                            {addOnChoice.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0)
                                                                                .toFixed(2)
                                                                                .replace(
                                                                                    /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                    '$1,',
                                                                                )}
                                                                        </Text>
                                                                    </View>
                                                                </View>
                                                            );
                                                        })}
                                                    </View>
                                                </View>
                                            </View>

                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    width: '100%',
                                                    marginTop: 5,
                                                }}>
                                                <View style={{ width: '71.13%' }}>
                                                    {/* Moved Buttons */}
                                                    {index === item.cartItems.length - 1 ? (
                                                        <View
                                                            style={{
                                                                justifyContent: 'flex-start',
                                                                alignItems: 'center',
                                                                flexDirection: 'row',
                                                                height: 100,
                                                                marginHorizontal: 10,
                                                                marginTop: 20,
                                                            }}>
                                                            {
                                                                (
                                                                    (currOutlet && currOutlet.privileges &&
                                                                        currOutlet.privileges.includes(PRIVILEGES_NAME.REFUND_ORDER))
                                                                    && privileges && privileges.includes(PRIVILEGES_NAME.REFUND_ORDER))
                                                                    ?
                                                                    <>
                                                                        {
                                                                            !item.isRefundOrder
                                                                                ?
                                                                                <TouchableOpacity
                                                                                    onPress={async () => {
                                                                                        // var body = {
                                                                                        //   orderId: item.uniqueId,
                                                                                        // };       

                                                                                        if (
                                                                                            (item.paymentDetails &&
                                                                                                (
                                                                                                    item.paymentDetails.txn_ID ||
                                                                                                    item.paymentDetails.txnId
                                                                                                )
                                                                                            )
                                                                                        ) {
                                                                                            if (
                                                                                                typeof item.paymentDetails.txn_ID === 'number' ||
                                                                                                typeof item.paymentDetails.txnId === 'number'
                                                                                            ) {
                                                                                                Alert.alert('Info', `This order is not able to be refunded\n\nPlease send the following code to support team to check:\n\n${item.uniqueId}`);

                                                                                                return;
                                                                                            }
                                                                                        }

                                                                                        if (item.settlementDate === null) {
                                                                                            if (
                                                                                                (
                                                                                                    moment().isBefore(moment().hour(5).minute(0))
                                                                                                    &&
                                                                                                    moment(item.createdAt).isBefore(moment().subtract(1, 'day').endOf('day'))
                                                                                                )
                                                                                                ||
                                                                                                (
                                                                                                    moment(item.createdAt).isSameOrAfter(moment().startOf('day'))
                                                                                                )
                                                                                            ) {
                                                                                                var cartItemListTemp = [];

                                                                                                for (var i = 0; i < item.cartItems.length; i++) {
                                                                                                    if (selectedCartItemDict[item.cartItems[i].itemId + item.cartItems[i].cartItemDate]) {
                                                                                                        cartItemListTemp.push({
                                                                                                            ...item.cartItems[i],
                                                                                                            actionQuantity: selectedCartItemDict[item.cartItems[i].itemId + item.cartItems[i].cartItemDate].actionQuantity,
                                                                                                        });
                                                                                                    }
                                                                                                }

                                                                                                if (cartItemListTemp.length <= 0) {
                                                                                                    Alert.alert('Info', 'Please select at least one item to proceed.');

                                                                                                    return;
                                                                                                }

                                                                                                setCartItemList(cartItemListTemp);

                                                                                                setRefundOrder(item);

                                                                                                setRefundOrderId(item.uniqueId);

                                                                                                setRefundNote('');
                                                                                                setRefundReason(REFUND_REASON_LIST[0].value);

                                                                                                setRefundModal(true);

                                                                                                if (item.paymentDetails &&
                                                                                                    item.paymentDetails.channel) {
                                                                                                    if (item.paymentDetails.channel === OFFLINE_PAYMENT_METHOD_TYPE.CASH.channel) {
                                                                                                        await openCashDrawer();
                                                                                                    }
                                                                                                }
                                                                                                else {
                                                                                                    // is offline/cash

                                                                                                    // await openCashDrawer();
                                                                                                }
                                                                                            }
                                                                                            else {
                                                                                                Alert.alert('Info', 'Order(s) that placed one day before, only able to refund until the next day of 5:00 AM.');
                                                                                            }
                                                                                        }
                                                                                        else {
                                                                                            Alert.alert('Info', 'This order is already settled for the daily payout.');
                                                                                        }


                                                                                        /* (netInfo.isInternetReachable && netInfo.isConnected
                                                                                          ? ApiClient.POST(API.refundUserOrder, body)
                                                                                          : APILocal.refundUserOrder({
                                                                                            body: {
                                                                                              orderId: item.uniqueId,
                                                                                            },
                                                                                            uid: firebaseUid,
                                                                                          })
                                                                                        )
                                                                                          .then((result) => {
                                                                                            if (result && result.status === 'success') {
                                                                                              Alert.alert('Success', 'Order has been refunded');
                                                                                            } else {
                                                                                              Alert.alert('Error', 'Failed to refund order');
                                                                                            }
                                                                                          })
                                                                                          .catch((err) => {
                                                                                            // console.log(err);
                                                                  
                                                                                            Alert.alert('Error', 'Failed to refund order');
                                                                                          }); */
                                                                                    }}
                                                                                    style={[
                                                                                        {
                                                                                            height: '100%',
                                                                                            justifyContent: 'center',
                                                                                            alignItems: 'center',
                                                                                            backgroundColor: Colors.tabRed,
                                                                                            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                                            width: 75,
                                                                                            marginLeft: 25,
                                                                                            borderRadius: 10,
                                                                                        },
                                                                                        switchMerchant
                                                                                            ? {
                                                                                                width: windowWidth * 0.08,
                                                                                            }
                                                                                            : {},
                                                                                    ]}
                                                                                >
                                                                                    <Close width={45} height={45} color='white' />

                                                                                    <Text
                                                                                        style={[
                                                                                            {
                                                                                                color: Colors.whiteColor,
                                                                                                fontSize: 12,
                                                                                                fontFamily: 'NunitoSans-Regular',
                                                                                                textAlign: 'center',
                                                                                                width: '80%',
                                                                                            },
                                                                                            switchMerchant
                                                                                                ? {
                                                                                                    fontSize: 10,
                                                                                                }
                                                                                                : {},
                                                                                        ]}>
                                                                                        {'Refund\nOrder'}
                                                                                    </Text>
                                                                                </TouchableOpacity>
                                                                                :
                                                                                <TouchableOpacity
                                                                                    onPress={async () => {

                                                                                        // if (item.paymentDetails &&
                                                                                        //   (
                                                                                        //     item.paymentDetails.txn_ID ||
                                                                                        //     item.paymentDetails.txnId
                                                                                        //   )) {
                                                                                        //     Alert.alert('Info', 'Paid first order is only able to submit the refund request once, and is unable to undo refund.');
                                                                                        // }
                                                                                        // else {

                                                                                        // }

                                                                                        if (item.settlementDate === null) {
                                                                                            if (
                                                                                                (
                                                                                                    moment().isBefore(moment().hour(14).minute(0))
                                                                                                    &&
                                                                                                    moment(item.createdAt).isBefore(moment().subtract(1, 'day').endOf('day'))
                                                                                                )
                                                                                                ||
                                                                                                (
                                                                                                    moment(item.createdAt).isSameOrAfter(moment().startOf('day'))
                                                                                                )
                                                                                            ) {
                                                                                                var cartItemListTemp = [];

                                                                                                for (var i = 0; i < item.cartItems.length; i++) {
                                                                                                    if (selectedCartItemDict[item.cartItems[i].itemId + item.cartItems[i].cartItemDate]) {
                                                                                                        cartItemListTemp.push({
                                                                                                            ...item.cartItems[i],
                                                                                                            actionQuantity: selectedCartItemDict[item.cartItems[i].itemId + item.cartItems[i].cartItemDate].actionQuantity,
                                                                                                        });
                                                                                                    }
                                                                                                }

                                                                                                if (cartItemListTemp.length <= 0) {
                                                                                                    Alert.alert('Info', 'Please select at least one item to proceed.');

                                                                                                    return;
                                                                                                }

                                                                                                setCartItemList(cartItemListTemp);

                                                                                                setRefundOrder(item);

                                                                                                setRefundOrderId(item.uniqueId);

                                                                                                setRefundNote(item.refundNote || '');

                                                                                                if (REFUND_REASON_LIST.find(dropdownItem => dropdownItem.value === item.refundReason)) {
                                                                                                    setRefundReason(item.refundReason);
                                                                                                }
                                                                                                else {
                                                                                                    setRefundReason(REFUND_REASON_LIST[0].value);
                                                                                                }

                                                                                                setUndoRefundModal(true);

                                                                                                if (item.paymentDetails &&
                                                                                                    item.paymentDetails.channel) {
                                                                                                    if (item.paymentDetails.channel === OFFLINE_PAYMENT_METHOD_TYPE.CASH.channel) {
                                                                                                        await openCashDrawer();
                                                                                                    }
                                                                                                }
                                                                                                else {
                                                                                                    // is offline/cash

                                                                                                    // await openCashDrawer();
                                                                                                }
                                                                                            }
                                                                                            else {
                                                                                                Alert.alert('Info', 'Order(s) that placed one day before, only able to be undo refund until the next day of 14:00 PM.');
                                                                                            }
                                                                                        }
                                                                                        else {
                                                                                            Alert.alert('Info', 'This order is already settled for the daily payout.');
                                                                                        }

                                                                                        // var body = {
                                                                                        //   orderId: item.uniqueId,
                                                                                        // };

                                                                                        // (netInfo.isInternetReachable && netInfo.isConnected
                                                                                        //   ? ApiClient.POST(API.undoRefundUserOrder, body)
                                                                                        //   : APILocal.undoRefundUserOrder({
                                                                                        //     body: {
                                                                                        //       orderId: item.uniqueId,
                                                                                        //     },
                                                                                        //     uid: firebaseUid,
                                                                                        //   })
                                                                                        // )
                                                                                        //   .then((result) => {
                                                                                        //     if (result && result.status === 'success') {
                                                                                        //       Alert.alert('Success', 'Order refund has been undone');
                                                                                        //     } else {
                                                                                        //       Alert.alert('Error', 'Failed to undo the refund order');
                                                                                        //     }
                                                                                        //   })
                                                                                        //   .catch((err) => {
                                                                                        //     // console.log(err);

                                                                                        //     Alert.alert('Error', 'Failed to undo the refund order');
                                                                                        //   });
                                                                                    }}
                                                                                    style={[
                                                                                        {
                                                                                            height: '100%',
                                                                                            justifyContent: 'center',
                                                                                            alignItems: 'center',
                                                                                            backgroundColor: Colors.primaryColor,
                                                                                            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                                            width: 75,
                                                                                            marginLeft: 25,
                                                                                            borderRadius: 10,
                                                                                        },
                                                                                        switchMerchant
                                                                                            ? {
                                                                                                width: windowWidth * 0.08,
                                                                                            }
                                                                                            : {},
                                                                                    ]}>
                                                                                    {switchMerchant ? (
                                                                                        <EvilIcons name="undo" size={14} color={Colors.whiteColor} />
                                                                                    ) : (
                                                                                        <EvilIcons name="undo" size={54} color={Colors.whiteColor} />
                                                                                    )}

                                                                                    <Text
                                                                                        style={[
                                                                                            {
                                                                                                color: Colors.whiteColor,
                                                                                                fontSize: 12,
                                                                                                fontFamily: 'NunitoSans-Regular',
                                                                                                textAlign: 'center',
                                                                                                width: '80%',
                                                                                            },
                                                                                            switchMerchant
                                                                                                ? {
                                                                                                    fontSize: 10,
                                                                                                }
                                                                                                : {},
                                                                                        ]}>
                                                                                        {'Undo\nRefund'}
                                                                                    </Text>
                                                                                </TouchableOpacity>
                                                                        }
                                                                    </>
                                                                    :
                                                                    <></>
                                                            }

                                                            <TouchableOpacity
                                                                onPress={async () => {
                                                                    Alert.alert('Info', 'Receipt has been added to print queue');

                                                                    // NetPrinter.closeConn(); // no need anymore

                                                                    await printUserOrder(
                                                                        {
                                                                            orderId: item.uniqueId,
                                                                            receiptNote: currOutlet.receiptNote || '',
                                                                        },
                                                                        false,
                                                                        [PRINTER_USAGE_TYPE.RECEIPT],
                                                                        // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                        false,
                                                                        false,
                                                                        false,
                                                                        // may-31 2023 printer function hee hide
                                                                        // netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                                                                        // [PRINTER_USAGE_TYPE.ORDER_SUMMARY, PRINTER_USAGE_TYPE.KITCHEN_DOCKET]
                                                                        // [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                    );

                                                                    // if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY) {
                                                                    //   await printUserOrder(
                                                                    //     {
                                                                    //       orderData: item,
                                                                    //     },
                                                                    //     false,
                                                                    //     [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                    //     false,
                                                                    //     false,
                                                                    //     false,
                                                                    //     { isInternetReachable: true, isConnected: true },
                                                                    //   );
                                                                    // }
                                                                    // else if (global.outletKdVariation === KD_PRINT_VARIATION.SUMMARY_CATEGORY) {
                                                                    //   await printKDSummaryCategoryWrapper(
                                                                    //     {
                                                                    //       orderData: item,
                                                                    //     },
                                                                    //   );
                                                                    // }
                                                                    // else if (global.outletKdVariation === KD_PRINT_VARIATION.INDIVIDUAL) {
                                                                    //   for (let bdIndex = 0; bdIndex < item.cartItems.length; bdIndex++) {
                                                                    //     await printDocketForKD(
                                                                    //       {
                                                                    //         userOrder: item,
                                                                    //         cartItem: item.cartItems[bdIndex],
                                                                    //       },
                                                                    //       // true,
                                                                    //       [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
                                                                    //       // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                                                    //     );
                                                                    //   }
                                                                    // }

                                                                    // await openCashDrawer();

                                                                    // 2022-10-16 - No need print bd
                                                                    // if (item && item.cartItems) {
                                                                    //   for (var i = 0; i < item.cartItems.length; i++) {
                                                                    //     if (item.cartItems[i].isDocket) {
                                                                    //       // if (i > 0) {
                                                                    //       //   await waitForSeconds(1); // no need anymore
                                                                    //       // }

                                                                    //       // NetPrinter.closeConn(); // no need anymore

                                                                    //       await printDocket(
                                                                    //         item.cartItems[i],
                                                                    //         // true,
                                                                    //         [PRINTER_USAGE_TYPE.RECEIPT],
                                                                    //         // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
                                                                    //       );
                                                                    //     }
                                                                    //   }
                                                                    // }

                                                                    // await printUserOrder(
                                                                    //   {
                                                                    //     orderId: item.uniqueId,
                                                                    //   },
                                                                    //   true,
                                                                    //   [PRINTER_USAGE_TYPE.RECEIPT],
                                                                    //   false,
                                                                    //   true,
                                                                    //   true,
                                                                    // );            
                                                                }}
                                                                style={[
                                                                    {
                                                                        height: '100%',
                                                                        justifyContent: 'center',
                                                                        alignItems: 'center',
                                                                        backgroundColor: Colors.secondaryColor,
                                                                        underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                        width: 75,
                                                                        marginLeft: 25,
                                                                        borderRadius: 10,
                                                                    },
                                                                    switchMerchant
                                                                        ? {
                                                                            width: windowWidth * 0.08,
                                                                        }
                                                                        : {},
                                                                ]}>
                                                                {switchMerchant ? (
                                                                    <IIcon name="receipt-outline" size={10} color={Colors.whiteColor} />
                                                                ) : (
                                                                    <IIcon name="receipt-outline" size={40} color={Colors.whiteColor} />
                                                                )}

                                                                <Text
                                                                    style={[
                                                                        {
                                                                            color: Colors.whiteColor,
                                                                            fontSize: 12,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                            textAlign: 'center',
                                                                            width: '80%',
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {'Reprint\nReceipt'}
                                                                </Text>
                                                            </TouchableOpacity>

                                                            <TouchableOpacity
                                                                onPress={() => {
                                                                    setRefundOrder(item);

                                                                    setRefundOrderId(item.uniqueId);

                                                                    setPaymentMethodModal(true);
                                                                }}
                                                                style={{
                                                                    height: '100%',
                                                                    justifyContent: 'center',
                                                                    alignItems: 'center',
                                                                    backgroundColor: Colors.tabCyan,
                                                                    underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                    width: 75,
                                                                    marginLeft: 25,
                                                                    borderRadius: 10,
                                                                }}>
                                                                <CreditcardMultiple width={40} height={40} color='white' />
                                                                <Text
                                                                    style={[
                                                                        {
                                                                            color: Colors.whiteColor,
                                                                            fontSize: 12,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                            textAlign: 'center',
                                                                            width: '80%',
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {`Payment\nMethod`}
                                                                </Text>
                                                            </TouchableOpacity>

                                                            <TouchableOpacity
                                                                onPress={() => {
                                                                    // setRefundOrderId(item.uniqueId);

                                                                    if (item.userPhone) {
                                                                        var foundUser = crmUsers.find(user => user.number === item.userPhone);
                                                                        if (foundUser && foundUser.email && !foundUser.email.startsWith('user-') && foundUser.email.length < 32) {
                                                                            setReceiptEmailToSent(foundUser.email);
                                                                        }
                                                                        else if (foundUser && foundUser.emailSecond && !foundUser.emailSecond.startsWith('user-') && foundUser.emailSecond.length < 32) {
                                                                            setReceiptEmailToSent(foundUser.emailSecond);
                                                                        }
                                                                        else {
                                                                            setReceiptEmailToSent('');
                                                                        }
                                                                    }

                                                                    setReceiptEmailOrder(item);
                                                                    setReceiptEmailModal(true);
                                                                }}
                                                                style={{
                                                                    height: '100%',
                                                                    justifyContent: 'center',
                                                                    alignItems: 'center',
                                                                    backgroundColor: Colors.primaryColor,
                                                                    underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
                                                                    width: 75,
                                                                    marginLeft: 25,
                                                                    borderRadius: 10,
                                                                }}>
                                                                <SnedEmail width={40} height={40} color='white' />
                                                                <Text
                                                                    style={[
                                                                        {
                                                                            color: Colors.whiteColor,
                                                                            fontSize: 12,
                                                                            fontFamily: 'NunitoSans-Regular',
                                                                            textAlign: 'center',
                                                                            width: '80%',
                                                                        },
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                            }
                                                                            : {},
                                                                    ]}>
                                                                    {`Send\nReceipt`}
                                                                </Text>
                                                            </TouchableOpacity>

                                                            {/* <TouchableOpacity
          // onPress={() => {
          //   cancelQueue(item.uniqueId);
          // }}
          style={{
            height: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: Colors.primaryColor,
            underlayColor: 'rgba(0, 0, 0, 1, 0.6)',
            // paddingBottom: 6,
            width: 75,
          }}>

          <MIcon name="email-outline" size={40} color={Colors.whiteColor} />

          <Text style={{ color: Colors.whiteColor, fontSize: 12, fontFamily: 'NunitoSans-Regular', textAlign: 'center', width: '80%' }}>{'Email\nReceipt'}</Text>

        </TouchableOpacity> */}
                                                        </View>
                                                    ) : (
                                                        <></>
                                                    )}
                                                </View>
                                                <View style={{ width: 10 }} />
                                                {index === item.cartItems.length - 1 ? (
                                                    <View
                                                        style={{
                                                            flexDirection: 'row',
                                                            //backgroundColor: 'yellow',
                                                            width: '25.8%',
                                                            // borderWidth: 1
                                                        }}>
                                                        <View
                                                            style={{
                                                                justifyContent: 'center',
                                                                width: '100%',
                                                            }}>
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    // borderWidth: 1,
                                                                }}>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                                width: '53.5%',
                                                                            }
                                                                            : {
                                                                                fontSize: 16,
                                                                                width: '50%',
                                                                                fontFamily: 'Nunitosans-Bold',
                                                                            }
                                                                    }>
                                                                    Subtotal:
                                                                </Text>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        width: switchMerchant ? '39%' : '50%',
                                                                        marginHorizontal: 1,
                                                                        // borderWidth: 1,
                                                                    }}>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? { fontSize: 10 }
                                                                                : { fontSize: 16 }
                                                                        }>
                                                                        RM
                                                                    </Text>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : {
                                                                                    fontSize: 16,
                                                                                    paddingRight: 20,
                                                                                }
                                                                        }>
                                                                        {((item.isRefundOrder && item.finalPrice <= 0) ? 0 : (
                                                                            item.totalPrice +
                                                                            getOrderDiscountInfo(item)
                                                                        ))
                                                                            .toFixed(2)
                                                                            .replace(
                                                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                '$1,',
                                                                            )}
                                                                    </Text>
                                                                </View>
                                                            </View>
                                                            {cartItem.orderType === ORDER_TYPE.DELIVERY ? (
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                    }}>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                    width: '53.5%',
                                                                                }
                                                                                : {
                                                                                    fontSize: 16,
                                                                                    width: '50%',
                                                                                    fontFamily: 'Nunitosans-Bold',
                                                                                }
                                                                        }>
                                                                        Delivery Fee:
                                                                    </Text>
                                                                    <View
                                                                        style={{
                                                                            flexDirection: 'row',
                                                                            justifyContent: 'space-between',
                                                                            width: switchMerchant ? '39%' : '50%',
                                                                            marginHorizontal: 1,
                                                                            // borderWidth: 1,
                                                                        }}>
                                                                        <Text
                                                                            style={
                                                                                switchMerchant
                                                                                    ? { fontSize: 10 }
                                                                                    : { fontSize: 16 }
                                                                            }>
                                                                            RM
                                                                        </Text>
                                                                        <Text
                                                                            style={
                                                                                switchMerchant
                                                                                    ? {
                                                                                        fontSize: 10,
                                                                                    }
                                                                                    : {
                                                                                        fontSize: 16,
                                                                                        paddingRight: 20,
                                                                                    }
                                                                            }>
                                                                            {item.deliveryFee
                                                                                .toFixed(2)
                                                                                .replace(
                                                                                    /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                    '$1,',
                                                                                )}
                                                                        </Text>
                                                                    </View>
                                                                </View>
                                                            ) : (
                                                                <></>
                                                            )}

                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    // borderWidth: 1,
                                                                }}>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                                width: '53.5%',
                                                                            }
                                                                            : {
                                                                                fontSize: 16,
                                                                                width: '50%',
                                                                                fontFamily: 'Nunitosans-Bold',
                                                                            }
                                                                    }>
                                                                    Discount:
                                                                </Text>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        width: switchMerchant ? '39%' : '50%',
                                                                        marginHorizontal: 1,
                                                                        // borderWidth: 1,
                                                                    }}>
                                                                    <Text
                                                                        style={{
                                                                            fontSize: switchMerchant ? 10 : 16,
                                                                        }}>
                                                                        RM
                                                                    </Text>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : {
                                                                                    fontSize: 16,
                                                                                    paddingRight: 20,
                                                                                }
                                                                        }>
                                                                        {((item.isRefundOrder && item.finalPrice <= 0) ? 0 : (
                                                                            // (!isNaN(item.discount) ? item.discount : 0) +
                                                                            getOrderDiscountInfoInclOrderBased(item)
                                                                        ))
                                                                            .toFixed(2)
                                                                            .replace(
                                                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                '$1,',
                                                                            )}
                                                                    </Text>
                                                                </View>
                                                            </View>

                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    // borderWidth: 1,
                                                                }}>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                                width: '53.5%',
                                                                            }
                                                                            : {
                                                                                fontSize: 16,
                                                                                width: '50%',
                                                                                fontFamily: 'Nunitosans-Bold',
                                                                            }
                                                                    }>
                                                                    Tax:
                                                                </Text>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        width: switchMerchant ? '39%' : '50%',
                                                                        marginHorizontal: 1,
                                                                        // borderWidth: 1,
                                                                    }}>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : {
                                                                                    fontSize: 16,
                                                                                }
                                                                        }>
                                                                        RM
                                                                    </Text>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : {
                                                                                    fontSize: 16,
                                                                                    paddingRight: 20,
                                                                                }
                                                                        }>
                                                                        {item.tax
                                                                            .toFixed(2)
                                                                            .replace(
                                                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                '$1,',
                                                                            )}
                                                                    </Text>
                                                                </View>
                                                            </View>

                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                }}>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                                width: '53.5%',
                                                                                fontFamily: 'Nunitosans-Bold',
                                                                            }
                                                                            : {
                                                                                fontSize: 16,
                                                                                width: '50%',
                                                                                fontFamily: 'Nunitosans-Bold',
                                                                            }
                                                                    }>
                                                                    Service Charge:
                                                                </Text>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        width: switchMerchant ? '39%' : '50%',
                                                                        marginHorizontal: 1,
                                                                    }}>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : { fontSize: 16 }
                                                                        }>
                                                                        RM
                                                                    </Text>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                    // fontFamily: 'NunitoSans-Regular',
                                                                                }
                                                                                : {
                                                                                    fontSize: 16,
                                                                                    paddingRight: 20,
                                                                                    // fontFamily: 'NunitoSans-Regular',
                                                                                }
                                                                        }>
                                                                        {(item.sc || 0)
                                                                            .toFixed(2)
                                                                            .replace(
                                                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                '$1,',
                                                                            )}
                                                                    </Text>
                                                                </View>
                                                            </View>
                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    // borderWidth: 1,
                                                                }}>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                                width: '53.5%',
                                                                            }
                                                                            : {
                                                                                fontSize: 16,
                                                                                width: '50%',
                                                                                fontFamily: 'Nunitosans-Bold',
                                                                            }
                                                                    }>
                                                                    Rounding:
                                                                </Text>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        width: switchMerchant ? '39%' : '50%',
                                                                        marginHorizontal: 1,
                                                                        // borderWidth: 1,
                                                                    }}>
                                                                    <Text
                                                                        style={{
                                                                            fontSize: switchMerchant ? 10 : 16,
                                                                        }}>
                                                                        RM
                                                                    </Text>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : {
                                                                                    fontSize: 16,
                                                                                    paddingRight: 20,
                                                                                }
                                                                        }>
                                                                        {(
                                                                            (!isNaN(item.finalPrice) ? item.finalPrice : 0)
                                                                                ?
                                                                                ((!isNaN(item.finalPrice) ? item.finalPrice : 0) - (!isNaN(item.finalPriceBefore) ? item.finalPriceBefore : 0))
                                                                                :
                                                                                0
                                                                        )
                                                                            .toFixed(2)
                                                                            .replace(
                                                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                '$1,',
                                                                            )}
                                                                    </Text>
                                                                </View>
                                                            </View>

                                                            <View
                                                                style={{
                                                                    flexDirection: 'row',
                                                                    // borderWidth: 1,
                                                                }}>
                                                                <Text
                                                                    style={
                                                                        switchMerchant
                                                                            ? {
                                                                                fontSize: 10,
                                                                                width: '53.5%',
                                                                            }
                                                                            : {
                                                                                fontSize: 16,
                                                                                width: '50%',
                                                                                fontFamily: 'Nunitosans-Bold',
                                                                            }
                                                                    }>
                                                                    Total:
                                                                </Text>
                                                                <View
                                                                    style={{
                                                                        flexDirection: 'row',
                                                                        justifyContent: 'space-between',
                                                                        width: switchMerchant ? '39%' : '50%',
                                                                        marginHorizontal: 1,
                                                                        // borderWidth: 1,
                                                                    }}>
                                                                    <Text
                                                                        style={{
                                                                            fontSize: switchMerchant ? 10 : 16,
                                                                        }}>
                                                                        RM
                                                                    </Text>
                                                                    <Text
                                                                        style={
                                                                            switchMerchant
                                                                                ? {
                                                                                    fontSize: 10,
                                                                                }
                                                                                : {
                                                                                    fontSize: 16,
                                                                                    paddingRight: 20,
                                                                                }
                                                                        }>
                                                                        {(!isNaN(item.finalPrice) ? item.finalPrice : 0)
                                                                            .toFixed(2)
                                                                            .replace(
                                                                                /(\d)(?=(\d{3})+(?!\d))/g,
                                                                                '$1,',
                                                                            )}
                                                                    </Text>
                                                                </View>
                                                            </View>
                                                        </View>
                                                    </View>
                                                ) : (
                                                    <></>
                                                )}
                                            </View>
                                            {/* <View style={{alignItems:'flex-end'}}>
                          <View style={{ flexDirection: 'row' }}>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: 16 }}>Subtotal: {(Math.ceil(cartItem.price * 20-0.05) /20).toFixed(2)}</Text>
                          </View>
                        </View> */}
                                            {/* {(cartItem.remarks && cartItem.remarks.length > 0) ?
                        <View style={{ alignItems: 'center', flexDirection: 'row' }}>
                          
                          <View style={{ flex: 1, justifyContent: 'center', }}>
                            <Text style={{ fontFamily: 'NunitoSans-SemiBold', fontSize: 15 }}>{cartItem.remarks}</Text>
                          </View>
                          
                        </View>
                        : <></>
                      } */}
                                        </View>
                                    );
                                })}
                            </TouchableOpacity>
                        ) : null}
                    </View>
                </TouchableOpacity>
                {/* </Swipeable> */}
            </View>
        );
    };

    useEffect(() => {
        filterOrders({ 'value': 1 })
    }, [])

    return (
        // <View style={styles.container}>
        //   <View style={styles.sidebar}>
        // <UserIdleWrapper disabled={!isMounted}>
        <View
            style={[
                styles.container,
                // !isTablet()
                //     ? {
                //         transform: [{ scaleX: 1 }, { scaleY: 1 }],
                //     }
                //     : {},
            ]}>
            <View
                style={[
                    styles.sidebar,
                    // !isTablet()
                    //     ? {
                    //         width: windowWidth * 0.08,
                    //     }
                    //     : {},
                    // switchMerchant
                    //     ? {
                    //         // width: '10%'
                    //     }
                    //     : {},
                ]}>
                <SideBar
                    navigation={props.navigation}
                    selectedTab={1}
                    expandOperation
                />
            </View>

            <Modal
                style={{}}
                visible={refundModal}
                supportedOrientations={['portrait', 'landscape']}
                transparent
                animationType={'fade'}>
                <View
                    style={{
                        flex: 1,
                        backgroundColor: Colors.modalBgColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}>
                    <View style={styles.modalContainer}>
                        <View style={[styles.modalView, switchMerchant ? {
                            height: Dimensions.get('window').width * 0.4,
                        } : {}]}>
                            <TouchableOpacity
                                style={[styles.closeButton,
                                {
                                    right: windowWidth * 0.02,
                                    top: windowWidth * 0.02,
                                },]}
                                onPress={() => {
                                    setRefundModal(false);
                                }}>
                                {switchMerchant ? (
                                    <AntDesign
                                        name="closecircle"
                                        size={15}
                                        color={Colors.fieldtTxtColor}
                                    />
                                ) : (
                                    <AntDesign
                                        name="closecircle"
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                    />
                                )}
                            </TouchableOpacity>
                            <View
                                style={{
                                    flexDirection: 'column',
                                    paddingTop: switchMerchant
                                        ? windowHeight * 0.1
                                        : 40,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}>
                                <Text
                                    style={[
                                        switchMerchant
                                            ? {
                                                fontSize: 16,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginBottom: 30,
                                            }
                                            : {
                                                fontSize: switchMerchant ? 16 : 20,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginBottom: 30,
                                            },
                                    ]}>
                                    Select Reason
                                </Text>
                                <View style={{ borderWidth: 1, borderColor: 'black', width: windowWidth * 0.3, borderRadius: 5, alignItems: 'center' }}>
                                    {/* rn picker need fix */}
                                    {/* <RNPickerSelect
                                        useNativeAndroidPickerStyle={false}
                                        // style={{
                                        //   inputIOS: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, color: 'black', paddingVertical: 5 },
                                        //   inputAndroid: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, color: 'black', paddingVertical: 5 },
                                        //   inputAndroidContainer: {
                                        //     width: '100%',
                                        //   }
                                        // }}
                                        style={pickerStyle}
                                        items={REFUND_REASON_LIST}
                                        placeholder={{}}
                                        placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}
                                        onValueChange={(value) => { setRefundReason(value); }}
                                        value={refundReason}
                                    /> */}
                                </View>
                                <View style={{
                                    flexDirection: 'column',
                                    paddingTop: 40,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}>
                                    <Text
                                        style={[
                                            switchMerchant
                                                ? {
                                                    fontSize: 16,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    marginBottom: '1%',
                                                }
                                                : {
                                                    fontSize: switchMerchant ? 16 : 20,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    marginBottom: '1%',
                                                },
                                        ]}>
                                        Add Notes
                                    </Text>
                                    <View>
                                        <TextInput style={{ borderBottomWidth: 1, borderBottomColor: 'black', width: 250, paddingBottom: 5, textAlign: 'center' }}
                                            onChangeText={(text) => { setRefundNote(text) }}
                                            value={refundNote}
                                        />
                                    </View>
                                </View>
                                <View style={[{ zIndex: -1000, paddingTop: 50 }, switchMerchant ? {
                                    paddingTop: 5,
                                } : {}]}>
                                    <TouchableOpacity
                                        style={{
                                            marginTop: 20,
                                            justifyContent: 'center',
                                            flexDirection: 'row',
                                            borderWidth: 1,
                                            borderColor: Colors.tabRed,
                                            backgroundColor: Colors.tabRed,
                                            borderRadius: 5,
                                            width: 100,
                                            paddingHorizontal: 10,
                                            height: 40,
                                            alignItems: 'center',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                            zIndex: -1000,
                                        }}
                                        onPress={() => {
                                            var body = {
                                                orderId: refundOrderId,
                                                refundReason,
                                                refundNote,

                                                cartItemList,
                                            };

                                            (
                                                // netInfo.isInternetReachable && netInfo.isConnected
                                                //   ? ApiClient.POST(API.refundUserOrder, body)
                                                //   :
                                                APILocal.refundUserOrder({
                                                    body: {
                                                        orderId: refundOrderId,
                                                        refundReason,
                                                        refundNote,

                                                        cartItemList,

                                                        orderIdHuman: `${(refundOrder.orderType === ORDER_TYPE.DINEIN ? '' : 'T') + refundOrder.orderId}`,
                                                    },
                                                    uid: firebaseUid,
                                                })
                                            )
                                                .then(async (result) => {
                                                    if (result && result.status === 'success') {
                                                        Alert.alert('Success', 'Request submitted, please wait until the next settlement cycle each day, for us to process the refunds.');
                                                    } else {
                                                        Alert.alert('Error', 'Failed to refund order');
                                                    }

                                                    setRefundModal(false);

                                                    setSelectedCartItemDict({});

                                                    setCartItemList([]);

                                                    // NetPrinter.closeConn(); // no need anymore

                                                    // await AsyncStorage.setItem('isPrintingReceipt', '1');

                                                    global.isPrintingReceipt = '1';

                                                    await printUserOrder(
                                                        {
                                                            orderId: refundOrderId,
                                                            receiptNote: currOutlet.receiptNote || '',
                                                        },
                                                        true,
                                                        [PRINTER_USAGE_TYPE.RECEIPT],
                                                        false,
                                                        true,
                                                        true,
                                                        // may-31 2023 printer function hee hide
                                                        // netInfoData = { isInternetReachable: netInfo.isInternetReachable, isConnected: netInfo.isConnected },
                                                    );

                                                    // await AsyncStorage.setItem('isPrintingReceipt', '0');

                                                    global.isPrintingReceipt = '0';
                                                })
                                                .catch((err) => {
                                                    // console.log(err);

                                                    Alert.alert('Error', 'Failed to refund order');

                                                    setRefundModal(false);

                                                    setSelectedCartItemDict({});

                                                    setCartItemList([]);
                                                });
                                        }}>
                                        <Text
                                            style={{
                                                color: Colors.whiteColor,
                                                //marginLeft: 5,
                                                fontSize: switchMerchant ? 10 : 16,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                            REFUND
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </View>
                </View>
            </Modal>

            <Modal
                style={{}}
                visible={undoRefundModal}
                supportedOrientations={['portrait', 'landscape']}
                transparent
                animationType={'fade'}>
                <View
                    style={{
                        flex: 1,
                        backgroundColor: Colors.modalBgColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}>
                    <View style={styles.modalContainer}>
                        <View style={[styles.modalView, switchMerchant ? {
                            height: Dimensions.get('window').width * 0.4,
                        } : {}]}>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => {
                                    setUndoRefundModal(false);
                                }}>
                                {switchMerchant ? (
                                    <AntDesign
                                        name="closecircle"
                                        size={15}
                                        color={Colors.fieldtTxtColor}
                                    />
                                ) : (
                                    <AntDesign
                                        name="closecircle"
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                    />
                                )}
                            </TouchableOpacity>
                            <View
                                style={{
                                    flexDirection: 'column',
                                    paddingTop: switchMerchant
                                        ? windowHeight * 0.1
                                        : 40,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}>
                                <Text
                                    style={[
                                        switchMerchant
                                            ? {
                                                fontSize: 16,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginBottom: 30,
                                            }
                                            : {
                                                fontSize: switchMerchant ? 16 : 20,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginBottom: 30,
                                            },
                                    ]}>
                                    Reason
                                </Text>
                                <View style={{ borderWidth: 1, borderColor: 'black', width: windowWidth * 0.3, borderRadius: 5, alignItems: 'center' }}>
                                    {/* rn picker need fix */}
                                    {/* <RNPickerSelect
                                        useNativeAndroidPickerStyle={false}
                                        style={{
                                            inputIOS: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, color: 'black', paddingVertical: 5 },
                                            inputAndroid: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, color: 'black', paddingVertical: 5 },
                                            inputAndroidContainer: {
                                                width: '100%',
                                            }
                                        }}
                                        items={REFUND_REASON_LIST}
                                        placeholder={{}}
                                        placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}
                                        onValueChange={(value) => { setRefundReason(value); }}
                                        value={refundReason}
                                        disabled
                                    /> */}
                                </View>
                                <View style={{
                                    flexDirection: 'column',
                                    paddingTop: 40,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}>
                                    <Text
                                        style={[
                                            switchMerchant
                                                ? {
                                                    fontSize: 16,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    marginBottom: '1%',
                                                }
                                                : {
                                                    fontSize: switchMerchant ? 16 : 20,
                                                    fontFamily: 'NunitoSans-Bold',
                                                    marginBottom: '1%',
                                                },
                                        ]}>
                                        Notes
                                    </Text>
                                    <View>
                                        <TextInput style={{ borderBottomWidth: 1, borderBottomColor: 'black', width: 250, paddingBottom: 5, textAlign: 'center' }}
                                            onChangeText={(text) => { setRefundNote(text) }}
                                            value={refundNote}
                                            editable={false}
                                        />
                                    </View>
                                </View>
                                <View style={[{ zIndex: -1000, paddingTop: 50 }, switchMerchant ? {
                                    paddingTop: 5,
                                } : {}]}>
                                    <TouchableOpacity
                                        style={{
                                            marginTop: 20,
                                            justifyContent: 'center',
                                            flexDirection: 'row',
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            backgroundColor: '#4E9F7D',
                                            borderRadius: 5,
                                            width: 100,
                                            paddingHorizontal: 10,
                                            height: 40,
                                            alignItems: 'center',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                            zIndex: -1000,
                                        }}
                                        onPress={() => {
                                            var body = {
                                                orderId: refundOrderId,
                                                refundReason,
                                                refundNote,

                                                cartItemList,
                                            };

                                            (
                                                // netInfo.isInternetReachable && netInfo.isConnected
                                                //   ? ApiClient.POST(API.undoRefundUserOrder, body)
                                                //   :
                                                APILocal.undoRefundUserOrder({
                                                    body: {
                                                        orderId: refundOrderId,
                                                        refundReason,
                                                        refundNote,

                                                        cartItemList,

                                                        orderIdHuman: `${(refundOrder.orderType === ORDER_TYPE.DINEIN ? '' : 'T') + refundOrder.orderId}`,
                                                    },
                                                    uid: firebaseUid,
                                                })
                                            )
                                                .then((result) => {
                                                    if (result && result.status === 'success') {
                                                        Alert.alert('Success', 'Order refund has been undone');
                                                    } else {
                                                        Alert.alert('Error', 'Failed to undo the refund order');
                                                    }
                                                })
                                                .catch((err) => {
                                                    // console.log(err);

                                                    Alert.alert('Error', 'Failed to undo the refund order');
                                                });

                                            setUndoRefundModal(false);

                                            setSelectedCartItemDict({});

                                            setCartItemList([]);
                                        }}>
                                        <Text
                                            style={{
                                                color: Colors.whiteColor,
                                                //marginLeft: 5,
                                                fontSize: switchMerchant ? 10 : 16,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                            UNDO
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </View>
                </View>
            </Modal>

            <Modal
                style={{}}
                visible={paymentMethodModal}
                supportedOrientations={['portrait', 'landscape']}
                transparent
                animationType={'fade'}>
                <View
                    style={{
                        flex: 1,
                        backgroundColor: Colors.modalBgColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}>
                    <View style={styles.modalContainer}>
                        <View style={[styles.modalView, switchMerchant ? { height: Dimensions.get('window').width * 0.35, } : { height: Dimensions.get('window').width * 0.32, }]}>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => {
                                    setPaymentMethodModal(false);
                                }}>
                                {switchMerchant ? (
                                    <AntDesign
                                        name="closecircle"
                                        size={15}
                                        color={Colors.fieldtTxtColor}
                                    />
                                ) : (
                                    <AntDesign
                                        name="closecircle"
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                    />
                                )}
                            </TouchableOpacity>
                            <View
                                style={{
                                    flexDirection: 'column',
                                    paddingTop: switchMerchant
                                        ? windowHeight * 0.1
                                        : 40,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}>
                                <Text
                                    style={[
                                        switchMerchant
                                            ? {
                                                fontSize: 16,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginBottom: 30,
                                            }
                                            : {
                                                fontSize: switchMerchant ? 16 : 20,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginBottom: 30,
                                            },
                                    ]}>
                                    Payment Method
                                </Text>

                                <View
                                    style={{ borderWidth: 1, borderColor: 'black', width: windowWidth * 0.3, borderRadius: 5, alignItems: 'center', marginTop: 30 }}
                                // onPress={() => {
                                //   if (paymentMethodPickerRef && paymentMethodPickerRef.current) {
                                //     paymentMethodPickerRef.current.togglePicker();
                                //   }
                                // }}
                                >
                                    {/* rn picker need fix */}
                                    {/* <RNPickerSelect
                                        ref={paymentMethodPickerRef}
                                        useNativeAndroidPickerStyle={false}
                                        style={{
                                            inputIOS: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, color: 'black', paddingVertical: 10, paddingLeft: 10 },
                                            inputAndroid: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, color: 'black', paddingVertical: 10 },
                                            inputAndroidContainer: {
                                                width: '100%',
                                            }
                                        }}
                                        items={paymentMethodsDropdownList}
                                        placeholder={{}}
                                        placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}
                                        onValueChange={(value) => {
                                            console.log('onValueChange');
                                            console.log(value);

                                            console.log('default methods');
                                            console.log(paymentMethods);
                                            console.log('custom methods');
                                            console.log(outletPaymentMethodsDropdownList);
                                            console.log('combined methods');
                                            console.log(paymentMethodsDropdownList);

                                            setPaymentMethodChannel(value);
                                        }}
                                        // onClose={(value) => { setPaymentMethodChannel(value); }}
                                        value={paymentMethodChannel}
                                        fixAndroidTouchableBug
                                    /> */}
                                </View>

                                <View style={[{ zIndex: -1000, paddingTop: 50 }, switchMerchant ? {
                                    paddingTop: 5,
                                } : {}]}>
                                    <TouchableOpacity
                                        style={{
                                            marginTop: 20,
                                            justifyContent: 'center',
                                            flexDirection: 'row',
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            backgroundColor: '#4E9F7D',
                                            borderRadius: 5,
                                            width: 100,
                                            paddingHorizontal: 10,
                                            height: 40,
                                            alignItems: 'center',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                            zIndex: -1000,
                                        }}
                                        onPress={() => {
                                            updatePaymentMethod();
                                        }}>
                                        <Text
                                            style={{
                                                color: Colors.whiteColor,
                                                //marginLeft: 5,
                                                fontSize: switchMerchant ? 10 : 16,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                            SAVE
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </View>
                </View>
            </Modal>

            <Modal
                style={{}}
                visible={receiptEmailModal}
                supportedOrientations={['portrait', 'landscape']}
                transparent
                animationType={'fade'}>
                <View
                    style={{
                        flex: 1,
                        backgroundColor: Colors.modalBgColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                    }}>
                    <View style={styles.modalContainer}>
                        <View style={[styles.modalView, switchMerchant ? {
                            height: Dimensions.get('window').width * 0.28, // 0.35
                        } :
                            {
                                height: Dimensions.get('window').width * 0.26, // 0.32
                            }]}>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={() => {
                                    setReceiptEmailModal(false);
                                }}>
                                {switchMerchant ? (
                                    <AntDesign
                                        name="closecircle"
                                        size={15}
                                        color={Colors.fieldtTxtColor}
                                    />
                                ) : (
                                    <AntDesign
                                        name="closecircle"
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                    />
                                )}
                            </TouchableOpacity>
                            <View
                                style={{
                                    flexDirection: 'column',
                                    paddingTop: switchMerchant
                                        ? windowHeight * 0.1
                                        : 40,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                }}>
                                <Text
                                    style={[
                                        switchMerchant
                                            ? {
                                                fontSize: 16,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginBottom: 30,
                                            }
                                            : {
                                                fontSize: switchMerchant ? 16 : 20,
                                                fontFamily: 'NunitoSans-Bold',
                                                marginBottom: 30,
                                            },
                                    ]}>
                                    Send Receipt To Email
                                </Text>

                                <View>
                                    <TextInput style={{ borderBottomWidth: 1, borderBottomColor: 'black', width: 250, paddingBottom: 5, textAlign: 'center', marginTop: 20 }}
                                        onChangeText={(text) => { setReceiptEmailToSent(text) }}
                                        placeholder='<EMAIL>'
                                        value={receiptEmailToSent}
                                        keyboardType='email-address'
                                    />
                                </View>

                                <View style={[{ zIndex: -1000, paddingTop: 50 }, switchMerchant ? {
                                    paddingTop: 5,
                                } : {}]}>
                                    <TouchableOpacity
                                        style={{
                                            marginTop: 20,
                                            justifyContent: 'center',
                                            flexDirection: 'row',
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            backgroundColor: '#4E9F7D',
                                            borderRadius: 5,
                                            width: 100,
                                            paddingHorizontal: 10,
                                            height: 40,
                                            alignItems: 'center',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                            zIndex: -1000,
                                        }}
                                        onPress={async () => {
                                            if (receiptEmailToSent.length > 0 && receiptEmailToSent.includes('@')) {
                                                sendOrderReceiptEmail(receiptEmailOrder, receiptEmailToSent);

                                                Alert.alert('Success', 'Receipt has been sent to the email inbox.');
                                            }
                                            else {
                                                Alert.alert('Info', 'Please enter the email address to proceed.');
                                            }

                                            setReceiptEmailModal(false);
                                        }}>
                                        <Text
                                            style={{
                                                color: Colors.whiteColor,
                                                //marginLeft: 5,
                                                fontSize: switchMerchant ? 10 : 16,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                            SEND
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    </View>
                </View>
            </Modal>

            <View
                style={
                    switchMerchant
                        ? {
                            paddingHorizontal: 25,
                            flex: 1,
                            paddingTop: 30,
                            paddingBottom: windowHeight * 0.05,
                        }
                        : { flex: 1, paddingHorizontal: 25, paddingVertical: 30 }
                }>
                <DateTimePickerModal
                    //supportedOrientations={['landscape', 'portrait']}
                    isVisible={showDateTimePicker}
                    mode={'date'}
                    onConfirm={(text) => {
                        // setRev_date(moment(text).startOf('day'));
                        CommonStore.update(s => {
                            s.historyStartDate = moment(text).startOf('day');
                        });
                        setShowDateTimePicker(false);
                    }}
                    onCancel={() => {
                        setShowDateTimePicker(false);
                    }}
                    style={{ zIndex: 1000 }}
                    maximumDate={moment(historyEndDate).toDate()}
                    date={moment(historyStartDate).toDate()}
                />

                <DateTimePickerModal
                    //supportedOrientations={['landscape', 'portrait']}
                    isVisible={showDateTimePicker1}
                    mode={'date'}
                    onConfirm={(text) => {
                        // setRev_date1(moment(text).endOf('day'));
                        CommonStore.update(s => {
                            s.historyEndDate = moment(text).endOf('day');
                        });
                        setShowDateTimePicker1(false);
                    }}
                    onCancel={() => {
                        setShowDateTimePicker1(false);
                    }}
                    style={{ zIndex: 1000 }}
                    minimumDate={moment(historyStartDate).toDate()}
                    date={moment(historyEndDate).toDate()}
                />

                <View
                    style={[
                        {
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            paddingRight: 0,
                            // borderWidth: 1
                        },
                        switchMerchant
                            ? {
                                marginTop: '-2.5%',
                                alignItems: 'center',
                                // backgroundColor: 'red',
                                // paddingLeft: '0.3%',
                                // paddingRight: '0.5%',
                                paddingLeft: windowWidth * 0.005,
                                paddingRight: windowWidth * 0.003,
                            }
                            : {},
                        windowWidth === 1280 &&
                            windowHeight === 800
                            ? {
                                paddingRight: 3,
                            }
                            : {},
                    ]}>
                    <Text
                        style={[
                            {
                                // fontSize: 30,
                                fontSize: 26,
                                marginRight: 60,
                                fontFamily: 'NunitoSans-Bold',
                            },
                            switchMerchant
                                ? {
                                    fontSize: 15,
                                    //follow dashboard
                                    fontSize: 20,
                                    // borderWidth: 1,
                                    // top: windowHeight * -0.05,
                                    marginRight: 0,
                                }
                                : {},
                        ]}>
                        Order History
                    </Text>
                    {/* <View style={{ marginRight: 40, flexDirection: 'row', justifyContent: 'center', alignItems: 'center', paddingLeft: 30, borderRadius: 10, height: windowHeight * 0.055 }}>
            <View style={{ position: 'absolute', backgroundColor: Colors.whiteColor, width: 220, height: windowHeight * 0.055, borderRadius: 10, }} />
            <Text style={{ fontSize: 15, borderRightWidth: StyleSheet.hairlineWidth, paddingRight: Platform.OS == 'ios' ? 30 : 20, borderColor: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Bold' }}>Sort By</Text>
            <DropDownPicker
              // controller={instance => controller = instance}
              controller={instance => setController(instance)}
              arrowColor={Colors.primaryColor}
              arrowSize={23}
              arrowStyle={{ fontWeight: 'bold' }}
              style={{ width: Platform.OS == 'ios' ? windowWidth * 0.14 : windowWidth * 0.1, borderWidth: 0 }}
              itemStyle={{ justifyContent: 'flex-start' }}
              placeholderStyle={{ color: 'black' }}
              items={[{ label: 'Order ID', value: 1 }, { label: 'Date/Time', value: 2 }, { label: 'Name', value: 3 }, { label: 'Waiting Time', value: 4 }, { label: 'Payment Method', value: 5 }, { label: 'Total', value: 6 }]}
              placeholder={"Order ID"}
              labelStyle={{ fontSize: 12.5 }}
              onChangeItem={selectedSort => {
                // setState({ sort: selectedSort }),
                sortingOrders(selectedSort);
              }
              }
              onOpen={() => controller1.close()}
            />
          </View> */}
                    <View
                        style={[
                            {
                                //marginBottom: -25,
                                flexDirection: 'row',
                                alignItems: 'center',
                            },
                            // !isTablet()
                            //     ? {
                            //         // right: windowWidth * 0.025,
                            //     }
                            //     : {},
                        ]}>
                        <View
                            style={[
                                {
                                    marginRight: 10,
                                    paddingHorizontal: 15,
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    borderRadius: 10,
                                    paddingVertical: 10,
                                    justifyContent: 'center',
                                    backgroundColor: Colors.whiteColor,
                                    shadowOpacity: 0,
                                    shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: 1,
                                },
                                switchMerchant
                                    ? {
                                        height: 35,
                                        width: windowWidth * 0.24,
                                        // top: windowHeight * -0.075,
                                    }
                                    : {},
                            ]}>
                            <View
                                style={{ alignSelf: 'center', marginRight: 5 }}
                                onPress={() => {
                                    setState({ pickerMode: 'date', showDateTimePicker: true });
                                }}>
                                {/* <EvilIcons name="calendar" size={25} color={Colors.primaryColor} /> */}
                                {switchMerchant ? (
                                    <GCalendar width={13} height={13} />
                                ) : (
                                    <GCalendar width={20} height={20} />
                                )}
                            </View>

                            <DatePicker
                                selected={moment(historyStartDate).toDate()}
                                onChange={(date) => {
                                    CommonStore.update(s => {
                                        s.historyStartDate = moment(date).startOf('day');
                                    });
                                }}
                                maxDate={moment(historyEndDate).toDate()}
                            />

                            <Text
                                style={
                                    switchMerchant
                                        ? {
                                            fontSize: 10,
                                            fontFamily: "NunitoSans-Regular",
                                        }
                                        : { fontFamily: "NunitoSans-Regular" }
                                }
                            >
                                -
                            </Text>

                            <DatePicker
                                selected={moment(historyEndDate).toDate()}
                                onChange={(date) => {
                                    // setRev_date1(moment(text).endOf('day'));
                                    CommonStore.update(s => {
                                        s.historyEndDate = moment(date).endOf('day');
                                    });
                                }}
                                minDate={moment(historyStartDate).toDate()}
                            />
                        </View>

                        <View
                            style={[
                                {
                                    flexDirection: 'row',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    paddingLeft: 10,
                                    borderBottomLeftRadius: 5,
                                    borderTopLeftRadius: 5,
                                    height: 40,
                                    // borderWidth: 1,
                                    // borderRightWidth: 0,
                                    // borderColor: '#E5E5E5',
                                    // backgroundColor: 'white',
                                    // marginRight: 15,
                                    // shadowColor: '#000',
                                    // shadowOffset: {
                                    //     width: 0,
                                    //     height: 2,
                                    // },
                                    // shadowOpacity: 0.22,
                                    // shadowRadius: 3.22,
                                    // elevation: 3,
                                    // left: windowWidth * 0.01,
                                    marginLeft: 25
                                },
                                switchMerchant
                                    ? {
                                        height: 35,
                                        paddingLeft: windowWidth * 0.002,
                                        // top: windowHeight * -0.075,
                                        // right: windowHeight * 0.08,
                                    }
                                    : {},
                            ]}>
                            <Text
                                style={[
                                    {
                                        fontSize: 16,
                                        paddingRight: Platform.OS == 'ios' ? 20 : 20,
                                        borderColor: Colors.fieldtTxtColor,
                                        fontFamily: 'NunitoSans-Bold',
                                        // marginLeft: -50,
                                    },
                                    switchMerchant
                                        ? {
                                            fontSize: 10,
                                            // borderWidth: 1
                                            // paddingLeft: '10%',
                                            // left: windowWidth * 0.005,
                                            paddingLeft: '1%',
                                        }
                                        : {},
                                ]}>
                                Filter
                            </Text>

                            <DropDownPicker
                                style={{
                                    backgroundColor: Colors.whiteColor,
                                    width: 200,
                                    height: 40,
                                    borderRadius: 5,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                    shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 3,
                                }}
                                dropDownContainerStyle={{
                                    width: 200,
                                    backgroundColor: Colors.whiteColor,
                                    borderColor: "#E5E5E5",
                                }}
                                labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                }}
                                textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',

                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                }}
                                selectedItemContainerStyle={{
                                    flexDirection: "row",
                                }}

                                showArrowIcon={true}
                                ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-down-outline"
                                    />
                                )}
                                ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-up-outline"
                                    />
                                )}

                                showTickIcon={true}
                                TickIconComponent={({ press }) => (
                                    <Ionicon
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        color={
                                            press ? Colors.fieldtBgColor : Colors.primaryColor
                                        }
                                        name={'md-checkbox'}
                                        size={25}
                                    />
                                )}
                                dropDownDirection="BOTTOM"
                                // disabled={selectedStockTransferEdit ? true : false}
                                items={[
                                    { label: 'All Orders', value: 1 },
                                    { label: 'Dine In', value: 2 },
                                    { label: 'Takeaway', value: 3 },
                                ]}
                                value={filterType}
                                placeholder={'All Orders'}
                                placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                }}
                                onSelectItem={(item) => {
                                    setFilterType(item.value);
                                }}
                                open={openFilter}
                                setOpen={setOpenFilter}
                            />
                        </View>
                    </View>
                </View>

                <View
                    style={[
                        { marginTop: 20, flex: 1, zIndex: -1 },
                        switchMerchant
                            ? {
                                // borderWidth: 2,
                                // height: windowHeight * 0.8,
                                // top: windowHeight * -0.065,
                            }
                            : {},
                    ]}>
                    <View
                        style={{
                            width: '100%',
                            flexDirection: 'row',
                            alignItems: 'center',
                            paddingBottom: 10,
                            paddingHorizontal: 3,
                        }}>
                        {/* <View style={{width: '2%'}}></View> */}
                        {/* <View style={{ flex: 0.8, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center' }}> */}
                        <View
                            style={[
                                {},
                                switchMerchant
                                    ? {
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        width: '9.9%',
                                        // backgroundColor: 'red',
                                        // borderWidth: 1,
                                    }
                                    : {
                                        alignItems: 'center',
                                        //marginLeft: '2.5%',
                                        width: '10.7%',
                                        // backgroundColor: 'blue',
                                        //marginHorizontal: 0.5,
                                        //left: Platform.OS == 'ios' ? 15 : '60%',
                                    },
                            ]}>
                            <TouchableOpacity
                                onPress={() => {
                                    sorting0(setSortSender(!sortSender));
                                }}>
                                <Text
                                    style={[
                                        {
                                            color: 'black',
                                            fontFamily: 'NunitoSans-Regular',
                                            textAlign: 'center',
                                        },
                                        switchMerchant
                                            ? {
                                                fontSize: 10,
                                            }
                                            : {},
                                    ]}>
                                    Type
                                </Text>
                            </TouchableOpacity>
                        </View>
                        {/* <View style={{ flex: 1, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center', right: 18 }}> */}
                        <View
                            style={[
                                {},
                                switchMerchant
                                    ? {
                                        width: '10.5%',
                                        // borderWidth: 1,
                                        paddingLeft: '1.3%',
                                    }
                                    : {
                                        alignItems: 'flex-start',
                                        width: '9.8%',
                                        //marginHorizontal: 0.5,
                                        //left: Platform.OS == 'ios' ? 5 : 0,
                                    },
                            ]}>
                            <TouchableOpacity
                                onPress={() => {
                                    sorting(setSortOrderID(!sortOrderID));
                                }}>
                                <Text
                                    style={[
                                        { color: 'black', fontFamily: 'NunitoSans-Regular' },
                                        switchMerchant
                                            ? {
                                                fontSize: 10,
                                            }
                                            : {},
                                    ]}>
                                    Status
                                </Text>
                            </TouchableOpacity>
                        </View>
                        {/* <View style={{ flex: 1, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center', right: 35 }}> */}
                        <View
                            style={
                                switchMerchant
                                    ? {
                                        alignItems: 'flex-start',
                                        width: '9.7%',
                                        // borderWidth: 1,
                                        paddingLeft: '0.5%',
                                        marginHorizontal: 1,
                                    }
                                    : {
                                        alignItems: 'flex-start',
                                        width: '9.7%',
                                        marginHorizontal: 1,
                                    }
                            }>
                            <TouchableOpacity
                                onPress={() => {
                                    sorting(setSortOrderID(!sortOrderID));
                                }}>
                                <Text
                                    style={[
                                        { color: 'black', fontFamily: 'NunitoSans-Regular' },
                                        switchMerchant
                                            ? {
                                                fontSize: 10,
                                            }
                                            : {},
                                    ]}>
                                    Order ID
                                </Text>
                            </TouchableOpacity>
                        </View>
                        {/* <View style={{ flex: 1.1, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center', right: 30 }}> */}
                        <View
                            style={[
                                {},
                                switchMerchant
                                    ? {
                                        width: '13.1%',
                                        // borderWidth: 1,
                                        paddingLeft: '0.2%',
                                    }
                                    : {
                                        alignItems: 'flex-start',
                                        width: '12.5%',
                                    },
                            ]}>
                            <TouchableOpacity
                                onPress={() => {
                                    sorting1(setSortDateTime(!sortDateTime));
                                }}>
                                <Text
                                    style={[
                                        { color: 'black', fontFamily: 'NunitoSans-Regular' },
                                        switchMerchant
                                            ? {
                                                fontSize: 10,
                                            }
                                            : {},
                                    ]}>
                                    Date/Time
                                </Text>
                            </TouchableOpacity>
                        </View>
                        {/* <View style={{ flex: 1.1, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center', right: 30 }}> */}
                        <View
                            style={[
                                switchMerchant
                                    ? {
                                        width: '12.5%',
                                        // borderWidth: 1,
                                        paddingLeft: '0.2%',
                                        marginHorizontal: 1,
                                        //backgroundColor: 'red',
                                    }
                                    : {
                                        alignItems: 'flex-start',
                                        width: '12%',
                                        marginHorizontal: 1,
                                        //left: Platform.OS == 'ios' ? 0 : 0,
                                    },
                            ]}>
                            <TouchableOpacity
                                onPress={() => {
                                    sorting2(setSortCustomerName(!sortCustomerName));
                                }}>
                                <Text
                                    style={[
                                        { color: 'black', fontFamily: 'NunitoSans-Regular' },
                                        switchMerchant
                                            ? {
                                                fontSize: 10,
                                            }
                                            : {},
                                    ]}>
                                    Staff
                                </Text>
                            </TouchableOpacity>
                        </View>
                        {/* <View style={{ flex: 1, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center', right: 40 }}> */}
                        <View
                            style={
                                switchMerchant
                                    ? {
                                        width: '13.2%',
                                        marginHorizontal: 1,
                                        // borderWidth: 1,
                                    }
                                    : {
                                        alignItems: 'flex-start',
                                        width: '13%',
                                        marginHorizontal: 1,
                                        //right: Platform.OS == 'ios' ? 5 : 0,
                                    }
                            }>
                            <Text
                                style={[
                                    { color: 'black', fontFamily: 'NunitoSans-Regular' },
                                    switchMerchant
                                        ? {
                                            fontSize: 10,
                                        }
                                        : {},
                                ]}>
                                Waiting Time
                            </Text>
                        </View>
                        {/* <View style={{ flex: 1.8, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center', right: 35 }}> */}
                        <View
                            style={
                                // switchMerchant
                                //   ? {
                                //       width: '16.3%',
                                //       // borderWidth: 1,
                                //       marginHorizontal: 1,
                                //       backgroundColor: 'blue',
                                //     }
                                //   : {
                                //       alignItems: 'flex-start',
                                //       width: '16.5%',
                                //       marginHorizontal: 1,
                                //       backgroundColor: 'red',
                                //       //marginHorizontal: 0.5,
                                //       //right: Platform.OS == 'ios' ? 5 : 0,
                                //     }
                                {
                                    width: switchMerchant ? '16.3%' : '16.5%',
                                    marginHorizontal: 1,
                                }
                            }>
                            <TouchableOpacity
                                onPress={() => {
                                    sorting3(setSortPaymentMethod(!sortPaymentMethod));
                                }}>
                                <Text
                                    style={[
                                        { color: 'black', fontFamily: 'NunitoSans-Regular' },
                                        switchMerchant
                                            ? {
                                                fontSize: 10,
                                            }
                                            : {},
                                    ]}>
                                    Payment Status
                                </Text>
                            </TouchableOpacity>
                        </View>
                        {/* <View style={{ flex: 1, paddingHorizontal: Platform.OS == 'ios' ? 10 : 20, alignItems: 'center', right: 35 }}> */}
                        <View
                            style={
                                switchMerchant
                                    ? {
                                        width: '9.95%',
                                        marginHorizontal: 1,
                                        // borderWidth: 1,
                                    }
                                    : {
                                        alignItems: 'flex-start',
                                        width: '13%',
                                        marginHorizontal: 1,
                                        //left: Platform.OS == 'ios' ? 0 : '110%',
                                    }
                            }>
                            <TouchableOpacity
                                onPress={() => {
                                    sorting4(setSortTotalPrice(!sortTotalPrice));
                                }}>
                                <Text
                                    style={[
                                        { color: 'black', fontFamily: 'NunitoSans-Regular' },
                                        switchMerchant
                                            ? {
                                                fontSize: 10,
                                            }
                                            : {
                                                // left:
                                                //   Platform.OS == 'ios'
                                                //     ? 0
                                                //     : windowWidth * -0.0055,
                                                // borderWidth: 1
                                            },
                                    ]}>
                                    Total
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                    {/* <View
            style={[
              {
                flex: 1,
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 1,
                // borderWidth: 2
              },
              switchMerchant
                ? {
                  // height: '120%'
                }
                : {},
            ]}> */}
                    <ScrollView showsVerticalScrollIndicator={false} style={{ height: Dimensions.get('screen').height * 0.6, paddingBottom: 80, }}>
                        <FlatList
                            showsVerticalScrollIndicator={false}
                            // data={historyOrders.slice(0).sort((a, b) => {
                            //   return b.orderDate - a.orderDate;
                            // })}
                            data={historyOrders}
                            renderItem={renderOrder}
                            keyExtractor={(item, index) => String(index)}
                            style={{}}
                        />
                    </ScrollView>
                    {/* </View> */}
                </View>
            </View>
        </View>
        // </UserIdleWrapper>
    );
});

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: 'row',
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    list: {
        paddingVertical: 20,
        paddingHorizontal: 30,
        flexDirection: 'row',
        alignItems: 'center',
    },

    listItem: {
        backgroundColor: Colors.whiteColor,
        borderWidth: StyleSheet.hairlineWidth,
        borderColor: '#c4c4c4',
        borderRadius: 20,
        paddingVertical: 20,
        paddingHorizontal: 20,
        marginRight: 10,
        marginBottom: 10,
        width: (Dimensions.get('window').width - 150) / 2,
    },

    tablebox: {
        backgroundColor: Colors.whiteColor,
        shadowColor: '#c4c4c4',
        shadowOffset: {
            width: 8,
            height: 8,
        },
        shadowOpacity: 0.55,
        shadowRadius: 10.32,
        width: 100,
        height: 100,
        marginRight: 25,
        borderRadius: 10,
        marginBottom: 30,
        marginTop: 10,
        marginHorizontal: 20,
    },

    sidebar: {
        width: Dimensions.get('window').width * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //   width: 0,
        //   height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    circleIcon: {
        width: 30,
        height: 30,
        // resizeMode: 'contain',
        marginRight: 10,
        alignSelf: 'center',
    },
    headerLeftStyle: {
        width: Dimensions.get('window').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalView: {
        height: Dimensions.get('window').width * 0.37,
        width: Dimensions.get('window').width * 0.42,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: 20,
        paddingTop: 25,
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('window').width * 0.04,
        top: Dimensions.get('window').width * 0.04,

        elevation: 1000,
        zIndex: 1000,
    },

    addBtn: {
        backgroundColor: Colors.primaryColor,
        width: 45,
        height: 45,
        justifyContent: 'center',
        alignItems: 'center',
    },
});

const pickerStyle = StyleSheet.create({
    // RNPickerSelect Icon Style
    iconContainer: {
        top: 4,
        left: '85%',
        //flexDirection: 'row',
        elevation: 1,
        alignItems: 'center',
        borderWidth: 0,
    },

    // RNPickerSelect Style with
    inputAndroid: {
        fontFamily: 'NunitoSans-Bold',
        fontSize: 16,
        // borderWidth: 1,
        // borderColor: '#E5E5E5',
        // backgroundColor: Colors.fieldtBgColor,
        color: Colors.blackColor,
        // borderRadius: 5,
        // width: 200,
        height: 40,
        justifyContent: 'center',
        paddingHorizontal: 10,
        paddingVertical: 5,

        // shadowColor: '#000',
        // shadowOffset: {
        //   width: 0,
        //   height: 2,
        // },
        // shadowOpacity: 0.22,
        // shadowRadius: 3.22,
        // elevation: 1,
    },
    inputIOS: {
        fontFamily: 'NunitoSans-Bold',
        fontSize: 16,
        // borderWidth: 1,
        // borderColor: '#E5E5E5',
        // backgroundColor: Colors.fieldtBgColor,
        color: Colors.blackColor,
        // borderRadius: 5,
        // width: 200,
        height: 40,
        justifyContent: 'center',
        paddingHorizontal: 10,
        paddingVertical: 5,

        // shadowColor: '#000',
        // shadowOffset: {
        //   width: 0,
        //   height: 2,
        // },
        // shadowOpacity: 0.22,
        // shadowRadius: 3.22,
        // elevation: 1,
    },
});

export default HistoryScreen;
