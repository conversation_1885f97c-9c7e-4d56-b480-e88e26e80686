/*

const VIE_LANGUAGE = '\x1b\x74\x27'; //x27 => codePage VN in model printer, you can replace it
const CHINESE_OFF = '\x1c\x2e';
const SET_INTERNATIONAL_CHARACTER = '\x1b\x52\x12';

use cp858 encoding, and add it in EPToolkit.js :
var encoding_mappings_bytes = {
// single byte encodings
"CP437": Buffer.from([27, 116, 0]),
// multiple bit encodings
"GB18030": Buffer.from([28, 38, 28, 67, 0]),
"BIG5": Buffer.from([28, 38, 28, 67, 1]),
"UTF8": Buffer.from([28, 38, 28, 67, 255]),
"CP858": Buffer.from([27, 116, 19]), <-- Add this line
};

const encoding_mappings_bytes = {
    // single byte encodings
    "CP437": Buffer.from([27, 116, 0]),
    // multiple bit encodings
    "GB18030": Buffer.from([28, 38, 28, 67, 0]),
    "BIG5": Buffer.from([28, 38, 28, 67, 1]),
    "UTF8": Buffer.from([28, 38, 28, 67, 255]),
  };

  "GB18030": '\x1c\x26\x1c\x43\x00',
    "BIG5": '\x1c\x26\x1c\x43\x01',
    "UTF8": '\x1c\x26\x1c\x43\xFF',

var convertedText = iconv.encode("时顺地?abc地", 'gb18030'); //使用main.js中声明的全局常量iconv
    console.log(convertedText);</script>

// sound
ASCII      ESC   B     n   t
HEX        1B    42    n   t
Decimal    27    66    n   t
1<=n<=9,1<=t<=9

• "n" Refers to the number of buzzer times.
• "t" Refers to the buzzer beeps every few hours (t * 100) milliseconds.

*/

export const CODEPAGE = {
  AUTO: 'auto',
  CP936: 'cp936', // chinese
};

export const ESCPOS_CMD = {
  CP850: '\x1b\x74\x02',
  CP936: '\x1C\x21\x00',

  NEWLINE: '\x0a',
  LEFT: '\x1b\x61\x00',
  RIGHT: '\x1b\x61\x02',
  CENTER: '\x1b\x61\x01',

  BOLD_OFF: '\x1b\x45\x00',
  BOLD_ON: '\x1b\x45\x01',

  UNDERLINE_OFF: '\x1b\x2d\x00',
  UNDERLINE_ONE_DOT_ON: '\x1b\x2d\x01',
  UNDERLINE_TWO_DOT_ON: '\x1b\x2d\x02',

  CUT_PARTIAL: '\x1d\x56\x01',
  CUT_FULL: '\x1d\x56\x00',

  CUT_SUNMI: '\x1d\x56\x42\x00',

  SIZE_NORMAL: '\x1b\x21\x00',
  SIZE_2H: '\x1b\x21\x10',
  SIZE_2W: '\x1b\x21\x20',
  SIZE_2X: '\x1b\x21\x30',
  SIZE_5X: '\x1b\x21\x40',

  BARCODE_TXT_OFF: '\x1d\x48\x00',
  BARCODE_EAN13: '\x1d\x6b\x02',
  BARCODE_HEIGHT: '\x1d\x68\x64',
  BARCODE_WIDTH: '\x1d\x77\x03',

  BARCODE_FONT_A: '\x1d\x66\x00',
  BARCODE_FONT_B: '\x1d\x66\x01',

  CD_KICK_2: '\x1b\x70\x00', // Sends a pulse to pin 2 []
  CD_KICK_5: '\x1b\x70\x01', // Sends a pulse to pin 5 []

  HW_INIT: '\x1b\x40', // Clear data in buffer and reset modes
  HW_SELECT: '\x1b\x3d\x01', // Printer select

  BEEP: '\x1b\x42\x03\x02',
};

export const PRINTER_USAGE_TYPE = {
  RECEIPT: 'RECEIPT',
  KITCHEN_DOCKET: 'KITCHEN_DOCKET',
  ORDER_SUMMARY: 'ORDER_SUMMARY',
};

export const PRINTER_USAGE_TYPE_DROPDOWN_LIST = [
  {
    label: 'Receipt',
    value: PRINTER_USAGE_TYPE.RECEIPT,
  },
  {
    label: 'Kitchen',
    value: PRINTER_USAGE_TYPE.KITCHEN_DOCKET,
  },
  {
    label: 'Summary',
    value: PRINTER_USAGE_TYPE.ORDER_SUMMARY,
  },
];

export const PRINTER_PAPER_WIDTH = {
  _80MM: '_80MM',
  _76MM: '_76MM',

  _58MM: '_58MM',
};

export const PRINTER_PAPER_WIDTH_DROPDOWN_LIST = [
  {
    label: '80mm',
    value: '_80MM',
  },
  {
    label: '76mm',
    value: '_76MM',
  },

  {
    label: '58mm',
    value: '_58MM',
  },
];

export const PW_CONFIG_TYPE = {
  PUO_RECEIPT: 'PUO_RECEIPT',
};

export const PW_CONFIG = {
  '_80MM': {
    'PSR_SHIFT': {
      LINE_FULL_W: 48,

      FULL_W: 48,
      ITEM_W: 20,
      ITEM_W_RIGHT_PAD: 20,
      HALF_W: 24,
      HALF_W_RIGHT_PAD: 24,
      QUARTER_W: 12,
      T_QUARTER_W: 36,

      SPACE_16_W: 16,

      TOTAL_W: 12,
      TOTAL_W_RIGHT_PAD: 12,

      ITEM_NAME_SLICE_W: 34,
      ITEM_NAME_SLICE_MAX_W: 36,
      ITEM_QTY_W: 4,
      ITEM_AMOUNT_W: 8,
    },

    'PUO_RECEIPT': {
      LINE_FULL_W: 48,

      FULL_W: 48,
      ITEM_W: 20,
      ITEM_W_RIGHT_PAD: 20,
      ITEM_W_RECEIPT: 20,
      PRICE_W: 8,
      DISC_W: 8,
      SUB_W: 8,
      HALF_W: 24,
      HALF_W_RIGHT_PAD: 24,
      QUARTER_W: 12,
      T_QUARTER_W: 36,

      SPACE_16_W: 16,

      TOTAL_W: 12,
      TOTAL_W_RIGHT_PAD: 12,

      ITEM_NAME_SLICE_W: 42,
      ITEM_NAME_SLICE_MAX_W: 44,
      ITEM_QTY_W: 4,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2H,

      ITEM_FULL_W: 48,
    },

    'PUO_KITCHEN_DOCKET': {
      LINE_FULL_W: 48,

      FULL_W: 48,
      ITEM_W: 20,
      ITEM_W_RIGHT_PAD: 20,
      HALF_W: 24,
      HALF_W_RIGHT_PAD: 24,
      QUARTER_W: 12,
      T_QUARTER_W: 36,

      SPACE_16_W: 16,

      TOTAL_W: 12,
      TOTAL_W_RIGHT_PAD: 12,

      ITEM_NAME_SLICE_W: 42,
      ITEM_NAME_SLICE_MAX_W: 44,
      ITEM_QTY_W: 4,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2H,

      ITEM_FULL_W: 48,
    },

    'PUO_ORDER_SUMMARY': {
      LINE_FULL_W: 48,

      FULL_W: 48,
      ITEM_W: 20,
      ITEM_W_RIGHT_PAD: 20,
      HALF_W: 24,
      HALF_W_RIGHT_PAD: 24,
      QUARTER_W: 12,
      T_QUARTER_W: 36,

      SPACE_16_W: 16,

      TOTAL_W: 12,
      TOTAL_W_RIGHT_PAD: 12,

      ITEM_NAME_SLICE_W: 42,
      ITEM_NAME_SLICE_MAX_W: 44,
      ITEM_QTY_W: 4,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2H,

      ITEM_FULL_W: 48,
    },

    'PUO_KITCHEN_DOCKET_SMALL': {
      LINE_FULL_W: 48,

      FULL_W: 48,
      ITEM_W: 20,
      ITEM_W_RIGHT_PAD: 20,
      HALF_W: 24,
      HALF_W_RIGHT_PAD: 24,
      QUARTER_W: 12,
      T_QUARTER_W: 36,

      SPACE_16_W: 16,

      TOTAL_W: 12,
      TOTAL_W_RIGHT_PAD: 12,

      ITEM_NAME_SLICE_W: 42,
      ITEM_NAME_SLICE_MAX_W: 44,
      ITEM_QTY_W: 4,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_NORMAL,

      ITEM_FULL_W: 48,
    },

    'PUO_KITCHEN_DOCKET_LARGE': {
      LINE_FULL_W: 24,

      FULL_W: 48,
      ITEM_W: 20,
      ITEM_W_RIGHT_PAD: 20,
      HALF_W: 24,
      HALF_W_RIGHT_PAD: 24,
      QUARTER_W: 12,
      T_QUARTER_W: 36,

      SPACE_16_W: 16,

      TOTAL_W: 12,
      TOTAL_W_RIGHT_PAD: 12,

      ITEM_NAME_SLICE_W: 16,
      ITEM_NAME_SLICE_MAX_W: 18,
      ITEM_QTY_W: 6,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2W,

      ITEM_FULL_W: 24,
    },

    'PUO_KITCHEN_DOCKET_EXTRA_LARGE': {
      LINE_FULL_W: 24,

      FULL_W: 48,
      ITEM_W: 20,
      ITEM_W_RIGHT_PAD: 20,
      HALF_W: 24,
      HALF_W_RIGHT_PAD: 24,
      QUARTER_W: 12,
      T_QUARTER_W: 36,

      SPACE_16_W: 16,

      TOTAL_W: 12,
      TOTAL_W_RIGHT_PAD: 12,

      ITEM_NAME_SLICE_W: 16,
      ITEM_NAME_SLICE_MAX_W: 18,
      ITEM_QTY_W: 6,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2X,

      ITEM_FULL_W: 24,
    },
  },

  //////////////////////////////////////

  '_76MM': {
    'PSR_SHIFT': {
      LINE_FULL_W: 46,

      FULL_W: 46,
      ITEM_W: 18,
      ITEM_W_RIGHT_PAD: 20,
      HALF_W: 22,
      HALF_W_RIGHT_PAD: 24,
      QUARTER_W: 10,
      T_QUARTER_W: 34,

      SPACE_16_W: 16,

      TOTAL_W: 11,
      TOTAL_W_RIGHT_PAD: 12,

      ITEM_NAME_SLICE_W: 32,
      ITEM_NAME_SLICE_MAX_W: 34,
      ITEM_QTY_W: 4,
      ITEM_AMOUNT_W: 8,
    },

    'PUO_RECEIPT': {
      LINE_FULL_W: 46,

      FULL_W: 46,
      ITEM_W: 18,
      ITEM_W_RIGHT_PAD: 20,
      ITEM_W_RECEIPT: 18,
      PRICE_W: 8,
      DISC_W: 8,
      SUB_W: 8,
      HALF_W: 22,
      HALF_W_RIGHT_PAD: 24,
      QUARTER_W: 10,
      T_QUARTER_W: 34,

      SPACE_16_W: 16,

      TOTAL_W: 11,
      TOTAL_W_RIGHT_PAD: 12,

      ITEM_NAME_SLICE_W: 40,
      ITEM_NAME_SLICE_MAX_W: 42,
      ITEM_QTY_W: 4,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2H,

      ITEM_FULL_W: 46,
    },

    'PUO_KITCHEN_DOCKET': {
      LINE_FULL_W: 46,

      FULL_W: 46,
      ITEM_W: 18,
      ITEM_W_RIGHT_PAD: 20,
      HALF_W: 22,
      HALF_W_RIGHT_PAD: 24,
      QUARTER_W: 10,
      T_QUARTER_W: 34,

      SPACE_16_W: 16,

      TOTAL_W: 11,
      TOTAL_W_RIGHT_PAD: 12,

      ITEM_NAME_SLICE_W: 40,
      ITEM_NAME_SLICE_MAX_W: 42,
      ITEM_QTY_W: 4,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2H,

      ITEM_FULL_W: 46,
    },

    'PUO_ORDER_SUMMARY': {
      LINE_FULL_W: 46,

      FULL_W: 46,
      ITEM_W: 18,
      ITEM_W_RIGHT_PAD: 20,
      HALF_W: 22,
      HALF_W_RIGHT_PAD: 24,
      QUARTER_W: 10,
      T_QUARTER_W: 34,

      SPACE_16_W: 16,

      TOTAL_W: 11,
      TOTAL_W_RIGHT_PAD: 12,

      ITEM_NAME_SLICE_W: 40,
      ITEM_NAME_SLICE_MAX_W: 42,
      ITEM_QTY_W: 4,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2H,

      ITEM_FULL_W: 46,
    },

    'PUO_KITCHEN_DOCKET_SMALL': {
      LINE_FULL_W: 46,

      FULL_W: 46,
      ITEM_W: 18,
      ITEM_W_RIGHT_PAD: 20,
      HALF_W: 22,
      HALF_W_RIGHT_PAD: 24,
      QUARTER_W: 10,
      T_QUARTER_W: 34,

      SPACE_16_W: 16,

      TOTAL_W: 11,
      TOTAL_W_RIGHT_PAD: 12,

      ITEM_NAME_SLICE_W: 40,
      ITEM_NAME_SLICE_MAX_W: 42,
      ITEM_QTY_W: 4,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_NORMAL,

      ITEM_FULL_W: 46,
    },

    'PUO_KITCHEN_DOCKET_LARGE': {
      LINE_FULL_W: 23,

      FULL_W: 46,
      ITEM_W: 18,
      ITEM_W_RIGHT_PAD: 20,
      HALF_W: 22,
      HALF_W_RIGHT_PAD: 24,
      QUARTER_W: 10,
      T_QUARTER_W: 34,

      SPACE_16_W: 16,

      TOTAL_W: 11,
      TOTAL_W_RIGHT_PAD: 12,

      ITEM_NAME_SLICE_W: 14,
      ITEM_NAME_SLICE_MAX_W: 16,
      ITEM_QTY_W: 6,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2W,

      ITEM_FULL_W: 22,
    },

    'PUO_KITCHEN_DOCKET_EXTRA_LARGE': {
      LINE_FULL_W: 23,

      FULL_W: 46,
      ITEM_W: 18,
      ITEM_W_RIGHT_PAD: 20,
      HALF_W: 22,
      HALF_W_RIGHT_PAD: 24,
      QUARTER_W: 10,
      T_QUARTER_W: 34,

      SPACE_16_W: 16,

      TOTAL_W: 11,
      TOTAL_W_RIGHT_PAD: 12,

      ITEM_NAME_SLICE_W: 14,
      ITEM_NAME_SLICE_MAX_W: 16,
      ITEM_QTY_W: 6,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2X,

      ITEM_FULL_W: 22,
    },
  },

  //////////////////////////////////////

  // 2024-11-14 - 58mm paper width support
  // 2mm = 1 char

  '_58MM': {
    'PSR_SHIFT': {
      LINE_FULL_W: 32,

      FULL_W: 32,
      ITEM_W: 8, // 4
      ITEM_W_RIGHT_PAD: 16,
      HALF_W: 12, // 8
      HALF_W_RIGHT_PAD: 20,
      QUARTER_W: 0, // -4
      T_QUARTER_W: 20, // 20

      SPACE_16_W: 12,

      TOTAL_W: 6, // 4
      TOTAL_W_RIGHT_PAD: 10,

      ITEM_NAME_SLICE_W: 18,
      ITEM_NAME_SLICE_MAX_W: 20,
      ITEM_QTY_W: 4,
      ITEM_AMOUNT_W: 8,
    },

    'PUO_RECEIPT': {
      LINE_FULL_W: 32,

      FULL_W: 32,
      ITEM_W: 8, // 4
      ITEM_W_RIGHT_PAD: 16,
      ITEM_W_RECEIPT: 10, // use 2 more spaces that saved by PRICE_W, DISC_W, and SUB_W
      PRICE_W: 6,
      DISC_W: 6,
      SUB_W: 6,
      HALF_W: 12, // 8
      HALF_W_RIGHT_PAD: 20,
      QUARTER_W: 0, // -4
      T_QUARTER_W: 20, // 20

      SPACE_16_W: 12,

      TOTAL_W: 6, // 4
      TOTAL_W_RIGHT_PAD: 10,

      ITEM_NAME_SLICE_W: 26,
      ITEM_NAME_SLICE_MAX_W: 28,
      ITEM_QTY_W: 4,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2H,

      ITEM_FULL_W: 32,
    },

    'PUO_KITCHEN_DOCKET': {
      LINE_FULL_W: 32,

      FULL_W: 32,
      ITEM_W: 8, // 4
      ITEM_W_RIGHT_PAD: 16,
      HALF_W: 12, // 8
      HALF_W_RIGHT_PAD: 20,
      QUARTER_W: 0, // -4
      T_QUARTER_W: 20, // 20

      SPACE_16_W: 12,

      TOTAL_W: 6, // 4
      TOTAL_W_RIGHT_PAD: 10,

      ITEM_NAME_SLICE_W: 26,
      ITEM_NAME_SLICE_MAX_W: 28,
      ITEM_QTY_W: 4,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2H,

      ITEM_FULL_W: 32,
    },

    'PUO_ORDER_SUMMARY': {
      LINE_FULL_W: 32,

      FULL_W: 32,
      ITEM_W: 8, // 4
      ITEM_W_RIGHT_PAD: 16,
      HALF_W: 12, // 8
      HALF_W_RIGHT_PAD: 20,
      QUARTER_W: 0, // -4
      T_QUARTER_W: 20, // 20

      SPACE_16_W: 12,

      TOTAL_W: 6, // 4
      TOTAL_W_RIGHT_PAD: 10,

      ITEM_NAME_SLICE_W: 26,
      ITEM_NAME_SLICE_MAX_W: 28,
      ITEM_QTY_W: 4,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2H,

      ITEM_FULL_W: 32,
    },

    'PUO_KITCHEN_DOCKET_SMALL': {
      LINE_FULL_W: 32,

      FULL_W: 32,
      ITEM_W: 8, // 4
      ITEM_W_RIGHT_PAD: 16,
      HALF_W: 12, // 8
      HALF_W_RIGHT_PAD: 20,
      QUARTER_W: 0, // -4
      T_QUARTER_W: 20, // 20

      SPACE_16_W: 12,

      TOTAL_W: 6, // 4
      TOTAL_W_RIGHT_PAD: 10,

      ITEM_NAME_SLICE_W: 26,
      ITEM_NAME_SLICE_MAX_W: 28,
      ITEM_QTY_W: 4,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_NORMAL,

      ITEM_FULL_W: 32,
    },

    'PUO_KITCHEN_DOCKET_LARGE': {
      LINE_FULL_W: 16,

      FULL_W: 32,
      ITEM_W: 8, // 4
      ITEM_W_RIGHT_PAD: 16,
      HALF_W: 12, // 8
      HALF_W_RIGHT_PAD: 20,
      QUARTER_W: 0, // -4
      T_QUARTER_W: 20, // 20

      SPACE_16_W: 12,

      TOTAL_W: 6, // 4
      TOTAL_W_RIGHT_PAD: 10,

      ITEM_NAME_SLICE_W: 4, // 0
      ITEM_NAME_SLICE_MAX_W: 6, // 2
      ITEM_QTY_W: 6,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2W,

      ITEM_FULL_W: 8,
    },

    'PUO_KITCHEN_DOCKET_EXTRA_LARGE': {
      LINE_FULL_W: 16,

      FULL_W: 32,
      ITEM_W: 8, // 4
      ITEM_W_RIGHT_PAD: 16,
      HALF_W: 12, // 8
      HALF_W_RIGHT_PAD: 20,
      QUARTER_W: 0, // -4
      T_QUARTER_W: 20, // 20

      SPACE_16_W: 12,

      TOTAL_W: 6, // 4
      TOTAL_W_RIGHT_PAD: 10,

      ITEM_NAME_SLICE_W: 4, // 0
      ITEM_NAME_SLICE_MAX_W: 6, // 2
      ITEM_QTY_W: 6,
      ITEM_FONT_SIZE: ESCPOS_CMD.SIZE_2X,

      ITEM_FULL_W: 8,
    },
  },
};

export const PRINTER_TASK_TYPE = {
  PRINT_SHIFT_REPORT: 'PRINT_SHIFT_REPORT',
  OPEN_CASH_DRAWER: 'OPEN_CASH_DRAWER',

  PRINT_USER_ORDER_RECEIPT: 'PRINT_USER_ORDER_RECEIPT',
  PRINT_USER_ORDER_KITCHEN_DOCKET: 'PRINT_USER_ORDER_KITCHEN_DOCKET',
  PRINT_USER_ORDER_ORDER_SUMMARY: 'PRINT_USER_ORDER_ORDER_SUMMARY',

  PRINT_TABLE_QR: 'PRINT_TABLE_QR',
  PRINT_DOCKET: 'PRINT_DOCKET',
  PRINT_DOCKET_FOR_KD: 'PRINT_DOCKET_FOR_KD',
  PRINT_KD_SUMMARY_CATEGORY: 'PRINT_KD_SUMMARY_CATEGORY',

  PRINT_CATEGORY_AND_PRODUCT_SALES_REPORT: 'PRINT_CATEGORY_AND_PRODUCT_SALES_REPORT',

  PRINT_DOCKET_ITEM: 'PRINT_DOCKET_ITEM',
  PRINT_DOCKET_FOR_KD_ITEM: 'PRINT_DOCKET_FOR_KD_ITEM',

  PRINT_TABLE_OS: 'PRINT_TABLE_OS',
};

export const PRINTER_TASK_TYPE_PARSED = {
  PRINT_SHIFT_REPORT: 'Print Shift Report',
  OPEN_CASH_DRAWER: 'Open Cash Drawer',

  PRINT_USER_ORDER_RECEIPT: 'Print Receipt',
  PRINT_USER_ORDER_KITCHEN_DOCKET: 'Print Kitchen Docket',
  PRINT_USER_ORDER_ORDER_SUMMARY: 'Print Order Summary',

  PRINT_TABLE_QR: 'Print Table QR',
  PRINT_DOCKET: 'Print Docket',
  PRINT_DOCKET_FOR_KD: 'Print Kitchen Docket (Individual)',
  PRINT_KD_SUMMARY_CATEGORY: 'Print Kitchen Docket (Summary Category)',

  PRINT_CATEGORY_AND_PRODUCT_SALES_REPORT: 'Print Category and Product Sales Report',

  PRINT_DOCKET_ITEM: 'Print Docket (Item)',
  PRINT_DOCKET_FOR_KD_ITEM: 'Print Kitchen Docket (Item)',

  PRINT_TABLE_OS: 'Print Table Order Summary',
};

export const KD_OPTIONS_DELIVER_REJECT = {
  AFFECTED_ITEMS_ONLY: 'AFFECTED_ITEMS_ONLY',
  DELIVERED_REJECTED_ITEMS: 'DELIVERED_REJECTED_ITEMS',
};

export const KD_OPTIONS_DELIVER_REJECT_DROPDOWN_LIST = [
  {
    label: 'Print Affected Items Only',
    value: 'AFFECTED_ITEMS_ONLY',
  },
  {
    label: 'Print Delivered/Rejected Items',
    value: 'DELIVERED_REJECTED_ITEMS',
  },
];

export const PRINTER_DEVICE_TYPE = {
  LAN: 'LAN',
  SUNMI: 'SUNMI',
  LABEL: 'LABEL',
  IMIN: 'IMIN',
  BLE: 'BLE',
  USB: 'USB',
};

export const PRINTER_COMMAND_TYPE = {
  ESCPOS: 'ESCPOS',
  TSCPOS: 'TSCPOS',
};

export const PRINTER_COMMAND_TYPE_DROPDOWN_LIST = [
  {
    label: 'ESC/POS',
    value: 'ESCPOS',
  },
  {
    label: 'TSC/POS',
    value: 'TSCPOS',
  },
];

