import React, { Component, useReducer, useState, useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  TouchableOpacity,
  Dimensions,
  Modal,
  Platform,
  ActivityIndicator,
  useWindowDimensions,
  Picker,
} from "react-native";
import Colors from "../constant/Colors";
import SideBar from "./SideBar";
import Icon from "react-native-vector-icons/Feather";
import AntDesign from "react-native-vector-icons/AntDesign";
import Ionicon from "react-native-vector-icons/Ionicons";
import SimpleLineIcons from "react-native-vector-icons/SimpleLineIcons";
import { TextInput, FlatList } from "react-native-gesture-handler";
// import DropDownPicker from 'react-native-dropdown-picker';
// import DateTimePickerModal from 'react-native-modal-datetime-picker';
import API from "../constant/API";
import ApiClient from "../util/ApiClient";
// import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
// import DialogInput from 'react-native-dialog-input';
import * as User from "../util/User";
import LoginScreen from "./LoginScreen";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { OutletStore } from "../store/outletStore";
// import CheckBox from 'react-native-check-box';
import { color } from "react-native-reanimated";
import Close from "react-native-vector-icons/AntDesign";
// import NumericInput from 'react-native-numeric-input';
import Styles from "../constant/Styles";
import moment, { isDate } from "moment";
// import Barcode from "react-native-barcode-builder";
// import Switch from 'react-native-switch-pro';
import Ionicons from "react-native-vector-icons/Ionicons";
// import { isTablet } from 'react-native-device-detection';
import { UserStore } from "../store/userStore";
import { MerchantStore } from "../store/merchantStore";
// import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
// import RNPickerSelect from 'react-native-picker-select';
// import { useKeyboard } from '../hooks';
import { CommonStore } from "../store/commonStore";
import FontAwesome5 from "react-native-vector-icons/FontAwesome5";
import { useFilePicker } from "use-file-picker";
import AsyncImage from "../components/asyncImage";
import { ReactComponent as Edit } from "../assets/svg/Edit.svg";
import {
  sliceUnicodeStringV2WithDots,
  getTransformForScreenInsideNavigation,
} from "../util/common";
import Select from "react-select";
import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import receiptlogo from "../assets/image/logo.png";
import DropDownPicker from "react-native-dropdown-picker";
import { EI_ID_DROPDOWN_LIST, EI_ID_TYPE, EP_STATE_DROPDOWN_LIST } from "../constant/einvoice";
import APILocal from "../util/apiLocalReplacers";
import { v4 as uuidv4 } from "uuid";

const SettingsWhatsappScreen = (props) => {
  const { navigation } = props;

  const { height: windowHeight, width: windowWidth } = useWindowDimensions();

  // const [keyboardHeight] = useKeyboard();
  // copied from settingsreceiptscreen

  const [loading, setLoading] = useState(false);

  const [switchMerchant, setSwitchMerchant] = useState(false);

  const [outlet, setOutlet] = useState([]);
  const [merInfo, setMerInfo] = useState([]);
  const [merchantId, setMerchantId] = useState([]);

  const [logo, setLogo] = useState("");
  const [cover, setCover] = useState("");
  const [name, setName] = useState("");
  const [tname, setTname] = useState("");
  const [rate, setRate] = useState("");
  const [address, setAddress] = useState("");
  const [phone, setPhone] = useState("");

  const [myTextInput, setMyTextInput] = useState(React.createRef());
  const [showNote, setShowNote] = useState(false);
  const [expandView, setExpandView] = useState(false);
  const [value, setValue] = useState("");
  const [extendOption, setExtendOption] = useState([
    { optionId: 1, price: 20, day: 7, days: false },
  ]);

  const [taxList, setTaxList] = useState([]);
  const [note1, setNote1] = useState("");
  const [note2, setNote2] = useState("");
  const [note3, setNote3] = useState("");

  const [addressSecond, setAddressSecond] = useState("");
  const [merchantLogoImage, setMerchantLogoImage] = useState("");
  const [merchantLogoImageType, setMerchantLogoImageType] = useState("");
  const [isMerchantLogoImageChanged, setIsMerchantLogoImageChanged] =
    useState(false);

  ////////////////////////////////////////////////////////////////

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState("");

  const allOutlets = MerchantStore.useState((s) => s.allOutlets);

  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const currOutletId = MerchantStore.useState((s) => s.currOutletId);

  const isLoading = CommonStore.useState((s) => s.isLoading);

  //2024-06-14 ()
  const [eiRegName, setEiRegName] = useState('');
  const [eiPhone, setEiPhone] = useState('');
  const [epStateFrom, setEpStateFrom] = useState('sgr');
  const [epAddr1From, setEpAddr1From] = useState('');
  const [epCityFrom, setEpCityFrom] = useState('');
  const [epCodeFrom, setEpCodeFrom] = useState('');
  const [eiEmail, setEiEmail] = useState('');
  const [eiTin, setEiTin] = useState('');
  const [eiId, setEiId] = useState('');
  const [eiIdType, setEiIdType] = useState(EI_ID_TYPE.NRIC);

  const [openDpEiIdType, setOpenDpEiIdType] = useState(false);
  const [openDpEpStateFrom, setOpenDpEpStateFrom] = useState(false);

  const idTypeOption = [
    {
      label: 'NRIC',
      value: 'nric',
    },
    {
      label: 'Passport',
      value: 'passport',
    },
    {
      label: 'MyTentera',
      value: 'mytentera',
    },


  ];

  const statesOption = [
    {
      label: 'Johor',
      value: 'jhr',
    },
    {
      label: 'Kedah',
      value: 'kdh',
    },
    {
      label: 'Kelantan',
      value: 'ktn',
    },
    {
      label: 'Melaka',
      value: 'mlk',
    },
    {
      label: 'Negeri Sembilan',
      value: 'nsn',
    },
    {
      label: 'Pahang',
      value: 'phg',
    },
    {
      label: 'Perak',
      value: 'prk',
    },
    {
      label: 'Perlis',
      value: 'pls',
    },
    {
      label: 'Pulau Pinang',
      value: 'png',
    },
    {
      label: 'Selangor',
      value: 'sgr',
    },
    {
      label: 'Terengganu',
      value: 'trg',
    },
    {
      label: 'Kuala Lumpur',
      value: 'kul',
    },
    {
      label: 'Putra Jaya',
      value: 'pjy',
    },
    {
      label: 'Sarawak',
      value: 'srw',
    },
    {
      label: 'Sabah',
      value: 'sbh',
    },
    {
      label: 'Labuan',
      value: 'lbn',
    },
  ];

  const merchantLogo = MerchantStore.useState((s) => s.logo);
  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView
  );

  useEffect(() => {
    setLogo(merchantLogo);
  }, [merchantLogo]);

  useEffect(() => {
    setName(merchantName);
  }, [merchantName]);

  useEffect(() => {
    const selectedTargetOutlet = allOutlets.find(
      (outlet) => outlet.uniqueId === selectedTargetOutletId
    );

    if (selectedTargetOutlet) {
      setPhone(selectedTargetOutlet.phone);
      setAddress(selectedTargetOutlet.address);
    }
  }, [selectedTargetOutletId]);

  useEffect(() => {
    setTargetOutletDropdownList(
      allOutlets.map((outlet) => ({
        label: outlet.name,
        value: outlet.uniqueId,
      }))
    );

    if (selectedTargetOutletId === "" && allOutlets.length > 0) {
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
    }
  }, [allOutlets]);

  ////////////////////////////////

  // 2024-06-18 - e-invoice changes

  useEffect(() => {
    if (currOutlet && currOutlet.uniqueId) {
      setEiRegName(currOutlet.eiRegName ? currOutlet.eiRegName : currOutlet.name);
      setEiPhone(currOutlet.eiPhone ? currOutlet.eiPhone : currOutlet.phone);
      setEpStateFrom(currOutlet.epStateFrom ? currOutlet.epStateFrom : 'sgr');
      setEpAddr1From(currOutlet.epAddr1From ? currOutlet.epAddr1From : '');
      setEpCityFrom(currOutlet.epCityFrom ? currOutlet.epCityFrom : '');
      setEpCodeFrom(currOutlet.epCodeFrom ? currOutlet.epCodeFrom : '');
      setEiEmail(currOutlet.eiEmail ? currOutlet.eiEmail : '');
      setEiTin(currOutlet.eiTin ? currOutlet.eiTin : '');
      setEiId(currOutlet.eiId ? currOutlet.eiId : '');
      setEiIdType(currOutlet.eiIdType ? currOutlet.eiIdType : EI_ID_TYPE.NRIC);
    }
  }, [currOutlet]);

  ////////////////////////////////

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false,
  // });

  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus
  );
  // const [outletDropdownList, setOutletDropdownList] = useState([]);
  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  const [openOutlet, setOpenOutlet] = useState(false);

  var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));



  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
        style={{
          width: 124,
          height: 26,
        }}
        resizeMode="contain"
        source={require('../assets/image/logo.png')}
      /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          WhatsApp Settings
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo BackOffice")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
            style={{
              width: windowHeight * 0.05,
            height: windowHeight * 0.05,
              alignSelf: 'center',
            }}
            source={require('../assets/image/profile-pic.jpg')}
          /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  const [
    openFileSelector,
    {
      plainFiles,
      filesContent,
      loading: loadingImageInput,
      clear: clearImageContainer,
      errors,
    },
  ] = useFilePicker({
    readAs: "DataURL",
    accept: "image/*",
    multiple: false,
  });

  useEffect(() => {
    console.log(plainFiles, filesContent, loadingImageInput);

    // display the image when it is finish loaded
    if (plainFiles.length && filesContent.length && !loadingImageInput) {
      // image in base64 filecontent
      setMerchantLogoImage(filesContent[0].content);
      setMerchantLogoImageType(
        filesContent[0].name.slice(filesContent[0].name.lastIndexOf("."))
      );
      setIsMerchantLogoImageChanged(true);
    }

    if (errors.length) console.error(errors);
  }, [plainFiles, filesContent, loadingImageInput, errors]);

  const _logout = async () => {
    await AsyncStorage.clear();
    User.setlogin(false);
    User.getRefreshMainScreen();
  };

  const addSection = () => {
    // setState({ showNote: true });
    setShowNote(true);
  };

  const updateEInvoiceDetails = async (param) => {
    if (eiPhone && !eiPhone.startsWith('60')) {
      window.confirm('Info\n\nInvalid phone number.');
      return;
    }

    if (eiEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(eiEmail)) {
      window.confirm('Info\n\nInvalid email format.');
      return;
    }

    if (eiIdType === 'NRIC' && eiId && !/^\d{12}$/.test(eiId)) {
      window.confirm('Info\n\nInvalid NRIC format.');
      return;
    }

    if (eiIdType === 'PASSPORT' && eiId && !/^[A-Za-z0-9]{9,11}$/.test(eiId)) {
      window.confirm('Info\n\nInvalid Passport format.');
      return;
    }

    if (eiIdType === 'ARMY' && eiId && !eiId.startsWith('T')) {
      window.confirm('Info\n\nInvalid MyTentera format.');
      return;
    }

    if (eiRegName && eiPhone && epAddr1From && epCityFrom && epCodeFrom && epStateFrom &&
      // selectedEpRate &&
      // selectedEpRate.service_id &&
      eiIdType && eiId && eiTin && eiEmail) {
      // can proceed
    }
    else {
      window.confirm('Info\n\nPlease fill in all the details before proceed.');
      return;
    }

    setLoading(true);

    var body = {
      outletId: currOutletId,

      eiRegName,
      eiPhone,

      epAddr1From: epAddr1From,
      epCityFrom: epCityFrom,
      epCodeFrom: epCodeFrom,
      epStateFrom: epStateFrom,

      eiEmail: eiEmail,
      eiTin: eiTin,
      eiIdType: eiIdType,
      eiId: eiId,
    };

    // console.log('body', body);

    APILocal.updateEInvoiceDetails({ body }).then((result) => { });
    if (window.confirm("Success\n\ne-Invoice details has been updated.") == true) {
      setLoading(false);
    }
  };

  ////////////////////////////////////////////////////////////////////////////////////////////////

  // 2024-09-10 - whatsapp integration

  const openFacebookOAuth = () => {
    const clientId = process.env.META_KD_MESSENGING_APP_ID; // Replace with your Facebook App ID
    const redirectUri = `${process.env.META_KD_REDIRECT_BASE_URI}/${API.waOauthRedirectCallback}`; // Your backend callback URL
    const state = uuidv4(); // Any state string for CSRF protection
    const scope = 'business_management,whatsapp_business_management,whatsapp_business_messaging';

    // Construct the OAuth URL
    const facebookOAuthUrl = `https://www.facebook.com/v17.0/dialog/oauth?client_id=${clientId}&redirect_uri=${redirectUri}&state=${state}&scope=${scope}`;

    // Open the Facebook login in a popup window
    const popup = window.open(
      facebookOAuthUrl,
      'facebookOAuth',
      'width=600,height=700'
    );

    // Set up an event listener to capture the Access Token once the popup is closed
    window.addEventListener('message', (event) => {
      // Check if the event is from our domain and the popup has sent the Access Token
      if (event.origin === window.location.origin && event.data.accessToken) {
        console.log('Access Token received:', event.data.accessToken);
        // Use the Access Token to make Graph API calls
      }
    });
  };

  ////////////////////////////////////////////////////////////////////////////////////////////////

  // function end

  return (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>
    <View
      style={[
        styles.container,
        {
          height: windowHeight,
          width: windowWidth,
          ...getTransformForScreenInsideNavigation(),
        },
      ]}
    >
      {/* Sidebar view */}
      <View style={{ flex: 0.8 }}>
        <SideBar navigation={navigation} selectedTab={8} />
      </View>

      <View style={{ height: windowHeight, flex: 8 }}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ width: windowWidth * 0.9 }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}
        >
          {/* <ScrollView horizontal={true} showsHorizontalScrollIndicator={true}> */}
          <View
            style={{
              paddingVertical: 30,
              marginHorizontal: 30,
            }}>
            <View
              style={{
                backgroundColor: Colors.whiteColor,
                width: windowWidth * 0.877,
                // height: windowHeight * 0.7,
                marginTop: 10,
                marginBottom: 30,
                marginHorizontal: 30,
                alignSelf: "center",
                borderRadius: 5,
                shadowOpacity: 0,
                shadowColor: "#000",
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,
              }}
            >
              <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={
                  {
                    //top: Platform.OS === 'ios' ? -keyboardHeight * 0.3 : 0,
                  }
                }
              >


                <View style={{ marginHorizontal: '10%', marginVertical: '5%' }}>
                  <ScrollView>
                    <View style={{
                      alignItems: "center",
                      marginBottom: 10
                    }}>
                      <Text style={{
                        fontFamily: "NunitoSans-Regular", fontSize: switchMerchant ? 10 : 26,
                      }}>
                        WhatsApp Settings
                      </Text>
                    </View>

                    <View style={{
                      marginVertical: '3%'
                    }}>

                      {/* Name & Phone Number */}
                      <View style={{ flexDirection: "row", alignItems: "center", justifyContent: 'center' }}>

                        <View style={{ flexDirection: "row", alignItems: 'center', width: '45%' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '30%'
                          }}>
                            Status
                          </Text>

                          <TouchableOpacity
                            style={{
                              marginTop: 10,
                              justifyContent: "center",
                              flexDirection: "row",
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: "#4E9F7D",
                              borderRadius: 5,
                              width: 120,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: "center",
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}
                            disabled={loading}
                            onPress={() => {
                              // editOutlet(outlet.id);
                              // setNote1(text);
                              // setNote2(note1);

                              updateEInvoiceDetails();
                            }}
                          >
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                //marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: "NunitoSans-Bold",
                              }}
                            >
                              {loading ? "LOADING..." : "AUTHORIZE"}
                            </Text>
                          </TouchableOpacity>
                        </View>

                        <View style={{ width: '10%' }} />

                        <View style={{ flexDirection: "row", alignItems: 'center', width: '45%', opacity: 0, }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '30%',

                          }}>
                            Phone Number
                          </Text>
                          <TextInput
                            underlineColorAndroid={Colors.fieldtBgColor}
                            style={[
                              styles.textInput3,
                              { fontSize: switchMerchant ? 10 : 14 },
                            ]}
                            placeholder={
                              outlet == null
                                ? "6016123456"
                                : "6012345679"
                            }
                            placeholderTextColor={Platform.select({
                              ios: "#a9a9a9",
                            })}
                            keyboardType={
                              Platform.OS === "android"
                                ? "numeric"
                                : "decimal-pad"
                            }
                            onChangeText={(text) => {
                              // setState({ phone: text });
                              setEiPhone(text);
                            }}
                            value={eiPhone}
                          // ref={myTextInput}
                          />
                        </View>

                      </View>

                      {/* Email Address & TIN */}
                      <View style={{ flexDirection: "row", alignItems: "center", display: 'none', }}>

                        <View style={{ flexDirection: "row", alignItems: 'center', width: '45%' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '30%'
                          }}>
                            Email Address
                          </Text>
                          <TextInput
                            underlineColorAndroid={Colors.fieldtBgColor}
                            style={[
                              styles.textInput3,
                              { fontSize: switchMerchant ? 10 : 14 },
                            ]}
                            placeholder={
                              "<EMAIL>"
                            }
                            placeholderStyle={{
                              fontFamily: "NunitoSans-Regular",
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                            placeholderTextColor={Platform.select({
                              ios: "#a9a9a9",
                            })}
                            onChangeText={(text) => {
                              // setState({ address: text });
                              setEiEmail(text);
                            }}
                            value={eiEmail}
                          // ref={myTextInput}
                          />
                        </View>

                        <View style={{ width: '10%' }} />

                        <View style={{ flexDirection: "row", alignItems: 'center', width: '45%' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '30%',
                          }}>
                            TIN
                          </Text>
                          <TextInput
                            underlineColorAndroid={Colors.fieldtBgColor}
                            style={[
                              styles.textInput3,
                              { fontSize: switchMerchant ? 10 : 14 },
                            ]}
                            placeholder="AAAB12345678901"
                            placeholderStyle={{
                              fontFamily: "NunitoSans-Regular",
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                            placeholderTextColor={Platform.select({
                              ios: "#a9a9a9",
                            })}
                            onChangeText={(text) => {
                              // setState({ name: text });
                              setEiTin(text);
                            }}
                            value={eiTin}
                          />
                        </View>

                      </View>

                      {/* ID Type and ID */}
                      <View style={{ flexDirection: "row", alignItems: "center", zIndex: 1, display: 'none', }}>

                        <View style={{ flexDirection: "row", alignItems: 'center', width: '45%' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '30%'
                          }}>
                            ID Type
                          </Text>

                          <View style={{
                            zIndex: 1000,
                            width: '50%'
                          }}>
                            <DropDownPicker
                              style={{
                                backgroundColor: Colors.fieldtBgColor,
                                height: 45,
                                borderRadius: 10,
                                flexDirection: "row",
                                alignItems: "center"
                              }}
                              dropDownContainerStyle={{
                                backgroundColor: Colors.fieldtBgColor,
                                borderColor: "#E5E5E5",
                              }}
                              labelStyle={{
                                marginLeft: 10,
                                flexDirection: "row",
                              }}
                              textStyle={{
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Regular',
                                marginLeft: 10,
                                paddingVertical: 10,
                                flexDirection: "row",
                              }}
                              selectedItemContainerStyle={{
                                flexDirection: "row",
                              }}

                              showArrowIcon={true}
                              ArrowDownIconComponent={({ style }) => (
                                <Ionicon
                                  size={25}
                                  color={Colors.fieldtTxtColor}
                                  style={{ paddingHorizontal: 5, marginTop: 5 }}
                                  name="chevron-down-outline"
                                />
                              )}
                              ArrowUpIconComponent={({ style }) => (
                                <Ionicon
                                  size={25}
                                  color={Colors.fieldtTxtColor}
                                  style={{ paddingHorizontal: 5, marginTop: 5 }}
                                  name="chevron-up-outline"
                                />
                              )}

                              showTickIcon={true}
                              TickIconComponent={({ press }) => (
                                <Ionicon
                                  style={{ paddingHorizontal: 5, marginTop: 5 }}
                                  color={
                                    press ? Colors.fieldtBgColor : Colors.primaryColor
                                  }
                                  name={'md-checkbox'}
                                  size={25}
                                />
                              )}
                              placeholderStyle={{
                                color: Colors.fieldtTxtColor,
                                marginLeft: 10, // Adjust this value as needed
                              }}
                              dropDownDirection="BOTTOM"
                              onSelectItem={(item) => {
                                if (item) {
                                  setEiIdType(item.value);
                                }
                              }}
                              value={eiIdType}
                              items={EI_ID_DROPDOWN_LIST}
                              // multiple={true}
                              // multipleText={`${scOrderTypes.length} type(s) selected`}
                              open={openDpEiIdType}
                              setOpen={setOpenDpEiIdType}
                            />
                          </View>
                        </View>

                        <View style={{ width: '10%' }} />

                        <View style={{ flexDirection: "row", alignItems: 'center', width: '45%' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '30%',
                          }}>
                            ID
                          </Text>
                          <TextInput
                            underlineColorAndroid={Colors.fieldtBgColor}
                            style={[
                              styles.textInput3,
                              { fontSize: switchMerchant ? 10 : 14 },
                            ]}
                            placeholder="0010101020303"
                            placeholderStyle={{
                              fontFamily: "NunitoSans-Regular",
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                            placeholderTextColor={Platform.select({
                              ios: "#a9a9a9",
                            })}
                            onChangeText={(text) => {
                              // setState({ name: text });
                              setEiId(text);
                            }}
                            value={eiId}
                          />
                        </View>

                      </View>

                      {/* Address & Postcode */}
                      <View style={{ flexDirection: "row", alignItems: "center", display: 'none', }}>

                        <View style={{ flexDirection: "row", alignItems: 'center', width: '45%' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '30%'
                          }}>
                            Street
                          </Text>

                          <TextInput
                            underlineColorAndroid={Colors.fieldtBgColor}
                            style={[
                              styles.textInput3,
                              { fontSize: switchMerchant ? 10 : 14 },
                            ]}
                            placeholder="Lot 1210, Jalan Lapangan Terbang "
                            placeholderStyle={{
                              fontFamily: "NunitoSans-Regular",
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                            placeholderTextColor={Platform.select({
                              ios: "#a9a9a9",
                            })}
                            onChangeText={(text) => {
                              // setState({ name: text });
                              setEpAddr1From(text);
                            }}
                            value={epAddr1From}
                          />
                        </View>

                        <View style={{ width: '10%' }} />

                        <View style={{ flexDirection: "row", alignItems: 'center', width: '45%' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '30%',
                          }}>
                            Postcode
                          </Text>

                          <TextInput
                            underlineColorAndroid={Colors.fieldtBgColor}
                            style={[
                              styles.textInput3,
                              { fontSize: switchMerchant ? 10 : 14 },
                            ]}
                            placeholder="40150"
                            placeholderStyle={{
                              fontFamily: "NunitoSans-Regular",
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                            placeholderTextColor={Platform.select({
                              ios: "#a9a9a9",
                            })}
                            onChangeText={(text) => {
                              // setState({ name: text });
                              setEpCodeFrom(text);
                            }}
                            value={epCodeFrom}
                          />
                        </View>

                      </View>

                      {/* City & State */}
                      <View style={{ flexDirection: "row", alignItems: "center", display: 'none', }}>

                        <View style={{ flexDirection: "row", alignItems: 'center', width: '45%' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '30%'
                          }}>
                            City
                          </Text>
                          <TextInput
                            underlineColorAndroid={Colors.fieldtBgColor}
                            style={[
                              styles.textInput3,
                              { fontSize: switchMerchant ? 10 : 14 },
                            ]}
                            placeholder="Shah Alam"
                            placeholderStyle={{
                              fontFamily: "NunitoSans-Regular",
                              fontSize: switchMerchant ? 10 : 14,
                            }}
                            placeholderTextColor={Platform.select({
                              ios: "#a9a9a9",
                            })}
                            onChangeText={(text) => {
                              // setState({ name: text });
                              setEpCityFrom(text);
                            }}
                            value={epCityFrom}
                          />
                        </View>

                        <View style={{ width: '10%' }} />

                        <View style={{ flexDirection: "row", alignItems: 'center', width: '45%' }}>
                          <Text style={{
                            fontSize: switchMerchant ? 10 : 16,
                            width: '30%',
                          }}>
                            State
                          </Text>

                          <View style={{
                            zIndex: 1000,
                            width: '50%'
                          }}>
                            <DropDownPicker
                              style={{
                                backgroundColor: Colors.fieldtBgColor,
                                height: 47,
                                borderRadius: 10,

                                flexDirection: "row",
                                alignItems: "center"
                              }}
                              dropDownContainerStyle={{
                                backgroundColor: Colors.fieldtBgColor,
                                borderColor: "#E5E5E5",
                              }}
                              labelStyle={{
                                marginLeft: 10,
                                flexDirection: "row",
                              }}
                              textStyle={{
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Regular',

                                marginLeft: 10,
                                paddingVertical: 10,
                                flexDirection: "row",
                              }}
                              selectedItemContainerStyle={{
                                flexDirection: "row",
                              }}

                              showArrowIcon={true}
                              ArrowDownIconComponent={({ style }) => (
                                <Ionicon
                                  size={25}
                                  color={Colors.fieldtTxtColor}
                                  style={{ paddingHorizontal: 5, marginTop: 5 }}
                                  name="chevron-down-outline"
                                />
                              )}
                              ArrowUpIconComponent={({ style }) => (
                                <Ionicon
                                  size={25}
                                  color={Colors.fieldtTxtColor}
                                  style={{ paddingHorizontal: 5, marginTop: 5 }}
                                  name="chevron-up-outline"
                                />
                              )}

                              showTickIcon={true}
                              TickIconComponent={({ press }) => (
                                <Ionicon
                                  style={{ paddingHorizontal: 5, marginTop: 5 }}
                                  color={
                                    press ? Colors.fieldtBgColor : Colors.primaryColor
                                  }
                                  name={'md-checkbox'}
                                  size={25}
                                />
                              )}
                              placeholderStyle={{
                                color: Colors.fieldtTxtColor,
                                marginLeft: 10,
                              }}
                              dropDownDirection="BOTTOM"
                              onSelectItem={(item) => {
                                if (item) {
                                  setEpStateFrom(item.value);
                                }
                              }}
                              value={epStateFrom}
                              items={EP_STATE_DROPDOWN_LIST}
                              // multiple={true}
                              // multipleText={`${scOrderTypes.length} type(s) selected`}
                              open={openDpEpStateFrom}
                              setOpen={setOpenDpEpStateFrom}
                            />
                          </View>
                        </View>

                      </View>

                    </View>

                    <View style={{ alignItems: "center", display: 'none', }}>
                      <TouchableOpacity
                        style={{
                          marginTop: 10,
                          justifyContent: "center",
                          flexDirection: "row",
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: "#4E9F7D",
                          borderRadius: 5,
                          width: 120,
                          paddingHorizontal: 10,
                          height: switchMerchant ? 35 : 40,
                          alignItems: "center",
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        disabled={loading}
                        onPress={() => {
                          // editOutlet(outlet.id);
                          // setNote1(text);
                          // setNote2(note1);

                          updateEInvoiceDetails();
                        }}
                      >
                        <Text
                          style={{
                            color: Colors.whiteColor,
                            //marginLeft: 5,
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: "NunitoSans-Bold",
                          }}
                        >
                          {loading ? "LOADING..." : "SAVE"}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </ScrollView>
                </View>

              </ScrollView>
            </View>
          </View>
          {/* </ScrollView> */}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: "row",
  },
  iosStyle: {
    paddingHorizontal: Platform.OS !== "ios" ? 0 : 30,
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  headerLogo1: {
    width: "100%",
    height: "100%",
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: "row",
    alignItems: "center",
  },
  listItem: {
    fontFamily: "NunitoSans-Regular",
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    //width: windowWidth * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.highlightColor,
  },
  textInput: {
    fontFamily: "NunitoSans-Regular",
    width: 300,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: "row",
  },
  textInputLocation: {
    fontFamily: "NunitoSans-Regular",
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textInput8: {
    fontFamily: "NunitoSans-Regular",
    width: 75,
    height: 50,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
  },
  textInput9: {
    fontFamily: "NunitoSans-Regular",
    width: 110,
    height: Platform.OS == "ios" ? 30 : 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: "row",
    justifyContent: "center",
  },
  textInput10: {
    fontFamily: "NunitoSans-Regular",
    width: 200,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: "row",
    justifyContent: "center",
  },
  textInput1: {
    fontFamily: "NunitoSans-Regular",
    width: 250,
    height: 40,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 10,
  },
  textInput2: {
    fontFamily: "NunitoSans-Regular",
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginRight: 30,
  },
  textInput3: {
    fontFamily: "NunitoSans-Regular",
    width: "70%",
    height: 45,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
    paddingHorizontal: 10,
  },
  textInputPhone: {
    fontFamily: "NunitoSans-Regular",
    width: Platform.OS === "ios" ? "100%" : "85%",
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
    alignSelf: "center",
    paddingHorizontal: 10,
  },
  textInput4: {
    width: "85%",
    height: 70,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
  },
  textInput5: {
    fontFamily: "NunitoSans-Regular",
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
  },
  textInput6: {
    fontFamily: "NunitoSans-Regular",
    width: "80 %",
    padding: 16,
    backgroundColor: Colors.fieldtBgColor,
    marginRight: 30,
    borderRadius: 10,
    alignSelf: "center",
  },
  textInput7: {
    fontFamily: "NunitoSans-Regular",
    width: 300,
    height: 80,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 7,
    marginRight: 30,
  },
  button: {
    backgroundColor: Colors.primaryColor,
    width: 150,
    padding: 8,
    borderRadius: 10,
    alignItems: "center",
    alignSelf: "center",
    marginBottom: 20,
  },
  button1: {
    width: "15%",
    padding: 8,
    borderRadius: 10,
    alignItems: "center",
    alignSelf: "center",
    marginBottom: 20,
  },
  button2: {
    backgroundColor: Colors.primaryColor,
    width: "60%",
    padding: 8,
    borderRadius: 10,
    alignItems: "center",
    marginLeft: "2%",
  },
  button3: {
    backgroundColor: Colors.primaryColor,
    width: "30%",
    height: 50,
    borderRadius: 10,
    alignItems: "center",
    alignSelf: "center",
    marginBottom: 30,
  },
  textSize: {
    fontSize: 19,
    fontFamily: "NunitoSans-SemiBold",
  },
  viewContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    flex: 0,
    width: "100%",
    marginBottom: 15,
  },
  openHourContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    flex: 1,
    marginBottom: 15,
    width: "100%",
  },
  addButtonView: {
    flexDirection: "row",
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: "center",
  },
  addButtonView1: {
    flexDirection: "row",
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: "center",
  },
  addNewView: {
    flexDirection: "row",
    justifyContent: "flex-start",
    marginBottom: 65,
    marginTop: 7,
    width: "83%",
    alignSelf: "flex-end",
  },
  addNewView1: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 10,
    alignItems: "center",
  },
  merchantDisplayView: {
    flexDirection: "row",
    flex: 1,
    marginLeft: "17%",
  },
  shiftView: {
    flexDirection: "row",
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
    width: 200,
    height: 60,
    alignItems: "center",
    marginTop: 30,
    alignSelf: "center",
  },
  shiftText: {
    marginLeft: "15%",
    color: Colors.primaryColor,
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 25,
  },
  closeView: {
    flexDirection: "row",
    borderRadius: 5,
    borderColor: Colors.primaryColor,
    borderWidth: 1,
    width: 200,
    height: 40,
    alignItems: "center",
    marginTop: 30,
    alignSelf: "center",
  },
  taxView: {
    flexDirection: "row",
    borderWidth: 2,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: "center",
    marginTop: 20,
    alignSelf: "flex-end",
  },
  sectionView: {
    flexDirection: "row",
    borderRadius: 5,
    padding: 16,
    alignItems: "center",
  },
  receiptView: {
    flexDirection: "row",
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: "center",
    alignSelf: "flex-end",
  },
  pinBtn: {
    backgroundColor: Colors.fieldtBgColor,
    width: 70,
    height: 70,
    marginBottom: 16,
    alignContent: "center",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
  },
  pinNo: {
    fontSize: 20,
    fontWeight: "bold",
  },
  confirmBox: {
    width: "30%",
    height: "30%",
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  headerLeftStyle: {
    width: useWindowDimensions.width * 0.17,
    justifyContent: "center",
    alignItems: "center",
  },
});
export default SettingsWhatsappScreen;
