// import { Text } from "react-native-fast-text";
import React, { Component, useReducer, useState, useEffect, useCallback, useRef, useMemo } from 'react';
import {
  Platform,
  StyleSheet,
  ScrollView,
  Image,
  View,
  Alert,
  TouchableOpacity,
  TextInput,
  Dimensions,
  FlatList,
  Modal,
  PermissionsAndroid,
  useWindowDimensions,
  ActivityIndicator,
  Text,
  Picker,
  Animated,
} from 'react-native';
import DatePicker from "react-datepicker";
import Ionicon from 'react-native-vector-icons/Ionicons';
// import firestore from '@react-native-firebase/firestore';
import firebase from 'firebase';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import * as User from '../util/User';
import Icon from 'react-native-vector-icons/Feather';
import Icon1 from 'react-native-vector-icons/FontAwesome';
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import Icon2 from 'react-native-vector-icons/EvilIcons';
import Icon3 from 'react-native-vector-icons/Foundation';
import Icon4 from 'react-native-vector-icons/FontAwesome5';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import DropDownPicker from 'react-native-dropdown-picker';
// import { ceil } from 'react-native-reanimated';
// import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from "react-native-modal-datetime-picker";
import moment from 'moment';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Styles from '../constant/Styles'
// import DocumentPicker from 'react-native-document-picker';
import Ionicons from 'react-native-vector-icons/Ionicons';
// import RNFetchBlob from 'rn-fetch-blob';
// import RNHTMLtoPDF from 'react-native-html-to-pdf';
import {
  // isTablet,
  getTransformForModalInsideNavigation,
  getTransformForScreenInsideNavigation,
  generateEmailPdf,
  isMobile,
} from '../util/common';
import 'react-native-get-random-values';
import { customAlphabet } from 'nanoid';
import { CommonStore } from '../store/commonStore';
import { MerchantStore } from '../store/merchantStore';
import { PURCHASE_ORDER_STATUS, PURCHASE_ORDER_STATUS_PARSED, EMAIL_REPORT_TYPE, EXPAND_TAB_TYPE, } from '../constant/common';
import { UserStore } from '../store/userStore';
import { convertArrayToCSV, generateEmailReport, getPathForFirebaseStorageFromBlob, parseValidPriceText } from '../util/common';
// import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
// import RNPickerSelect from 'react-native-picker-select';
import { useKeyboard } from '../hooks';
import AsyncImage from '../components/asyncImage';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import XLSX from 'xlsx';
// import Coins from '../assets/svg/Coins'
import { v4 as uuidv4 } from 'uuid';
// import Dish from '../assets/svg/Dish'
// import Hand from '../assets/svg/Hand'
import DateTimePickerModal from "react-native-modal-datetime-picker";
// import GCalendar from '../assets/svg/GCalendar'
// import GCalendarGrey from '../assets/svg/GCalendarGrey';
import { ReactComponent as GCalendar } from "../assets/svg/GCalendar.svg";
import { ReactComponent as GCalendarGrey } from "../assets/svg/GCalendarGrey.svg";
//import StyleSheet from 'react-native-media-query';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import APILocal from '../util/apiLocalReplacers';
import { logEventAnalytics } from '../util/common';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';
import { Collections } from "../constant/firebase";
import { convertPOtoPDF } from "../templates/purchase_order";
import { convertDOItoPDF } from "../templates/do_invoice";
import { convertDOtoPDF } from "../templates/delivery_order";
import BigNumber from "bignumber.js";
import TopBar from './TopBar';

// const RNFS = require('react-native-fs');

const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const nanoid = customAlphabet(alphabet, 12);

const FILTER_PURCHASE_ORDER = {
  ALL_PO: 'FILTER_PURCHASE_ORDER.ALL_PO',
  PO_TODAY: 'FILTER_PURCHASE_ORDER.PO_TODAY',
  PENDING_PO: 'FILTER_PURCHASE_ORDER.PENDING_PO',
  COMPLETED_PO: 'FILTER_PURCHASE_ORDER.COMPLETED_PO',
  CANCELLED_PO: 'FILTER_PURCHASE_ORDER.CANCELLED_PO',
};

let subscriberListenToSnapshotChanges = null;

const isTablet = () => {
  return true;
};

const PurchaseOrderScreen = props => {
  const {
    navigation,
  } = props;

  const [openSupplier, setOpenSupplier] = useState(false);
  const [openTargetStore, setOpenTargetStore] = useState(false);

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const [keyboardHeight] = useKeyboard();
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const [filterPurchaseOrder, setFilterPurchaseOrder] = useState(FILTER_PURCHASE_ORDER.ALL_PO);
  const [purchaseOrder, setPurchaseOrder] = useState(true);
  const [addPurchase, setAddPurchase] = useState(false);
  const [editPurchase, setEditPurchase] = useState(false);
  const [itemsToOrder, setItemsToOrder] = useState([{}]);
  const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
  const [date, setDate] = useState(Date.now());
  const [visible, setVisible] = useState(false);
  const [Email, setEmail] = useState(null);
  const [search, setSearch] = useState('');
  const [switchMerchant, setSwitchMerchant] = useState(isTablet() ? false : true);
  const [loading, setLoading] = useState(false);
  const [choice2, setChoice2] = useState('Print P.O');

  const [exportModal, setExportModal] = useState(false);
  const [importModal, setImportModal] = useState(false);

  const [expandThreeDots, setExpandThreeDots] = useState({}); //Use to expand the view when three dots are tapped

  const [purchaseOrderToday, setPurchaseOrderToday] = useState(0);
  const [purchaseOrderPending, setPurchaseOrderPending] = useState(0);
  const [purchaseOrderCompleted, setPurchaseOrderCompleted] = useState(0);
  const [purchaseOrderCancelled, setPurchaseOrderCancelled] = useState(0);

  const [exportEmail, setExportEmail] = useState('');

  const [isLoadingExcel, setIsLoadingExcel] = useState(false);
  const [isLoadingCsv, setIsLoadingCsv] = useState(false);
  const [isLoadingLocalExcel, setIsLoadingLocalExcel] = useState(false);
  const [isLoadingLocalCsv, setIsLoadingLocalCsv] = useState(false);
  const [isLoadingSave, setIsLoadingSave] = useState(false);
  const [isLoadingSaveAndSend, setIsLoadingSaveAndSend] = useState(false);
  const [isLoadingSaveReady, setIsLoadingSaveReady] = useState(false);
  const [isLoadingSaveCompleted, setIsLoadingSaveCompleted] = useState(false);

  ///////////////////////////////////////////////////////////////////

  const [poId, setPoId] = useState('');
  const [editMode, setEditMode] = useState(false);

  const [supplierDropdownList, setSupplierDropdownList] = useState([]);
  const [selectedSupplierId, setSelectedSupplierId] = useState('');

  const [poStatus, setPoStatus] = useState(PURCHASE_ORDER_STATUS.CREATED);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const [supplyItemDropdownList, setSupplyItemDropdownList] = useState([]);

  const [poItems, setPoItems] = useState([
    {
      supplyItemId: '',
      image: '',
      name: '',
      sku: '',
      skuMerchant: '',
      quantity: 0,
      orderQuantity: 0,
      receivedQuantity: 0,
      price: 0,
      totalPrice: 0,
      unit: '',
      lastReceivedQuantity: 0,

      receivedQuantityActual: 0,

      supplyItem: null,
    }
  ]);

  const [selectedSupplier, setSelectedSupplier] = useState({
    taxRate: 0,
  });

  const [subtotal, setSubtotal] = useState(0);
  const [taxTotal, setTaxTotal] = useState(0);
  const [discountTotal, setDiscountTotal] = useState(0);
  const [finalTotal, setFinalTotal] = useState(0);

  const [expandViewDict, setExpandViewDict] = useState({});

  const outletSupplyItemsSkuDict = CommonStore.useState(s => s.outletSupplyItemsSkuDict);
  const supplyItemsSkuDict = CommonStore.useState(s => s.supplyItemsSkuDict);

  // const supplyItems = CommonStore.useState(s => s.supplyItems);
  const [supplyItems, setSupplyItems] = useState([]);
  const supplyItemsAll = CommonStore.useState(s => s.supplyItems);

  const supplyItemsDict = CommonStore.useState(s => s.supplyItemsDict);
  const suppliers = CommonStore.useState(s => s.suppliers);
  const allOutlets = MerchantStore.useState(s => s.allOutlets);
  const merchantId = UserStore.useState(s => s.merchantId);
  const purchaseOrders = CommonStore.useState(s => s.purchaseOrders);
  const purchaseOrdersOutlet = CommonStore.useState(s => s.purchaseOrdersOutlet !== undefined ? s.purchaseOrdersOutlet : []);
  const purchaseOrdersSupplierOutlet = CommonStore.useState(s => s.purchaseOrdersSupplierOutlet !== undefined ? s.purchaseOrdersSupplierOutlet : []);

  const userName = UserStore.useState(s => s.name);
  const poNumber = MerchantStore.useState(s => s.poNumber);

  const isLoading = CommonStore.useState((s) => s.isLoading);
  const selectedPurchaseOrderEdit = CommonStore.useState(s => s.selectedPurchaseOrderEdit);
  const selectedPurchaseOrderEditFromSupplier = CommonStore.useState(s => s.selectedPurchaseOrderEditFromSupplier);

  const [selectedPurchaseOrderView, setSelectedPurchaseOrderView] = useState(null);

  const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const userEmail = UserStore.useState((s) => s.email);

  const createPO = 'Create Purchase Order';
  const editPO = 'Edit Purchase Order';

  const [purchaseOrderList, setPurchaseOrderLsit] = useState([]);
  const currOutletId = MerchantStore.useState(s => s.currOutletId);
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
  const [rev_date, setRev_date] = useState(moment().subtract(6, 'days').startOf('day'));
  const [rev_date1, setRev_date1] = useState(moment().endOf(Date.now()).endOf('day'));

  const [showDateTimePickerEta, setShowDateTimePickerEta] = useState(false);
  const [etaDate, setEtaDate] = useState(moment().add(3, 'days').startOf('day'));

  const [exportPDF, setExportPDF] = useState(false);

  const [temp, setTemp] = useState('');

  useEffect(() => {
    if (currOutletId !== '' &&
      allOutlets.length > 0 &&
      purchaseOrders.length > 0) {
      var purchaseOrdersTemp = [];
      for (var i = 0; i < purchaseOrders.length; i++) {
        if ((moment(rev_date).isSameOrBefore(purchaseOrders[i].createdAt)) &&
          (moment(rev_date1).isAfter(purchaseOrders[i].createdAt))
        ) {
          purchaseOrdersTemp.push(purchaseOrders[i]);
        }
      }
      purchaseOrdersTemp.sort((a, b) => b.orderDate - a.orderDate)
      setPurchaseOrderLsit(purchaseOrdersTemp);
    }
  }, [currOutletId, rev_date, rev_date1, purchaseOrders])

  useEffect(() => {
    if (selectedPurchaseOrderEditFromSupplier !== null) {
      CommonStore.update(s => {
        s.selectedPurchaseOrderEdit = selectedPurchaseOrderEditFromSupplier;
        s.selectedPurchaseOrderEditFromSupplier = null;
      });

      setPurchaseOrder(false);
      setAddPurchase(true);
    }
  }, [selectedPurchaseOrderEditFromSupplier]);

  useEffect(() => {
    if (selectedPurchaseOrderEdit) {
      // insert info

      setPoId(selectedPurchaseOrderEdit.poId);
      setSelectedSupplierId(selectedPurchaseOrderEdit.supplierId);
      setPoStatus(selectedPurchaseOrderEdit.status);
      setSelectedTargetOutletId(selectedPurchaseOrderEdit.outletId);
      setDate(selectedPurchaseOrderEdit.estimatedArrivalDate);

      if (selectedPurchaseOrderEdit.poItems) {
        setPoItems(selectedPurchaseOrderEdit.poItems.map(poItem => {
          return {
            ...poItem,
            ...(poItem.receivedQuantityActual === undefined) && {
              receivedQuantityActual: 0,
            },
          }
        }));
      }
    }
    else {
      // designed to always mounted, thus need clear manually...

      // setPoId(nanoid());
      // setPoId(`PO${moment().format('MMM').slice(0, 2).toUpperCase() + moment().format('YY') + poNumber.toString().padStart(4, '0')}`);
      setPoId(`PO${moment().format('MMM').toUpperCase() + moment().format('YY') + (poNumber + 1).toString().padStart(4, '0')}`);
      suppliers.length > 0 && setSelectedSupplierId(suppliers[0].uniqueId);
      setPoStatus(PURCHASE_ORDER_STATUS.CREATED);
      // setSelectedTargetOutletId(allOutlets[0].uniqueId);
      setSelectedTargetOutletId(currOutletId);
      setDate(Date.now());

      // if (supplyItems.length > 0 && Object.keys(outletSupplyItemsSkuDict).length > 0) {
      //   setPoItems([
      //     {
      //       supplyItemId: supplyItems[0].uniqueId,
      //       name: supplyItems[0].name,
      //       sku: supplyItems[0].sku,
      //       quantity: outletSupplyItemsSkuDict[supplyItems[0].sku] ? outletSupplyItemsSkuDict[supplyItems[0].sku].quantity : 0, // check if the supply item sku for this outlet existed
      //       orderQuantity: 0,
      //       receivedQuantity: 0,
      //       price: supplyItems[0].price,
      //       totalPrice: 0,
      //     }
      //   ]);

      // }
      // else {
      //   setPoItems([
      //     {
      //       supplyItemId: '',
      //       name: '',
      //       sku: '',
      //       quantity: 0,
      //       orderQuantity: 0,
      //       receivedQuantity: 0,
      //       price: 0,
      //       totalPrice: 0,
      //     }
      //   ]);
      // }

      // if (supplyItems.length > 0 && supplyItems[0] &&
      //   supplyItems[0].sku && outletSupplyItemsSkuDict &&
      //   outletSupplyItemsSkuDict[supplyItems[0].sku]) {
      //   setPoItems([
      //     {
      //       supplyItemId: supplyItems[0].uniqueId,
      //       name: supplyItems[0].name,
      //       sku: supplyItems[0].sku,
      //       skuMerchant: supplyItems[0].skuMerchant,
      //       quantity: outletSupplyItemsSkuDict[supplyItems[0].sku] ? outletSupplyItemsSkuDict[supplyItems[0].sku].quantity : 0, // check if the supply item sku for this outlet existed
      //       orderQuantity: 0,
      //       unit: supplyItems[0].unit,
      //       receivedQuantity: 0,

      //       price: supplyItems[0].price,
      //       totalPrice: 0,

      //       lastReceivedQuantity: 0,

      //       supplyItem: supplyItems[0],
      //     }
      //   ]);
      // }
    }
  }, [selectedPurchaseOrderEdit, addPurchase, poNumber]);

  useEffect(() => {
    setPoId(`PO${moment().format('MMM').toUpperCase() + moment().format('YY') + (poNumber + 1).toString().padStart(4, '0')}`);
  }, [poNumber]);

  useEffect(() => {
    const supplyItemsTemp = supplyItemsAll.filter(supplyItem => {
      if (supplyItem.supplierId === selectedSupplierId) {
        return true;
      }
    });

    ///////////////////////////
    // reset poItems when supplierId changed

    ///////////////////////////

    setSupplyItems(supplyItemsTemp);
  }, [supplyItemsAll, selectedSupplierId]);

  useEffect(() => {
    setSupplierDropdownList(suppliers.map(supplier => {
      let labelName = supplier.name;
      if (supplier.linkOutlet && supplier.linkOutlet !== 'N/A') {
        const outlet = allOutlets.find(outlet => outlet.uniqueId === supplier.linkOutlet);

        if (outlet && outlet.name) {
          labelName = `${labelName} | ${outlet.name}`;
        }
      }

      return { label: labelName, value: supplier.uniqueId };
    }));

    if (selectedSupplierId === '' && suppliers.length > 0) {
      setSelectedSupplierId(suppliers[0].uniqueId);
    }
  }, [suppliers, allOutlets.length]);

  useEffect(() => {
    setTargetOutletDropdownList(allOutlets.map(outlet => ({ label: outlet.name, value: outlet.uniqueId })));

    if (selectedTargetOutletId === '' && allOutlets.length > 0) {
      // setSelectedTargetOutletId(allOutlets[0].uniqueId);
      setSelectedTargetOutletId(currOutletId);
    }
  }, [allOutlets, currOutletId,]);

  useEffect(() => {
    setSupplyItemDropdownList(supplyItems.map(supplyItem => {
      // if (selectedSupplierId === supplyItem.supplierId) {
      //   return { label: supplyItem.name, value: supplyItem.uniqueId };
      // }      

      return { label: supplyItem.name, value: supplyItem.uniqueId };
    }));

    if (supplyItems.length > 0 &&
      poItems.length === 1 &&
      poItems[0].supplyItemId === '') {
      setPoItems([
        {
          supplyItemId: supplyItems[0].uniqueId,
          name: supplyItems[0].name,
          sku: supplyItems[0].sku,
          skuMerchant: supplyItems[0].skuMerchant,
          quantity: outletSupplyItemsSkuDict[supplyItems[0].sku] ? outletSupplyItemsSkuDict[supplyItems[0].sku].quantity : 0, // check if the supply item sku for this outlet existed
          orderQuantity: 0,
          unit: supplyItems[0].unit,
          receivedQuantity: 0,

          price: supplyItems[0].price,
          totalPrice: 0,

          lastReceivedQuantity: 0,

          receivedQuantityActual: 0,

          supplyItem: supplyItems[0],
        }
      ]);
    }
    else if (poItems[0].supplyItemId !== '' &&
      Object.keys(supplyItemsDict).length > 0) {

      // if (selectedPurchaseOrderEdit &&
      //   selectedPurchaseOrderEdit.outletId === global.currOutlet.uniqueId) {

      // }

      var poItemsTemp = [
        ...poItems,
      ];

      for (var i = 0; i < poItemsTemp.length; i++) {
        const supplyItem = supplyItemsDict[poItemsTemp[i].supplyItemId];

        if (supplyItem) {
          poItemsTemp[i] = {
            ...poItemsTemp[i],
            quantity: outletSupplyItemsSkuDict[supplyItem.sku] ? outletSupplyItemsSkuDict[supplyItem.sku].quantity : 0, // check if the supply item sku for this outlet existed | might changed in real time
            // 2025-03-17 - the price just following the price settings
            // price: supplyItem.price, // might changed in real time
          };
        }
        // else {
        //   poItemsTemp[i] = {
        //     ...poItemsTemp[i],
        //     quantity: 0,
        //     // price: supplyItem.price, // might changed in real time
        //   };
        // }
      }

      setPoItems(poItemsTemp);
    }
  }, [
    supplyItems,
    supplyItemsDict,
    outletSupplyItemsSkuDict,
    // currOutletId,
  ]);

  useEffect(() => {
    if (suppliers.length > 0 && selectedSupplierId !== '') {
      setSelectedSupplier(suppliers.find(supplier => supplier.uniqueId === selectedSupplierId));
    }
  }, [suppliers, selectedSupplierId]);

  useEffect(() => {
    // console.log('subtotal');
    // console.log(poItems.reduce((accum, poItem) => accum + (poItem.supplyItem && poItem.supplyItem.supplierId === selectedSupplierId ? poItem.totalPrice : 0), 0));
    setSubtotal(poItems.reduce((accum, poItem) => accum + (poItem.supplyItem && poItem.supplyItem.supplierId === selectedSupplierId ? poItem.totalPrice : 0), 0));
  }, [poItems, selectedSupplierId]);

  useEffect(() => {
    // console.log('taxTotal');
    // console.log(subtotal * (selectedSupplier ? selectedSupplier.taxRate : 0.06));
    setTaxTotal(subtotal * (selectedSupplier ? selectedSupplier.taxRate : 0.06));
  }, [subtotal]);

  useEffect(() => {
    // console.log('finalTotal');
    // console.log((subtotal - discountTotal) + taxTotal);
    setFinalTotal((subtotal - discountTotal) + taxTotal);
  }, [subtotal, discountTotal, taxTotal]);

  useEffect(() => {
    requestStoragePermission();

    // if (poId === '') {
    //   setPoId(nanoid());
    // }    
    // setPoId(nanoid());
  }, []);

  useEffect(() => {
    var poToday = 0;
    var poPending = 0;
    var poCompleted = 0;
    var poCancelled = 0;
    var currDay = moment(Date.now());

    for (var i = 0; i < purchaseOrderList.length; i++) {
      // console.log(moment(Date.now()))
      // console.log('hello')
      if (moment(currDay).isSame(purchaseOrderList[i].orderDate, 'day')) {
        // console.log('hello123')
        poToday += 1;
      }
      if (purchaseOrderList[i].status === PURCHASE_ORDER_STATUS.CREATED ||
        purchaseOrderList[i].status === PURCHASE_ORDER_STATUS.PARTIALLY_RECEIVED ||
        purchaseOrderList[i].status === PURCHASE_ORDER_STATUS.ALL_RECEIVED ||
        purchaseOrderList[i].status === PURCHASE_ORDER_STATUS.READY
      ) {
        poPending += 1;
      }
      if (purchaseOrderList[i].status === PURCHASE_ORDER_STATUS.COMPLETED) {
        poCompleted += 1;
      }
      if (purchaseOrderList[i].status !== PURCHASE_ORDER_STATUS.CREATED &&
        purchaseOrderList[i].status !== PURCHASE_ORDER_STATUS.COMPLETED &&
        purchaseOrderList[i].status !== PURCHASE_ORDER_STATUS.PARTIALLY_RECEIVED &&
        purchaseOrderList[i].status !== PURCHASE_ORDER_STATUS.ORDERED &&
        purchaseOrderList[i].status !== PURCHASE_ORDER_STATUS.ALL_RECEIVED &&
        purchaseOrderList[i].status !== PURCHASE_ORDER_STATUS.READY
      ) {
        poCancelled += 1;
      }
    }

    setPurchaseOrderToday(poToday);
    setPurchaseOrderPending(poPending);
    setPurchaseOrderCompleted(poCompleted);
    setPurchaseOrderCancelled(poCancelled);

  }, [purchaseOrderList])

  useEffect(() => {
    if (selectedSupplierId && supplyItems.length > 0 &&
      selectedPurchaseOrderEdit === null
    ) {
      const setInitialQuantities = async () => {
        // Get all supply items for selected supplier
        const supplierItems = supplyItems.filter(item => item.supplierId === selectedSupplierId);

        if (supplierItems.length === 0) {
          // Alert.alert('Info', 'No items found for this supplier');
          console.log('No items found for this supplier');
          return;
        }

        // Find items below warning level
        const lowStockItems = supplierItems.filter(item => {
          const currentStock = outletSupplyItemsSkuDict[item.sku]?.quantity || 0;
          const warningLevel = outletSupplyItemsSkuDict[item.sku]?.stockWarningQuantity || 0;
          return currentStock < warningLevel && warningLevel > 0;
        });

        let newPoItems = [];

        if (lowStockItems.length > 0) {
          // If there are low stock items, add them all with calculated quantities
          newPoItems = lowStockItems.map(item => {
            const currentStock = outletSupplyItemsSkuDict[item.sku]?.quantity || 0;
            const warningLevel = outletSupplyItemsSkuDict[item.sku]?.stockWarningQuantity || 0;
            const targetQuantity = Math.ceil(warningLevel * 1.5);
            const orderQuantity = targetQuantity - currentStock;

            return {
              supplyItemId: item.uniqueId,
              image: item.image,
              sku: item.sku,
              skuMerchant: item.skuMerchant,
              quantity: currentStock,
              orderQuantity: orderQuantity,
              unit: item.unit,
              receivedQuantity: orderQuantity,
              price: item.price,
              totalPrice: orderQuantity * item.price,
              lastReceivedQuantity: 0,
              receivedQuantityActual: 0,
              supplyItem: item,
            };
          });

          // Alert.alert('Success', `Added ${lowStockItems.length} low stock items to purchase order`);
          console.log(`Added ${lowStockItems.length} low stock items to purchase order`);
        } else {
          // If no low stock items, just add the first item with calculated quantity
          const firstItem = supplierItems[0];
          const currentStock = outletSupplyItemsSkuDict[firstItem.sku]?.quantity || 0;
          const warningLevel = outletSupplyItemsSkuDict[firstItem.sku]?.stockWarningQuantity || 0;
          const targetQuantity = Math.ceil(warningLevel * 1.5);
          const orderQuantity = targetQuantity - currentStock;

          newPoItems = [{
            supplyItemId: firstItem.uniqueId,
            image: firstItem.image,
            sku: firstItem.sku,
            skuMerchant: firstItem.skuMerchant,
            quantity: currentStock,
            orderQuantity: Math.max(orderQuantity, 0), // Ensure non-negative
            unit: firstItem.unit,
            receivedQuantity: Math.max(orderQuantity, 0),
            price: firstItem.price,
            totalPrice: Math.max(orderQuantity, 0) * firstItem.price,
            lastReceivedQuantity: 0,
            receivedQuantityActual: 0,
            supplyItem: firstItem,
          }];

          // Alert.alert('Info', 'No low stock items found. Added first item to list.');
          console.log('No low stock items found. Added first item to list.');
        }

        // Set the PO items
        setPoItems(newPoItems);
      };

      setInitialQuantities();
    }
  }, [selectedSupplierId, supplyItems, outletSupplyItemsSkuDict, selectedPurchaseOrderEdit]);

  const setState = () => { };

  const [openTS, setOpenTS] = useState(false);
  const [openS, setOpenS] = useState(false);
  const [openList, setOpenList] = useState([]);

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false
  // });

  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Purchase Order
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo Manager")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
              style={{
                width: windowHeight * 0.05,
              height: windowHeight * 0.05,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {
  //   // setInterval(() => {
  //   //   getStockOrder()
  //   //   getStockTransfer()
  //   //   getLowStock()
  //   // }, 1000);
  //   getStockOrder();

  // }

  // async componentWillMount = () => {
  //   await requestStoragePermission()
  // }

  const requestStoragePermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: "KooDoo Merchant Storage Permission",
          message:
            "KooDoo Merchant App needs access to your storage ",
          buttonNegative: "Cancel",
          buttonPositive: "OK"
        }
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        // console.log("Storage permission granted");
      } else {
        // console.log("Storage permission denied");
      }
    } catch (err) {
      console.warn(err);
    }
  }

  //when press three dots this function will work
  const expandThreeDotsFunc = (param) => {
    if (expandThreeDots[param.uniqueId]) {
      setExpandThreeDots({
        ...expandThreeDots,
        [param.uniqueId]: false,
      });
    } else {
      setExpandThreeDots({
        // ...expandThreeDots,
        [param.uniqueId]: true,
      });
    }
  }

  const convertTemplateToExcelFormat = () => {
    var excelTemplate = [];

    for (var i = 0; i < poItems.length; i++) {
      var excelColumn = {
        'Product Name': poItems[i].name,
        'SKU': poItems[i].skuMerchant,
        'On hand': poItems[i].receivedQuantity,
        'Order Qty': poItems[i].orderQuantity,
        'Unit': poItems[i].unit,
        'Received Qty': poItems[i].lastReceivedQuantity,
        'Sent Qty': poItems[i].receivedQuantityActual,
        'Supplier Price': poItems[i].price,
        'Total(RM)': poItems[i].totalPrice,
      }
      excelTemplate.push(excelColumn);

    }

    // console.log('excelTemplate');
    // console.log(excelTemplate);

    return excelTemplate;

  };

  // const importTemplateData = async () => {
  //   CommonStore.update(s => {
  //     s.isLoading = true;
  //   });

  //   try {
  //     var res = null;
  //     if (Platform.OS === 'ios') {
  //       res = await DocumentPicker.pick({
  //         type: [DocumentPicker.types.zip],
  //         copyTo: 'documentDirectory',
  //       });
  //     }
  //     else {
  //       res = await DocumentPicker.pickSingle({
  //         type: [DocumentPicker.types.zip],
  //         copyTo: 'documentDirectory',
  //       });
  //     }
  //     // console.log('res');
  //     // console.log(res);

  //     var referenceId = uuidv4();
  //     var referencePath = `/merchant/${merchantId}}/batchUploads/${referenceId}.zip`;

  //     var translatedPath = '';
  //     if (Platform.OS === 'ios') {
  //       translatedPath = await getPathForFirebaseStorageFromBlob(res[0]);
  //     }
  //     else {
  //       translatedPath = await getPathForFirebaseStorageFromBlob(res);
  //     }

  //     if (Platform.OS === 'ios') {
  //       if (translatedPath && translatedPath.fileCopyUri) {
  //         translatedPath = translatedPath.fileCopyUri;
  //       }
  //     }

  //     // console.log('translatedPath');
  //     // console.log(translatedPath);

  //     await uploadFileToFirebaseStorage(translatedPath, referencePath);

  //     const body = {
  //       zipId: referenceId,
  //       zipPath: referencePath,

  //       userId: firebaseUid,
  //       merchantId: merchantId,
  //       outletId: currOutletId,
  //     };

  //     ApiClient.POST(API.batchCreateOutletSupplyItems, body).then((result) => {
  //       if (result && result.status === 'success') {
  //         Alert.alert('Success', 'Submitted to upload queue, we will notify you once the process is done');
  //       }
  //       else {
  //         Alert.alert('Error', 'Failed to import the data');
  //       }

  //       CommonStore.update(s => {
  //         s.isLoading = false;
  //       });
  //     }).catch(err => {
  //       // console.log(err);

  //       Alert.alert('Error', 'Failed to import the data.');

  //       CommonStore.update(s => {
  //         s.isLoading = false;
  //       });
  //     });
  //   } catch (err) {
  //     CommonStore.update(s => {
  //       s.isLoading = false;
  //     });

  //     if (DocumentPicker.isCancel(err)) {
  //       // User cancelled the picker, exit any dialogs or menus and move on
  //     } else {
  //       console.error(err);

  //       Alert.alert('Error', 'Failed to import the data');
  //     }
  //   }
  // }

  //error show readAsArrayBuffer not implemented
  // const importTemplate = (file) => {
  //   const promise = new Promise((resolve, reject) => {
  //     const fileReader = new FileReader();
  //     fileReader.readAsArrayBuffer(file);

  //     fileReader.onload = (e) => {
  //       const bufferArray = e.target.result;

  //       const wb = XLSX.read(bufferArray, { type: "buffer" });

  //       const wsname = wb.SheetNames[0];

  //       const ws = wb.Sheets[wsname];

  //       const data = XLSX.utils.sheet_to_json(ws);

  //       resolve(data);
  //     };

  //     fileReader.onerror = (error) => {
  //       reject(error);
  //     };
  //   });

  //   promise.then((d) => {
  //     // console.log(d);
  //   });
  // }

  const convertDataToExcelFormat = () => {
    var excelData = [];

    //var stringPoItems = poItems.toString();

    for (var i = 0; i < purchaseOrderList.length; i++) {
      for (var j = 0; j < purchaseOrderList[i].poItems.length; j++) {
        var excelRow = {
          'Purchase Item': purchaseOrderList[i].poItems[j].name,
          'Current Quantity': purchaseOrderList[i].poItems[j].quantity ? purchaseOrderList[i].poItems[j].quantity.toFixed(2) : '0.00',
          'Ordered Quantity': purchaseOrderList[i].poItems[j].orderQuantity ? purchaseOrderList[i].poItems[j].orderQuantity.toFixed(2) : '0.00',
          'Total Item Price': +parseFloat(purchaseOrderList[i].poItems[j].totalPrice ? purchaseOrderList[i].poItems[j].totalPrice : 0).toFixed(2),
          // 'Current Quantity': purchaseOrderList[i].poItems.map(item => item.quantity).join(','),
          // 'Ordered Quantity': purchaseOrderList[i].poItems.map(item => item.orderQuantity).join(','),
          // 'Total Item Price': +parseFloat(purchaseOrderList[i].poItems.map(item => item.totalPrice).join(',')).toFixed(2),
          'Purchase Order ID': purchaseOrderList[i].poId,
          'Created Date': moment(purchaseOrderList[i].orderDate).format('DD/MM/YYYY'),
          'Estimated Time': moment(purchaseOrderList[i].estimatedArrivalDate).format('DD/MM/YYYY'),
          'Target Store': purchaseOrderList[i].outletName,
          'Supplier': purchaseOrderList[i].supplierName,
          // 'Total Purchase Amount': +parseFloat(purchaseOrderList[i].totalPrice).toFixed(2),          
          'Status': purchaseOrderList[i].status,
        };

        excelData.push(excelRow);
      }

      excelData.push(excelRow);
    }



    // console.log('excelData');
    // console.log(excelData);

    return excelData;
  };
  // const handleExportExcel = () => {
  //   const excelData = convertDataToExcelFormat();

  //   var ws = XLSX.utils.json_to_sheet(excelData);
  //   var wb = XLSX.utils.book_new();

  //   XLSX.utils.book_append_sheet(wb, ws, "KooDoo Purchase Order Report");
  //   const wbout = XLSX.write(wb, { type: 'binary', bookType: "xlsx" });
  //   RNFS.writeFile(`${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Purchase-Order-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.xlsx`, wbout, 'ascii').then((success) => {
  //     Alert.alert(
  //       'Success',
  //       `Exported to ${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Purchase-Order-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.xlsx`,
  //       [
  //         {
  //           text: 'OK',
  //           onPress: () => {
  //             CommonStore.update((s) => {
  //               s.isLoading = false;
  //             });
  //             setIsLoadingLocalExcel(false);
  //             setExportModal(false);

  //             logEventAnalytics({
  //               eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_ALERT,
  //               eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL_ALERT,
  //             });
  //           },
  //         },
  //       ],
  //       { cancelable: false },
  //     );
  //     console.log('Success');
  //   }).catch((e) => {
  //     console.log('Error', e);
  //   });
  // };

  // const handleExportCsv = () => {

  //   const excelData = convertDataToExcelFormat();

  //   var ws = XLSX.utils.json_to_sheet(excelData);
  //   var wb = XLSX.utils.book_new();

  //   XLSX.utils.book_append_sheet(wb, ws, "KooDoo Purchase Order Report");
  //   const wbout = XLSX.write(wb, { type: 'binary', bookType: "csv" });
  //   RNFS.writeFile(`${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Purchase-Order-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.csv`, wbout, 'ascii').then((success) => {
  //     Alert.alert(
  //       'Success',
  //       `Exported to ${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/KooDoo-Purchase-Order-Report-${moment().format('YYYY-MM-DD-HH-mm-ss',)}.csv`,
  //       [
  //         {
  //           text: 'OK',
  //           onPress: () => {
  //             CommonStore.update((s) => {
  //               s.isLoading = false;
  //             });
  //             setIsLoadingLocalCsv(false);
  //             setExportModal(false);

  //             logEventAnalytics({
  //               eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_ALERT,
  //               eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV_ALERT,
  //             });
  //           },
  //         },
  //       ],
  //       { cancelable: false },
  //     );
  //     console.log('Success');
  //   }).catch((e) => {
  //     console.log('Error', e);
  //   });

  // };

  // function here
  /*   const showDateTimePicker = () => {
      // setState({ isDateTimePickerVisible: true });
      setIsDateTimePickerVisible(true);
    }; */

  const renderOrderItem = ({ item }) => {
    // console.log('renderOrderItem');
    // console.log(item);

    return (
      <TouchableOpacity onPress={() => {
        // setState({
        //   lowStockAlert: false,
        //   purchaseOrder: false,
        //   stockTransfer: false,
        //   stockTake: false,
        //   addPurchase: false,
        //   editPurchase: true,
        //   addStockTransfer: false,
        //   addStockTake: false,
        // });

        // disable first
        // setEditPurchase(true);
        // setPurchaseOrder(false);

        CommonStore.update(s => {
          s.selectedPurchaseOrderEdit = item;
        });

        setPurchaseOrder(false);
        setAddPurchase(true);

        logEventAnalytics({
          eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_C_ITEM,
          eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_C_ITEM,
        });
      }}>
        <View
          style={{
            backgroundColor: '#ffffff',
            flexDirection: 'row',
            paddingVertical: 30,
            paddingHorizontal: 7,
            paddingRight: 2,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#c4c4c4',
            width: '100%',
            alignItems: 'center'
          }}>
          <Text style={{ fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Regular', width: '16%', marginRight: 2, color: Colors.primaryColor, left: Platform.OS == 'ios' ? 0 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 0 : 20 }}>
            {`PO${item.poId}`}
          </Text>
          <Text style={{ fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Regular', width: '11%', marginHorizontal: 2 }}>
            {moment(item.orderDate).format('DD MMM YYYY')}
          </Text>
          <Text style={{ fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Regular', width: '11%', marginHorizontal: 2 }}>
            {moment(item.estimatedArrivalDate).format('DD MMM YYYY')}
          </Text>
          <Text style={{ fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Regular', width: '16%', marginHorizontal: 2 }}>
            {item.outletName}
          </Text>
          <Text style={{ fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Regular', width: '15.5%', marginHorizontal: 4 }}>
            {item.supplierName}
          </Text>
          <Text style={{ fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Regular', width: '10.5%', marginHorizontal: 2, }}>
            RM{(item.poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0)).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
          </Text>
          <View style={{
            width: switchMerchant ? '12%' : '14%',
            marginLeft: 2,
          }}>
            <View style={{
              borderRadius: 10,
              //borderWidth: 1,
              //borderColor: '#E5E5E5',
              width: switchMerchant ? 70 : 120,
              height: 30,
              alignItems: "center",
              justifyContent: 'center',
              //paddingHorizontal: 10,
              backgroundColor: item.status == 0 ? '#dedede' : item.status == 1 ? '#969696' : item.status == 2 ? Colors.secondaryColor : Colors.primaryColor,
            }}>
              <Text style={{
                fontSize: switchMerchant ? 10 : 14, fontFamily: 'NunitoSans-Regular', color: item.status == 0 ? Colors.blackColor : item.status == 1 ? Colors.whiteColor : item.status == 2 ? Colors.blackColor : Colors.whiteColor,
              }}>
                {/* {item.status == 0 ? "Fail" : item.status == 1 ? "In Progress" : item.status == 2 ? "Arrived" : "Completed"} */}
                {PURCHASE_ORDER_STATUS_PARSED[item.status]}
                {/* Partially */}
              </Text>
            </View>
          </View>
          <View style={{
            width: '2%',
            marginLeft: 2,
            // backgroundColor: 'red'
          }}>
            <View>
              {(expandThreeDots[item.uniqueId] == true) ?
                <View style={{
                  position: 'absolute',
                  width: 110,
                  //height: 50,
                  marginLeft: -110,
                  paddingVertical: 10,
                  zIndex: 1,
                  flexDirection: 'column',
                  backgroundColor: '#FFFFFF',
                  borderWidth: 1,
                  //borderColor: '#E7E7E7',
                  borderColor: Colors.highlightColor,
                  borderRadius: 7,
                  shadowOpacity: 0,
                  shadowColor: '#000',
                  shadowOffset: {
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 2,
                  top: -25,
                }}>

                  <TouchableOpacity style={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                    onPress={() => {
                      duplicatePurchaseOrder(item);

                      logEventAnalytics({
                        eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_ITEM_C_DUPLICATE,
                        eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_ITEM_C_DUPLICATE,
                      });
                    }}>
                    <View style={{ width: '30%', paddingLeft: 10 }}>
                      <MaterialIcons name='content-copy' size={switchMerchant ? 15 : 17} color='grey' />
                    </View>
                    <View style={{ width: '70%' }}>
                      <Text style={{ marginLeft: 5, fontSize: switchMerchant ? 10 : 16, }}>
                        Duplicate
                      </Text>
                    </View>
                  </TouchableOpacity>

                  <View style={{
                    borderWidth: 0.5,
                    borderColor: Colors.fieldtBgColor,
                    marginVertical: 4,
                  }} />

                  <TouchableOpacity style={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                    onPress={() => {
                      setSelectedPurchaseOrderView(item);
                      setExportPDF(true);
                    }}>
                    <View style={{ width: '30%', paddingLeft: 10 }}>
                      <Icon name="download" size={switchMerchant ? 15 : 17} color='grey' />
                    </View>
                    <View style={{ width: '70%' }}>
                      <Text style={{ marginLeft: 5, fontSize: switchMerchant ? 10 : 16, }}>
                        Export
                      </Text>
                    </View>
                  </TouchableOpacity>
                  {/* <View style={{ borderWidth: 1, borderColor: Colors.fieldtBgColor }} /> */}
                </View>
                : null}
            </View>

            <View style={{
              flexDirection: 'row',
              // backgroundColor: 'red',
            }}>
              <TouchableOpacity style={{ marginTop: 2, alignSelf: 'flex-start', alignItems: 'flex-start' }}
                onPress={() => {
                  expandThreeDotsFunc(item);

                  logEventAnalytics({
                    eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_ITEM_C_DOTS,
                    eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_ITEM_C_DOTS,
                  });
                }}
              >
                <Entypo name='dots-three-vertical' size={switchMerchant ? 20 : 25}
                  color={Colors.tabGrey} style={{ alignSelf: 'flex-start' }} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  }

  const renderAddPO = ({ item, index }) => {
    // // console.log('poItems[index]');
    // // console.log(poItems[index]);
    // // console.log('supplyItemDropdownList');
    // // console.log(supplyItemDropdownList);

    var isValidToRender = false;

    for (var i = 0; i < supplyItemDropdownList.length; i++) {
      if (supplyItemDropdownList[i].value === item.supplyItemId) {
        isValidToRender = true;
        break;
      }
    }

    let isEditable = true;
    if (selectedPurchaseOrderEdit &&
      (
        (
          selectedPurchaseOrderEdit.supplierOutlet &&
          selectedPurchaseOrderEdit.supplierOutlet !== currOutlet.uniqueId
        )
      )
    ) {
      isEditable = false;

      if (
        (
          selectedPurchaseOrderEdit.supplierOutlet === 'N/A' ||
          selectedPurchaseOrderEdit.supplierOutlet === undefined
        )
        &&
        selectedPurchaseOrderEdit.outletId === currOutletId
        &&
        (
          selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.CREATED ||
          selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.ORDERED
        )
      ) {
        isEditable = true;
      }
    }

    let isEditableReceivedQuantityActual = false;
    if (selectedPurchaseOrderEdit &&
      (
        (
          selectedPurchaseOrderEdit.supplierOutlet &&
          selectedPurchaseOrderEdit.supplierOutlet !== currOutlet.uniqueId &&
          selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.READY
        )
        ||
        (
          selectedPurchaseOrderEdit.supplierOutlet === 'N/A' &&
          selectedPurchaseOrderEdit.supplierOutlet === undefined
        )
      )
      &&
      selectedPurchaseOrderEdit.outletId === currOutletId
    ) {
      isEditableReceivedQuantityActual = true;
    }

    let orderQuantity = 0;

    const currentStock = outletSupplyItemsSkuDict[poItems[index].sku]?.quantity || 0;
    const warningLevel = outletSupplyItemsSkuDict[poItems[index].sku]?.stockWarningQuantity || 0;

    if (warningLevel > 0) {
      // Calculate target quantity (150% of warning level)
      const targetQuantity = Math.ceil(warningLevel * 1.5);
      // Calculate how much we need to order to reach target
      const suggestedOrder = targetQuantity - currentStock;

      // Only suggest positive order quantities
      orderQuantity = suggestedOrder > 0 ? suggestedOrder : 0;

      orderQuantity = selectedPurchaseOrderEdit ?
        poItems[index].orderQuantity.toFixed(2) :
        orderQuantity.toString();
    }
    else {
      orderQuantity = selectedPurchaseOrderEdit ?
        poItems[index].orderQuantity.toFixed(2) :
        '0';
    }

    // becoz poitems don't store supplier id
    if (
      isValidToRender
      // supplyItemsDict[poItems[index].supplyItemId].supplierId === selectedSupplierId
      // true
    ) {
      return (
        <View
          style={{
            backgroundColor: '#ffffff',
            flexDirection: 'row',
            paddingVertical: 20,
            paddingLeft: 10,
            paddingRight: 5,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: '#c4c4c4',
            alignItems: 'center',
            width: '100%',
            // height: (Dimensions.get('window').width * 0.1) * 3,
          }}>
          {
            supplyItemsDict[item.supplyItemId] && supplyItemsDict[item.supplyItemId].image ?
              <AsyncImage
                source={{ uri: supplyItemsDict[item.supplyItemId].image }}
                item={supplyItemsDict[item.supplyItemId]}
                // hideLoading={true}
                style={{ width: switchMerchant ? 40 : 45, height: switchMerchant ? 40 : 45, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5 }}
              />
              :
              <AsyncImage
                source={{ uri: 'https://socialistmodernism.com/wp-content/uploads/2017/07/placeholder-image.png?w=640' }}
                style={{ width: switchMerchant ? 40 : 45, height: switchMerchant ? 40 : 45, borderWidth: 1, borderColor: '#E5E5E5', borderRadius: 5 }}
              />
          }
          {/* <Text style={{width: '1%'}}></Text> */}
          <View style={{ width: '16%', zIndex: 1, marginLeft: 10, marginRight: 1 }}>
            <View style={
              selectedPurchaseOrderEdit ?
                {
                  width: '80%',
                  zIndex: 1,
                  height: 35,
                  //borderRadius: 5,
                  justifyContent: 'center',
                  backgroundColor: 'white',
                  //paddingHorizontal: 10,
                  //borderWidth: 1,
                  //borderColor: '#E5E5E5',
                }
                :
                {
                  width: '80%',
                  zIndex: 1,
                  height: 35,
                  borderRadius: 5,
                  justifyContent: 'center',
                  backgroundColor: 'white',
                  // paddingHorizontal: 10,
                  borderWidth: 1,
                  borderColor: '#E5E5E5',
                  shadowOpacity: 0,
                  shadowColor: '#000',
                  shadowOffset: {
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  elevation: 2,
                }}>
              {!selectedPurchaseOrderEdit ?
                // <DropDownPicker
                //   style={{
                //     backgroundColor: Colors.fieldtBgColor,
                //     width: 210,
                //     height: 40,
                //     borderRadius: 10,
                //     borderWidth: 1,
                //     borderColor: "#E5E5E5",
                //     flexDirection: "row",
                //   }}
                //   dropDownContainerStyle={{
                //     width: 210,
                //     backgroundColor: Colors.fieldtBgColor,
                //     borderColor: "#E5E5E5",
                //   }}
                //   labelStyle={{
                //     marginLeft: 5,
                //     flexDirection: "row",
                //   }}
                //   textStyle={{
                //     fontSize: 14,
                //     fontFamily: 'NunitoSans-Regular',

                //     marginLeft: 5,
                //     paddingVertical: 10,
                //     flexDirection: "row",
                //   }}
                //   selectedItemContainerStyle={{
                //     flexDirection: "row",
                //   }}

                //   showArrowIcon={true}
                //   ArrowDownIconComponent={({ style }) => (
                //     <Ionicons
                //       size={25}
                //       color={Colors.fieldtTxtColor}
                //       style={{ paddingHorizontal: 5, marginTop: 5 }}
                //       name="chevron-down-outline"
                //     />
                //   )}
                //   ArrowUpIconComponent={({ style }) => (
                //     <Ionicons
                //       size={25}
                //       color={Colors.fieldtTxtColor}
                //       style={{ paddingHorizontal: 5, marginTop: 5 }}
                //       name="chevron-up-outline"
                //     />
                //   )}

                //   showTickIcon={true}
                //   TickIconComponent={({ press }) => (
                //     <Ionicons
                //       style={{ paddingHorizontal: 5, marginTop: 5 }}
                //       color={
                //         press ? Colors.fieldtBgColor : Colors.primaryColor
                //       }
                //       name={'md-checkbox'}
                //       size={25}
                //     />
                //   )}
                //   dropDownDirection="BOTTOM"
                //   disabled={
                //     selectedPurchaseOrderEdit ? true : false
                //   }
                //   // items={supplyItemDropdownList}
                //   items={supplyItemDropdownList.filter(dropdownItem => {
                //     const existingItems = poItems.map(item => item.supplyItemId);
                //     // Allow current item and items not in the list
                //     return dropdownItem.value === poItems[index].supplyItemId ||
                //       !existingItems.includes(dropdownItem.value);
                //   })}

                //   placeholder="Select..."
                //   placeholderStyle={{
                //     color: Colors.fieldtTxtColor,
                //     // marginTop: 15,
                //   }}
                //   onSelectItem={(value) => {
                //     if (value) {
                //       // setPoItems(
                //       //   poItems.map((poItem, i) =>
                //       //     i === index
                //       //       ? {
                //       //         ...poItem,
                //       //         supplyItemId: value.value,
                //       //         image: supplyItemsDict[value.value].image,
                //       //         sku: supplyItemsDict[value.value].sku,
                //       //         quantity: outletSupplyItemsSkuDict[
                //       //           supplyItemsDict[value.value].sku
                //       //         ]
                //       //           ? outletSupplyItemsSkuDict[
                //       //             supplyItemsDict[value.value].sku
                //       //           ].quantity
                //       //           : 0,
                //       //         orderQuantity: 0,
                //       //         unit: supplyItemsDict[value.value].unit,
                //       //         receivedQuantity: 0,
                //       //         price: supplyItemsDict[value.value].price,
                //       //         totalPrice: 0,

                //       //         lastReceivedQuantity: 0,
                //       //         supplyItem: supplyItemsDict[value.value],
                //       //       }
                //       //       : poItem
                //       //   )
                //       // );

                //       setPoItems(poItems.map((poItem, i) => (i === index ? {
                //         ...poItem,
                //         supplyItemId: value,
                //         image: supplyItemsDict[value].image,
                //         sku: supplyItemsDict[value].sku,
                //         quantity: outletSupplyItemsSkuDict[supplyItemsDict[value].sku] ? outletSupplyItemsSkuDict[supplyItemsDict[value].sku].quantity : 0,
                //         orderQuantity: 0,
                //         unit: supplyItemsDict[value].unit,
                //         receivedQuantity: 0,
                //         price: supplyItemsDict[value].price,
                //         totalPrice: 0,

                //         name: supplyItemsDict[value].name,
                //         skuMerchant: supplyItemsDict[value].skuMerchant,

                //         lastReceivedQuantity: 0,

                //         receivedQuantityActual: 0,

                //         supplyItem: supplyItemsDict[value],
                //       } : poItem)))
                //     }
                //   }}
                //   searchable={true}
                //   value={
                //     poItems[index].supplyItemId
                //       ? poItems[index].supplyItemId
                //       : ""
                //   }
                //   open={openList[index]}
                //   setOpen={(value) => {
                //     setOpenList((prevOpenList) => {
                //       const newOpenList = [...prevOpenList];
                //       newOpenList[index] = value;
                //       return newOpenList;
                //     });
                //   }}
                // />

                <Picker
                  selectedValue={
                    poItems[index].supplyItemId
                      ? poItems[index].supplyItemId
                      : ""
                  }
                  style={{
                    // width: switchMerchant ? 90 : 210,
                    paddingVertical: 0,
                    backgroundColor: Colors.fieldtBgColor,
                    borderRadius: 10,
                    fontSize: switchMerchant ? 11 : 14,

                    backgroundColor: Colors.fieldtBgColor,
                    width: 150,
                    height: 40,
                    borderRadius: 10,
                    borderWidth: 1,
                    borderColor: "#E5E5E5",
                    flexDirection: "row",
                  }}
                  disabled={
                    selectedPurchaseOrderEdit ? true : false
                  }
                  onValueChange={(value) => {
                    setPoItems(poItems.map((poItem, i) => (i === index ? {
                      ...poItem,
                      supplyItemId: value,
                      image: supplyItemsDict[value].image,
                      sku: supplyItemsDict[value].sku,
                      quantity: outletSupplyItemsSkuDict[supplyItemsDict[value].sku] ? outletSupplyItemsSkuDict[supplyItemsDict[value].sku].quantity : 0,
                      orderQuantity: 0,
                      unit: supplyItemsDict[value].unit,
                      receivedQuantity: 0,
                      price: supplyItemsDict[value].price,
                      totalPrice: 0,

                      name: supplyItemsDict[value].name,
                      skuMerchant: supplyItemsDict[value].skuMerchant,

                      lastReceivedQuantity: 0,

                      receivedQuantityActual: 0,

                      supplyItem: supplyItemsDict[value],
                    } : poItem)))
                  }}
                >
                  {supplyItemDropdownList.filter(dropdownItem => {
                    const existingItems = poItems.map(item => item.supplyItemId);
                    // Allow current item and items not in the list
                    return dropdownItem.value === poItems[index].supplyItemId ||
                      !existingItems.includes(dropdownItem.value);
                  }).map((value, index) => {
                    return (
                      <Picker.Item
                        key={index}
                        label={value.label}
                        value={value.value}
                      />
                    );
                  })}
                </Picker>
                // <RNPickerSelect
                //   placeholder={{}}
                //   disabled={selectedPurchaseOrderEdit ? true : false}
                //   useNativeAndroidPickerStyle={false}
                //   //pickerProps={{ style: { height: 160, overflow: 'hidden',} }}
                //   style={{
                //     inputIOS: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, paddingVertical: 5 },
                //     inputAndroid: { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, paddingVertical: 5 },
                //     inputAndroidContainer: {
                //       //backgroundColor: 'red',
                //       width: '100%',
                //     }
                //   }}
                //   //contentContainerStyle={{ fontSize: switchMerchant ? 10 : 14, }}
                //   items={supplyItemDropdownList.filter(dropdownItem => {
                //     const existingItems = poItems.map(item => item.supplyItemId);
                //     // Allow current item and items not in the list
                //     return dropdownItem.value === poItems[index].supplyItemId ||
                //       !existingItems.includes(dropdownItem.value);
                //   })}
                //   value={
                //     poItems[index].supplyItemId ? poItems[index].supplyItemId : ''
                //   }
                //   onValueChange={(value) => {
                //     setPoItems(poItems.map((poItem, i) => (i === index ? {
                //       ...poItem,
                //       supplyItemId: value,
                //       image: supplyItemsDict[value].image,
                //       sku: supplyItemsDict[value].sku,
                //       quantity: outletSupplyItemsSkuDict[supplyItemsDict[value].sku] ? outletSupplyItemsSkuDict[supplyItemsDict[value].sku].quantity : 0,
                //       orderQuantity: 0,
                //       unit: supplyItemsDict[value].unit,
                //       receivedQuantity: 0,
                //       price: supplyItemsDict[value].price,
                //       totalPrice: 0,

                //       name: supplyItemsDict[value].name,
                //       skuMerchant: supplyItemsDict[value].skuMerchant,

                //       lastReceivedQuantity: 0,

                //       receivedQuantityActual: 0,

                //       supplyItem: supplyItemsDict[value],
                //     } : poItem)))

                //     logEventAnalytics({
                //       eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_DD_PRODUCT_NAME,
                //       eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_DD_PRODUCT_NAME,
                //     });
                //   }}
                // />
                :
                <Text style={{ color: '#8f8f8f', marginLeft: 0, marginRight: 0, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                  {supplyItemsDict[poItems[index].supplyItemId] ? supplyItemsDict[poItems[index].supplyItemId].name : 'N/A'}
                </Text>
                // <RNPickerSelect
                //   disabled={selectedPurchaseOrderEdit ? true : false}
                //   items={supplyItemDropdownList}
                //   textInputProps={{ color: Colors.descriptionColor }}
                //   // useNativeAndroidPickerStyle={false}
                //   style={Styles.rnPickerSelectStyle}
                //   value={
                //     poItems[index].supplyItemId ? poItems[index].supplyItemId : ''
                //   }
                //   onValueChange={(value) =>
                //     setPoItems(poItems.map((poItem, i) => (i === index ? {
                //       ...poItem,
                //       supplyItemId: value,
                //       image: supplyItemsDict[value].image,
                //       sku: supplyItemsDict[value].sku,
                //       quantity: outletSupplyItemsSkuDict[supplyItemsDict[value].sku] ? outletSupplyItemsSkuDict[supplyItemsDict[value].sku].quantity : 0,
                //       orderQuantity: 0,
                //       unit: supplyItemsDict[value].unit,
                //       receivedQuantity: 0,
                //       price: supplyItemsDict[value].price,
                //       totalPrice: 0,

                //       lastReceivedQuantity: 0,
                //       supplyItem: supplyItemsDict[value],
                //     } : poItem)))
                //   }
                // />
              }
            </View>
          </View>
          {/* <Text style={{ width: '2%', color: '#8f8f8f' }}></Text> */}
          <Text style={{ width: '9%', color: '#8f8f8f', marginLeft: 5, marginRight: 1, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
            {poItems[index].skuMerchant || '-'}
          </Text>
          <Text style={{ width: '8%', color: '#8f8f8f', marginHorizontal: 3, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}>
            {/* {poItems[index].quantity} */}
            {outletSupplyItemsSkuDict[poItems[index].sku] ? (outletSupplyItemsSkuDict[poItems[index].sku]?.quantity || 0).toFixed(2) : 0.00}
            {(outletSupplyItemsSkuDict[poItems[index].sku] && supplyItemsSkuDict[poItems[index].sku].subUnit && supplyItemsSkuDict[poItems[index].sku].cAmount) ? `\n(${((outletSupplyItemsSkuDict[poItems[index].sku]?.quantity || 0) / poItems[index].supplyItem.cAmount).toFixed(2)})` : ''}
          </Text>

          <View style={[{
            width: '8%',
            marginHorizontal: 3,
            // marginLeft: 50,
            // backgroundColor: 'blue',
          }, Dimensions.get('screen').width === 1280 && Dimensions.get('screen').height === 800 ? {
            right: 5,
          } : {}]}>
            {!selectedPurchaseOrderEdit ?
              <View style={{
                display: 'flex',
                alignItems: 'flex-start',
                justifyContent: 'flex-start',
                flexDirection: 'column',
              }}>
                <TextInput
                  editable={selectedPurchaseOrderEdit ? false : true}
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={{
                    backgroundColor: Colors.fieldtBgColor,
                    width: switchMerchant ? 50 : 60,
                    height: 35,
                    borderRadius: 5,
                    // padding: 5,
                    marginVertical: 5,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    paddingLeft: 10,
                    fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
                    // alignSelf: 'center',
                  }}
                  placeholder={'0'}
                  placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  keyboardType={'decimal-pad'}
                  onChangeText={(text) => {
                    // const amount = text.length > 0 ? parseFloat(text) : 0;

                    // setPoItems(poItems.map((poItem, i) => (i === index ? {
                    //   ...poItem,
                    //   orderQuantity: amount,
                    //   totalPrice: amount * poItem.price,
                    // } : poItem)))

                    setPoItems(poItems.map((poItem, i) => {
                      let poItemNew = poItem;

                      if (i === index) {
                        let orderQuantity = text.length > 0 ? parseFloat(text) : 0;
                        if (poItem.supplyItem.subUnit && typeof poItem.supplyItem.cAmount === 'number') {
                          orderQuantity = orderQuantity * poItem.supplyItem.cAmount;
                        }

                        poItemNew = {
                          ...poItem,
                          orderQuantity: orderQuantity,
                          totalPrice: orderQuantity * poItem.price,
                        };
                      }

                      return poItemNew;
                    }));

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_ORDERED_QTY,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_ORDERED_QTY,
                    });
                  }}
                  defaultValue={((poItems[index].supplyItem.subUnit && poItems[index].supplyItem.cAmount) ?
                    (poItems[index].orderQuantity / poItems[index].supplyItem.cAmount) :
                    poItems[index].orderQuantity).toFixed(2)}
                />

                <Text
                  style={{
                    //backgroundColor: Colors.fieldtBgColor,
                    width: switchMerchant ? 50 : 60,
                    //height: 35,
                    borderRadius: 5,
                    // padding: 5,
                    marginBottom: 5,
                    //borderWidth: 1,
                    //borderColor: '#E5E5E5',
                    //paddingLeft:10,
                    color: '#8f8f8f',
                    fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14
                  }}
                >
                  {`${(poItems[index].orderQuantity).toFixed(2)}`}
                </Text>

                {
                  (poItems[index].supplyItem.subUnit && poItems[index].supplyItem.cAmount)
                    ?
                    <Text
                      style={{
                        //backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 50 : 60,
                        //height: 35,
                        borderRadius: 5,
                        // padding: 5,
                        marginBottom: 5,
                        //borderWidth: 1,
                        //borderColor: '#E5E5E5',
                        //paddingLeft:10,
                        color: '#8f8f8f',
                        fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14
                      }}
                    >
                      {`(${(poItems[index].orderQuantity / poItems[index].supplyItem.cAmount).toFixed(2)})`}
                    </Text>
                    :
                    <></>
                }
              </View>
              :
              <Text
                style={{
                  //backgroundColor: Colors.fieldtBgColor,
                  width: switchMerchant ? 50 : 60,
                  //height: 35,
                  borderRadius: 5,
                  padding: 5,
                  marginVertical: 5,
                  //borderWidth: 1,
                  //borderColor: '#E5E5E5',
                  //paddingLeft:10,
                  color: '#8f8f8f',
                  fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14
                }}
              >
                {poItems[index].orderQuantity.toFixed(2)}{(poItems[index].supplyItem.subUnit && poItems[index].supplyItem.cAmount) ? `\n(${(poItems[index].orderQuantity / poItems[index].supplyItem.cAmount).toFixed(2)})` : ''}
              </Text>
            }
          </View>
          <View style={{ width: '9%', marginHorizontal: 3 }}>
            <Text style={{ color: '#8f8f8f', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}>{
              poItems[index].unit}
            </Text>
            {poItems[index].supplyItem && poItems[index].supplyItem.subUnit ?
              <Text style={{ color: '#8f8f8f', fontFamily: 'NunitoSans- Regular', fontSize: switchMerchant ? 10 : 14 }}>{
                poItems[index].supplyItem.subUnit ? `(${poItems[index].supplyItem.subUnit})` : ''}
              </Text>
              : null}
          </View>
          {/* <Text style={{ width: '14%', marginLeft: 50, color: '#8f8f8f' }}>50</Text> */}

          {/* <View style={{
          width: '14%',
          marginLeft: 30,
          marginRight: 30,
          // backgroundColor: 'red',
        }}>
          <TextInput
            underlineColorAndroid={Colors.fieldtBgColor}
            style={{
              height: 40,
              width: 100,
              paddingHorizontal: 20,
              backgroundColor: Colors.fieldtBgColor,
              borderRadius: 10,
            }}
            placeholder={'50'}
            keyboardType={'decimal-pad'}
            // placeholder={itemName}
            // onChangeText={(text) => {
            //   // setState({ itemName: text });
            //   setPoItems(poItems.map((poItem, i) => (i === index ? {
            //     ...poItem,
            //     orderQuantity: parseInt(text),
            //     totalPrice: parseInt(text) * poItem.price,
            //   } : poItem)))
            // }}
            value={poItems[index].receivedQuantity}
          // ref={myTextInput}
          />
        </View> */}

          {
            (
              !selectedPurchaseOrderEdit ||
              poStatus === PURCHASE_ORDER_STATUS.COMPLETED ||
              poStatus === PURCHASE_ORDER_STATUS.PARTIALLY_RECEIVED ||
              poStatus === PURCHASE_ORDER_STATUS.ALL_RECEIVED ||
              !isEditable
            ) ?
              <Text style={{
                width: '8%',
                marginHorizontal: 3,
                color: '#8f8f8f',
                fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14
              }}>
                {poItems[index].receivedQuantity.toFixed(2)}{(poItems[index].supplyItem.subUnit && poItems[index].supplyItem.cAmount) ? `\n(${(poItems[index].receivedQuantity / poItems[index].supplyItem.cAmount).toFixed(2)})` : ''}
              </Text>
              :
              <View style={{
                width: '8%',
                marginHorizontal: 3,

                display: 'flex',
                alignItems: 'flex-start',
                justifyContent: 'flex-start',
                flexDirection: 'column',
              }}>
                <TextInput
                  editable={isEditable}
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={{
                    backgroundColor: Colors.fieldtBgColor,
                    width: 60,
                    height: 35,
                    borderRadius: 5,
                    // padding: 5,
                    marginVertical: 5,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    paddingLeft: 10,
                    fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
                  }}
                  placeholder={'50'}
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                  keyboardType={'decimal-pad'}
                  // placeholder={itemName}
                  onChangeText={(text) => {
                    // setState({ itemName: text });
                    // setPoItems(poItems.map((poItem, i) => (i === index ? {
                    //   ...poItem,
                    //   receivedQuantity: text.length > 0 ? parseFloat(text) : 0,
                    // } : poItem)))

                    setPoItems(poItems.map((poItem, i) => {
                      let poItemNew = poItem;

                      if (i === index) {
                        let receivedQuantity = text.length > 0 ? parseFloat(text) : 0;
                        if (poItem.supplyItem.subUnit && typeof poItem.supplyItem.cAmount === 'number') {
                          receivedQuantity = receivedQuantity * poItem.supplyItem.cAmount;
                        }

                        poItemNew = {
                          ...poItem,
                          receivedQuantity: receivedQuantity,
                        };
                      }

                      return poItemNew;
                    }));

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_RECEIVED_QTY,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_RECEIVED_QTY,
                    });
                  }}
                  // defaultValue={poItems[index].receivedQuantity.toFixed(2)}
                  defaultValue={((poItems[index].supplyItem.subUnit && poItems[index].supplyItem.cAmount) ?
                    (poItems[index].receivedQuantity / poItems[index].supplyItem.cAmount) :
                    poItems[index].receivedQuantity).toFixed(2)}
                // ref={myTextInput}
                />

                <Text
                  style={{
                    //backgroundColor: Colors.fieldtBgColor,
                    width: switchMerchant ? 50 : 60,
                    //height: 35,
                    borderRadius: 5,
                    // padding: 5,
                    marginBottom: 5,
                    //borderWidth: 1,
                    //borderColor: '#E5E5E5',
                    //paddingLeft:10,
                    color: '#8f8f8f',
                    fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14
                  }}
                >
                  {`${(poItems[index].receivedQuantity).toFixed(2)}`}
                </Text>

                {
                  (poItems[index].supplyItem.subUnit && poItems[index].supplyItem.cAmount)
                    ?
                    <Text
                      style={{
                        //backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 50 : 60,
                        //height: 35,
                        borderRadius: 5,
                        // padding: 5,
                        marginBottom: 5,
                        //borderWidth: 1,
                        //borderColor: '#E5E5E5',
                        //paddingLeft:10,
                        color: '#8f8f8f',
                        fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14
                      }}
                    >
                      {`(${(poItems[index].receivedQuantity / poItems[index].supplyItem.cAmount).toFixed(2)})`}
                    </Text>
                    :
                    <></>
                }
              </View>
          }

          {/* actual receive */}
          <View style={[{
            width: '8%',
            marginHorizontal: 3,
            // marginLeft: 50,
            // backgroundColor: 'blue',
          }, Dimensions.get('screen').width === 1280 && Dimensions.get('screen').height === 800 ? {
            right: 5,
          } : {}]}>
            {(
              // !selectedPurchaseOrderEdit ||
              isEditableReceivedQuantityActual
            )
              ?
              <View style={{
                width: '8%',
                marginHorizontal: 3,

                display: 'flex',
                alignItems: 'flex-start',
                justifyContent: 'flex-start',
                flexDirection: 'column',
              }}>
                <TextInput
                  // editable={selectedPurchaseOrderEdit ? false : true}
                  editable={isEditableReceivedQuantityActual}
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={{
                    backgroundColor: Colors.fieldtBgColor,
                    width: switchMerchant ? 50 : 60,
                    height: 35,
                    borderRadius: 5,
                    // padding: 5,
                    marginVertical: 5,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    paddingLeft: 10,
                    fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
                  }}
                  placeholder={'0'}
                  placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  keyboardType={'decimal-pad'}
                  onChangeText={(text) => {
                    // setState({ itemName: text });
                    setPoItems(poItems.map((poItem, i) => {
                      let poItemNew = poItem;

                      if (i === index) {
                        let receivedQuantityActual = text.length > 0 ? parseFloat(text) : 0;
                        if (poItem.supplyItem.subUnit && typeof poItem.supplyItem.cAmount === 'number') {
                          receivedQuantityActual = receivedQuantityActual * poItem.supplyItem.cAmount;
                        }

                        poItemNew = {
                          ...poItem,
                          receivedQuantityActual: receivedQuantityActual,
                        };
                      }

                      return poItemNew;
                    }));
                  }}
                  // defaultValue={poItems[index].receivedQuantityActual.toFixed(2)}
                  defaultValue={((poItems[index].supplyItem.subUnit && poItems[index].supplyItem.cAmount) ?
                    (poItems[index].receivedQuantityActual / poItems[index].supplyItem.cAmount) :
                    poItems[index].receivedQuantityActual).toFixed(2)}
                />

                <Text
                  style={{
                    //backgroundColor: Colors.fieldtBgColor,
                    width: switchMerchant ? 50 : 60,
                    //height: 35,
                    borderRadius: 5,
                    // padding: 5,
                    marginBottom: 5,
                    //borderWidth: 1,
                    //borderColor: '#E5E5E5',
                    //paddingLeft:10,
                    color: '#8f8f8f',
                    fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14
                  }}
                >
                  {`${(poItems[index].receivedQuantityActual).toFixed(2)}`}
                </Text>

                {
                  (poItems[index].supplyItem.subUnit && poItems[index].supplyItem.cAmount)
                    ?
                    <Text
                      style={{
                        //backgroundColor: Colors.fieldtBgColor,
                        width: switchMerchant ? 50 : 60,
                        //height: 35,
                        borderRadius: 5,
                        // padding: 5,
                        marginBottom: 5,
                        //borderWidth: 1,
                        //borderColor: '#E5E5E5',
                        //paddingLeft:10,
                        color: '#8f8f8f',
                        fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14
                      }}
                    >
                      {`(${(poItems[index].receivedQuantityActual / poItems[index].supplyItem.cAmount).toFixed(2)})`}
                    </Text>
                    :
                    <></>
                }
              </View>
              :
              <Text
                style={{
                  //backgroundColor: Colors.fieldtBgColor,
                  width: switchMerchant ? 50 : 60,
                  //height: 35,
                  borderRadius: 5,
                  padding: 5,
                  marginVertical: 5,
                  //borderWidth: 1,
                  //borderColor: '#E5E5E5',
                  //paddingLeft:10,
                  color: '#8f8f8f',
                  fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14
                }}
              >
                {poItems[index].receivedQuantityActual.toFixed(2)}{(poItems[index].supplyItem.subUnit && poItems[index].supplyItem.cAmount) ? `\n(${(poItems[index].receivedQuantityActual / poItems[index].supplyItem.cAmount).toFixed(2)})` : ''}
              </Text>
            }
          </View>

          <View style={{ width: '8%', flexDirection: 'row', justifyContent: 'space-between', marginHorizontal: 3, }}>
            {/* <Text style={{ color: '#8f8f8f', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>{`RM `}</Text> */}
            {
              (selectedPurchaseOrderEdit && selectedPurchaseOrderEdit.uniqueId)
                ?
                <Text style={{
                  color: '#8f8f8f',
                  fontFamily: 'NunitoSans-Regular',
                  fontSize: switchMerchant ? 10 : 14,
                  paddingRight: '2%',
                }}>{(poItems[index].price || 0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                </Text>
                :
                <TextInput
                  // editable={isEditable}
                  underlineColorAndroid={Colors.fieldtBgColor}
                  style={{
                    backgroundColor: Colors.fieldtBgColor,
                    width: 60,
                    height: 35,
                    borderRadius: 5,
                    padding: 5,
                    marginVertical: 5,
                    borderWidth: 1,
                    borderColor: '#E5E5E5',
                    paddingLeft: 10,
                    fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
                  }}
                  placeholder={'50'}
                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                  placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                  keyboardType={'decimal-pad'}
                  // placeholder={itemName}
                  onChangeText={(text) => {
                    // setState({ itemName: text });
                    setPoItems(poItems.map((poItem, i) => (i === index ? {
                      ...poItem,
                      price: text.length > 0 ? parseFloat(text) : 0,
                      totalPrice: poItem.orderQuantity * (text.length > 0 ? parseFloat(text) : 0),
                    } : poItem)))

                    // logEventAnalytics({
                    //   eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_RECEIVED_QTY,
                    //   eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_TB_RECEIVED_QTY,
                    // });
                  }}
                  defaultValue={poItems[index].price.toFixed(2)}
                // ref={myTextInput}
                />
            }

          </View>
          <View style={{ width: '2.25%' }} />

          {/* <Text style={{ width: switchMerchant ? '12%' : '13%', color: '#8f8f8f', marginHorizontal: 3, fontSize: switchMerchant ? 10 : 14 }}>{`RM${poItems[index].price.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}`}</Text> */}
          <View style={{ width: switchMerchant ? '8%' : '8%', flexDirection: 'row', justifyContent: 'space-between', marginHorizontal: 3, }}>
            <Text style={{ color: '#8f8f8f', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>{`RM `}</Text>
            <Text style={{ color: '#8f8f8f', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, paddingRight: '2%', }}>{poItems[index].totalPrice.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
          </View>
          {/* {!selectedPurchaseOrderEdit ?
            <TouchableOpacity style={{ marginLeft: 10 }}
              onPress={() => {
                setPoItems([
                  ...poItems.slice(0, index),
                  ...poItems.slice(index + 1),
                ]);
              }}>
              <Icon name="trash-2" size={20} color="#eb3446" />
            </TouchableOpacity>
            : <></>
          } */}
          {
            selectedPurchaseOrderEdit ?
              <></>
              :
              <TouchableOpacity style={{ marginLeft: switchMerchant ? 5 : 10, }}
                disabled={poStatus === PURCHASE_ORDER_STATUS.COMPLETED}
                onPress={() => {
                  setPoItems([
                    ...poItems.slice(0, index),
                    ...poItems.slice(index + 1),
                  ]);

                  logEventAnalytics({
                    eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_C_DELETE,
                    eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_C_DELETE,
                  });
                }}>
                <Icon name="trash-2" size={switchMerchant ? 15 : 20} color="#eb3446" />
              </TouchableOpacity>
          }

        </View >
      );
    }
  };

  const email = () => {
    var body = {
      stockTransferId: 1,
      email: Email
    }
    // ApiClient.POST(API.emailStockTransfer, body, false).then((result) => {

    //   if (result == true) {

    //     Alert.alert(
    //       'Success',
    //       'The email had sent',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  }

  // const createStockOrder = () => {
  //   var body = {
  //     poId: poId,
  //     poItems: poItems,
  //     supplierId: selectedSupplierId,
  //     status: poStatus,
  //     outletId: selectedTargetOutletId,
  //     tax: taxTotal,
  //     discount: discountTotal,
  //     totalPrice: subtotal,
  //     finalTotal: finalTotal,
  //     estimatedArrivalDate: date,

  //     merchantId: merchantId,
  //     remarks: '',
  //   };

  //   ApiClient.POST(API.createPurchaseOrder, body).then((result) => {
  //     if (result && result.uniqueId) {
  //       Alert.alert(
  //         'Success',
  //         'Purchase order created successfully',
  //         [
  //           { text: "OK", onPress: () => { props.navigation.goBack() } }
  //         ],
  //         { cancelable: false },
  //       );
  //     }
  //   });
  // }

  const removePurchaseOrder = () => {
    for (var i = 0; i < poItems.length; i++) {
      var currItem = poItems[i];
    }
    // console.log(currItem);
  };

  const createPurchaseOrder = () => {
    // console.log('on createPurchaseOrder');


    CommonStore.update(s => {
      s.isLoading = true;
    });

    const errorString = poItems
      .filter(poItem => poItem.orderQuantity <= 0 && poItem.supplyItem && poItem.supplyItem.supplierId === selectedSupplierId)
      .map(poItem => `${(poItem.name ? poItem.name : 'N/A').trim()}: ${(poItem.orderQuantity ? poItem.orderQuantity : 0).toFixed(2)}`)
      .join('\n');

    if (errorString.length > 0) {
      // Alert.alert(
      //   'Error',
      //   `Following items amount are invalid: \n\n${errorString}`,
      //   [
      //     {
      //       text: "OK", onPress: () => {
      //         setIsLoadingSave(false);
      //         setIsLoadingSaveAndSend(false);
      //         setIsLoadingSaveReady(false);

      //         CommonStore.update(s => {
      //           s.isLoading = false;
      //         });

      //         logEventAnalytics({
      //           eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_ERROR_ALERT,
      //           eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_ERROR_ALERT,
      //         });
      //       }
      //     }
      //   ],
      // );

      window.confirm(`Following items amount are invalid: \n\n${errorString}`);

      setIsLoadingSave(false);
      setIsLoadingSaveAndSend(false);
      setIsLoadingSaveReady(false);

      CommonStore.update(s => {
        s.isLoading = false;
      });

      logEventAnalytics({
        eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_ERROR_ALERT,
        eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_ERROR_ALERT,
      });

      return;
    }

    // if (supplierOutletId === 'N/A' ||
    //   supplierOutletId === undefined
    // ) {
    //   Alert.alert('Info', 'Please link your supplier to the central kitchen first before proceed.');

    //   setIsLoadingSave(false);
    //   setIsLoadingSaveAndSend(false);
    //   setIsLoadingSaveReady(false);

    //   CommonStore.update(s => {
    //     s.isLoading = false;
    //   });

    //   return;
    // }

    if (selectedPurchaseOrderEdit === null) {
      var poItemsValid = [];

      for (var index = 0; index < poItems.length; index++) {
        for (var i = 0; i < supplyItemDropdownList.length; i++) {
          if (supplyItemDropdownList[i].value === poItems[index].supplyItemId &&
            poItems[index].orderQuantity !== 0 &&
            poItems[index].supplyItem && poItems[index].supplyItem.supplierId === selectedSupplierId
          ) {
            poItemsValid.push(poItems[index]);
            break;

          }
        }
      }


      if (poItemsValid.length > 0) {

        var sPICList = [];
        sPICList = suppliers.find((supplier) => supplier.uniqueId === selectedSupplierId) ? suppliers.find((supplier) => supplier.uniqueId === selectedSupplierId).picItems : []

        let supplierOutletId = 'N/A';
        let supplierFound = suppliers.find(
          (supplier) => supplier.uniqueId === selectedSupplierId
        );
        if (supplierFound && supplierFound.linkOutlet) {
          supplierOutletId = supplierFound.linkOutlet;
        }

        const supplierPICList = suppliers.find(
          (supplier) => supplier.uniqueId === selectedSupplierId
        ).picItems;

        var emailCCList = [];

        if (supplierPICList && supplierPICList.length > 0) {
          emailCCList = supplierPICList.filter(item => item.email).map(item => item.email);
        }

        // if (employeeEmail) {
        //   emailCCList.push(employeeEmail);
        // }

        // if (outletEmail) {
        //   emailCCList.push(outletEmail);
        // }

        var body = {
          poId: poId,
          poItems: poItemsValid
          // .map(poItem => {
          //   let quantity = poItem.quantity;
          //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
          //     quantity = poItem.quantity * poItem.cAmount;
          //   }

          //   let orderQuantity = poItem.orderQuantity;
          //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
          //     orderQuantity = poItem.orderQuantity * poItem.cAmount;
          //   }

          //   let receivedQuantity = poItem.receivedQuantity;
          //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
          //     receivedQuantity = poItem.receivedQuantity * poItem.cAmount;
          //   }

          //   let receivedQuantityActual = poItem.receivedQuantityActual;
          //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
          //     receivedQuantityActual = poItem.receivedQuantityActual * poItem.cAmount;
          //   }

          //   return {
          //     ...poItem,
          //     quantity: quantity,
          //     orderQuantity: orderQuantity,
          //     receivedQuantity: receivedQuantity, // sent qty
          //     receivedQuantityActual: receivedQuantityActual, // received
          //   };
          // })
          ,
          supplierId: selectedSupplierId,
          status: poStatus,
          outletId: selectedTargetOutletId,
          tax: +(parseFloat(taxTotal).toFixed(2)),
          discount: +(parseFloat(discountTotal).toFixed(2)),
          totalPrice: +(parseFloat(subtotal).toFixed(2)),
          finalTotal: +(parseFloat(finalTotal).toFixed(2)),
          estimatedArrivalDate: moment(etaDate).valueOf(),

          outletName: allOutlets.find(outlet => outlet.uniqueId === selectedTargetOutletId).name,
          supplierName: suppliers.find(supplier => supplier.uniqueId === selectedSupplierId).name,

          merchantId: merchantId,
          remarks: '',

          supplierPICList: supplierPICList,
          supplierPIC: sPICList[0],
          employeeEmail: userEmail,
          outletEmail: currOutlet.outletEmail ? currOutlet.outletEmail : currOutlet.email,
          supplierOutlet: supplierOutletId,

          supplierPICEmail: sPICList[0].email,
          supplierPICEmailCCList: emailCCList,
        };

        if (supplierOutletId !== 'N/A') {
          let supplierOutletData = allOutlets.find(outlet => outlet.uniqueId === supplierOutletId);

          body.supplierPICEmail = supplierOutletData.email;
          body.supplierPICEmailCCList = [];

          if (supplierOutletData.outletEmail) {
            body.supplierPICEmail = supplierOutletData.outletEmail;
          }
        }

        //  for( var index = 0; index < poItems.length; index++) {
        //   for (var i = 0; i < supplyItemDropdownList.length; i++) {
        //     if( poItems[index].orderQuantity !== 2 ) {


        ApiClient.POST(API.createPurchaseOrder, body).then((result) => {
          if (result && result.status === 'success') {
            /////////////////////////////////

            // 2025-03-13 - here can send the po

            setTimeout(async () => {
              if (result.data && result.data.uniqueId) {
                const poFile = await convertPOtoPDF(result.data);

                generateEmailPdf(
                  EMAIL_REPORT_TYPE.PDF,
                  poFile.base64,
                  '[KooDoo]',
                  `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}]-po-${result.data.outletName}-${result.data.supplierName}-${result.data.poId}.pdf`,
                  `/merchant/${merchantId}/po/${uuidv4()}.pdf`,
                  result.data.supplierPICEmail,
                  `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}] PO ${result.data.outletName} | ${result.data.supplierName} | ${result.data.poId}.pdf`,
                  `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}] PO ${result.data.outletName} | ${result.data.supplierName} | ${result.data.poId}.pdf`,
                  () => {
                  },
                );

                const poInvoiceFile = await convertDOItoPDF(result.data);

                generateEmailPdf(
                  EMAIL_REPORT_TYPE.PDF,
                  poInvoiceFile.base64,
                  '[KooDoo]',
                  `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}]-po-invoice-${result.data.outletName}-${result.data.supplierName}-${result.data.poId}.pdf`,
                  `/merchant/${merchantId}/po/${uuidv4()}.pdf`,
                  result.data.supplierPICEmail,
                  `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}] PO Invoice ${result.data.outletName} | ${result.data.supplierName} | ${result.data.poId}.pdf`,
                  `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}] PO Invoice ${result.data.outletName} | ${result.data.supplierName} | ${result.data.poId}.pdf`,
                  () => {
                  },
                );
              }
            }, 500);

            /////////////////////////////////

            // Alert.alert(
            //   'Success',
            //   'Purchase order has been created',
            //   [
            //     {
            //       text: "OK", onPress: () => {
            //         setPurchaseOrder(true);
            //         setAddPurchase(false);
            //         setIsLoadingSave(false);
            //         setIsLoadingSaveAndSend(false);

            //         logEventAnalytics({
            //           eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_SUCCESS_ALERT,
            //           eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_SUCCESS_ALERT,
            //         });
            //       }
            //     }
            //   ],
            //   { cancelable: false },
            // );

            setPurchaseOrder(true);
            setAddPurchase(false);
            setIsLoadingSave(false);
            setIsLoadingSaveAndSend(false);

            logEventAnalytics({
              eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_SUCCESS_ALERT,
              eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_SUCCESS_ALERT,
            });
          }
          else {
            console.log('result');
            console.log(result);
            alert(
              'Error, Failed to create purchase order',
            );
          }
        });

        CommonStore.update(s => {
          s.isLoading = false;
        });
        setIsLoadingSave(false);
        setIsLoadingSaveAndSend(false);

      }
      else {
        // Alert.alert(
        //   'Error',
        //   'Please add at least one supply item',
        //   [
        //     {
        //       text: "OK", onPress: () => {
        //         setIsLoadingSave(false);
        //         setIsLoadingSaveAndSend(false);

        //         logEventAnalytics({
        //           eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_ERROR_QUANTITY_ALERT,
        //           eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_ERROR_QUANTITY_ALERT,
        //         });
        //       }
        //     }
        //   ],
        // );

        window.confirm(`Please add at least one supply item`);

        CommonStore.update(s => {
          s.isLoading = false;
        });
        setIsLoadingSave(false);
        setIsLoadingSaveAndSend(false);
      }

    }
    else {

      var sPICList = [];
      sPICList = suppliers.find((supplier) => supplier.uniqueId === selectedSupplierId) ? suppliers.find((supplier) => supplier.uniqueId === selectedSupplierId).picItems : [];

      let supplierOutletId = 'N/A';
      let supplierFound = suppliers.find(
        (supplier) => supplier.uniqueId === selectedSupplierId
      );
      if (supplierFound && supplierFound.linkOutlet) {
        supplierOutletId = supplierFound.linkOutlet;
      }

      if (selectedPurchaseOrderEdit && selectedPurchaseOrderEdit.supplierOutlet) {
        supplierOutletId = selectedPurchaseOrderEdit.supplierOutlet;
      }

      let selectedTargetOutletIdNew = selectedTargetOutletId;
      if (selectedPurchaseOrderEdit && selectedPurchaseOrderEdit.outletId) {
        selectedTargetOutletIdNew = selectedPurchaseOrderEdit.outletId;
      }

      var body = {
        poId: poId,
        poItems: poItems
        // .map(poItem => {
        //   let quantity = poItem.quantity;
        //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
        //     quantity = poItem.quantity * poItem.cAmount;
        //   }

        //   let orderQuantity = poItem.orderQuantity;
        //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
        //     orderQuantity = poItem.orderQuantity * poItem.cAmount;
        //   }

        //   let receivedQuantity = poItem.receivedQuantity;
        //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
        //     receivedQuantity = poItem.receivedQuantity * poItem.cAmount;
        //   }

        //   let receivedQuantityActual = poItem.receivedQuantityActual;
        //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
        //     receivedQuantityActual = poItem.receivedQuantityActual * poItem.cAmount;
        //   }

        //   return {
        //     ...poItem,
        //     quantity: quantity,
        //     orderQuantity: orderQuantity,
        //     receivedQuantity: receivedQuantity, // sent qty
        //     receivedQuantityActual: receivedQuantityActual, // received
        //   };
        // })
        ,
        supplierId: selectedSupplierId,
        status: poStatus,
        outletId: selectedTargetOutletIdNew,
        tax: +(parseFloat(taxTotal).toFixed(2)),
        discount: +(parseFloat(discountTotal).toFixed(2)),
        totalPrice: +(parseFloat(subtotal).toFixed(2)),
        finalTotal: +(parseFloat(finalTotal).toFixed(2)),
        estimatedArrivalDate: moment(etaDate).valueOf(),

        outletName: allOutlets.find(outlet => outlet.uniqueId === selectedTargetOutletIdNew).name,
        supplierName: suppliers.find(supplier => supplier.uniqueId === selectedSupplierId).name,

        merchantId: merchantId,
        remarks: '',

        uniqueId: selectedPurchaseOrderEdit.uniqueId,

        supplierPICList: suppliers.find(
          (supplier) => supplier.uniqueId === selectedSupplierId
        ).picItems,
        supplierPIC: sPICList[0],
        employeeEmail: userEmail,
        outletEmail: currOutlet.outletEmail ? currOutlet.outletEmail : currOutlet.email,
        supplierOutlet: supplierOutletId,

        outletIdNow: currOutletId,
      };

      // console.log(body);

      var isValidReceivedQuantity = true;

      // check if the inputed received quantity of poItems valid or not

      // 2025-03-17 - no need this checking first
      // for (var i = 0; i < poItems.length; i++) {
      //   if (poItems[i].receivedQuantity > poItems[i].lastReceivedQuantity &&
      //     poItems[i].receivedQuantity <= poItems[i].orderQuantity) {
      //     // means valid
      //   }
      //   else if (poItems[i].receivedQuantity === 0) {
      //     // means valid
      //   } else {
      //     isValidReceivedQuantity = false;
      //     break;
      //   }
      // }

      if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.READY) {
        let poItemsReceivedQuantityActualCount = poItems.reduce((accum, item) => {
          return BigNumber(accum).plus(item.receivedQuantityActual ? item.receivedQuantityActual : 0).toNumber();
        }, 0);

        let poItemsOrderQuantityCount = poItems.reduce((accum, item) => {
          return BigNumber(accum).plus(item.orderQuantity ? item.orderQuantity : 0).toNumber();
        }, 0);

        if (poItemsReceivedQuantityActualCount <= 0
          ||
          poItemsReceivedQuantityActualCount > poItemsOrderQuantityCount
        ) {
          isValidReceivedQuantity = false;
        }
      }
      else {
        let poItemsReceivedQuantityCount = poItems.reduce((accum, item) => {
          return BigNumber(accum).plus(item.receivedQuantity ? item.receivedQuantity : 0).toNumber();
        }, 0);

        let poItemsOrderQuantityCount = poItems.reduce((accum, item) => {
          return BigNumber(accum).plus(item.orderQuantity ? item.orderQuantity : 0).toNumber();
        }, 0);

        if (poItemsReceivedQuantityCount <= 0
          ||
          poItemsReceivedQuantityCount > poItemsOrderQuantityCount
        ) {
          isValidReceivedQuantity = false;
        }
      }

      if (isValidReceivedQuantity) {
        APILocal.updatePurchaseOrder({ body: body }).then((result) => {
          // ApiClient.POST(API.updatePurchaseOrder, body).then((result) => {
          if (result && result.status === 'success') {
            // Alert.alert(
            //   'Success',
            //   'Purchase order has been updated',
            //   [
            //     {
            //       text: "OK", onPress: () => {
            //         setPurchaseOrder(true);
            //         setAddPurchase(false);
            //         setIsLoadingSave(false);
            //         setIsLoadingSaveAndSend(false);

            //         // props.navigation.goBack();

            //         logEventAnalytics({
            //           eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_UPDATE_SUCCESS_ALERT,
            //           eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_UPDATE_SUCCESS_ALERT,
            //         });
            //       }
            //     }
            //   ],
            //   { cancelable: false },
            // );

            setPurchaseOrder(true);
            setAddPurchase(false);
            setIsLoadingSave(false);
            setIsLoadingSaveAndSend(false);

            // props.navigation.goBack();

            logEventAnalytics({
              eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_UPDATE_SUCCESS_ALERT,
              eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_UPDATE_SUCCESS_ALERT,
            });

          }
          else {
            alert(
              'Error, Failed to update purchase order',
            );
          }

          CommonStore.update(s => {
            s.isLoading = false;
          });
          setIsLoadingSave(false);
          setIsLoadingSaveAndSend(false);
        });
      }
      else {
        // Alert.alert('Info', 'Invalid inputed quantity.');
        window.confirm('Invalid inputed quantity.');

        CommonStore.update(s => {
          s.isLoading = false;
        });
        setIsLoadingSave(false);
        setIsLoadingSaveAndSend(false);
      }
    }


  }

  const createPurchaseOrderReady = () => {
    CommonStore.update(s => {
      s.isLoading = true;
    });

    var sPICList = [];
    sPICList = suppliers.find((supplier) => supplier.uniqueId === selectedSupplierId) ? suppliers.find((supplier) => supplier.uniqueId === selectedSupplierId).picItems : [];

    let supplierOutletId = 'N/A';
    let supplierFound = suppliers.find(
      (supplier) => supplier.uniqueId === selectedSupplierId
    );
    if (supplierFound && supplierFound.linkOutlet) {
      supplierOutletId = supplierFound.linkOutlet;
    }

    var body = {
      poId: poId,
      poItems: poItems
      // .map(poItem => {
      //   let quantity = poItem.quantity;
      //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
      //     quantity = poItem.quantity * poItem.cAmount;
      //   }

      //   let orderQuantity = poItem.orderQuantity;
      //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
      //     orderQuantity = poItem.orderQuantity * poItem.cAmount;
      //   }

      //   let receivedQuantity = poItem.receivedQuantity;
      //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
      //     receivedQuantity = poItem.receivedQuantity * poItem.cAmount;
      //   }

      //   let receivedQuantityActual = poItem.receivedQuantityActual;
      //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
      //     receivedQuantityActual = poItem.receivedQuantityActual * poItem.cAmount;
      //   }

      //   return {
      //     ...poItem,
      //     quantity: quantity,
      //     orderQuantity: orderQuantity,
      //     receivedQuantity: receivedQuantity, // sent qty
      //     receivedQuantityActual: receivedQuantityActual, // received
      //   };
      // })
      ,
      supplierId: selectedSupplierId,
      status: poStatus,
      outletId: selectedTargetOutletId,
      tax: +(parseFloat(taxTotal).toFixed(2)),
      discount: +(parseFloat(discountTotal).toFixed(2)),
      totalPrice: +(parseFloat(subtotal).toFixed(2)),
      finalTotal: +(parseFloat(finalTotal).toFixed(2)),
      estimatedArrivalDate: moment(etaDate).valueOf(),

      outletName: allOutlets.find(outlet => outlet.uniqueId === selectedTargetOutletId).name,
      supplierName: suppliers.find(supplier => supplier.uniqueId === selectedSupplierId).name,

      merchantId: merchantId,
      remarks: '',

      uniqueId: selectedPurchaseOrderEdit.uniqueId,

      supplierPICList: suppliers.find(
        (supplier) => supplier.uniqueId === selectedSupplierId
      ).picItems,
      supplierPIC: sPICList[0],
      employeeEmail: userEmail,
      outletEmail: currOutlet.outletEmail ? currOutlet.outletEmail : currOutlet.email,
      supplierOutlet: supplierOutletId,
    };

    APILocal.updatePurchaseOrderReady({ body: body }).then((result) => {
      // ApiClient.POST(API.updatePurchaseOrder, body).then((result) => {
      if (result && result.status === 'success') {

        /////////////////////////////////

        // 2025-03-13 - here can send the do

        setTimeout(async () => {
          if (result.data && result.data.uniqueId) {
            const doFile = await convertDOtoPDF(result.data);

            generateEmailPdf(
              EMAIL_REPORT_TYPE.PDF,
              doFile.base64,
              '[KooDoo]',
              `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}]-do-${result.data.outletName}-${result.data.supplierName}-${result.data.poId}.pdf`,
              `/merchant/${merchantId}/po/${uuidv4()}.pdf`,
              result.data.outletEmail,
              `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}] DO ${result.data.outletName} | ${result.data.supplierName} | ${result.data.poId}.pdf`,
              `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}] DO ${result.data.outletName} | ${result.data.supplierName} | ${result.data.poId}.pdf`,
              () => {
              },
            );
          }
        }, 500);

        /////////////////////////////////

        // Alert.alert(
        //   'Success',
        //   'Purchase order has been updated',
        //   [
        //     {
        //       text: "OK", onPress: () => {
        //         setPurchaseOrder(true);
        //         setAddPurchase(false);
        //         setIsLoadingSave(false);
        //         setIsLoadingSaveAndSend(false);
        //         setIsLoadingSaveReady(false);

        //         // props.navigation.goBack();

        //         // logEventAnalytics({
        //         //   eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_UPDATE_SUCCESS_ALERT,
        //         //   eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_UPDATE_SUCCESS_ALERT,
        //         // });
        //       }
        //     }
        //   ],
        //   { cancelable: false },
        // );

        setPurchaseOrder(true);
        setAddPurchase(false);
        setIsLoadingSave(false);
        setIsLoadingSaveAndSend(false);
        setIsLoadingSaveReady(false);

      }
      else {
        alert(
          'Error, Failed to update purchase order',
        );
      }

      CommonStore.update(s => {
        s.isLoading = false;
      });
      setIsLoadingSave(false);
      setIsLoadingSaveAndSend(false);
      setIsLoadingSaveReady(false);
    });
  }

  const createPurchaseOrderCompleted = () => {
    CommonStore.update(s => {
      s.isLoading = true;
    });

    var sPICList = [];
    sPICList = suppliers.find((supplier) => supplier.uniqueId === selectedSupplierId) ? suppliers.find((supplier) => supplier.uniqueId === selectedSupplierId).picItems : [];

    let supplierOutletId = 'N/A';
    let supplierFound = suppliers.find(
      (supplier) => supplier.uniqueId === selectedSupplierId
    );
    if (supplierFound && supplierFound.linkOutlet) {
      supplierOutletId = supplierFound.linkOutlet;
    }

    var body = {
      poId: poId,
      poItems: poItems
      // .map(poItem => {
      //   let quantity = poItem.quantity;
      //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
      //     quantity = poItem.quantity * poItem.cAmount;
      //   }

      //   let orderQuantity = poItem.orderQuantity;
      //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
      //     orderQuantity = poItem.orderQuantity * poItem.cAmount;
      //   }

      //   let receivedQuantity = poItem.receivedQuantity;
      //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
      //     receivedQuantity = poItem.receivedQuantity * poItem.cAmount;
      //   }

      //   let receivedQuantityActual = poItem.receivedQuantityActual;
      //   if (poItem.subUnit && typeof poItem.cAmount === 'number') {
      //     receivedQuantityActual = poItem.receivedQuantityActual * poItem.cAmount;
      //   }

      //   return {
      //     ...poItem,
      //     quantity: quantity,
      //     orderQuantity: orderQuantity,
      //     receivedQuantity: receivedQuantity, // sent qty
      //     receivedQuantityActual: receivedQuantityActual, // received
      //   };
      // })
      ,
      supplierId: selectedSupplierId,
      status: poStatus,
      outletId: selectedTargetOutletId,
      tax: +(parseFloat(taxTotal).toFixed(2)),
      discount: +(parseFloat(discountTotal).toFixed(2)),
      totalPrice: +(parseFloat(subtotal).toFixed(2)),
      finalTotal: +(parseFloat(finalTotal).toFixed(2)),
      estimatedArrivalDate: moment(etaDate).valueOf(),

      outletName: allOutlets.find(outlet => outlet.uniqueId === selectedTargetOutletId).name,
      supplierName: suppliers.find(supplier => supplier.uniqueId === selectedSupplierId).name,

      merchantId: merchantId,
      remarks: '',

      uniqueId: selectedPurchaseOrderEdit.uniqueId,

      supplierPICList: suppliers.find(
        (supplier) => supplier.uniqueId === selectedSupplierId
      ).picItems,
      supplierPIC: sPICList[0],
      employeeEmail: userEmail,
      outletEmail: currOutlet.outletEmail ? currOutlet.outletEmail : currOutlet.email,
      supplierOutlet: supplierOutletId,
    };

    APILocal.updatePurchaseOrderCompleted({ body: body }).then((result) => {
      // ApiClient.POST(API.updatePurchaseOrder, body).then((result) => {
      if (result && result.status === 'success') {
        // Alert.alert(
        //   'Success',
        //   'Purchase order has been updated',
        //   [
        //     {
        //       text: "OK", onPress: () => {
        //         setPurchaseOrder(true);
        //         setAddPurchase(false);
        //         setIsLoadingSave(false);
        //         setIsLoadingSaveAndSend(false);
        //         setIsLoadingSaveReady(false);
        //         setIsLoadingSaveCompleted(false);

        //         // props.navigation.goBack();

        //         // logEventAnalytics({
        //         //   eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_UPDATE_SUCCESS_ALERT,
        //         //   eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_UPDATE_SUCCESS_ALERT,
        //         // });
        //       }
        //     }
        //   ],
        //   { cancelable: false },
        // );

        setPurchaseOrder(true);
        setAddPurchase(false);
        setIsLoadingSave(false);
        setIsLoadingSaveAndSend(false);
        setIsLoadingSaveReady(false);
        setIsLoadingSaveCompleted(false);

      }
      else {
        alert(
          'Error, Failed to update purchase order',
        );
      }

      CommonStore.update(s => {
        s.isLoading = false;
      });
      setIsLoadingSave(false);
      setIsLoadingSaveAndSend(false);
      setIsLoadingSaveReady(false);
      setIsLoadingSaveCompleted(false);
    });
  }

  const duplicatePurchaseOrder = async (item) => {
    const body = {
      purchaseOrderId: item.uniqueId,
    };

    ApiClient.POST(API.duplicatePurchaseOrder, body).then((result) => {
      if (result && result.status === 'success') {
        alert(
          'Success, urchase order has been duplicated',
        );

        expandThreeDotsFunc(item);
        setPurchaseOrder(false);
        setAddPurchase(true);

        CommonStore.update(s => {
          s.selectedPurchaseOrderEdit = item;
        });
      }
      else {
        alert(
          'Error, Failed to duplicate Purchase order',
        );
      }
    });
  };

  useEffect(() => {
    if (currOutletId !== '') {
      typeof subscriberListenToSnapshotChanges === 'function' && subscriberListenToSnapshotChanges();
      subscriberListenToSnapshotChanges = () => { };

      let subscriber = listenToSnapshotChanges(currOutletId, rev_date, rev_date1);

      subscriberListenToSnapshotChanges = subscriber;

      return () => {
        typeof subscriber === 'function' && subscriber();
      };
    }
  }, [
    currOutletId,

    rev_date,
    rev_date1,
  ]);

  const listenToSnapshotChanges = async (outletId, startDate, endDate) => {
    var subscriberPurchaseOrder = firebase.firestore()
      .collection(Collections.PurchaseOrder)
      .where('outletId', '==', outletId)
      .where('updatedAt', '>=', moment(startDate).valueOf())
      .where('updatedAt', '<=', moment(endDate).valueOf())
      .orderBy('updatedAt', 'desc')
      .limit(global.currOutlet.ciNum ? global.currOutlet.ciNum : 50)
      .onSnapshot(async (snapshot) => {
        var purchaseOrders = [];

        if (snapshot && !snapshot.empty) {
          for (var i = 0; i < snapshot.size; i++) {
            const record = snapshot.docs[i].data();

            purchaseOrders.push(record);
          }
        }

        purchaseOrders.sort((a, b) => b.updatedAt - a.updatedAt);

        if (snapshot) {
          CommonStore.update((s) => {
            s.purchaseOrdersOutlet = purchaseOrders;
          });
        }
      });

    var subscriberPurchaseOrderSupplierOutlet = firebase.firestore()
      .collection(Collections.PurchaseOrder)
      .where('supplierOutlet', '==', outletId)
      .where('updatedAt', '>=', moment(startDate).valueOf())
      .where('updatedAt', '<=', moment(endDate).valueOf())
      .orderBy('updatedAt', 'desc')
      .limit(global.currOutlet.ciNum ? global.currOutlet.ciNum : 50)
      .onSnapshot(async (snapshot) => {
        var purchaseOrders = [];

        if (snapshot && !snapshot.empty) {
          for (var i = 0; i < snapshot.size; i++) {
            const record = snapshot.docs[i].data();

            purchaseOrders.push(record);
          }
        }

        purchaseOrders.sort((a, b) => b.updatedAt - a.updatedAt);

        if (snapshot) {
          CommonStore.update((s) => {
            s.purchaseOrdersSupplierOutlet = purchaseOrders;
          });
        }
      });

    return () => {
      subscriberPurchaseOrder();
      subscriberPurchaseOrderSupplierOutlet();
    };
  };

  function removeDuplicatesByUniqueId(arr) {
    const uniqueMap = new Map();
    return arr.filter(obj => {
      if (!uniqueMap.has(obj.uniqueId)) {
        uniqueMap.set(obj.uniqueId, true);
        return true;
      }
      return false;
    });
  }

  useEffect(() => {
    const result = removeDuplicatesByUniqueId(purchaseOrdersOutlet.concat(purchaseOrdersSupplierOutlet));
    CommonStore.update(s => {
      s.purchaseOrders = result;
    });
  }, [
    purchaseOrdersOutlet,
    purchaseOrdersSupplierOutlet,
  ]);

  var titleName = selectedPurchaseOrderEdit ? editPO : createPO;

  // function end
  const simpliFiedLayout = CommonStore.useState(
    (s) => s.simpliFiedLayout,
  );

  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [dimensions, setDimensions] = useState({
    window: Dimensions.get('window')
  });

  // ScreenSize Listener
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setDimensions({ window });

      if (!isSidebarOpen) {
        const newSidebarWidth = calculateSidebarWidth(window.width);
        sidebarXValue.setValue(-newSidebarWidth * 1.2);
      }
    });

    return () => subscription.remove();
  }, [isSidebarOpen]);

  const calculateSidebarWidth = (width) => {
    const isLandscapeMode = width > Dimensions.get('window').height;

    if (isLandscapeMode) {
      return isMobile() ? width * 0.23 : width * 0.08;
    } else {
      return isMobile() ? width * 0.23 : width * 0.08;
    }
  };

  const sidebarClose = () => {
    setIsSidebarOpen(false);
    Animated.timing(sidebarXValue, {
      toValue: -sidebarWidth,
      duration: 200,
      useNativeDriver: true,
    }).start();

    Animated.timing(contentXValue, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const sidebarOpen = () => {
    setIsSidebarOpen(true);
    Animated.timing(sidebarXValue, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();

    Animated.timing(contentXValue, {
      toValue: sidebarWidth * 0.55,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const sidebarWidth = useMemo(() => {
    return calculateSidebarWidth(dimensions.window.width);
  }, [dimensions.window.width]);

  const sidebarXValue = useRef(new Animated.Value(
    isSidebarOpen ? 0 : -calculateSidebarWidth(Dimensions.get('window').width) * 1.2
  )).current;
  const contentXValue = useRef(new Animated.Value(0)).current;

  return (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>
    <View>
      {isMobile() && <TopBar navigation={navigation} />}
      <View
        style={[
          styles.container,
          {
            height: windowHeight,
            width: windowWidth,
          },
        ]}
      >
        <Animated.View
          style={{
            transform: [{ translateX: sidebarXValue }],
            flex: 0.8,
            ...isMobile() && {
              flex: 1,
            },
            position: 'absolute',
            top: 0,
            left: 0,
            zIndex: 2,
            height: '100%',
            width: sidebarWidth,
            marginTop: 50,
          }}>
          <SideBar
            navigation={props.navigation}
            selectedTab={8}
            expandReport
          />
        </Animated.View>
        <View style={[{ height: windowHeight, flex: 9, },
        {
          ...isMobile() && {
            flex: 3,
            //backgroundColor:'yellow',
          }
        },
        ]}>
          <View
            style={{
              width: isMobile() ? windowWidth * 0.23 : 123,
              height: 70,

              //backgroundColor: 'red',

              zIndex: 3,

              marginLeft: isSidebarOpen ? 0 : 0,
            }}
          >
            {isSidebarOpen ?
              <Animated.View style=
                {{
                  transform: [{ translateX: sidebarXValue }],
                  backgroundColor: Colors.whiteColor,
                  height: '100%',
                  width: sidebarWidth,
                }}>
                <TouchableOpacity
                  style={{
                    marginTop: 5,

                  }}
                  onPress={sidebarClose}
                >
                  <MaterialIcons name='keyboard-capslock' color={Colors.primaryColor} size={isMobile() ? 35 : 30} style={{ alignSelf: 'center', transform: [{ rotate: '270deg' }], marginTop: 7, marginLeft: -3 }} />
                </TouchableOpacity>
              </Animated.View>
              :
              <View style={{}}>
                <TouchableOpacity
                  style={{ marginTop: 5, }}
                  onPress={sidebarOpen}
                >
                  <View style={{
                    flexDirection: 'row',
                    marginLeft: 10,
                    marginVertical: 10,
                    justifyContent: 'center',
                    flexDirection: 'row',
                    borderWidth: 1,
                    borderColor: Colors.primaryColor,
                    backgroundColor: '#4E9F7D',
                    borderRadius: 5,
                    //width: 160,
                    height: switchMerchant ? 35 : 40,
                    width: windowWidth * 0.4,
                    alignItems: 'center',
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 1,
                    zIndex: -1,

                  }}>
                    <MaterialIcons name='keyboard-capslock' color={Colors.whiteColor} size={isMobile() ? 25 : 30} style={{ alignSelf: 'flex-start', transform: [{ rotate: '90deg' }], marginTop: 5 }} />
                    <Text style={{
                      color: Colors.whiteColor,
                      marginLeft: 5,
                      fontSize: switchMerchant ? 10 : 16,
                      fontFamily: 'NunitoSans-Bold',
                    }}
                    >
                      MORE PAGES
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            }

          </View>
          <Animated.ScrollView
            showsVerticalScrollIndicator={false}
            style={{
              transform: [{ translateX: contentXValue }],
              // ...isMobile() && {

              // },
              // marginLeft: isSidebarOpen
              //   ? (isMobile() ? '20%' : '7%')
              //   : -10,

            }}
            contentContainerStyle={{
              paddingBottom: windowHeight * 0.1,
            }}>
            <ScrollView horizontal={true} showsHorizontalScrollIndicator={simpliFiedLayout ? isSidebarOpen : true}>
              <View style={[styles.content, {
                padding: 16,
                width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
                ...isMobile() && {
                  width: windowWidth * 3,
                }
              }]}>
                <Modal
                  style={{
                    // flex: 1
                  }}
                  supportedOrientations={['portrait', 'landscape']}
                  visible={exportModal}
                  transparent={true}
                  animationType={'fade'}
                >
                  <View style={{
                    flex: 1,
                    backgroundColor: Colors.modalBgColor,
                    alignItems: 'center',
                    justifyContent: 'center',

                    top:
                      Platform.OS === 'android'
                        ? 0
                        : keyboardHeight > 0
                          ? -keyboardHeight * 0.45
                          : 0,
                  }}>
                    <View style={{
                      height: Dimensions.get('screen').width * 0.32,
                      width: Dimensions.get('screen').width * 0.4,
                      backgroundColor: Colors.whiteColor,
                      borderRadius: 12,
                      padding: Dimensions.get('screen').width * 0.03,
                      alignItems: 'center',
                      justifyContent: 'center',

                      // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
                      ...getTransformForModalInsideNavigation(),
                    }}>
                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          position: 'absolute',
                          right: Dimensions.get('screen').width * 0.02,
                          top: Dimensions.get('screen').width * 0.02,

                          elevation: 1000,
                          zIndex: 1000,
                        }}
                        onPress={() => {
                          setExportModal(false);

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_C_CLOSE,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_C_CLOSE,
                          });
                        }}>
                        <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                      </TouchableOpacity>
                      <View style={{
                        alignItems: 'center',
                        top: 20,
                        position: 'absolute',
                      }}>
                        <Text style={{
                          fontFamily: 'NunitoSans-Bold',
                          textAlign: 'center',
                          fontSize: switchMerchant ? 18 : 20,
                        }}>
                          Download Report
                        </Text>
                      </View>
                      <View style={{ top: switchMerchant ? '14%' : '10%', }}>
                        <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold' }}>Email Address:</Text>
                        <TextInput
                          style={{
                            backgroundColor: Colors.fieldtBgColor,
                            width: switchMerchant ? 240 : 370,
                            height: switchMerchant ? 35 : 50,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft: 10,
                            fontSize: switchMerchant ? 10 : 16,
                          }}
                          autoCapitalize='none'
                          placeholderStyle={{ padding: 5 }}
                          placeholder="Enter your email"
                          placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                          onChangeText={(text) => {
                            setExportEmail(text);

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS,
                            });
                          }}
                          value={exportEmail}
                        />
                        <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold', marginTop: 15 }}>Send As:</Text>


                        <View style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                          //top: '10%',
                          flexDirection: 'row',
                          marginTop: 15,
                        }}>
                          <TouchableOpacity
                            disabled={isLoading}
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              width: switchMerchant ? 100 : 120,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}
                            onPress={() => {
                              if (exportEmail.length > 0) {
                                CommonStore.update(s => {
                                  s.isLoading = true;
                                });
                                setIsLoadingExcel(true);

                                const excelData = convertDataToExcelFormat();

                                if (excelData && excelData.length > 0) {
                                  generateEmailReport(
                                    EMAIL_REPORT_TYPE.EXCEL,
                                    excelData,
                                    'KooDoo Purchase Order Report',
                                    'KooDoo Purchase Order Report.xlsx',
                                    `/merchant/${merchantId}/reports/${uuidv4()}.xlsx`,
                                    exportEmail,
                                    'KooDoo Purchase Order Report',
                                    'KooDoo Purchase Order Report',
                                    () => {
                                      CommonStore.update(s => {
                                        s.isLoading = false;
                                      });
                                      setIsLoadingExcel(false);
                                      alert('Success, Report will be sent to the email address shortly');

                                      setExportModal(false);
                                    },
                                  );
                                }
                                else {
                                  alert('Info, Empty data to export.');

                                  CommonStore.update(s => {
                                    s.isLoading = false;
                                  });
                                  setIsLoadingExcel(false);
                                }
                              }
                              else {
                                alert('Info, Invalid email address');
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_SEND_AS_C_EXCEL,
                              });
                            }}>
                            {
                              isLoadingExcel
                                ?
                                <ActivityIndicator
                                  size={'small'}
                                  color={Colors.whiteColor}
                                />
                                :
                                <Text style={{
                                  color: Colors.whiteColor,
                                  //marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                  EXCEL</Text>

                            }
                          </TouchableOpacity>

                          <TouchableOpacity
                            disabled={isLoading}
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              width: switchMerchant ? 100 : 120,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                              marginLeft: 15,
                            }}
                            onPress={() => {
                              if (exportEmail.length > 0) {
                                CommonStore.update(s => {
                                  s.isLoading = true;
                                });
                                setIsLoadingCsv(true);
                                const csvData = convertArrayToCSV(purchaseOrderList);

                                generateEmailReport(
                                  EMAIL_REPORT_TYPE.CSV,
                                  csvData,
                                  'KooDoo Purchase Order Report',
                                  'KooDoo Purchase Order Report.csv',
                                  `/merchant/${merchantId}/reports/${uuidv4()}.csv`,
                                  exportEmail,
                                  'KooDoo Purchase Order Report',
                                  'KooDoo Purchase Order Report',
                                  () => {
                                    CommonStore.update(s => {
                                      s.isLoading = false;
                                    });
                                    setIsLoadingCsv(false);
                                    alert('Success, Report will be sent to the email address shortly');

                                    setExportModal(false);
                                  },
                                );
                              }
                              else {
                                alert('Info, Invalid email address');
                              }

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_SEND_AS_C_CSV,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_SEND_AS_C_CSV,
                              });
                            }}>
                            {
                              isLoadingCsv
                                ?
                                <ActivityIndicator
                                  size={'small'}
                                  color={Colors.whiteColor}
                                />
                                :
                                <Text style={{
                                  color: Colors.whiteColor,
                                  marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                  CSV</Text>
                            }
                          </TouchableOpacity>
                        </View>
                        <Text
                          style={{
                            fontSize: switchMerchant ? 10 : 16,
                            fontFamily: 'NunitoSans-Bold',
                            marginTop: 15,
                          }}>
                          Download As:
                        </Text>
                        <View
                          style={{
                            alignItems: 'center',
                            justifyContent: 'center',
                            flexDirection: 'row',
                            marginTop: 10,
                          }}>
                          <TouchableOpacity
                            disabled={isLoading}
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              width: switchMerchant ? 100 : 120,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                              marginRight: 15,
                            }}
                            onPress={() => {
                              CommonStore.update((s) => {
                                s.isLoading = true;
                              });
                              setIsLoadingLocalExcel(true);
                              // handleExportExcel();

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_EXCEL,
                              });
                            }}>
                            {isLoadingLocalExcel ? (
                              <ActivityIndicator
                                size={'small'}
                                color={Colors.whiteColor}
                              />
                            ) : (
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  //marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                EXCEL
                              </Text>
                            )}
                          </TouchableOpacity>
                          <TouchableOpacity
                            disabled={isLoading}
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              width: switchMerchant ? 100 : 120,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}
                            onPress={() => {
                              CommonStore.update((s) => {
                                s.isLoading = true;
                              });
                              setIsLoadingLocalCsv(true);
                              // handleExportCsv();

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_DOWNLOAD_AS_C_CSV,
                              });
                            }}>
                            {isLoadingLocalCsv ? (
                              <ActivityIndicator
                                size={'small'}
                                color={Colors.whiteColor}
                              />
                            ) : (
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  //marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                CSV
                              </Text>
                            )}
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </View>
                </Modal>

                <Modal
                  style={{
                    // flex: 1
                  }}
                  supportedOrientations={['portrait', 'landscape']}
                  visible={exportPDF}
                  transparent={true}
                  animationType={'fade'}
                >
                  <View style={{
                    flex: 1,
                    backgroundColor: Colors.modalBgColor,
                    alignItems: 'center',
                    justifyContent: 'center',

                    top:
                      Platform.OS === 'android'
                        ? 0
                        : keyboardHeight > 0
                          ? -keyboardHeight * 0.45
                          : 0,
                  }}>
                    <View style={{
                      height: Dimensions.get('screen').width * 0.25,
                      width: Dimensions.get('screen').width * 0.3,
                      backgroundColor: Colors.whiteColor,
                      borderRadius: 12,
                      padding: Dimensions.get('screen').width * 0.03,
                      alignItems: 'center',
                      justifyContent: 'center',

                      // top: keyboardHeight > 0 ? -keyboardHeight * 0.45 : 0,
                      ...getTransformForModalInsideNavigation(),
                    }}>
                      <TouchableOpacity
                        disabled={isLoading}
                        style={{
                          position: 'absolute',
                          right: Dimensions.get('screen').width * 0.02,
                          top: Dimensions.get('screen').width * 0.02,

                          elevation: 1000,
                          zIndex: 1000,
                        }}
                        onPress={() => {
                          setExportPDF(false);
                        }}>
                        <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                      </TouchableOpacity>
                      <View style={{
                        alignItems: 'center',
                        top: 20,
                        position: 'absolute',
                      }}>
                        <Text style={{
                          fontFamily: 'NunitoSans-Bold',
                          textAlign: 'center',
                          fontSize: switchMerchant ? 18 : 20,
                        }}>
                          Export PDF
                        </Text>
                      </View>
                      <View style={{ top: switchMerchant ? '14%' : '10%', }}>
                        {/* <Text style={{ fontSize: switchMerchant ? 10 : 16, fontFamily: 'NunitoSans-Bold' }}>Email Address:</Text>
                      <TextInput
                        style={{
                          backgroundColor: Colors.fieldtBgColor,
                          width: switchMerchant ? 240 : 370,
                          height: switchMerchant ? 35 : 50,
                          borderRadius: 5,
                          padding: 5,
                          marginVertical: 5,
                          borderWidth: 1,
                          borderColor: '#E5E5E5',
                          paddingLeft: 10,
                          fontSize: switchMerchant ? 10 : 16,
                        }}
                        autoCapitalize='none'
                        placeholderStyle={{ padding: 5 }}
                        placeholder="Enter your email"
                        placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        onChangeText={(text) => {
                          setExportEmail(text);

                          logEventAnalytics({
                            eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS,
                            eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_DOWNLOAD_MODAL_TB_EMAIL_ADDRESS,
                          });
                        }}
                        value={exportEmail}
                      /> */}

                        <View style={{
                          alignItems: 'center',
                          justifyContent: 'center',
                          //top: '10%',
                        }}>
                          <TouchableOpacity
                            disabled={isLoading}
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              width: 250,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,

                              marginVertical: 10,
                            }}
                            onPress={async () => {
                              const result = {
                                data: selectedPurchaseOrderView,
                              };

                              const poFile = await convertPOtoPDF(result.data);

                              generateEmailPdf(
                                EMAIL_REPORT_TYPE.PDF,
                                poFile.base64,
                                '[KooDoo]',
                                `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}]-po-${result.data.outletName}-${result.data.supplierName}-${result.data.poId}.pdf`,
                                `/merchant/${merchantId}/po/${uuidv4()}.pdf`,
                                result.data.outletEmail,
                                `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}] PO ${result.data.outletName} | ${result.data.supplierName} | ${result.data.poId}.pdf`,
                                `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}] PO ${result.data.outletName} | ${result.data.supplierName} | ${result.data.poId}.pdf`,
                                () => {
                                  alert(`Info, Email had been sent to ${result.data.outletEmail} already`);
                                },
                              );
                            }}>
                            {
                              isLoading
                                ?
                                <ActivityIndicator
                                  size={'small'}
                                  color={Colors.whiteColor}
                                />
                                :
                                <Text style={{
                                  color: Colors.whiteColor,
                                  //marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                  Purchase Order PDF
                                </Text>
                            }
                          </TouchableOpacity>

                          <TouchableOpacity
                            disabled={isLoading}
                            style={{
                              justifyContent: 'center',
                              flexDirection: 'row',
                              borderWidth: 1,
                              borderColor: Colors.primaryColor,
                              backgroundColor: '#4E9F7D',
                              borderRadius: 5,
                              width: 250,
                              paddingHorizontal: 10,
                              height: switchMerchant ? 35 : 40,
                              alignItems: 'center',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,

                              marginVertical: 10,
                            }}
                            onPress={async () => {
                              const result = {
                                data: selectedPurchaseOrderView,
                              };

                              const poInvoiceFile = await convertDOItoPDF(result.data,);

                              generateEmailPdf(
                                EMAIL_REPORT_TYPE.PDF,
                                poInvoiceFile.base64,
                                '[KooDoo]',
                                `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}]-po-invoice-${result.data.outletName}-${result.data.supplierName}-${result.data.poId}.pdf`,
                                `/merchant/${merchantId}/po/${uuidv4()}.pdf`,
                                result.data.outletEmail,
                                `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}] PO Invoice ${result.data.outletName} | ${result.data.supplierName} | ${result.data.poId}.pdf`,
                                `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}] PO Invoice ${result.data.outletName} | ${result.data.supplierName} | ${result.data.poId}.pdf`,
                                () => {
                                  alert(`Info, Email had been sent to ${result.data.outletEmail} already`);
                                },
                              );
                            }}>
                            {
                              isLoading
                                ?
                                <ActivityIndicator
                                  size={'small'}
                                  color={Colors.whiteColor}
                                />
                                :
                                <Text style={{
                                  color: Colors.whiteColor,
                                  //marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                  Purchase Order Invoice PDF
                                </Text>
                            }
                          </TouchableOpacity>

                          {
                            selectedPurchaseOrderView &&
                              (
                                selectedPurchaseOrderView.status === PURCHASE_ORDER_STATUS.READY
                                ||
                                selectedPurchaseOrderView.status === PURCHASE_ORDER_STATUS.COMPLETED
                              )
                              ?
                              <TouchableOpacity
                                disabled={isLoading}
                                style={{
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  backgroundColor: '#4E9F7D',
                                  borderRadius: 5,
                                  width: 250,
                                  paddingHorizontal: 10,
                                  height: switchMerchant ? 35 : 40,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                  marginVertical: 10,
                                }}
                                onPress={async () => {
                                  const result = {
                                    data: selectedPurchaseOrderView,
                                  };

                                  const doFile = await convertDOtoPDF(result.data);

                                  generateEmailPdf(
                                    EMAIL_REPORT_TYPE.PDF,
                                    doFile.base64,
                                    '[KooDoo]',
                                    `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}]-do-${result.data.outletName}-${result.data.supplierName}-${result.data.poId}.pdf`,
                                    `/merchant/${merchantId}/po/${uuidv4()}.pdf`,
                                    result.data.outletEmail,
                                    `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}] DO ${result.data.outletName} | ${result.data.supplierName} | ${result.data.poId}.pdf`,
                                    `[${moment(result.data.createdAt).format('YYYY-MMM-DD')}] DO ${result.data.outletName} | ${result.data.supplierName} | ${result.data.poId}.pdf`,
                                    () => {
                                      alert(`Info, Email had been sent to ${result.data.outletEmail} already`);
                                    },
                                  );
                                }}>
                                {
                                  isLoading
                                    ?
                                    <ActivityIndicator
                                      size={'small'}
                                      color={Colors.whiteColor}
                                    />
                                    :
                                    <Text style={{
                                      color: Colors.whiteColor,
                                      //marginLeft: 5,
                                      fontSize: switchMerchant ? 10 : 16,
                                      fontFamily: 'NunitoSans-Bold',
                                    }}>
                                      Delivery Order PDF
                                    </Text>
                                }
                              </TouchableOpacity>
                              :
                              <></>
                          }
                        </View>
                      </View>
                    </View>
                  </View>
                </Modal>

                <Modal
                  style={{
                    // flex: 1
                  }}
                  supportedOrientations={['portrait', 'landscape']}
                  visible={importModal}
                  transparent={true}
                  animationType={'fade'}
                >
                  <View style={styles.modalContainer}>
                    <View style={[styles.modalView, {
                      ...getTransformForModalInsideNavigation(),
                    }]}>
                      <TouchableOpacity
                        style={[styles.closeButton, {
                          right: windowWidth * 0.02,
                          top: windowWidth * 0.02,
                        },]}
                        onPress={() => {
                          // setState({ changeTable: false });
                          setImportModal(false);
                        }}>
                        <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                      </TouchableOpacity>
                      <View style={styles.modalTitle}>
                        <Text style={styles.modalTitleText}>
                          Upload Options
                        </Text>
                      </View>
                      <View style={{
                        alignItems: 'center',
                        top: '10%',
                      }}>

                        <TouchableOpacity
                          style={[styles.modalSaveButton, {
                            zIndex: -1,
                            flexDirection: 'row',
                          }]}
                          onPress={() => {
                            // importTemplateData()
                          }}
                          disabled={isLoading}
                        >
                          <Text style={[styles.modalDescText, { color: Colors.primaryColor }]}>{
                            isLoading ? 'Loading...' : 'Batch Upload'
                          }</Text>

                          {isLoading ?
                            <ActivityIndicator style={{
                              marginLeft: 5,
                            }} color={Colors.primaryColor} size={'small'} />
                            : <></>
                          }
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </Modal>

                <DateTimePickerModal
                  isVisible={showDateTimePickerEta}
                  mode={'date'}
                  onConfirm={(text) => {
                    setEtaDate(moment(text).startOf('day'));
                    setShowDateTimePickerEta(false);
                  }}
                  onCancel={() => {
                    setShowDateTimePickerEta(false);
                  }}
                  minimumDate={moment().toDate()}
                  date={moment(etaDate).toDate()}
                />

                <DateTimePickerModal
                  isVisible={showDateTimePicker}
                  mode={'date'}
                  onConfirm={(text) => {
                    setRev_date(moment(text).startOf('day'));
                    setShowDateTimePicker(false);
                  }}
                  onCancel={() => {
                    setShowDateTimePicker(false);
                  }}
                  maximumDate={moment(rev_date1).toDate()}
                  // minimumDate={moment().toDate()}
                  date={moment(rev_date).toDate()}
                />

                <DateTimePickerModal
                  isVisible={showDateTimePicker1}
                  mode={'date'}
                  onConfirm={(text) => {
                    setRev_date1(moment(text).endOf('day'));
                    setShowDateTimePicker1(false);
                  }}
                  onCancel={() => {
                    setShowDateTimePicker1(false);
                  }}
                  minimumDate={moment(rev_date).toDate()}
                  date={moment(rev_date1).toDate()}
                />

                {/* <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: Platform.OS == 'ios' ? 0 : 10 }} >
            <View style={{ marginRight: 248, }}>
              <View style={{ flexDirection: 'row', flex: 1 }}>
                <TouchableOpacity style={[styles.submitText, {
                  height: Dimensions.get('screen').height * 0.05,
                }]} onPress={() => { importCSV() }}>
                  <View style={{ flexDirection: 'row' }}>
                    <Icon name="download" size={20} color={Colors.primaryColor} />
                    <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                      Import
                    </Text>
                  </View>
                </TouchableOpacity>
                <TouchableOpacity style={[styles.submitText, {
                  height: 40,
                }]} onPress={() => { setExportModal(true) }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Icon name="download" size={20} color={Colors.primaryColor} />
                    <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                      Download
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View> */}
                {/* <TextInput
                editable={!loading}
                underlineColorAndroid={Colors.whiteColor}
                clearButtonMode="while-editing"
                style={styles.textInput}
                placeholder="🔍  Search"
                onChangeText={(text) => {
                  setState({ search: text.trim() });
                }}
                value={email}
              /> */}

                {/* <Ionicons
              name="search-outline"
              size={20}
              style={styles.searchIcon}
            />

            <TextInput
              editable={loading}
              clearButtonMode="while-editing"
              style={[styles.textInput, { fontFamily: "NunitoSans-Bold" }]}
              placeholder="Search"
              onChangeText={(text) => {
                setState({
                  search: text.trim(),
                });
              }}
              value={email}
            /> */}

                {/* <View
              style={[{
                // flex: 1,
                // alignContent: 'flex-end',
                // marginBottom: 10,
                // flexDirection: 'row',
                // marginRight: '-40%',
                // marginLeft: 310,
                // backgroundColor: 'red',
                // alignItems: 'flex-end',
                // right: '-50%',
                // width: '28%',
                height: 40,

              }, !isTablet() ? {
                marginLeft: 0,
              } : {}]}>
              <View style={{
                width: 250,
                height: 40,
                backgroundColor: 'white',
                borderRadius: 10,
                // marginLeft: '53%',
                flexDirection: 'row',
                alignContent: 'center',
                alignItems: 'center',

                //marginRight: Dimensions.get('screen').width * Styles.sideBarWidth,

                position: 'absolute',
                right: '35%',

                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 3,
              }}>
                <Icon name="search" size={18} color={Colors.primaryColor} style={{ marginLeft: 15 }} />
                <TextInput
                  editable={!loading}
                  underlineColorAndroid={Colors.whiteColor}
                  style={{
                    width: 250,
                    fontSize: 15,
                    fontFamily: 'NunitoSans-Regular',
                  }}
                  clearButtonMode="while-editing"
                  placeholder=" Search"
                  onChangeText={(text) => {
                    setSearch(text.trim());
                    // setList1(false);
                    // setSearchList(true);
                  }}
                  value={search}
                />
              </View>
            </View> */}

                {/* </View> */}

                {purchaseOrder ? (
                  // <ScrollView 
                  //   scrollEnabled={switchMerchant}
                  //   horizontal={true}>
                  // <ScrollView scrollEnabled={switchMerchant}>
                  <View style={{
                    height: Platform.OS === 'ios' ? Dimensions.get('window').height * 0.63 : Dimensions.get('screen').height * 0.73, width: Dimensions.get('screen').width * 0.87,
                    alignItem: 'center',
                    alignSelf: 'center',
                    ...isMobile() && {
                      width: '100%',
                    }
                  }}>
                    {/* <View> */}
                    <View style={{}}>
                      <View style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        width: Dimensions.get('screen').width * 0.87,
                        alignItem: 'center',
                        alignSelf: 'center',
                        marginBottom: Platform.OS == 'ios' ? 10 : 10,
                        // paddingLeft: '1.5%',
                        // paddingRight: '1.5%',
                        ...isMobile() && {
                          width: '100%',
                        }
                      }}>
                        <View style={{ flexDirection: 'column', }}>
                          <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                            <Text style={{  //fontSize: 30,
                              fontSize: switchMerchant ? 20 : 26, fontFamily: 'NunitoSans-Bold'
                            }}>
                              {/* {stockList.length} */}
                              {purchaseOrderList.length} Purchase Order
                            </Text>
                          </View>
                        </View>

                        <View style={{ flexDirection: 'row', }}>
                          <View style={{ flexDirection: 'row', }}>
                            {/* {isTablet() && ( */}
                            <View
                              style={{ alignItem: 'center', }}>
                              <TouchableOpacity
                                style={{
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  backgroundColor: '#4E9F7D',
                                  borderRadius: 5,
                                  //width: 160,
                                  paddingHorizontal: 10,
                                  height: switchMerchant ? 35 : 40,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                }}
                                onPress={() => {
                                  setExportModal(true);

                                  logEventAnalytics({
                                    eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_C_DOWNLOAD,
                                    eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_C_DOWNLOAD,
                                  });
                                }}>
                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                  <Icon name="download" size={switchMerchant ? 10 : 20} color={Colors.whiteColor} />
                                  <Text style={{
                                    color: Colors.whiteColor,
                                    marginLeft: 5,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                    DOWNLOAD
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                            {/* )} */}
                          </View>

                          <View style={{ flexDirection: 'row', }}>
                            {/* {isTablet() && ( */}
                            <View style={{ alignSelf: 'flex-start', }}>
                              <TouchableOpacity
                                style={{
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  backgroundColor: '#4E9F7D',
                                  borderRadius: 5,
                                  //width: 160,
                                  paddingHorizontal: switchMerchant ? 5 : 10,
                                  height: switchMerchant ? 35 : 40,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                  marginHorizontal: switchMerchant ? 10 : 15,
                                }}
                                onPress={() => {
                                  //// console.log(Dimensions.get('screen').width)
                                  // console.log('NEW PO')
                                  //// console.log('hello123')
                                  CommonStore.update(s => {
                                    s.selectedPurchaseOrderEdit = null;
                                  });

                                  setPurchaseOrder(false);
                                  setAddPurchase(true);

                                  if (supplyItems.length > 0 && supplyItems[0] &&
                                    supplyItems[0].sku && outletSupplyItemsSkuDict &&
                                    outletSupplyItemsSkuDict[supplyItems[0].sku]) {
                                    setPoItems([
                                      {
                                        supplyItemId: supplyItems[0].uniqueId,
                                        name: supplyItems[0].name,
                                        sku: supplyItems[0].sku,
                                        skuMerchant: supplyItems[0].skuMerchant,
                                        quantity: outletSupplyItemsSkuDict[supplyItems[0].sku] ? outletSupplyItemsSkuDict[supplyItems[0].sku].quantity : 0, // check if the supply item sku for this outlet existed
                                        orderQuantity: 0,
                                        unit: supplyItems[0].unit,
                                        receivedQuantity: 0,

                                        price: supplyItems[0].price,
                                        totalPrice: 0,

                                        lastReceivedQuantity: 0,

                                        receivedQuantityActual: 0,

                                        supplyItem: supplyItems[0],
                                      }
                                    ]);
                                  }

                                  logEventAnalytics({
                                    eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_C_ADD_PO,
                                    eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_C_ADD_PO,
                                  });
                                }}>
                                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                  <AntDesign name="pluscircle" size={switchMerchant ? 10 : 20} color={Colors.whiteColor} />
                                  <Text style={{
                                    color: Colors.whiteColor,
                                    marginLeft: 5,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Bold',
                                  }}>
                                    PURCHASE ORDER
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            </View>
                            {/* )} */}
                          </View>

                          <View style={{ flexDirection: 'row', }}>
                            <View
                              style={[{
                                height: switchMerchant ? 35 : 40,

                              }, !isTablet() ? {
                                marginLeft: 0,
                              } : {}]}>
                              <View style={{
                                width: Dimensions.get('screen').width <= 1024 ? 140 : 250,
                                height: switchMerchant ? 35 : 40,
                                backgroundColor: 'white',
                                borderRadius: 5,
                                // marginLeft: '53%',
                                flexDirection: 'row',
                                alignContent: 'center',
                                alignItems: 'center',
                                shadowColor: '#000',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 3,
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                              }}>
                                <Icon name="search" size={switchMerchant ? 13 : 18} color={Colors.primaryColor} style={{ marginLeft: 15 }} />
                                <TextInput
                                  editable={!loading}
                                  underlineColorAndroid={Colors.whiteColor}
                                  style={{
                                    width: Dimensions.get('screen').width <= 1024 ? 110 : 220,
                                    fontSize: switchMerchant ? 10 : 16,
                                    fontFamily: 'NunitoSans-Regular',
                                    paddingLeft: 5,
                                    height: 45,
                                  }}
                                  clearButtonMode="while-editing"
                                  placeholder="Search"
                                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                  onChangeText={(text) => {
                                    // setSearch(text.trim());
                                    setSearch(text);
                                    // setList1(false);
                                    // setSearchList(true);

                                    logEventAnalytics({
                                      eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_TB_SEARCH,
                                      eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_TB_SEARCH,
                                    });
                                  }}
                                  value={search}
                                />
                              </View>
                            </View>
                          </View>
                        </View>

                      </View>
                      <View style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>
                        <View
                          style={[
                            {
                              //marginRight: Platform.OS === 'ios' ? 0 : 10,
                              // paddingLeft: 15,
                              paddingHorizontal: 15,
                              flexDirection: 'row',
                              alignItems: 'center',
                              borderRadius: 10,
                              paddingVertical: 10,
                              justifyContent: 'center',
                              backgroundColor: Colors.whiteColor,
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              marginBottom: 10,
                              marginTop: 10,
                              gap: 10,
                            },
                          ]}>
                          <View
                            style={{ alignSelf: 'center', marginRight: 5 }}
                            onPress={() => {
                              setState({
                                pickerMode: 'date',
                                showDateTimePicker: true,
                              });
                            }}>
                            {/* <EvilIcons name="calendar" size={25} color={Colors.primaryColor} /> */}
                            <GCalendar
                              width={switchMerchant ? 15 : 20}
                              height={switchMerchant ? 15 : 20}
                            />
                          </View>

                          <DatePicker
                            selected={moment(rev_date).toDate()}
                            onChange={(date) => {
                              setRev_date(moment(date).startOf("day"));
                            }}
                            maxDate={moment(rev_date1).toDate()}
                            portalId="1"
                          />

                          {/* <TouchableOpacity
                            onPress={() => {
                              setShowDateTimePicker(true);
                              setShowDateTimePicker1(false);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_C_START_DATE,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_C_START_DATE,
                              });
                            }}
                            style={{
                              marginHorizontal: 4,
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                                  : { fontFamily: 'NunitoSans-Regular' }
                              }>
                              {moment(rev_date).format('DD MMM yyyy')}
                            </Text>
                          </TouchableOpacity> */}

                          <Text
                            style={
                              switchMerchant
                                ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                                : { fontFamily: 'NunitoSans-Regular' }
                            }>
                            -
                          </Text>

                          <DatePicker
                            selected={moment(rev_date1).toDate()}
                            onChange={(date) => {
                              setRev_date1(moment(date).endOf("day"));
                            }}
                            minDate={moment(rev_date).toDate()}
                            portalId="2"
                          />

                          {/* <TouchableOpacity
                            onPress={() => {
                              setShowDateTimePicker(false);
                              setShowDateTimePicker1(true);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_C_END_DATE,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_C_END_DATE,
                              });
                            }}
                            style={{
                              marginHorizontal: 4,
                            }}>
                            <Text
                              style={
                                switchMerchant
                                  ? { fontSize: 10, fontFamily: 'NunitoSans-Regular' }
                                  : { fontFamily: 'NunitoSans-Regular' }
                              }>
                              {moment(rev_date1).format('DD MMM yyyy')}
                            </Text>
                          </TouchableOpacity> */}
                        </View>
                      </View>

                      {renderOrderItem ?
                        <View style={{
                          flexDirection: "row", justifyContent: 'space-between', marginTop: 15, width: Dimensions.get('screen').width * 0.87, alignSelf: 'center',
                          ...isMobile() && {
                            width: '100%',
                          }
                        }}>
                          <TouchableOpacity
                            onPress={() => {
                              setFilterPurchaseOrder(FILTER_PURCHASE_ORDER.PO_TODAY);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_C_PO_TOADY,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_C_PO_TOADY,
                              });
                            }}
                            style={{
                              width: '24%', height: switchMerchant ? 80 : 100, backgroundColor: Colors.tabMint, borderRadius: 10, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 30, paddingVertical: 15, shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 3,
                            }}>
                            <View style={{
                              marginTop: -15,
                            }}>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.whiteColor, fontSize: switchMerchant ? 25 : 28, }}>{purchaseOrderToday}</Text>
                              <Text style={{ fontFamily: 'NunitoSans-Regular', color: Colors.whiteColor, fontSize: switchMerchant ? 10 : 13, fontWeight: '500', }}>PO Today</Text>
                            </View>
                            <View>
                              <AntDesign name='filetext1' size={switchMerchant ? 42 : 50} style={{ color: '#F7F7F7', opacity: 0.68 }} />
                            </View>
                          </TouchableOpacity>

                          <TouchableOpacity
                            onPress={() => {
                              setFilterPurchaseOrder(FILTER_PURCHASE_ORDER.PENDING_PO);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_C_PENDING_PO,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_C_PENDING_PO,
                              });
                            }}
                            style={{
                              width: '24%', height: switchMerchant ? 80 : 100, backgroundColor: Colors.tabGold, borderRadius: 10, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 30, paddingVertical: 15, shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 3,
                            }}>
                            <View style={{
                              marginTop: -15,
                            }}>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.whiteColor, fontSize: switchMerchant ? 25 : 28, }}>{purchaseOrderPending}</Text>
                              <Text style={{ fontFamily: 'NunitoSans-Regular', color: Colors.whiteColor, fontSize: switchMerchant ? 10 : 13, fontWeight: '500', }}>Pending PO</Text>
                            </View>
                            <View>
                              {/* <Coins height={70} width={70} opacity={1} /> */}
                              <AntDesign name='file1' size={switchMerchant ? 42 : 50} style={{ color: '#F7F7F7', opacity: 0.68 }} />
                              <Icon name='loader' size={switchMerchant ? 13 : 20} style={{ color: '#F7F7F7', opacity: 0.68, position: 'absolute', top: 24, left: 17 }} />
                            </View>
                          </TouchableOpacity>

                          <TouchableOpacity
                            onPress={() => {
                              setFilterPurchaseOrder(FILTER_PURCHASE_ORDER.COMPLETED_PO);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_C_COMPLETED_PO,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_C_COMPLETED_PO,
                              });
                            }}
                            style={{
                              width: '24%', height: switchMerchant ? 80 : 100, backgroundColor: Colors.tabCyan, borderRadius: 10, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 30, paddingVertical: 15, shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 3,
                            }}>
                            <View style={{
                              marginTop: -15,
                            }}>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.whiteColor, fontSize: switchMerchant ? 25 : 28, }}>{purchaseOrderCompleted}</Text>
                              <Text style={{ fontFamily: 'NunitoSans-Regular', color: Colors.whiteColor, fontSize: switchMerchant ? 10 : 13, fontWeight: '500', }}>Completed PO</Text>
                            </View>
                            <View>
                              <AntDesign name='file1' size={switchMerchant ? 42 : 50} style={{ color: '#F7F7F7', opacity: 0.68 }} />
                              <Icon name='check' size={switchMerchant ? 17 : 23} style={{ color: '#F7F7F7', opacity: 0.68, position: 'absolute', top: 24, left: 17 }} />
                            </View>
                          </TouchableOpacity>

                          <TouchableOpacity
                            onPress={() => {
                              setFilterPurchaseOrder(FILTER_PURCHASE_ORDER.CANCELLED_PO);

                              logEventAnalytics({
                                eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_C_CANCELLED_PO,
                                eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_C_CANCELLED_PO,
                              });
                            }}
                            style={{
                              width: '24%', height: switchMerchant ? 80 : 100, backgroundColor: Colors.tabRed, borderRadius: 10, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 30, paddingVertical: 15, shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 3,
                            }}>
                            <View style={{
                              marginTop: -15,
                            }}>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', color: Colors.whiteColor, fontSize: switchMerchant ? 25 : 28, }}>{purchaseOrderCancelled}</Text>
                              <Text style={{ fontFamily: 'NunitoSans-Regular', color: Colors.whiteColor, fontSize: switchMerchant ? 10 : 13, fontWeight: '500', }}>Cancelled PO</Text>
                            </View>
                            <View>
                              <AntDesign name='file1' size={switchMerchant ? 42 : 50} style={{ color: '#F7F7F7', opacity: 0.68 }} />
                              <Icon name='x' size={switchMerchant ? 17 : 23} style={{ color: '#F7F7F7', opacity: 0.6, position: 'absolute', top: 24, left: 17 }} />
                            </View>
                          </TouchableOpacity>
                        </View>
                        : null}

                      <View style={
                        switchMerchant ?
                          {
                            backgroundColor: Colors.whiteColor,
                            width: Dimensions.get('screen').width * 0.87,
                            height: Dimensions.get('screen').height * 0.67,
                            marginTop: 20,
                            marginHorizontal: 30,
                            marginBottom: 30,
                            alignSelf: 'center',
                            //borderRadius: 5,
                            shadowOpacity: 0,
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 3,
                            borderRadius: 5,
                            ...isMobile() && {
                              width: '100%',
                            }
                          }
                          :
                          {
                            backgroundColor: Colors.whiteColor,
                            //backgroundColor: 'red',
                            width: Dimensions.get('screen').width * 0.87,
                            height: Platform.OS === 'ios' ? Dimensions.get('screen').height * 0.65 : Dimensions.get('screen').height * 0.53,
                            paddingBottom: 5,
                            marginTop: 20,
                            marginHorizontal: 30,
                            paddingBottom: 30,
                            alignSelf: 'center',
                            //borderRadius: 5,
                            shadowOpacity: 0,
                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 3,
                            borderRadius: 5,
                            ...isMobile() && {
                              width: '100%',
                            }
                          }}>
                        {filterPurchaseOrder === FILTER_PURCHASE_ORDER.ALL_PO ?
                          null
                          :
                          <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 5, marginLeft: 10 }}>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 16 : 18 }}>
                              {filterPurchaseOrder === FILTER_PURCHASE_ORDER.PO_TODAY ? 'Purchase Order Today' :
                                filterPurchaseOrder === FILTER_PURCHASE_ORDER.PENDING_PO ? 'Pending Purchase Order' :
                                  filterPurchaseOrder === FILTER_PURCHASE_ORDER.COMPLETED_PO ? 'Completed Purchase Order' :
                                    filterPurchaseOrder === FILTER_PURCHASE_ORDER.CANCELLED_PO ? 'Cancelled Purchase Order' :
                                      null
                              }
                            </Text>
                            <TouchableOpacity
                              style={{
                                //marginTop: 10,
                                marginLeft: 10,
                                justifyContent: 'center',
                                flexDirection: 'row',
                                borderWidth: 1,
                                borderColor: Colors.primaryColor,
                                backgroundColor: '#4E9F7D',
                                borderRadius: 5,
                                width: switchMerchant ? 90 : 120,
                                paddingHorizontal: 10,
                                height: switchMerchant ? 35 : 40,
                                alignItems: 'center',
                                shadowOffset: {
                                  width: 0,
                                  height: 2,
                                },
                                shadowOpacity: 0.22,
                                shadowRadius: 3.22,
                                elevation: 1,
                                zIndex: -1,
                              }}
                              onPress={() => {
                                setFilterPurchaseOrder(FILTER_PURCHASE_ORDER.ALL_PO);

                                logEventAnalytics({
                                  eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_C_SUMMARY,
                                  eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_C_SUMMARY,
                                });
                              }}
                            >
                              <AntDesign name="arrowleft" size={switchMerchant ? 10 : 20} color={Colors.whiteColor} />
                              <Text style={{
                                color: Colors.whiteColor,
                                marginLeft: 5,
                                fontSize: switchMerchant ? 10 : 16,
                                fontFamily: 'NunitoSans-Bold',
                                marginBottom: Platform.OS === 'ios' ? 0 : 2
                              }}>
                                Summary
                              </Text>
                            </TouchableOpacity>
                          </View>
                        }

                        <View
                          style={{
                            backgroundColor: '#ffffff',
                            flexDirection: 'row',
                            paddingVertical: 20,
                            //paddingTop: 10,
                            paddingHorizontal: 7,
                            paddingRight: 2,
                            //marginTop: 10,
                            borderBottomWidth: StyleSheet.hairlineWidth,
                            width: '100%',
                            borderTopLeftRadius: 5,
                            borderTopRightRadius: 5,
                            borderRadius: 5,
                          }}>
                          <Text style={{ fontSize: switchMerchant ? 10 : 14, width: '16%', alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginRight: 2, left: Platform.OS == 'ios' ? 0 : (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? 0 : 20 }}>
                            PO ID
                          </Text>
                          <Text style={{ fontSize: switchMerchant ? 10 : 14, width: '11%', alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginHorizontal: 2 }}>
                            Created Date
                          </Text>
                          <Text style={{ fontSize: switchMerchant ? 10 : 14, width: '11%', alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginHorizontal: 2 }}>
                            Est Arrival
                          </Text>
                          <Text style={{ fontSize: switchMerchant ? 10 : 14, width: '16%', alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginHorizontal: 2 }}>
                            Target Store
                          </Text>
                          <Text style={{ fontSize: switchMerchant ? 10 : 14, width: '15.5%', alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginHorizontal: 4 }}>
                            Supplier
                          </Text>
                          <Text style={{ fontSize: switchMerchant ? 10 : 14, width: '10.5%', alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginHorizontal: 2 }}>
                            Amount
                          </Text>
                          <Text style={{ fontSize: switchMerchant ? 10 : 14, width: switchMerchant ? '12%' : '14%', alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginLeft: 2, }}>
                            Status
                          </Text>
                          <Text style={{ fontSize: switchMerchant ? 10 : 14, width: '2%', alignSelf: 'center', fontFamily: 'NunitoSans-Bold', marginLeft: 2, }}>
                          </Text>
                        </View>
                        <FlatList
                          nestedScrollEnabled={true}
                          showsVerticalScrollIndicator={false}
                          data={purchaseOrderList.filter(item => {
                            if (search !== '') {
                              const searchLowerCase = search.toLowerCase();

                              return item.supplierName.toLowerCase().includes(searchLowerCase) || item.outletName.toLowerCase().includes(searchLowerCase);
                            }
                            else if (filterPurchaseOrder === FILTER_PURCHASE_ORDER.PO_TODAY) {
                              var currDay = moment(Date.now());

                              return (
                                moment(currDay).isSame(item.orderDate, 'day')
                              );
                            }
                            else if (filterPurchaseOrder === FILTER_PURCHASE_ORDER.PENDING_PO) {
                              return (
                                item.status !== PURCHASE_ORDER_STATUS.COMPLETED
                              );
                            }
                            else if (filterPurchaseOrder === FILTER_PURCHASE_ORDER.COMPLETED_PO) {
                              return (
                                item.status === PURCHASE_ORDER_STATUS.COMPLETED
                              );
                            }
                            else if (filterPurchaseOrder === FILTER_PURCHASE_ORDER.CANCELLED_PO) {
                              return (
                                item.status === PURCHASE_ORDER_STATUS.CANCELLED
                              );
                            }
                            else {
                              return true;
                            }

                          })}
                          // extraData={purchaseOrders.filter(item => {
                          //   if (search !== '') {
                          //     const searchLowerCase = search.toLowerCase();

                          //     return item.supplierName.toLowerCase().includes(searchLowerCase) || item.outletName.toLowerCase().includes(searchLowerCase);
                          //   }
                          //   else {
                          //     return true;
                          //   }
                          // })}
                          renderItem={renderOrderItem}
                          keyExtractor={(item, index) => String(index)}
                          style={{ width: '100%', borderRadius: 5, }}
                          contentContainerStyle={{ borderRadius: 5 }}
                        />
                      </View>
                    </View>
                  </View>
                  // </ScrollView>
                  // </ScrollView>
                ) : null}

                {addPurchase ? (
                  <View style={{
                    height: Platform.OS === 'ios' ? Dimensions.get('window').height - 200 : Dimensions.get('screen').height * 0.76,
                    ...isMobile() && {
                      width: '100%',
                    }
                  }}>
                    <View style={{
                      shadowColor: '#000',
                      shadowOffset: {
                        width: 0,
                        height: 1,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 1,
                    }}>
                      <Modal
                        supportedOrientations={['portrait', 'landscape']}
                        style={{ flex: 1 }}
                        visible={visible}
                        transparent={true}
                        animationType="slide">
                        <View
                          style={{
                            backgroundColor: 'rgba(0,0,0,0.5)',
                            flex: 1,
                            justifyContent: 'center',
                            alignItems: 'center',
                            minHeight: Dimensions.get('window').height,
                          }}>
                          <View style={[styles.confirmBox, { ...getTransformForModalInsideNavigation(), }]}>
                            <TouchableOpacity
                              onPress={() => {
                                setState({ visible: false });
                              }}>
                              <View
                                style={{
                                  alignSelf: 'flex-end',
                                  padding: 16,
                                }}>
                                {/* <Close name="closecircle" size={25} /> */}
                                <AntDesign name="closecircle" size={25} color={Colors.fieldtTxtColor} />
                              </View>
                            </TouchableOpacity>
                            <View>
                              <Text
                                style={{
                                  textAlign: 'center',
                                  fontWeight: '700',
                                  fontSize: 28,
                                }}>
                                Purchase Order
                              </Text>
                            </View>
                            <View style={{ marginTop: 20 }}>
                              <Text
                                style={{
                                  textAlign: 'center',
                                  color: Colors.descriptionColor,
                                  fontSize: 25,
                                }}>
                                Fill in the email information
                              </Text>
                            </View>
                            <View style={{ backgroundColor: 'white', alignSelf: 'center', flexDirection: "row" }}>
                              <Text style={{ fontSize: 20, marginTop: 70 }}>
                                Email:
                              </Text>
                              <View style={{ marginTop: 60, backgroundColor: '#f7f5f5', marginLeft: 10 }}>
                                <TextInput
                                  editable={!loading}
                                  underlineColorAndroid={Colors.fieldtBgColor}
                                  clearButtonMode="while-editing"
                                  style={styles.textCapacity}
                                  placeholder="<EMAIL>"
                                  placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                  onChangeText={(text) => {
                                    setState({ Email: text });
                                  }}
                                  value={Email}
                                />
                              </View>
                            </View>
                            <View
                              style={{
                                flexDirection: 'row',
                                alignSelf: 'center',
                                marginTop: 20,
                                justifyContent: 'center',
                                alignItems: 'center',
                                width: '50%',
                                alignContent: 'center',
                                zIndex: 6000,
                              }}>

                            </View>
                            <View
                              style={{
                                alignSelf: 'center',
                                marginTop: 20,
                                justifyContent: 'center',
                                alignItems: 'center',
                                width: 250,
                                height: 40,
                                alignContent: 'center',
                                flexDirection: "row",
                                marginTop: 40
                              }}>
                              <TouchableOpacity
                                onPress={() => {
                                  // email(),
                                  //   setState({ visible: false });
                                }}
                                style={{
                                  backgroundColor: Colors.fieldtBgColor,
                                  width: '60%',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  alignContent: 'center',
                                  borderRadius: 10,
                                  height: 60,
                                }}>
                                <Text style={{ fontSize: 28, color: Colors.primaryColor }}>
                                  Send
                                </Text>
                              </TouchableOpacity>
                              <TouchableOpacity
                                onPress={() => {
                                  setState({ visible: false });
                                }}
                                style={{
                                  backgroundColor: Colors.fieldtBgColor,
                                  width: '60%',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  alignContent: 'center',
                                  borderRadius: 10,
                                  height: 60,
                                  marginLeft: 30
                                }}>
                                <Text style={{ fontSize: 28, color: Colors.primaryColor }}>
                                  No
                                </Text>
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                      </Modal>

                      <View style={{
                        width: Dimensions.get('screen').width * 0.886,
                        marginTop: switchMerchant ? -10 : 10,
                        alignSelf: 'center',
                        ...isMobile() && {
                          width: '100%',
                        }
                      }}>
                        <TouchableOpacity style={{ flexDirection: 'row', alignContent: 'center', alignItems: 'center', }}
                          onPress={() => {
                            setPurchaseOrder(true);
                            setAddPurchase(false);

                            logEventAnalytics({
                              eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_BACK,
                              eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_BACK,
                            });
                          }}>
                          <Icon name="chevron-left" size={switchMerchant ? 20 : 30} color={Colors.primaryColor} />
                          <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 15 : 17, color: Colors.primaryColor, marginBottom: Platform.OS === 'ios' ? 0 : 2 }}>Back</Text>
                        </TouchableOpacity>
                      </View>

                      <View style={{
                        backgroundColor: Colors.whiteColor,
                        width: Dimensions.get('screen').width * 0.87,
                        height: Platform.OS === 'ios' ? Dimensions.get('screen').height * 0.8 : Dimensions.get('screen').height * 0.7,
                        marginTop: switchMerchant ? 5 : 10,
                        marginHorizontal: 30,
                        marginBottom: 30,
                        alignSelf: 'center',
                        //borderRadius: 5,
                        shadowOpacity: 0,
                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                        borderRadius: 5,
                        ...isMobile() && {
                          width: '100%',
                        }
                      }}>

                        {/* <ScrollView horizontal={true}
                  contentContainerStyle={{
                    width: switchMerchant ? Dimensions.get('screen').width * 1 : Dimensions.get('screen').width * 0.87,
                  }}
                > */}
                        <ScrollView
                          nestedScrollEnabled={true}
                          showsVerticalScrollIndicator={false}
                          style={{
                            // backgroundColor: 'red',
                            // borderRadius: 10,
                            // marginTop: 10,
                            paddingTop: 20,
                          }}
                        // contentContainerStyle={{
                        //   top: Platform.OS === 'ios' ? -keyboardHeight * 0.3 : 0,
                        // }}
                        >

                          {/* <View style={{ width: 140, marginRight: 10, marginTop: 10, alignSelf: "flex-end" }}>
                    <DropDownPicker
                      items={[
                        {
                          label: '🖨️  Print P.O',
                          value: 'Print P.O',
                        },
                        {
                          label: '📧  Email P.O',
                          value: 'Chicken',
                        },
                        {
                          label: '📤  Export Labels',
                          value: 'Export Labels',
                        },
                        {
                          label: '❌  Cancel P.O',
                          value: 'Cancel P.O',
                        },
                        {
                          label: '🗑️  Delete P.O',
                          value: 'Delete P.O',
                        },
                      ]}
                      defaultValue={choice2}
                      placeholder=""
                      containerStyle={{ height: 30 }}
                      style={{ backgroundColor: '#FAFAFA' }}
                      itemStyle={{
                        justifyContent: 'flex-start',
                      }}
                      dropDownStyle={{ backgroundColor: '#FAFAFA' }}
                      onChangeItem={(item) =>
                        setState({
                          choice2: item.value,
                        })
                      }
                    />
                  </View> */}
                          <View
                            style={{
                              borderBottomWidth: StyleSheet.hairlineWidth,
                            }}>
                            <View>
                              <View
                                style={{
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                }}>
                                <View
                                  style={{
                                    width: 140,
                                    paddingLeft: 10,
                                    alignSelf: 'center',
                                    height: 35,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: '#E5E5E5',
                                    justifyContent: 'center',
                                    opacity: 0,
                                  }}>
                                  {/* <RNPickerSelect
                                  items={[
                                    {
                                      label: '🖨️  Print P.O',
                                      value: 'Print P.O',
                                    },
                                    {
                                      label: '📧  Email P.O',
                                      value: 'Chicken',
                                    },
                                    {
                                      label: '📤  Export Labels',
                                      value: 'Export Labels',
                                    },
                                    {
                                      label: '❌  Cancel P.O',
                                      value: 'Cancel P.O',
                                    },
                                    {
                                      label: '🗑️  Delete P.O',
                                      value: 'Delete P.O',
                                    },
                                  ]}
                                  defaultValue={choice2}
                                  placeholder={{}}
                                  containerStyle={{ height: 30 }}
                                  style={{
                                    backgroundColor: '#FAFAFA',
                                  }}
                                  itemStyle={{
                                    justifyContent: 'flex-start',
                                  }}
                                  dropDownStyle={{ backgroundColor: '#FAFAFA' }}
                                  onValueChange={(item) => {
                                    setState({
                                      choice2: item.value,
                                    })
                                  }}
                                /> */}
                                </View>
                                <View style={{ flexDirection: 'column' }}>
                                  <Text style={{ alignSelf: "center", marginTop: 20, fontSize: switchMerchant ? 20 : 40, fontWeight: 'bold' }}>{titleName}</Text>
                                  <Text style={{ alignSelf: "center", fontSize: switchMerchant ? 10 : 16, color: '#adadad' }}>Fill In The Purchase Order Information</Text>
                                </View>
                                <View style={{ marginRight: 10, marginTop: 20 }}>
                                  {(selectedPurchaseOrderEdit) ?
                                    <>
                                      {
                                        (
                                          selectedPurchaseOrderEdit.status !== PURCHASE_ORDER_STATUS.COMPLETED
                                        )
                                          ?
                                          <>
                                            <View
                                              style={{
                                                justifyContent: 'center',
                                                flexDirection: 'row',
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: '#4E9F7D',
                                                borderRadius: 5,
                                                width: switchMerchant ? 120 : 140,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 35 : 40,
                                                alignItems: 'center',
                                                shadowOffset: {
                                                  width: 0,
                                                  height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                                opacity: poStatus === PURCHASE_ORDER_STATUS.COMPLETED ? 0 : 100
                                              }}>
                                              <TouchableOpacity
                                                disabled={poStatus === PURCHASE_ORDER_STATUS.COMPLETED}
                                                style={{ width: switchMerchant ? 120 : 140, alignItems: 'center' }}
                                                onPress={() => {
                                                  if (selectedPurchaseOrderEdit.supplierOutlet === 'N/A' ||
                                                    selectedPurchaseOrderEdit.supplierOutlet === undefined
                                                  ) {
                                                    // means single outlet flow
                                                    if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.CREATED ||
                                                      selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.ORDERED
                                                    ) {
                                                      // Alert.alert('Info', `Will need ${supplierOutletData.name} to process the PO first.`);

                                                      // CommonStore.update(s => {
                                                      //   s.isLoading = false;
                                                      // });

                                                      createPurchaseOrder();
                                                    }
                                                    else if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.PARTIALLY_RECEIVED ||
                                                      selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.ALL_RECEIVED) {
                                                      // Alert.alert('Info', `This PO is already updated, please click on the READY button to proceed.`);
                                                      window.confirm(`This PO is already updated, please click on the READY button to proceed.`);

                                                      CommonStore.update(s => {
                                                        s.isLoading = false;
                                                      });
                                                    }
                                                    else if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.READY) {
                                                      // to update the po

                                                      // // Alert.alert('Info', `This PO can only updated from ${supplierOutletData.name}.`);
                                                      // Alert.alert('Info', `Please click on the COMPLETE button to finish the process.`);

                                                      // CommonStore.update(s => {
                                                      //   s.isLoading = false;
                                                      // });

                                                      createPurchaseOrder();
                                                    }
                                                    else if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.RECEIVED_ACTUAL) {
                                                      // to complete the po

                                                      // Alert.alert('Info', `This PO can only updated from ${supplierOutletData.name}.`);
                                                      // Alert.alert('Info', `Please click on the COMPLETE button to finish the process.`);
                                                      window.confirm(`Please click on the COMPLETE button to finish the process.`);

                                                      CommonStore.update(s => {
                                                        s.isLoading = false;
                                                      });

                                                      // createPurchaseOrder();
                                                    }
                                                    else if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.COMPLETED) {
                                                      // Alert.alert('Info', `This PO is already completed.`);
                                                      window.confirm(`This PO is already completed.`);

                                                      CommonStore.update(s => {
                                                        s.isLoading = false;
                                                      });
                                                    }
                                                  } else {
                                                    if (currOutlet.uniqueId === selectedPurchaseOrderEdit.supplierOutlet) {
                                                      // means now in the central kitchen outlet

                                                      if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.CREATED ||
                                                        selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.ORDERED ||
                                                        selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.PARTIALLY_RECEIVED
                                                      ) {
                                                        createPurchaseOrder();
                                                      }
                                                      else if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.ALL_RECEIVED
                                                      ) {
                                                        // Alert.alert('Info', `This PO is already updated, please click on the READY button to proceed.`);
                                                        window.confirm(`This PO is already updated, please click on the READY button to proceed.`);

                                                        CommonStore.update(s => {
                                                          s.isLoading = false;
                                                        });
                                                      }
                                                      else if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.READY) {
                                                        // Alert.alert('Info', `Will need ${selectedPurchaseOrderEdit.outletName} to receive and complete the PO.`);
                                                        window.confirm(`Will need ${selectedPurchaseOrderEdit.outletName} to receive and complete the PO.`);

                                                        CommonStore.update(s => {
                                                          s.isLoading = false;
                                                        });
                                                      }
                                                      else if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.RECEIVED_ACTUAL) {
                                                        // Alert.alert('Info', `Will need ${selectedPurchaseOrderEdit.outletName} to complete the PO.`);
                                                        window.confirm(`Will need ${selectedPurchaseOrderEdit.outletName} to complete the PO.`);

                                                        CommonStore.update(s => {
                                                          s.isLoading = false;
                                                        });
                                                      }
                                                      else if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.CANCELLED) {
                                                        // Alert.alert('Info', `This PO is already cancelled.`);
                                                        window.confirm(`This PO is already cancelled.`);

                                                        CommonStore.update(s => {
                                                          s.isLoading = false;
                                                        });
                                                      }
                                                      else if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.COMPLETED) {
                                                        // Alert.alert('Info', `This PO is already completed.`);
                                                        window.confirm(`This PO is already completed.`);

                                                        CommonStore.update(s => {
                                                          s.isLoading = false;
                                                        });
                                                      }
                                                      else {
                                                        // Alert.alert('Info', 'Something went wrong, please try again.');
                                                        window.confirm('Something went wrong, please try again.');

                                                        CommonStore.update(s => {
                                                          s.isLoading = false;
                                                        });
                                                      }
                                                    }
                                                    else {
                                                      // means now in the source outlet

                                                      let supplierOutletData = allOutlets.find(outlet => outlet.uniqueId === selectedPurchaseOrderEdit.supplierOutlet);

                                                      if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.CREATED ||
                                                        selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.ORDERED ||
                                                        selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.PARTIALLY_RECEIVED ||
                                                        selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.ALL_RECEIVED
                                                      ) {
                                                        // Alert.alert('Info', `Will need ${supplierOutletData.name} to process the PO first.`);
                                                        window.confirm(`Will need ${supplierOutletData.name} to process the PO first.`);

                                                        CommonStore.update(s => {
                                                          s.isLoading = false;
                                                        });
                                                      }
                                                      else if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.READY) {
                                                        // to update the po

                                                        // // Alert.alert('Info', `This PO can only updated from ${supplierOutletData.name}.`);
                                                        // Alert.alert('Info', `Please click on the COMPLETE button to finish the process.`);

                                                        // CommonStore.update(s => {
                                                        //   s.isLoading = false;
                                                        // });

                                                        createPurchaseOrder();
                                                      }
                                                      else if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.RECEIVED_ACTUAL) {
                                                        // to complete the po

                                                        // Alert.alert('Info', `This PO can only updated from ${supplierOutletData.name}.`);
                                                        // Alert.alert('Info', `Please click on the COMPLETE button to finish the process.`);
                                                        window.confirm(`Please click on the COMPLETE button to finish the process.`);

                                                        CommonStore.update(s => {
                                                          s.isLoading = false;
                                                        });

                                                        // createPurchaseOrder();
                                                      }
                                                      else if (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.COMPLETED) {
                                                        // Alert.alert('Info', `This PO is already completed.`);
                                                        window.confirm(`This PO is already completed.`);

                                                        CommonStore.update(s => {
                                                          s.isLoading = false;
                                                        });
                                                      }
                                                      else {
                                                        // Alert.alert('Info', 'Something went wrong, please try again.');
                                                        window.confirm('Something went wrong, please try again.');

                                                        CommonStore.update(s => {
                                                          s.isLoading = false;
                                                        });
                                                      }
                                                    }
                                                  }

                                                  logEventAnalytics({
                                                    eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_UPDATE,
                                                    eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_UPDATE,
                                                  });
                                                }}>
                                                {isLoading ?
                                                  <>
                                                    <ActivityIndicator color={Colors.whiteColor} size={"small"} />
                                                  </>
                                                  :
                                                  <Text style={{
                                                    color: Colors.whiteColor,
                                                    //marginLeft: 5,
                                                    fontSize: switchMerchant ? 10 : 16,
                                                    fontFamily: 'NunitoSans-Bold',
                                                  }}>
                                                    UPDATE
                                                  </Text>
                                                }
                                              </TouchableOpacity>
                                            </View>

                                            {
                                              (selectedPurchaseOrderEdit &&
                                                selectedPurchaseOrderEdit.supplierOutlet &&
                                                // selectedPurchaseOrderEdit.supplierOutlet !== 'N/A' &&
                                                selectedPurchaseOrderEdit.outletId === currOutlet.uniqueId &&
                                                (
                                                  selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.RECEIVED_ACTUAL
                                                )
                                              )
                                                ?
                                                <View
                                                  style={{
                                                    justifyContent: 'center',
                                                    flexDirection: 'row',
                                                    borderWidth: 1,
                                                    borderColor: Colors.primaryColor,
                                                    backgroundColor: '#4E9F7D',
                                                    borderRadius: 5,
                                                    width: switchMerchant ? 120 : 140,
                                                    paddingHorizontal: 10,
                                                    height: switchMerchant ? 35 : 40,
                                                    alignItems: 'center',
                                                    shadowOffset: {
                                                      width: 0,
                                                      height: 2,
                                                    },
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 1,
                                                    zIndex: -1,
                                                    marginTop: 10,
                                                  }}>
                                                  <TouchableOpacity
                                                    style={{ width: switchMerchant ? 120 : 140, alignItems: 'center' }}
                                                    onPress={() => {
                                                      createPurchaseOrderCompleted();
                                                      setIsLoadingSaveReady(true);

                                                      // logEventAnalytics({
                                                      //   eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_AND_SEND,
                                                      //   eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_AND_SEND,
                                                      // });
                                                    }}>
                                                    {
                                                      isLoadingSaveCompleted
                                                        ?
                                                        <>
                                                          <ActivityIndicator color={Colors.whiteColor} size={"small"} />
                                                        </>
                                                        :
                                                        <Text style={{
                                                          color: Colors.whiteColor,
                                                          //marginLeft: 5,
                                                          fontSize: switchMerchant ? 10 : 16,
                                                          fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                          COMPLETE
                                                        </Text>
                                                    }
                                                  </TouchableOpacity>
                                                </View>
                                                :
                                                <></>
                                            }
                                          </>
                                          :
                                          <></>
                                      }
                                    </>
                                    :
                                    <>
                                      <View
                                        style={{
                                          justifyContent: 'center',
                                          flexDirection: 'row',
                                          borderWidth: 1,
                                          borderColor: Colors.primaryColor,
                                          backgroundColor: '#4E9F7D',
                                          borderRadius: 5,
                                          width: switchMerchant ? 120 : 140,
                                          paddingHorizontal: 10,
                                          height: switchMerchant ? 35 : 40,
                                          alignItems: 'center',
                                          shadowOffset: {
                                            width: 0,
                                            height: 2,
                                          },
                                          shadowOpacity: 0.22,
                                          shadowRadius: 3.22,
                                          elevation: 1,
                                          zIndex: -1,
                                        }}>
                                        <TouchableOpacity
                                          style={{ width: switchMerchant ? 120 : 140, alignItems: 'center' }}
                                          onPress={() => {
                                            createPurchaseOrder();
                                            setIsLoadingSave(true);

                                            logEventAnalytics({
                                              eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE,
                                              eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE,
                                            });
                                          }}>
                                          {
                                            isLoadingSave
                                              ?
                                              <>
                                                <ActivityIndicator color={Colors.whiteColor} size={"small"} />
                                              </>
                                              :
                                              <Text style={{
                                                color: Colors.whiteColor,
                                                //marginLeft: 5,
                                                fontSize: switchMerchant ? 10 : 16,
                                                fontFamily: 'NunitoSans-Bold',
                                              }}>
                                                SAVE
                                              </Text>
                                          }
                                        </TouchableOpacity>
                                      </View>
                                      {/* <View
                                      style={{
                                        justifyContent: 'center',
                                        flexDirection: 'row',
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        backgroundColor: '#4E9F7D',
                                        borderRadius: 5,
                                        width: switchMerchant ? 120 : 140,
                                        paddingHorizontal: 10,
                                        height: switchMerchant ? 35 : 40,
                                        alignItems: 'center',
                                        shadowOffset: {
                                          width: 0,
                                          height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                        zIndex: -1,
                                        marginTop: 10,
                                      }}>
                                      <TouchableOpacity
                                        style={{ width: switchMerchant ? 120 : 140, alignItems: 'center' }}
                                        onPress={() => {
                                          createPurchaseOrder();
                                          setIsLoadingSaveAndSend(true);

                                          logEventAnalytics({
                                            eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_AND_SEND,
                                            eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_AND_SEND,
                                          });
                                        }}>
                                        {
                                          isLoadingSaveAndSend
                                            ?
                                            <>
                                              <ActivityIndicator color={Colors.whiteColor} size={"small"} />
                                            </>
                                            :
                                            <Text style={{
                                              color: Colors.whiteColor,
                                              //marginLeft: 5,
                                              fontSize: switchMerchant ? 10 : 16,
                                              fontFamily: 'NunitoSans-Bold',
                                            }}>
                                              SAVE & SEND
                                            </Text>
                                        }
                                      </TouchableOpacity>
                                    </View> */}
                                    </>
                                  }

                                  {/* 2025-03-13 - for mark this order ready button */}

                                  {
                                    (selectedPurchaseOrderEdit &&
                                      (
                                        selectedPurchaseOrderEdit.supplierOutlet === currOutlet.uniqueId
                                        ||
                                        (
                                          (
                                            selectedPurchaseOrderEdit.supplierOutlet === undefined
                                            ||
                                            selectedPurchaseOrderEdit.supplierOutlet === 'N/A'
                                          )
                                          &&
                                          // selectedPurchaseOrderEdit.supplierOutlet === currOutlet.uniqueId
                                          selectedPurchaseOrderEdit.outletId === currOutlet.uniqueId
                                        )
                                      ) &&
                                      (
                                        // selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.CREATED ||
                                        // selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.ORDERED ||
                                        selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.PARTIALLY_RECEIVED ||
                                        selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.ALL_RECEIVED
                                      )
                                    )
                                      ?
                                      <View
                                        style={{
                                          justifyContent: 'center',
                                          flexDirection: 'row',
                                          borderWidth: 1,
                                          borderColor: Colors.primaryColor,
                                          backgroundColor: '#4E9F7D',
                                          borderRadius: 5,
                                          width: switchMerchant ? 120 : 140,
                                          paddingHorizontal: 10,
                                          height: switchMerchant ? 35 : 40,
                                          alignItems: 'center',
                                          shadowOffset: {
                                            width: 0,
                                            height: 2,
                                          },
                                          shadowOpacity: 0.22,
                                          shadowRadius: 3.22,
                                          elevation: 1,
                                          zIndex: -1,
                                          marginTop: 10,
                                        }}>
                                        <TouchableOpacity
                                          style={{ width: switchMerchant ? 120 : 140, alignItems: 'center' }}
                                          onPress={() => {
                                            createPurchaseOrderReady();
                                            setIsLoadingSaveReady(true);

                                            // logEventAnalytics({
                                            //   eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_AND_SEND,
                                            //   eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_SAVE_AND_SEND,
                                            // });
                                          }}>
                                          {
                                            isLoadingSaveReady
                                              ?
                                              <>
                                                <ActivityIndicator color={Colors.whiteColor} size={"small"} />
                                              </>
                                              :
                                              <Text style={{
                                                color: Colors.whiteColor,
                                                //marginLeft: 5,
                                                fontSize: switchMerchant ? 10 : 16,
                                                fontFamily: 'NunitoSans-Bold',
                                              }}>
                                                READY
                                              </Text>
                                          }
                                        </TouchableOpacity>
                                      </View>
                                      :
                                      <></>
                                  }
                                </View>
                              </View>
                            </View>

                            {/* Status bar */}
                            <View
                              style={{
                                //width: '100%',
                                flexDirection: 'row',
                                alignItems: 'center',
                                top: '2%',
                                justifyContent: 'center',
                              }}
                            >
                              {selectedPurchaseOrderEdit && selectedPurchaseOrderEdit.status ?
                                <>
                                  <View
                                    style={{
                                      flexDirection: 'column',
                                      //marginTop: 10,
                                    }}
                                  >
                                    <Text
                                      style=
                                      {{
                                        color: Colors.descriptionColor,
                                        fontSize: 24,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        // marginRight: 10,
                                        textAlign: 'center',

                                        marginBottom: 43,
                                      }}
                                    >{'Pending'}</Text>
                                  </View>

                                  <MaterialCommunityIcons
                                    name="chevron-right"
                                    size={35}
                                    color={'black'}
                                    style={{ marginBottom: 43, }}
                                  />

                                  <View
                                    style={{
                                      flexDirection: 'column',
                                      alignItems: 'center',
                                      marginRight: 10,
                                      marginLeft: 5,

                                    }}
                                  >
                                    <Text
                                      style={{
                                        color: selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.CREATED ? Colors.primaryColor : Colors.descriptionColor,
                                        fontSize: 24,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        textAlign: "center",
                                        marginBottom: 43,
                                        ...(selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.CREATED) && {
                                          marginBottom: 0,
                                          marginTop: 10,
                                        },
                                      }}
                                    >{'Created'}</Text>

                                    {selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.CREATED ? (
                                      <View
                                        style={{
                                          marginTop: 15,
                                          marginLeft: 10,
                                        }}
                                      >

                                        <View
                                          style={{
                                            right: -18,
                                            width: 0,
                                            height: 0,
                                            backgroundColor: "transparent",
                                            borderStyle: "solid",
                                            borderLeftWidth: 5,
                                            borderRightWidth: 5,
                                            borderBottomWidth: 10,
                                            borderLeftColor: "transparent",
                                            borderRightColor: "transparent",
                                            borderBottomColor: Colors.tabCyan,

                                          }}
                                        />

                                        <View
                                          style={{
                                            left: -3,
                                            width: 55,
                                            height: 30,
                                            backgroundColor: Colors.tabCyan,
                                            borderRadius: 10,

                                          }}
                                        >
                                          <Text
                                            style={{
                                              color: 'white',
                                              fontSize: 16,
                                              fontFamily: 'NunitoSans-SemiBold',
                                              marginRight: 10,
                                              marginLeft: 10,
                                              marginTop: 3,
                                            }}
                                          >
                                            {'Now'}
                                          </Text>
                                        </View>
                                      </View>
                                    ) : <></>}

                                  </View>

                                  <MaterialCommunityIcons
                                    name="chevron-right"
                                    size={35}
                                    color={'black'}
                                    style={{ marginBottom: 43, }}
                                  />

                                  <View
                                    style={{
                                      flexDirection: 'column',
                                      alignItems: 'center',
                                      marginRight: 10,
                                      marginLeft: 5,
                                    }}
                                  >
                                    <Text
                                      style={{
                                        color: (selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.PARTIALLY_RECEIVED ||
                                          selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.ALL_RECEIVED) ? Colors.primaryColor : Colors.descriptionColor,
                                        fontSize: 24,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        marginBottom: 43,
                                        ...(selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.PARTIALLY_RECEIVED ||
                                          selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.ALL_RECEIVED) && {
                                          marginBottom: 0,
                                          marginTop: 10,
                                        },
                                      }}
                                    >{'Prepared'}</Text>
                                    {selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.PARTIALLY_RECEIVED ||
                                      selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.ALL_RECEIVED ? (
                                      <View
                                        style={{
                                          marginTop: 15,
                                          marginLeft: 10,
                                        }}
                                      >

                                        <View
                                          style={{
                                            right: -18,
                                            width: 0,
                                            height: 0,
                                            backgroundColor: "transparent",
                                            borderStyle: "solid",
                                            borderLeftWidth: 5,
                                            borderRightWidth: 5,
                                            borderBottomWidth: 10,
                                            borderLeftColor: "transparent",
                                            borderRightColor: "transparent",
                                            borderBottomColor: Colors.tabCyan,

                                          }}
                                        />

                                        <View
                                          style={{
                                            left: -3,
                                            width: 55,
                                            height: 30,
                                            backgroundColor: Colors.tabCyan,
                                            borderRadius: 10,

                                          }}
                                        >
                                          <Text
                                            style={{
                                              color: 'white',
                                              fontSize: 16,
                                              fontFamily: 'NunitoSans-SemiBold',
                                              marginRight: 10,
                                              marginLeft: 10,
                                              marginTop: 3,
                                            }}
                                          >
                                            {'Now'}
                                          </Text>
                                        </View>
                                      </View>
                                    ) : <></>}
                                  </View>

                                  <MaterialCommunityIcons
                                    name="chevron-right"
                                    size={35}
                                    color={'black'}
                                    style={{ marginBottom: 43, }}
                                  />
                                  <View
                                    style={{
                                      flexDirection: 'column',
                                      alignItems: 'center',
                                      marginRight: 10,
                                      marginLeft: 5,

                                    }}
                                  >
                                    <Text
                                      style={{
                                        color: selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.READY ? Colors.primaryColor : Colors.descriptionColor,
                                        fontSize: 24,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        textAlign: "center",
                                        marginBottom: 43,
                                        ...(selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.READY) && {
                                          marginBottom: 0,
                                          marginTop: 10,
                                        },
                                      }}
                                    >{'Ready'}</Text>
                                    {selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.READY ? (
                                      <View
                                        style={{
                                          marginTop: 15,
                                          marginLeft: 10,
                                        }}
                                      >

                                        <View
                                          style={{
                                            right: -18,
                                            width: 0,
                                            height: 0,
                                            backgroundColor: "transparent",
                                            borderStyle: "solid",
                                            borderLeftWidth: 5,
                                            borderRightWidth: 5,
                                            borderBottomWidth: 10,
                                            borderLeftColor: "transparent",
                                            borderRightColor: "transparent",
                                            borderBottomColor: Colors.tabCyan,

                                          }}
                                        />

                                        <View
                                          style={{
                                            left: -3,
                                            width: 55,
                                            height: 30,
                                            backgroundColor: Colors.tabCyan,
                                            borderRadius: 10,

                                          }}
                                        >
                                          <Text
                                            style={{
                                              color: 'white',
                                              fontSize: 16,
                                              fontFamily: 'NunitoSans-SemiBold',
                                              marginRight: 10,
                                              marginLeft: 10,
                                              marginTop: 3,
                                            }}
                                          >
                                            {'Now'}
                                          </Text>
                                        </View>
                                      </View>
                                    ) : <></>}

                                  </View>

                                  {/* ///////////////////// */}
                                  {/* 2025-03-19 - received */}

                                  <MaterialCommunityIcons
                                    name="chevron-right"
                                    size={35}
                                    color={'black'}
                                    style={{ marginBottom: 43, }}
                                  />
                                  <View
                                    style={{
                                      flexDirection: 'column',
                                      alignItems: 'center',
                                      marginRight: 10,
                                      marginLeft: 5,

                                    }}
                                  >
                                    <Text
                                      style={{
                                        color: selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.RECEIVED_ACTUAL ? Colors.primaryColor : Colors.descriptionColor,
                                        fontSize: 24,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        textAlign: "center",
                                        marginBottom: 43,
                                        ...(selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.RECEIVED_ACTUAL) && {
                                          marginBottom: 0,
                                          marginTop: 10,
                                        },
                                      }}
                                    >{'Received'}</Text>
                                    {selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.RECEIVED_ACTUAL ? (
                                      <View
                                        style={{
                                          marginTop: 15,
                                          marginLeft: 10,
                                        }}
                                      >

                                        <View
                                          style={{
                                            right: -18,
                                            width: 0,
                                            height: 0,
                                            backgroundColor: "transparent",
                                            borderStyle: "solid",
                                            borderLeftWidth: 5,
                                            borderRightWidth: 5,
                                            borderBottomWidth: 10,
                                            borderLeftColor: "transparent",
                                            borderRightColor: "transparent",
                                            borderBottomColor: Colors.tabCyan,

                                          }}
                                        />

                                        <View
                                          style={{
                                            left: -3,
                                            width: 55,
                                            height: 30,
                                            backgroundColor: Colors.tabCyan,
                                            borderRadius: 10,

                                          }}
                                        >
                                          <Text
                                            style={{
                                              color: 'white',
                                              fontSize: 16,
                                              fontFamily: 'NunitoSans-SemiBold',
                                              marginRight: 10,
                                              marginLeft: 10,
                                              marginTop: 3,
                                            }}
                                          >
                                            {'Now'}
                                          </Text>
                                        </View>
                                      </View>
                                    ) : <></>}

                                  </View>

                                  {/* ///////////////////// */}

                                  <MaterialCommunityIcons
                                    name="chevron-right"
                                    size={35}
                                    color={'black'}
                                    style={{ marginBottom: 43, }}
                                  />
                                  <View
                                    style={{
                                      flexDirection: 'column',
                                      alignItems: 'center',
                                      marginRight: 10,
                                      marginLeft: 5,

                                    }}
                                  >
                                    <Text
                                      style={{
                                        color: selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.COMPLETED ? Colors.primaryColor : Colors.descriptionColor,
                                        fontSize: 24,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        textAlign: "center",
                                        marginBottom: 43,
                                        ...(selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.COMPLETED) && {
                                          marginBottom: 0,
                                          marginTop: 10,
                                        },
                                      }}
                                    >{'Completed'}</Text>
                                    {selectedPurchaseOrderEdit.status === PURCHASE_ORDER_STATUS.COMPLETED ? (
                                      <View
                                        style={{
                                          marginTop: 15,
                                          marginLeft: 10,
                                        }}
                                      >

                                        <View
                                          style={{
                                            right: -18,
                                            width: 0,
                                            height: 0,
                                            backgroundColor: "transparent",
                                            borderStyle: "solid",
                                            borderLeftWidth: 5,
                                            borderRightWidth: 5,
                                            borderBottomWidth: 10,
                                            borderLeftColor: "transparent",
                                            borderRightColor: "transparent",
                                            borderBottomColor: Colors.tabCyan,

                                          }}
                                        />

                                        <View
                                          style={{
                                            left: -3,
                                            width: 55,
                                            height: 30,
                                            backgroundColor: Colors.tabCyan,
                                            borderRadius: 10,

                                          }}
                                        >
                                          <Text
                                            style={{
                                              color: 'white',
                                              fontSize: 16,
                                              fontFamily: 'NunitoSans-SemiBold',
                                              marginRight: 10,
                                              marginLeft: 10,
                                              marginTop: 3,
                                            }}
                                          >
                                            {'Now'}
                                          </Text>
                                        </View>
                                      </View>
                                    ) : <></>}

                                  </View>
                                </>
                                :
                                <>
                                  <View
                                    style={{
                                      flexDirection: 'column',
                                      //marginTop: 10,
                                      alignItems: 'center',
                                    }}
                                  >
                                    <Text
                                      style=
                                      {{
                                        color: true ? Colors.primaryColor : Colors.descriptionColor,
                                        fontSize: 24,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        marginRight: 10,
                                        textAlign: 'center',
                                        ...(true) && {
                                          marginBottom: 0,
                                          marginTop: 10,
                                        },
                                      }}
                                    >{'Pending'}</Text>

                                    {true ? (
                                      <View
                                        style={{
                                          marginTop: 15,
                                        }}
                                      >

                                        <View
                                          style={{
                                            right: -18,
                                            width: 0,
                                            height: 0,
                                            backgroundColor: "transparent",
                                            borderStyle: "solid",
                                            borderLeftWidth: 5,
                                            borderRightWidth: 5,
                                            borderBottomWidth: 10,
                                            borderLeftColor: "transparent",
                                            borderRightColor: "transparent",
                                            borderBottomColor: Colors.tabCyan,

                                          }}
                                        />

                                        <View
                                          style={{
                                            left: -3,
                                            width: 55,
                                            height: 30,
                                            backgroundColor: Colors.tabCyan,
                                            borderRadius: 10,

                                          }}
                                        >
                                          <Text
                                            style={{
                                              color: 'white',
                                              fontSize: 16,
                                              fontFamily: 'NunitoSans-SemiBold',
                                              marginRight: 10,
                                              marginLeft: 10,
                                              marginTop: 3,
                                            }}
                                          >
                                            {'Now'}
                                          </Text>
                                        </View>
                                      </View>
                                    ) : <></>}

                                  </View>

                                  <MaterialCommunityIcons
                                    name="chevron-right"
                                    size={35}
                                    color={'black'}
                                    style={{ marginBottom: 43, }}
                                  />

                                  <View
                                    style={{
                                      flexDirection: 'column',
                                      //marginTop: 10,
                                    }}
                                  >
                                    <Text
                                      style=
                                      {{
                                        color: Colors.descriptionColor,
                                        fontSize: 24,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        marginBottom: 43,
                                      }}
                                    >{'Created'}</Text>
                                  </View>

                                  <MaterialCommunityIcons
                                    name="chevron-right"
                                    size={35}
                                    color={'black'}
                                    style={{ marginBottom: 43, }}
                                  />

                                  <View
                                    style={{
                                      flexDirection: 'column',
                                      //marginTop: 10,
                                      marginRight: 10,
                                      marginLeft: 10,
                                    }}
                                  >
                                    <Text
                                      style={{
                                        color: Colors.descriptionColor,
                                        fontSize: 24,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        marginBottom: 43,
                                      }}
                                    >{'Prepared'}</Text>
                                  </View>

                                  <MaterialCommunityIcons
                                    name="chevron-right"
                                    size={35}
                                    color={'black'}
                                    style={{ marginBottom: 43, }}
                                  />
                                  <View
                                    style={{
                                      flexDirection: 'column',
                                      marginRight: 10,
                                      marginLeft: 5,
                                    }}
                                  >
                                    <Text
                                      style={{
                                        color: Colors.descriptionColor,
                                        fontSize: 24,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        textAlign: "center",
                                        marginBottom: 43,
                                      }}
                                    >{'Ready'}</Text>
                                  </View>

                                  <MaterialCommunityIcons
                                    name="chevron-right"
                                    size={35}
                                    color={'black'}
                                    style={{ marginBottom: 43, }}
                                  />

                                  <View
                                    style={{
                                      flexDirection: 'column',
                                      //marginTop: 10,
                                      marginRight: 10,
                                      marginLeft: 10,
                                    }}
                                  >
                                    <Text
                                      style={{
                                        color: Colors.descriptionColor,
                                        fontSize: 24,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        marginBottom: 43,
                                      }}
                                    >{'Received'}</Text>
                                  </View>

                                  <MaterialCommunityIcons
                                    name="chevron-right"
                                    size={35}
                                    color={'black'}
                                    style={{ marginBottom: 43, }}
                                  />
                                  <View
                                    style={{
                                      flexDirection: 'column',
                                      marginRight: 10,
                                      marginLeft: 5,
                                    }}
                                  >
                                    <Text
                                      style={{
                                        color: Colors.descriptionColor,
                                        fontSize: 24,
                                        fontFamily: 'NunitoSans-SemiBold',
                                        textAlign: "center",
                                        marginBottom: 43,
                                      }}
                                    >{'Completed'}</Text>
                                  </View>
                                </>
                              }
                            </View>

                            <View style={{ flexDirection: "row", marginTop: 10, justifyContent: 'space-evenly', marginTop: 50, width: '90%', alignSelf: 'center', marginLeft: Dimensions.get('screen').width * Styles.sideBarWidth * 0.5, zIndex: -1 }}>
                              <View style={{ flexDirection: "row", flex: 1, alignItems: 'center', width: '50%', }}>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '40%', textAlign: 'left' }}>Purchase Order ID</Text>

                                {titleName == createPO ?
                                  <TextInput
                                    editable={true}
                                    underlineColorAndroid={Colors.fieldtBgColor}
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width: 100,
                                      width: '40%',
                                      height: switchMerchant ? 35 : 40,
                                      borderRadius: 10,
                                      padding: 5,
                                      //marginVertical: 5,
                                      borderWidth: 1,
                                      borderColor: '#E5E5E5',
                                      paddingLeft: 10,
                                      fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
                                    }}
                                    onChangeText={(text) => {
                                      setPoId(text);

                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_TB_PURCHASE_ORDER_ID,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_TB_PURCHASE_ORDER_ID,
                                      });
                                    }}
                                    placeholder={'Max 12 Character'}
                                    placeholderStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                    keyboardType={'default'}
                                    maxLength={12}
                                  />
                                  :
                                  <View style={{
                                    height: switchMerchant ? 35 : 40,
                                    width: 100,
                                    // paddingHorizontal: 20,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderRadius: 10,
                                    width: '40%',
                                    borderColor: '#E5E5E5',
                                    borderWidth: 1,
                                    justifyContent: 'center',
                                    paddingLeft: 10,
                                  }}>
                                    <Text style={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                                      {poId}
                                    </Text>
                                  </View>
                                }
                                {selectedPurchaseOrderEdit ?
                                  <></>
                                  :
                                  <View style={{ marginLeft: 5 }}>
                                    {/* <AntDesign name="edit" size={20} color={Colors.primaryColor}/> */}
                                    {/* <TouchableOpacity
                                    onPress={() => {
                                      setEditMode(!editMode);

                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_PURCHASE_ORDER_ID_ICON,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_PURCHASE_ORDER_ID_ICON,
                                      });
                                    }}>                                    
                                    <FontAwesome name="edit" size={switchMerchant ? 17 : 23} color={Colors.primaryColor} />
                                  </TouchableOpacity> */}
                                  </View>
                                }
                              </View>
                              <View style={{ flexDirection: "row", flex: 1, alignItems: 'center', width: '50%' }}>
                                <Text style={{
                                  fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14,
                                  // marginLeft: 60,                        
                                  width: switchMerchant ? '33%' : '40%',
                                  textAlign: 'left'
                                }}>Supplier</Text>

                                <View style={{ width: switchMerchant ? '60%' : '50%', height: switchMerchant ? 35 : 40, backgroundColor: Colors.whiteColor, borderRadius: 10, justifyContent: 'center', alignSelf: "center", zIndex: 1000 }}>
                                  {supplierDropdownList.find(item => item.value === selectedSupplierId) &&
                                    // <DropDownPicker
                                    //   containerStyle={{ height: 35, zIndex: 2 }}
                                    //   arrowColor={'black'}
                                    //   arrowSize={20}
                                    //   arrowStyle={{ fontWeight: 'bold' }}
                                    //   labelStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}
                                    //   style={{ width: switchMerchant ? '100%' : 220, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 10 }}
                                    //   disabled={selectedPurchaseOrderEdit ? true : false}
                                    //   items={supplierDropdownList}
                                    //   itemStyle={{ justifyContent: 'flex-start', marginLeft: 5, zIndex: 2, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                    //   placeholder="Supplier"
                                    //   placeholderStyle={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                    //   dropDownMaxHeight={150}
                                    //   dropDownStyle={{ width: switchMerchant ? '100%' : 220, height: 150, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1, textAlign: 'left', zIndex: 5 }}
                                    //   customTickIcon={(press) => <Ionicons name={"md-checkbox"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                                    //   onChangeItem={(item) => {
                                    //     setSelectedSupplierId(item.value);

                                    //     logEventAnalytics({
                                    //       eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_DD_SUPPLIER,
                                    //       eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_DD_SUPPLIER,
                                    //     });
                                    //   }}
                                    //   value={selectedSupplierId}
                                    //   open={openSupplier}
                                    //   setOpen={setOpenSupplier}
                                    // />

                                    <DropDownPicker
                                      style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: 250,
                                        height: 40,
                                        borderRadius: 10,
                                        borderWidth: 1,
                                        borderColor: "#E5E5E5",
                                        flexDirection: "row",
                                      }}
                                      dropDownContainerStyle={{
                                        width: 250,
                                        backgroundColor: Colors.fieldtBgColor,
                                        borderColor: "#E5E5E5",
                                      }}
                                      labelStyle={{
                                        marginLeft: 5,
                                        flexDirection: "row",
                                      }}
                                      textStyle={{
                                        fontSize: 14,
                                        fontFamily: 'NunitoSans-Regular',

                                        marginLeft: 5,
                                        paddingVertical: 10,
                                        flexDirection: "row",
                                      }}
                                      selectedItemContainerStyle={{
                                        flexDirection: "row",
                                      }}
                                      showArrowIcon={true}
                                      ArrowDownIconComponent={({ style }) => (
                                        <Ionicon
                                          size={25}
                                          color={Colors.fieldtTxtColor}
                                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                                          name="chevron-down-outline"
                                        />
                                      )}
                                      ArrowUpIconComponent={({ style }) => (
                                        <Ionicon
                                          size={25}
                                          color={Colors.fieldtTxtColor}
                                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                                          name="chevron-up-outline"
                                        />
                                      )}
                                      showTickIcon={true}
                                      TickIconComponent={({ press }) => (
                                        <Ionicon
                                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                                          color={
                                            press ? Colors.fieldtBgColor : Colors.primaryColor
                                          }
                                          name={'md-checkbox'}
                                          size={25}
                                        />
                                      )}
                                      placeholder="Choose Printer Area"
                                      placeholderStyle={{
                                        color: Colors.fieldtTxtColor,
                                      }}
                                      items={supplierDropdownList}
                                      value={selectedSupplierId}
                                      onSelectItem={(item) => {
                                        setSelectedSupplierId(item.value);

                                        logEventAnalytics({
                                          eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_DD_SUPPLIER,
                                          eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_DD_SUPPLIER,
                                        });
                                      }}
                                      open={openSupplier}
                                      setOpen={setOpenSupplier}
                                      dropDownDirection="BOTTOM"
                                    />
                                  }
                                </View>
                              </View>
                            </View>

                            <View style={{ flexDirection: "row", marginTop: 10, justifyContent: 'space-evenly', marginTop: 50, width: '90%', alignSelf: 'center', marginLeft: Dimensions.get('screen').width * Styles.sideBarWidth * 0.5, zIndex: -2 }}>
                              <View style={{ flexDirection: "row", flex: 1, alignItems: 'center', width: '50%' }}>


                                <Text style={{
                                  fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14,
                                  width: '40%',
                                  textAlign: 'left'
                                }}>
                                  Order Date
                                </Text>
                                {/* <TextInput
                          editable={false}
                          underlineColorAndroid={Colors.fieldtBgColor}
                          style={{
                            backgroundColor: Colors.fieldtBgColor,
                            width: '51%',
                            height: 40,
                            borderRadius: 5,
                            padding: 5,
                            marginVertical: 5,
                            borderWidth: 1,
                            borderColor: '#E5E5E5',
                            paddingLeft:10,
                          }}
                          // placeholder={'50'}
                          value={moment().format('DD MMM YYYY')}
                          keyboardType={'default'}
                        /> */}

                                <View style={{
                                  width: 140,
                                  backgroundColor: '#f2f2f2',
                                  padding: 10,
                                  paddingHorizontal: 10,
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  width: '40%',
                                  height: switchMerchant ? 35 : 40,
                                  justifyContent: 'space-between',
                                  borderRadius: 10,
                                  borderColor: '#E5E5E5',
                                  borderWidth: 1,
                                  // paddingLeft: '2%',
                                }}>
                                  {/* <View style={{ width: '100%' }}> */}
                                  <TouchableOpacity style={{ width: '25%' }}
                                    disabled={true}
                                    onPress={showDateTimePicker}>
                                    {/* <TouchableOpacity style={{ alignSelf: "flex-end" }} onPress={showDateTimePicker}> */}
                                    <GCalendarGrey width={switchMerchant ? 15 : 20} height={switchMerchant ? 15 : 20} />
                                  </TouchableOpacity>
                                  <View style={{ justifyContent: 'center', width: '75%' }}>
                                    <Text style={[{
                                      fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
                                    }, Platform.OS === 'ios' ? {} : {
                                      // paddingTop: '10%',
                                      height: '200%',
                                      // borderWidth: 1,
                                      textAlignVertical: 'center'
                                    }]}>
                                      {moment().format('DD MMM YYYY')}
                                    </Text>
                                  </View>

                                  {/* </View> */}
                                </View>
                              </View>

                              <View style={{ flexDirection: "row", flex: 1, width: '50%', alignItems: 'center' }}>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: switchMerchant ? '33%' : '40%', textAlign: 'left' }}>Target Store</Text>
                                <View style={{ width: switchMerchant ? '60%' : '50%', height: 40, backgroundColor: Colors.whiteColor, borderRadius: 10, justifyContent: 'center', alignSelf: "center", }}>
                                  {targetOutletDropdownList.find(item => item.value === selectedTargetOutletId) &&
                                    <DropDownPicker
                                      style={{
                                        backgroundColor: Colors.fieldtBgColor,
                                        width: 250,
                                        height: 40,
                                        borderRadius: 10,
                                        borderWidth: 1,
                                        borderColor: "#E5E5E5",
                                        flexDirection: "row",
                                      }}
                                      dropDownContainerStyle={{
                                        width: 250,
                                        backgroundColor: Colors.fieldtBgColor,
                                        borderColor: "#E5E5E5",
                                      }}
                                      labelStyle={{
                                        marginLeft: 5,
                                        flexDirection: "row",
                                      }}
                                      textStyle={{
                                        fontSize: 14,
                                        fontFamily: 'NunitoSans-Regular',

                                        marginLeft: 5,
                                        paddingVertical: 10,
                                        flexDirection: "row",
                                      }}
                                      selectedItemContainerStyle={{
                                        flexDirection: "row",
                                      }}
                                      showArrowIcon={true}
                                      ArrowDownIconComponent={({ style }) => (
                                        <Ionicon
                                          size={25}
                                          color={Colors.fieldtTxtColor}
                                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                                          name="chevron-down-outline"
                                        />
                                      )}
                                      ArrowUpIconComponent={({ style }) => (
                                        <Ionicon
                                          size={25}
                                          color={Colors.fieldtTxtColor}
                                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                                          name="chevron-up-outline"
                                        />
                                      )}
                                      showTickIcon={true}
                                      TickIconComponent={({ press }) => (
                                        <Ionicon
                                          style={{ paddingHorizontal: 5, marginTop: 5 }}
                                          color={
                                            press ? Colors.fieldtBgColor : Colors.primaryColor
                                          }
                                          name={'md-checkbox'}
                                          size={25}
                                        />
                                      )}
                                      placeholder="Target Store"
                                      placeholderStyle={{
                                        color: Colors.fieldtTxtColor,
                                      }}
                                      items={targetOutletDropdownList}
                                      value={selectedTargetOutletId}
                                      onSelectItem={(item) => {
                                        setSelectedTargetOutletId(item.value);

                                        logEventAnalytics({
                                          eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_DD_TARGET_STORE,
                                          eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_DD_TARGET_STORE,
                                        });
                                      }}
                                      open={openTargetStore}
                                      setOpen={setOpenTargetStore}
                                      dropDownDirection="BOTTOM"
                                      disabled
                                      searchable
                                    />

                                    // <DropDownPicker
                                    //   containerStyle={{ height: 35, zIndex: 2 }}
                                    //   arrowColor={'black'}
                                    //   arrowSize={20}
                                    //   arrowStyle={{ fontWeight: 'bold' }}
                                    //   labelStyle={{ fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}
                                    //   style={{ width: switchMerchant ? '100%' : 220, paddingVertical: 0, backgroundColor: Colors.fieldtBgColor, borderRadius: 10 }}
                                    //   placeholderStyle={{ color: Colors.fieldtTxtColor, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                    //   // disabled={selectedPurchaseOrderEdit ? true : false}
                                    //   disabled={true}
                                    //   items={targetOutletDropdownList}
                                    //   itemStyle={{ justifyContent: 'flex-start', marginLeft: 5, zIndex: 2, }}
                                    //   placeholder="Target Store"
                                    //   customTickIcon={(press) => <Ionicons name={"md-checkbox"} color={press ? Colors.fieldtBgColor : Colors.primaryColor} size={25} />}
                                    //   onChangeItem={(item) => {
                                    //     setSelectedTargetOutletId(item.value);

                                    //     logEventAnalytics({
                                    //       eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_DD_TARGET_STORE,
                                    //       eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_DD_TARGET_STORE,
                                    //     });
                                    //   }}
                                    //   searchable={true}
                                    //   defaultValue={selectedTargetOutletId}
                                    //   dropDownMaxHeight={150}
                                    //   dropDownStyle={{ width: switchMerchant ? '100%' : 220, height: 150, backgroundColor: Colors.fieldtBgColor, borderRadius: 10, borderWidth: 1, textAlign: 'left', zIndex: 2 }}
                                    // />
                                  }
                                </View>
                              </View>
                            </View>

                            <View style={{ flexDirection: "row", marginTop: 10, justifyContent: "space-evenly", marginTop: 50, marginBottom: 40, width: '90%', alignSelf: 'center', marginLeft: Dimensions.get('screen').width * Styles.sideBarWidth * 0.5, zIndex: -3 }}>
                              <View style={{ flexDirection: "row", flex: 1, alignItems: 'center', width: '50%' }}>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '40%', textAlign: 'left' }}>Estimated Arrival</Text>
                                <View style={{
                                  width: 140,
                                  backgroundColor: '#f2f2f2',
                                  padding: 10,
                                  paddingHorizontal: 10,
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  width: '40%',
                                  height: switchMerchant ? 35 : 40,
                                  justifyContent: 'space-between',
                                  borderRadius: 10,
                                  borderColor: '#E5E5E5',
                                  borderWidth: 1,
                                }}>
                                  {/* <View style={{ width: '100%' }}> */}
                                  <TouchableOpacity style={{ width: '25%' }}
                                    disabled={poStatus === PURCHASE_ORDER_STATUS.COMPLETED}
                                    onPress={() => {
                                      setShowDateTimePickerEta(true);

                                      logEventAnalytics({
                                        eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_ESTIMATED_ARRIVAL_ICON,
                                        eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_C_ESTIMATED_ARRIVAL_ICON,
                                      });
                                    }}
                                  >
                                    {poStatus === PURCHASE_ORDER_STATUS.COMPLETED ?
                                      <GCalendarGrey width={switchMerchant ? 15 : 20} height={switchMerchant ? 15 : 20} />
                                      :
                                      <GCalendar width={switchMerchant ? 15 : 20} height={switchMerchant ? 15 : 20} />
                                    }
                                  </TouchableOpacity>

                                  <View style={{ justifyContent: 'center', width: '75%' }}>
                                    {/* <Text style={[{
                                      fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14,
                                    }, Platform.OS === 'ios' ? {} : {
                                      // paddingTop: '10%',
                                      height: '200%',
                                      // borderWidth: 1,
                                      textAlignVertical: 'center'
                                    }]}>
                                      {moment(etaDate).format('DD MMM YYYY')}
                                    </Text> */}

                                    <DatePicker
                                      selected={moment(etaDate).toDate()}
                                      onChange={(date) => {
                                        setEtaDate(moment(date).startOf("day"));
                                      }}
                                      minDate={moment().add(1, 'days').toDate()}
                                      dateFormat="dd MMM yyyy"
                                      portalId="3"
                                      className="background"
                                    />
                                  </View>

                                  {/* </View> */}
                                </View>

                                <DateTimePickerModal
                                  isVisible={isDateTimePickerVisible}
                                  // onConfirm={handleDatePicked}
                                  // onCancel={hideDateTimePicker}
                                  mode={'date'}
                                  onConfirm={(text) => {
                                    setDate(moment(text));

                                    setIsDateTimePickerVisible(false);
                                  }}
                                  onCancel={() => {
                                    setIsDateTimePickerVisible(false);
                                  }}
                                />
                              </View>


                              <View style={{ flexDirection: "row", flex: 1, alignItems: 'center', width: '50%', }}>
                                <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: switchMerchant ? '33%' : '40%', textAlign: 'left' }}>Current Status</Text>
                                <View style={{ paddingHorizontal: 18, paddingVertical: 10, alignItems: "center", backgroundColor: Colors.secondaryColor, borderRadius: 10, width: switchMerchant ? '60%' : 220 }}>
                                  <Text style={{ color: Colors.whiteColor, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>{PURCHASE_ORDER_STATUS_PARSED[poStatus]}</Text>
                                </View>
                              </View>
                            </View>
                          </View>

                          {/* <ScrollView horizontal={true}
                      nestedScrollEnabled={true}
                      scrollEnabled={switchMerchant}
                      style={{ flexDirection: 'column' }}
                      contentContainerStyle={{
                        flexDirection: 'column',
                        width: switchMerchant ? Dimensions.get('screen').width * 1.1 : Dimensions.get('screen').width * 0.87,
                        // maxHeight: switchMerchant ? Dimensions.get('screen').height * 0.9 : null, 
                      }}
                    > */}
                          <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
                            {/* <View
                        style={{
                          flexDirection: 'row',
                          //backgroundColor: '#ffffff',
                          justifyContent: 'center',
                          padding: 18,
                        }}>
                        <View style={{ alignItems: 'center' }}>
                          <Text style={{ fontSize: 30, fontWeight: 'bold' }}>
                            {purchaseOrders.length}
                          </Text>
                          <Text>PURCHASE ORDERS</Text>
                        </View>
                      </View> */}
                            <Text style={{ alignSelf: "center", marginTop: 30, marginBottom: 15, fontSize: switchMerchant ? 15 : 20, fontWeight: 'bold' }}>Items Ordered</Text>
                            {/* <View
                        style={{
                          //backgroundColor: Colors.primaryColor,
                          width: 150,
                          height: 40,
                          borderRadius: 5,
                          alignItems: 'center',
                          justifyContent: 'center',
                          marginRight: 15,
                          marginTop: 30,
                        }}>
                        Hide This Button First
                        <TouchableOpacity onPress={() => { setImportModal(true) }}>
                          <View style={{ flexDirection: 'row' }}>
                            <Icon name='upload' size={20} color={Colors.whiteColor} />
                            <Text
                              style={{
                                marginLeft: 10,
                                color: Colors.whiteColor,
                                alignSelf: 'center',
                              }}>
                              Batch-Upload
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View> */}
                          </View>

                          <View
                            style={{
                              backgroundColor: '#ffffff',
                              flexDirection: 'row',
                              paddingVertical: 20,
                              paddingHorizontal: 10,
                              paddingRight: 5,
                              marginTop: 10,
                              borderBottomWidth: StyleSheet.hairlineWidth,
                              width: '100%',
                            }}>
                            <Text style={{ width: switchMerchant ? 40 : 45 }}></Text>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '16%', fontWeight: '500', marginLeft: 10, marginRight: 1, paddingLeft: Platform.OS === 'ios' ? 0 : '0%' }}>
                              Product Name
                            </Text>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '9%', fontWeight: '500', marginLeft: 5, marginRight: 1 }}>
                              SKU
                            </Text>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '8%', fontWeight: '500', marginHorizontal: 3 }}>
                              In Stock
                            </Text>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '8%', fontWeight: '500', marginHorizontal: 3 }}>
                              {'Ordered\nQty'}
                            </Text>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '9%', fontWeight: '500', marginHorizontal: 3 }}>
                              {'Unit\n(Packaging)'}
                            </Text>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '8%', fontWeight: '500', marginHorizontal: 3 }}>
                              {'To Sent\nQty'}
                            </Text>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '8%', fontWeight: '500', marginHorizontal: 3 }}>
                              {'Received\nQty (Actual)'}
                            </Text>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: '10.5%', fontWeight: '500', marginHorizontal: 3 }}>
                              {'Supplier\nPrice (RM)'}
                            </Text>
                            <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14, width: switchMerchant ? '8%' : '8%', fontWeight: '500', marginHorizontal: 3, marginLeft: 0, }}>
                              Total (RM)
                            </Text>
                            <Text style={{ width: 22 }}></Text>
                          </View>

                          {((selectedPurchaseOrderEdit && selectedPurchaseOrderEdit.supplierId === selectedSupplierId && supplyItemsDict[poItems[0].supplyItemId]) || selectedPurchaseOrderEdit === null) &&
                            supplyItemDropdownList.length > 0 &&
                            Object.keys(supplyItemsDict).length > 0 &&
                            Object.keys(outletSupplyItemsSkuDict).length > 0 &&
                            <View style={{
                              shadowOpacity: 0,
                              shadowColor: '#000',
                              shadowOffset: {
                                width: 0,
                                height: 1,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 1.22,
                              elevation: 1,
                              //maxHeight: switchMerchant ? Dimensions.get('screen').height * 0.6 : null, 
                            }}>
                              <FlatList
                                nestedScrollEnabled={true}
                                //horizontal={true}
                                data={poItems}
                                extraData={poItems}
                                renderItem={renderAddPO}
                                keyExtractor={(item, index) => String(index)}
                              />
                            </View>
                          }

                          {/* <ScrollView>
                      {
                        // itemsToOrder2 && itemsToOrder2.map((item, i) => {
                        //   return renderAddPO(item);
                        // })
                        poItems && poItems.map((item, i) => {
                          return renderAddPO(item, i);
                        })
                      }
                    </ScrollView> */}

                          <View style={{ flexDirection: 'row', zIndex: -1, paddingLeft: 10, paddingRight: 5, }}>
                            {!selectedPurchaseOrderEdit ?
                              <View style={{ width: switchMerchant ? '76%' : '77%', }} >
                                <TouchableOpacity
                                  style={[
                                    styles.submitText2,
                                    {
                                      alignSelf: 'flex-start',
                                    }
                                  ]}
                                  onPress={() => {
                                    if (supplyItems.length > 0) {
                                      // Get all supplier items for current supplier
                                      const supplierItems = supplyItems.filter(item => item.supplierId === selectedSupplierId);

                                      if (supplierItems.length === 0) {
                                        alert('Error, No supplier items found');
                                        return;
                                      }

                                      // Get list of items already in PO
                                      const existingItemIds = poItems.map(item => item.supplyItemId);

                                      // Find first item that's not already in PO
                                      const nextItem = supplierItems.find(item => !existingItemIds.includes(item.uniqueId));

                                      if (!nextItem) {
                                        alert('Info, All items from this supplier are already in the list');
                                        return;
                                      }

                                      // Add the next available item
                                      setPoItems([
                                        ...poItems,
                                        {
                                          supplyItemId: nextItem.uniqueId,
                                          image: nextItem.image,
                                          name: nextItem.name,
                                          sku: nextItem.sku,
                                          skuMerchant: nextItem.skuMerchant,
                                          quantity: outletSupplyItemsSkuDict[nextItem.sku]?.quantity || 0,
                                          orderQuantity: 0,
                                          unit: nextItem.unit,
                                          receivedQuantity: 0,
                                          price: nextItem.price,
                                          totalPrice: 0,
                                          lastReceivedQuantity: 0,

                                          receivedQuantityActual: 0,

                                          supplyItem: nextItem
                                        }
                                      ]);
                                    } else {
                                      alert('Error, No supplier items found');
                                    }

                                    logEventAnalytics({
                                      eventName: ANALYTICS.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_C_ADD_PRODUCT_SLOT,
                                      eventNameParsed: ANALYTICS_PARSED.MODULE_COMPOSITE_PURCHASE_ORDER_INFO_ITEMS_ORDERED_C_ADD_PRODUCT_SLOT,
                                    });
                                  }}>
                                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                    <Icon1 name="plus-circle" size={switchMerchant ? 15 : 20} color={Colors.primaryColor} />
                                    <Text style={{ marginLeft: switchMerchant ? 5 : 10, color: Colors.primaryColor, marginBottom: Platform.OS === 'ios' ? 0 : 1, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>
                                      Add Product Slot
                                    </Text>
                                  </View>
                                </TouchableOpacity>
                              </View>
                              :
                              <View style={{ width: switchMerchant ? '76%' : '77%' }}>
                              </View>
                            }

                            {/* {selectedPurchaseOrderEdit ?
                        <View>
                          <Text></Text>
                        </View>
                        : <></>
                      } */}
                            <View style={[{ marginTop: 10, width: switchMerchant ? 87.5 : 92.5, }, !switchMerchant && Platform.OS === 'ios' ? {
                              justifyContent: 'center', marginTop: 12
                            } : {}]}>
                              <View style={{ flexDirection: "row", bottom: !switchMerchant && Platform.OS === 'ios' ? 2.5 : 0 }}>
                                <Text style={{ color: '#adadad', fontWeight: '500', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>Subtotal</Text>
                              </View>
                              <View style={[{ flexDirection: "row", marginTop: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }, !switchMerchant && Platform.OS === 'ios' ? {
                                marginVertical: 13
                              } : {}]}>
                                <Text style={{ color: '#adadad', fontWeight: '500', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>Tax</Text>
                              </View>
                              <View style={{ flexDirection: "row", marginTop: switchMerchant ? 10 : Platform.OS === 'ios' ? 0 : 10, marginBottom: 15, }}>
                                <Text style={{ fontWeight: 'bold', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>Total (RM)</Text>
                              </View>
                            </View>


                            <View style={{ marginTop: 10, width: '8%', }}>
                              <View style={{ flexDirection: "row", justifyContent: 'space-between', alignItems: 'center' }}>
                                {/* <Text style={{ color: '#adadad', fontWeight: '500', width: '52%' }}>Subtotal</Text> */}
                                <Text style={{ color: '#adadad', fontSize: switchMerchant ? 10 : 14, fontWeight: '500', fontFamily: 'NunitoSans-Regular', }}>{`RM`}</Text>
                                <Text style={{ color: '#adadad', fontSize: switchMerchant ? 10 : 14, fontWeight: '500', fontFamily: 'NunitoSans-Regular', paddingRight: '2%', }}>{subtotal.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
                              </View>
                              <View style={{ flexDirection: "row", justifyContent: 'space-between', alignItems: 'center', marginTop: 10, }}>
                                {/* <Text style={{ color: '#adadad', fontWeight: '500', width: '52%' }}>Tax</Text> */}
                                <Text style={{ color: '#adadad', fontWeight: '500', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14, }}>{`RM`}</Text>
                                <Text style={{ color: '#adadad', fontSize: switchMerchant ? 10 : 14, fontWeight: '500', fontFamily: 'NunitoSans-Regular', paddingRight: '2%', }}>{taxTotal.toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}</Text>
                              </View>
                              <View style={{ flexDirection: "row", justifyContent: 'space-between', alignItems: 'center', marginTop: 10, marginBottom: 15, }}>
                                {/* <Text style={{ fontWeight: 'bold', width: '52%' }}>Total (RM)</Text> */}
                                <Text style={{ fontWeight: 'bold', fontSize: switchMerchant ? 10 : 14, }}>{`RM`}</Text>
                                <Text style={{ fontWeight: 'bold', fontSize: switchMerchant ? 10 : 14, fontWeight: '500', fontFamily: 'NunitoSans-Bold', paddingRight: '2%', }}>{finalTotal.toFixed(2)}</Text>
                              </View>
                            </View>
                          </View>
                          {/* </ScrollView> */}

                          {/* Purpose of this view is To Push the scrollview up */}
                          <View style={{ height: 40 }} />

                          {/* <View style={{ flexDirection: "row", alignSelf: "center", justifyContent: "space-evenly", marginTop: 20 }}>
                    {selectedPurchaseOrderEdit ?
                      <View
                        style={{
                          backgroundColor: Colors.primaryColor,
                          width: 200,
                          height: 40,
                          marginVertical: 15,
                          borderRadius: 5,
                          alignSelf: 'center',
                        }}>
                        <TouchableOpacity onPress={createPurchaseOrder}>
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              alignSelf: 'center',
                              marginVertical: 10,
                            }}>
                            UPDATE
                        </Text>
                        </TouchableOpacity>
                      </View>
                      :
                      <>
                        <View
                          style={{
                            backgroundColor: Colors.primaryColor,
                            width: 200,
                            height: 40,
                            marginVertical: 15,
                            borderRadius: 5,
                            alignSelf: 'center',
                          }}>
                          <TouchableOpacity onPress={createPurchaseOrder}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                alignSelf: 'center',
                                marginVertical: 10,
                              }}>
                              SAVE
                        </Text>
                          </TouchableOpacity>
                        </View>
                        <View
                          style={{
                            backgroundColor: Colors.primaryColor,
                            width: 200,
                            height: 40,
                            marginVertical: 15,
                            borderRadius: 5,
                            alignSelf: 'center',
                            marginLeft: 40
                          }}>
                          <TouchableOpacity onPress={createPurchaseOrder}>
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                alignSelf: 'center',
                                marginVertical: 10,
                              }}>
                              SAVE & SEND
                        </Text>
                          </TouchableOpacity>
                        </View>
                      </>
                    }
                  </View> */}

                        </ScrollView>
                        {/* </ScrollView> */}

                      </View>
                    </View>


                  </View>
                ) : null}

                {editPurchase ? (
                  <View style={{ height: Dimensions.get('window').height - 200 }}>

                    <View>
                      <ScrollView>
                        <View style={{ width: 140, marginRight: 10, marginTop: 10, alignSelf: "flex-end", height: 40, backgroundColor: Colors.whiteColor, borderRadius: 10, justifyContent: 'center', }}>
                          <DropDownPicker
                            items={[
                              {
                                icon: () => { },
                                label: '🖨️  Print P.O',
                                value: 'Print P.O',
                              },
                              {
                                label: '📧  Email P.O',
                                value: 'Chicken',
                              },
                              {
                                label: '📤  Export Labels',
                                value: 'Export Labels',
                              },
                              {
                                label: '❌  Cancel P.O',
                                value: 'Cancel P.O',
                              },
                              {
                                label: '🗑️  Delete P.O',
                                value: 'Delete P.O',
                              },
                            ]}
                            // defaultValue={choice}
                            placeholder=""
                            placeholderStyle={{ color: 'black' }}
                            containerStyle={{ height: 30 }}
                            style={{ backgroundColor: '#FAFAFA' }}
                            itemStyle={{
                            }}
                            dropDownStyle={{ backgroundColor: '#FAFAFA' }}
                            onChangeItem={(item) => {
                              setState({
                                choice: item.value,
                              })
                            }}
                          />
                        </View>
                        <View style={{ borderBottomWidth: StyleSheet.hairlineWidth }}>
                          <View>
                            <Text style={{ alignSelf: "center", marginTop: 30, fontSize: 40, fontWeight: 'bold' }}>Edit Purchase Order</Text>
                            <Text style={{ alignSelf: "center", fontSize: 16, color: '#adadad' }}>Edit your purchase order information</Text>
                          </View>

                          <View style={{ flexDirection: "row", marginTop: 10, justifyContent: 'space-evenly', marginTop: 50 }}>
                            <View style={{ flexDirection: "row", flex: 1 }}>
                              <Text style={{ fontSize: 16, marginLeft: 80 }}>Purchase Order ID</Text>
                              <Text style={{ color: '#adadad', marginLeft: 170, fontSize: 16, fontFamily: 'NunitoSans-Regular' }}>P.O.1134</Text>
                            </View>
                            <View style={{ flexDirection: "row", flex: 1 }}>
                              <Text style={{ fontSize: 16, marginLeft: 80 }}>Supplier</Text>
                              <Text style={{ color: '#adadad', marginLeft: 100, fontSize: 16, fontFamily: 'NunitoSans-Regular' }}>My Burgers Enterprise</Text>
                            </View>
                          </View>

                          <View style={{ flexDirection: "row", marginTop: 10, justifyContent: 'space-evenly', marginTop: 50 }}>
                            <View style={{ flexDirection: "row", flex: 1 }}>
                              <Text style={{ fontSize: 16, marginLeft: 80, fontFamily: 'NunitoSans-Regular' }}>Current status</Text>
                              <View style={{ paddingHorizontal: 18, paddingVertical: 10, alignItems: "center", backgroundColor: '#838387', borderRadius: 10, marginLeft: 100 }}>
                                <Text style={{ color: Colors.whiteColor }}>Partially received</Text>
                              </View>

                            </View>
                            <View style={{ flexDirection: "row", flex: 1 }}>
                              <Text style={{ fontSize: 16, marginLeft: 80, fontFamily: 'NunitoSans-Regular' }}>Target Store</Text>
                              <Text style={{ color: '#adadad', marginLeft: 70, fontSize: 16, }}>MyBurgerlab (Seapark)</Text>
                            </View>
                          </View>

                          <View style={{ flexDirection: "row", marginTop: 10, justifyContent: "space-evenly", marginTop: 50, marginBottom: 40 }}>
                            <View style={{ flexDirection: "row", flex: 1 }}>
                              <Text style={{ fontSize: 16, marginLeft: 80, fontFamily: 'NunitoSans-Regular' }}>Estimated Arrival</Text>
                              <Text style={{ color: '#adadad', marginLeft: 50, fontSize: 16, }}>1/10/2020</Text>
                            </View>
                            <View style={{ flexDirection: "row", flex: 1 }}>
                              <Text style={{ fontSize: 16, marginLeft: 80, fontFamily: 'NunitoSans-Regular' }}>Order Date</Text>
                              <Text style={{ color: '#adadad', marginLeft: 80, fontSize: 16, }}>5/10/2020</Text>
                            </View>
                          </View>
                        </View>

                        <View>
                          <Text style={{ alignSelf: "center", marginTop: 30, marginBottom: 15, fontSize: 25, fontWeight: 'bold' }}>
                            {/* Items to Order */}
                            Items Ordered
                          </Text>
                        </View>

                        <View
                          style={{
                            backgroundColor: '#ffffff',
                            flexDirection: 'row',
                            paddingVertical: 20,
                            paddingHorizontal: 20,
                            marginTop: 10,
                            borderBottomWidth: StyleSheet.hairlineWidth,
                          }}>
                          <Text style={{ width: '8%' }}></Text>
                          <Text style={{ width: '14%', alignSelf: 'center', fontSize: 14, fontFamily: 'NunitoSans-Regular' }}>
                            Product Name
                          </Text>
                          <Text style={{ width: '16%', alignSelf: 'center', fontSize: 14, fontFamily: 'NunitoSans-Regular' }}>
                            SKU
                          </Text>
                          <Text style={{ width: '14%', alignSelf: 'center', fontSize: 14, fontFamily: 'NunitoSans-Regular' }}>
                            Ordered qty
                          </Text>
                          <Text style={{ width: '16%', alignSelf: 'center', fontSize: 14, fontFamily: 'NunitoSans-Regular' }}>Received qty</Text>
                          <Text style={{ width: '18%', alignSelf: 'center', fontSize: 14, fontFamily: 'NunitoSans-Regular' }}>Supplier Price </Text>
                          <Text style={{ width: '16%', alignSelf: 'center', fontSize: 14, fontFamily: 'NunitoSans-Regular' }}>Total (RM)</Text>
                        </View>
                        {/* <FlatList
                        nestedScrollEnabled={true}
                        data={itemsToOrder}
                        extraData={itemsToOrder}
                        renderItem={renderItemsToOrder}
                        keyExtractor={(item, index) => String(index)}
                      /> */}

                        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                          <View >
                            <TouchableOpacity
                              style={styles.submitText2}
                              onPress={() => { }}>
                              <View style={{ flexDirection: 'row' }}>
                                <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} />
                                <Text style={{ marginLeft: 10, color: Colors.primaryColor }}>
                                  Add product slot
                                </Text>
                              </View>
                            </TouchableOpacity>
                          </View>

                          <View style={{ marginRight: 30, marginTop: 10 }}>
                            <View style={{ flexDirection: "row" }}>
                              <Text style={{ color: '#adadad' }}>Subtotal</Text>
                              <Text style={{ color: '#adadad', marginLeft: 50 }}>RM360.00</Text>
                            </View>
                            <View style={{ flexDirection: "row", marginTop: 10 }}>
                              <Text style={{ color: '#adadad' }}>Tax</Text>
                              <Text style={{ color: '#adadad', marginLeft: 80 }}>RM0.00</Text>
                            </View>
                            <View style={{ flexDirection: "row", marginTop: 10 }}>
                              <Text style={{ color: '#adadad' }}>Discount</Text>
                              <Text style={{ color: '#adadad', marginLeft: 50 }}>RM0.00</Text>
                            </View>
                            <View style={{ flexDirection: "row", marginTop: 5 }}>
                              <Text style={{ fontWeight: 'bold' }}>Total (RM)</Text>
                              <Text style={{ fontWeight: 'bold', marginLeft: 40 }}>RM360.00</Text>
                            </View>
                          </View>

                        </View>

                        <View style={{ flexDirection: "row", alignSelf: "center", justifyContent: "space-evenly", marginTop: 20 }}>
                          <View
                            style={{
                              backgroundColor: Colors.primaryColor,
                              width: 200,
                              height: 40,
                              marginVertical: 15,
                              borderRadius: 5,
                              alignSelf: 'center',
                            }}>
                            <TouchableOpacity
                              onPress={() => {
                                // editStockOrder();
                              }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  alignSelf: 'center',
                                  marginVertical: 10,
                                }}>
                                SAVE
                              </Text>
                            </TouchableOpacity>
                          </View>
                          <View
                            style={{
                              backgroundColor: Colors.primaryColor,
                              width: 200,
                              height: 40,
                              marginVertical: 15,
                              borderRadius: 5,
                              alignSelf: 'center',
                              marginLeft: 40
                            }}>
                            <TouchableOpacity onPress={() => { }}>
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  alignSelf: 'center',
                                  marginVertical: 10,
                                }}>
                                SAVE & SEND
                              </Text>
                            </TouchableOpacity>
                          </View>
                        </View>


                        <View
                          style={{
                            flexDirection: 'row',
                            backgroundColor: '#ffffff',
                            justifyContent: 'center',
                            padding: 18,
                          }}>
                          <View style={{ alignItems: 'center' }}>
                            <Text style={{ fontSize: 26, fontFamily: 'NunitoSans-Bold' }}>
                              {/* {stockList.length} */}
                              {purchaseOrderList.length}
                            </Text>
                            <Text>PURCHASE ORDERS</Text>
                          </View>
                        </View>
                      </ScrollView>
                    </View>

                  </View>
                ) : null}
              </View>
              {/* <View style={{ height: Dimensions.get('screen').height * 0.1 }}></View> */}
            </ScrollView>
          </Animated.ScrollView >
        </View>
      </View >
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    // '@media (max-width:2048px) and (min-width: 1024px)' : {
    //   backgroundColor: 'red',
    // },
    // '@media (min-width: 1024px)' : {
    //   backgroundColor: 'blue',
    // },
    flexDirection: 'row',
    zIndex: -1,
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('screen').width * Styles.sideBarWidth,
    // shadowColor: "#000",
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 16,
    width: Dimensions.get('screen').width * (1 - Styles.sideBarWidth),
  },
  submitText: {
    height: Platform.OS == 'ios' ? Dimensions.get('screen').height * 0.06 : Dimensions.get('screen').height * 0.07,
    paddingVertical: 5,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  submitText1: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    alignSelf: "flex-end",
    marginRight: 20,
    height: 40,
    // marginTop: 15,
    width: 220
  },
  submitText2: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: 'row',
    color: '#4cd964',
    textAlign: 'center',
    alignSelf: "flex-end",
    marginRight: 20,
    marginTop: 15
  },
  /* textInput: {
    width: 300,
    height: '10%',
    padding: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 20,
  }, */

  textInput: {
    width: 200,
    height: 40,
    // padding:5,
    backgroundColor: Colors.whiteColor,
    borderRadius: 0,
    marginRight: '35%',
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'flex-end',

    shadowOffset: Platform.OS == 'ios' ? {
      width: 0,
      height: 0,
    } : {
      width: 0,
      height: 7,
    },
    shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
    shadowRadius: Platform.OS == 'ios' ? 0 : 0.51,
    elevation: 15,
  },
  searchIcon: {
    backgroundColor: Colors.whiteColor,
    height: 40,
    padding: 10,
    shadowOffset: Platform.OS == 'ios' ? {
      width: 0,
      height: 0,
    } : {
      width: 0,
      height: 7,
    },
    shadowOpacity: Platform.OS == 'ios' ? 0 : 0.43,
    shadowRadius: Platform.OS == 'ios' ? 0 : 9.51,

    elevation: 15,
  },
  textInput1: {
    width: 160,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
  },
  textInput2: {
    width: 100,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
    textAlign: 'center'
  },
  confirmBox: {
    width: 450,
    height: 450,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: 80,
    width: 900,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: 'center'
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: 'center',
    justifyContent: 'center'
  },
  modalView: {
    height: Dimensions.get('screen').width * 0.2,
    width: Dimensions.get('screen').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('screen').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center'
  },
  modalView1: {
    //height: Dimensions.get('screen').width * 0.2,
    width: Dimensions.get('screen').width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get('screen').width * 0.03,
    alignItems: 'center',
    justifyContent: 'center'
  },
  modalViewImport: {
    height: Dimensions.get('screen').width * 0.6,
    width: Dimensions.get('screen').width * 0.6,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: 20,
    paddingTop: 25,
    //paddingBottom: 30,
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('screen').width * 0.04,
    top: Dimensions.get('screen').width * 0.04,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: 'center',
    top: '20%',
    position: 'absolute',
  },
  modalBody: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center'

  },
  modalTitleText: {
    fontFamily: 'NunitoSans-Bold',
    textAlign: 'center',
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 18,
    color: Colors.fieldtTxtColor
  },
  modalBodyText: {
    flex: 1,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
    width: "20%",
  },
  modalSaveButton: {
    width: Dimensions.get('screen').width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
});
export default PurchaseOrderScreen;
