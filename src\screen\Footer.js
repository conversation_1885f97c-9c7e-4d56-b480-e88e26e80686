import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  useMemo,
} from 'react';
import {
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  Dimensions,
  TouchableOpacity,
  FlatList,
  SectionList,
  ListView,
  Linking,
  Modal,
  Platform,
  ActivityIndicator,
  StatusBar,
  TextInput,
  KeyboardAvoidingView,
  useWindowDimensions,
} from 'react-native';
import Colors from '../constant/Colors';
import Feather from 'react-native-vector-icons/Feather';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import moment, { now } from 'moment';
import Styles from '../constant/Styles';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import {
  //isTablet
  uploadImageToFirebaseStorage,
} from '../util/common';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Entypo from 'react-native-vector-icons/Entypo';
// import Switch from 'react-native-switch-pro';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
// import { Calendar, CalendarList, Agenda } from 'react-native-calendars';
import {
  USER_SORT_FIELD_TYPE,
  REPORT_SORT_FIELD_TYPE_COMPARE,
  USER_SORT_FIELD_TYPE_VALUE,
  REPORT_SORT_COMPARE_OPERATOR,
  EMAIL_REPORT_TYPE,
  USER_RESERVATION_STATUS
} from '../constant/common';
import AsyncImage from '../components/asyncImage';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
// import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import DropDownPicker from 'react-native-dropdown-picker';
// import RNPickerSelect from 'react-native-picker-select';
import TableScreen from './TableScreen'
// import { firebase } from '@react-native-firebase/storage';
import firebase from "firebase";
import { Collections } from '../constant/firebase';
import { RESERVATIONS_SHIFT_TYPE, RESERVATION_PRIORITY } from '../constant/common';
import ReservationTableScreen from './ReservationTableScreen';
import APILocal from '../util/apiLocalReplacers';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import { useNetInfo } from '@react-native-community/netinfo';
import { v4 as uuidv4 } from 'uuid';

import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";

import { ReactComponent as ArrowLeft } from "../assets/svg/ArrowLeft.svg";
import { ReactComponent as ArrowRight } from "../assets/svg/ArrowRight.svg";

import Ionicon from "react-native-vector-icons/Ionicons";
// import Switch from 'react-switch'

const Footer = (props) => {
  const { navigation } = props;

  const netInfo = useNetInfo();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [rev_date, setRev_date] = useState(moment().endOf(Date.now()).toDate());
  const [rev_date1, setRev_date1] = useState(moment().add(6, 'days').startOf('day').toDate());

  ///////footer/////////

  const [switchMerchant, setSwitchMerchant] = useState(false);
  const [showDateTimePicker, setShowDateTimePicker] = useState(false);
  const [showDateTimePicker1, setShowDateTimePicker1] = useState(false);
  const [addReservationModal1, setAddReservationModal1] = useState(false);
  const [alldayModal, setAlldayModal] = useState(false);
  const [paxrevModal, setPaxrevModal] = useState(false);
  const [partyAmount, setPartyAmount] = useState('2');
  const [dateSelected, setDateSelected] = useState(
    moment(Date.now()).format('ddd, DD MMM'),
  );
  const [timeSection, setTimeSection] = useState();
  const [timeSelected, setTimeSelected] = useState('');
  const [timeSelectedMilli, setTimeSelectedMilli] = useState('');
  const [showWaitlist, setShowWaitlist] = useState(false);
  const [waitlistStart, setWaitlistStart] = useState(false);
  const [waitlistEnd, setWaitlistEnd] = useState(false);
  const [waitlistStartTime, setWaitlistStartTime] = useState();
  const [waitlistEndTime, setWaitlistEndTime] = useState();
  const [search, setSearch] = useState('');
  const [selectedGuestName, setSelectedGuestName] = useState('');
  const [selectedGuestObject, setSelectedGuestObject] = useState({});

  const [selectedCalendarMarker, setSelectedCalendarMarker] = useState(new Date());

  const currOutlet = MerchantStore.useState((s) => s.currOutlet);
  const merchantLogo = MerchantStore.useState((s) => s.logo);
  const merchantName = MerchantStore.useState((s) => s.name);

  const calendarDataArray = CommonStore.useState((s) => s.calendarDataArray);
  const selectedCalendarArray = CommonStore.useState(
    (s) => s.selectedCalendarArray,
  );
  const selectedCalendarData = CommonStore.useState(
    (s) => s.selectedCalendarData,
  );
  const selectedReservationTableId = CommonStore.useState(
    (s) => s.selectedReservationTableId,
  );
  const selectedReservationTableCode = CommonStore.useState(
    (s) => s.selectedReservationTableCode,
  );

  const userReservations = OutletStore.useState((s) => s.userReservations);

  const allOutletTables = OutletStore.useState((s) => s.outletTables);
  const [outletTables, setOutletTables] = useState([]);
  const RESERVATION_SECTION = {
    DATETIME: 'DATETIME',
    GUEST: 'GUEST',
    TABLE: 'TABLE',
    CREATE_GUEST: 'CREATE_GUEST',
  };
  const [reservationSection, setReservationSection] = useState(
    RESERVATION_SECTION.DATETIME,
  );
  const [paxNum, setPaxNum] = useState(0);
  const [revNum, setRevNum] = useState(0);

  const [headerSorting, setHeaderSorting] = useState([]);
  const [sort, setSort] = useState('');
  const [guestAvatar, setGuestAvatar] = useState('');
  const [guestGender, setGuestGender] = useState('');
  const [guestName, setGuestName] = useState('');
  const [guestLastName, setGuestLastName] = useState('');
  const [guestPhone, setGuestPhone] = useState('');
  const [guestEmail, setGuestEmail] = useState('');

  const [image, setImage] = useState('');
  const [imageType, setImageType] = useState('');
  const [isImageChanged, setIsImageChanged] = useState(false);

  const [dietaryRestrictions, setDietaryRestrictions] = useState('');
  const [specialOccasions, setSpecialOccasions] = useState('');

  const [userReservation, setUserReservation] = useState([]);

  const [allReservationAvailabilityList, setAllReservationAvailabilityList] = useState([]);

  const [dateSelectedTimestamp, setDateSelectedTimestamp] = useState(moment().startOf('day').valueOf());

  // reservation availability list for the selected date
  const [reservationAvailabilityToday, setReservationAvailabilityToday] = useState([]);
  const [reservationAvailabilityTodayNames, setReservationAvailabilityTodayNames] = useState([]);

  // reservation availability list filtered by name
  const [reservationAvailabilityFiltered, setReservationAvailabilityFiltered] = useState([]);
  const [selectedReservationName, setSelectedReservationName] = useState('All Day');
  const [selectedReservationId, setSelectedReservationId] = useState('');

  const crmUsers = OutletStore.useState((s) => s.crmUsers);

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const firebaseUid = UserStore.useState((s) => s.firebaseUid);

  const [resDays, setResDays] = useState([]);
  const [resMonths, setResMonths] = useState([]);
  const [resYears, setResYears] = useState([]);
  const [selectedResDay, setSelectedResDay] = useState(moment().endOf(Date.now()).format('DD'));
  const [selectedResMonth, setSelectedResMonth] = useState(moment().endOf(Date.now()).format('MM'));
  const [selectedResYear, setSelectedResYear] = useState(moment().endOf(Date.now()).format('YYYY'));

  const [openDate, setOpenDate] = useState(false);
  const [openMonth, setOpenMonth] = useState(false);
  const [openYear, setOpenYear] = useState(false);
  const currPage = CommonStore.useState((s) => s.currPage);

  // to get the day, month and year
  useEffect(() => {
    var resDaysTemp = Array.from(Array(moment(rev_date).daysInMonth()), (_, i) => i + 1);
    setResDays(resDaysTemp);

    console.log('resDaysTemp');
    console.log(resDaysTemp);
  }, [rev_date]);

  useEffect(() => {
    var resMonthsTemp = []
    // resMonthsTemp = moment.localeData().monthsShort();
    resMonthsTemp = Array.from(Array(12), (_, i) => i + 1);
    setResMonths(resMonthsTemp);

    var currYear = parseInt(moment().format('YYYY'), 10);
    var startYear = currYear - 10;

    var resYearsTemp = []
    for (var i = 0; i < 20; i++) {
      resYearsTemp.push(startYear);
      startYear++;
    }
    setResYears(resYearsTemp);

    console.log('resMonthsTemp');
    console.log(resMonthsTemp);
    console.log('resYearsTemp');
    console.log(resYearsTemp);
  }, []);

  // update selected date
  useEffect(() => {
    /* var selectedResDayTemp = selectedResDay;
    var selectedResMonthTemp = selectedResMonth;
    var selectedResYearTemp = selectedResYear;

    // console.log('day change');
    // console.log(selectedResDayTemp);

    // console.log('month change');
    // console.log(selectedResMonthTemp);

    // console.log('year change');
    // console.log(selectedResYearTemp); */
    if (selectedCalendarData) {
      //console.log('selectedCalendarData true',moment(selectedCalendarData).startOf('day'));
      setSelectedResDay(moment(selectedCalendarData).format('DD'));
      setSelectedResMonth(moment(selectedCalendarData).format('MM'));
      setSelectedResYear(moment(selectedCalendarData).format('YYYY'));
      setRev_date(moment(selectedCalendarData).startOf('day'));

      console.log('set selectedCalendarData');
      console.log(moment(selectedCalendarData).format('YYYY-MM-DD'));
      console.log(selectedCalendarData);

      if (typeof selectedCalendarData === 'string') {
        CommonStore.update((s) => {
          s.selectedCalendarData = moment(selectedCalendarData, 'YYYY-MM-DD').format('YYYY-MM-DD');
        });
      }
      else {
        CommonStore.update((s) => {
          s.selectedCalendarData = moment(selectedCalendarData).format('YYYY-MM-DD');
        });
      }
    }
    else {
      var dateTemp = moment(`${selectedResDay}-${selectedResMonth}-${selectedResYear}`
      ).format('YYYY-MM-DDTHH:mm:ss.SSSS[Z]');
      // console.log('dateTemp', dateTemp)
      // console.log('s.selectedCalendarData', selectedCalendarData)
      // console.log(moment(dateTemp).startOf('day'))
      // console.log('time0 + ' + moment(dateTemp).format('DD MM YYYY'));
      // console.log(moment(dateTemp).format('YYYY-MM-DD')); */

      setRev_date(moment(dateTemp).startOf('day'));
      // console.log('time00 + ' + moment(dateTemp).format('DD MM YYYY'));

      console.log('set selectedCalendarData dateTemp');
      console.log(dateTemp);

      CommonStore.update((s) => {
        s.selectedCalendarData = moment(dateTemp).format('YYYY-MM-DD');
      });
    }


  }, [selectedResDay, selectedResMonth, selectedResYear]);

  // useEffect(() => {
  //   for (let i = 0; i < calendarDataArray.length; i++) {
  //     if (
  //       moment(calendarDataArray[i].date).format('YYYY-MM-DD') ===
  //       selectedCalendarData
  //     ) {
  //       setPaxNum(calendarDataArray[i].pax);
  //       setRevNum(calendarDataArray[i].rsv);
  //       break;
  //     } else {
  //       setPaxNum(0);
  //       setRevNum(0);
  //     }
  //   }
  //   // calendarDataArray.forEach((item, index) => {
  //   //   if (moment(item.date).format('YYYY-MM-DD') === selectedCalendarData) {
  //   //     setPaxNum(item.pax);
  //   //     setRevNum(item.rev);
  //   //     isValid = true;
  //   //   }
  //   // });
  //   // console.log('refreshed');
  // }, [selectedCalendarData]);

  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.selectedCalendarData = moment().format('YYYY-MM-DD');
  //   });
  // }, []);

  useEffect(() => {
    var customerListDict = {};
    setHeaderSorting(crmUsers);
  }, [crmUsers]);

  useEffect(() => {
    var tempArr = [];
    console.log('userReservations', userReservations)
    if (userReservations) {
      for (let i = 0; i < userReservations.length; i++) {
        if (
          moment(userReservations[i].reservationTime).format('YYYY-MM-DD') ===
          selectedCalendarData
          // moment(userReservations[i].reservationTime).isSameOrBefore(selectedCalendarData, 'day')
        ) {
          tempArr.push(userReservations[i]);
        }
      }
    }
    console.log('userReservations', userReservations)

    // var tempResArr = [];
    //var tempFinishedArr = [];
    //var tempWaitArr = [];

    // if (tempArr) {
    //   tempArr.forEach((item) => {
    //     if (selectedReservationStatus === USER_RESERVATION_STATUS.ALL) {
    //       if (
    //         item.status === USER_RESERVATION_STATUS.ACCEPTED ||
    //         item.status === USER_RESERVATION_STATUS.SEATED ||
    //         item.status === USER_RESERVATION_STATUS.CANCELED ||
    //         item.status === USER_RESERVATION_STATUS.NO_SHOW ||
    //         item.status === USER_RESERVATION_STATUS.PENDING
    //       ) {
    //         tempResArr.push(item);
    //       }
    //     } else if (item.status === selectedReservationStatus) {
    //       tempResArr.push(item);
    //     }
    //   });

    //   setReservationsArr(tempResArr);
    // }

    // reservations filter start

    let filteredUserReservationsTemp = tempArr;

    // section filter
    // if (Object.keys(roomChecked).length > 0) {
    //   // console.log('room check!');
    //   var roomTemp = [];
    //   var roomFilter = Object.entries(roomChecked).map(([key, value]) => {
    //     if (value) {
    //       return key;
    //     }
    //   });

    //   roomFilter
    //     .filter((room) => room !== undefined)
    //     .forEach((room) => {
    //       filteredUserReservationsTemp.forEach((item) => {
    //         outletTables.forEach((table) => {
    //           if (
    //             item.tableId === table.uniqueId &&
    //             table.outletSectionId === room
    //           ) {
    //             roomTemp.push(item);
    //           }
    //         });
    //       });
    //     });

    //   setIsFiltered(true);
    //   filteredUserReservationsTemp = roomTemp;
    // }

    // if (Object.keys(partyChecked).length > 0) {
    //   var partyTemp = [];
    //   var partyFilter = Object.entries(partyChecked).map(([key, value]) => {
    //     if (value) {
    //       return key;
    //     }
    //   });

    //   partyFilter
    //     .filter((party) => party !== undefined)
    //     .forEach((party) => {
    //       filteredUserReservationsTemp.forEach((item) => {
    //         if (party === '0') {
    //           if (item.pax > 0 && item.pax <= 6) {
    //             partyTemp.push(item);
    //           }
    //         } else if (party === '1') {
    //           if (item.pax > 6 && item.pax <= 12) {
    //             partyTemp.push(item);
    //           }
    //         } else if (party === '2') {
    //           if (item.pax > 12 && item.pax <= 18) {
    //             partyTemp.push(item);
    //           }
    //         } else if (party === '3') {
    //           if (item.pax > 18 && item.pax <= 24) {
    //             partyTemp.push(item);
    //           }
    //         } else if (party === '4') {
    //           if (item.pax > 24 && item.pax <= 30) {
    //             partyTemp.push(item);
    //           }
    //         } else if (party === '5') {
    //           if (item.pax > 30 && item.pax <= 36) {
    //             partyTemp.push(item);
    //           }
    //         }
    //       });
    //     });

    //   setIsFiltered(true);
    //   filteredUserReservationsTemp = partyTemp;
    // }

    // if (filterTags) {
    //   setIsFiltered(true);
    //   filteredUserReservationsTemp = filteredUserReservationsTemp.filter(
    //     (item) => {
    //       item.rsvTags || item.guestTags;
    //     },
    //   );
    // }

    // if (filterNotes) {
    //   setIsFiltered(true);
    //   filteredUserReservationsTemp = filteredUserReservationsTemp.filter(
    //     (item) => {
    //       item.remarks || item.guestNotes;
    //     },
    //   );
    // }

    // setReservationsArr(filteredUserReservationsTemp);

    // reservation filter end

    setPaxNum(filteredUserReservationsTemp.reduce((accum, reservation) => {
      return accum + reservation.pax;
    }, 0));

    setRevNum(filteredUserReservationsTemp.length);
  }, [
    selectedCalendarData,
    userReservations,
    // selectedReservationStatus,
    // roomChecked,
    // partyChecked,
    // filterTags,
    // filterNotes,
  ]);

  // use effect to get the current outlet tables from the firestore
  useEffect(() => {
    let tempOutletTables = [];
    // filter allOutletTables for the current outlet
    for (let i = 0; i < allOutletTables.length; i++) {
      if (allOutletTables[i].outletId === currOutletId) {
        tempOutletTables.push(allOutletTables[i]);
      }
    }
    setOutletTables(tempOutletTables);

  }, [currOutletId, allOutletTables]);

  // use effect to get the user reservation from the firestore
  useEffect(() => {
    try {
      const unsubscribe = firebase.firestore().collection(Collections.UserReservation)
        .where('outletId', '==', currOutletId)
        .onSnapshot(async (snapshot) => {
          const userReservation = [];
          if (snapshot && !snapshot.empty) {
            snapshot.forEach((doc) => {
              const docData = doc.data();

              userReservation.push({
                ...docData,
              });
            });
            setUserReservation(userReservation);
          }
          // console.log('User Reservation list', userReservation);
        });
      return (() => {
        // unsubscribe the listener
        unsubscribe()
      })
    }
    catch (e) {
      // console.log(e);
    }
  }, []);

  // use effect to get the reservationAvailability from the firestore
  useEffect(() => {
    try {
      const unsubscribe = firebase.firestore().collection(Collections.ReservationAvailability)
        .where('merchantId', '==', merchantId)
        .where('outletId', '==', currOutletId)
        .onSnapshot(async (snapshot) => {
          const reservationAvailability = [];
          if (snapshot && !snapshot.empty) {
            snapshot.forEach((doc) => {
              const docData = doc.data();

              reservationAvailability.push({
                ...docData,
              });
            });
            setAllReservationAvailabilityList(reservationAvailability);
          }
          // console.log('Reservation Availability List', reservationAvailability)

          // need move whole logic to start of app (dashboard ?) or add to store ???
        });
      return (() => {
        // unsubscribe the listener
        unsubscribe()
      })
    }
    catch (e) {
      // console.log(e);
    }
  }, []);

  const getAllAvailableTimeSlot = (startInput, end, availability, userReservation, partyAmount, outletTables,) => {
    let tempAvailabilityList = [];
    let start = moment(startInput);

    // interval, add start time with each interval
    while (end >= start.add(availability.intervalMin, 'minutes')) {
      // get the interval start time
      const intervalStartTime = moment(start).subtract(availability.intervalMin, 'minutes');

      // check if the intervalStartTime is before the current time
      if (moment(intervalStartTime).isBefore(moment())) {
        continue;
      }

      let guestNum = 0;
      userReservation.forEach((eachUserReservation) => {
        if (eachUserReservation.status === USER_RESERVATION_STATUS.ACCEPTED &&
          moment(eachUserReservation.reservationTime).isSame(
            moment(intervalStartTime))) {
          guestNum += eachUserReservation.pax;
        }
      });

      // skip current interval if the number of guest is more than the max
      if (guestNum + partyAmount > availability.defaultGuestLimit) {
        continue;
      }

      let haveSpace = false;

      // for each table look for a user reservation at that time
      outletTables.forEach((table) => {
        if (haveSpace) return;

        let isReserved = false;

        userReservation.forEach((eachUserReservation) => {
          // exit if already have space or table is reserved
          if (haveSpace || isReserved) return;

          // if table is reserved by the reservation
          if (eachUserReservation.tableId &&
            eachUserReservation.status === USER_RESERVATION_STATUS.ACCEPTED &&
            eachUserReservation.tableId === table.uniqueId) {

            // check if the reservation is between each interval
            if (moment(eachUserReservation.reservationTime).isBetween(
              moment(intervalStartTime), moment(start), 'minutes', '[]')) {
              isReserved = true;
            }

            // check if there is a table turn time clashed
            if (moment(intervalStartTime).isBetween(
              moment(eachUserReservation.reservationTime),
              moment(eachUserReservation.reservationTime).add(table.turnTime || 60, 'minutes'),
              'minutes', '[]')) {
              isReserved = true;
            }
          }
        });

        if (!isReserved) {
          // if there is no reservation, check the pax size
          // let maxPax = table.paxMax || 9999;
          let maxPax = table.capacity || 9999;
          let minPax = table.paxMin || 1;

          // check if it can fit and the pax is greater than the min
          if (table.capacity >= (isNaN(+partyAmount) ? 10 : +partyAmount) &&
            maxPax >= (isNaN(+partyAmount) ? 10 : +partyAmount) &&
            minPax <= (isNaN(+partyAmount) ? 10 : +partyAmount)) {
            // if pax size is less than the table size add timeslot to the list
            haveSpace = true;
          }
        }
      });

      if (haveSpace) {
        tempAvailabilityList.push({
          ...availability,
          startTime: intervalStartTime.format('hh:mm A'),
          endTime: start.format('hh:mm A'),
          startTimeMilli: intervalStartTime.valueOf(),
        });
      }
    }

    return tempAvailabilityList;
  }

  // when date changed, filter the time slot available to display
  useEffect(() => {
    let tempAvailabilityList = [];
    let tempAvailabilityNames = [];

    // repeat logic
    allReservationAvailabilityList.forEach((availability) => {
      let dailyHours = [];  // store start time and end time from weekdaysTimeRepeat
      let start;
      let end;
      // make sure the weekdaysTimeRepeat time is length 4 (0830)

      // check if there is stop date and already pass the date
      if (availability.dateStopRepeat &&
        moment(dateSelectedTimestamp).startOf('day').isAfter(moment(availability.dateStopRepeat).startOf('day'))) {
        // console.log('after end date')
        return;
      }

      // push unique names only
      if (!tempAvailabilityNames.includes(availability.reservationName)) {
        tempAvailabilityNames.push(availability.reservationName);
      }

      if (availability.repeatShiftType === RESERVATIONS_SHIFT_TYPE.DOES_NOT_REPEAT) {
        // DOES NOT REPEAT

        // check if dateStartSingle is within this day
        if (moment(availability.dateStartSingle).isBetween(
          moment(dateSelectedTimestamp).startOf('day'),
          moment(dateSelectedTimestamp).endOf('day'))) {
          dailyHours = availability.weekdaysTimeRepeat.split('-');
          start = moment(availability.dateStartSingle).startOf('day')
            .add(dailyHours[0].slice(0, 2), 'hours')
            .add(dailyHours[0].slice(2, 4), 'minutes');
          end = moment(availability.dateStartSingle).startOf('day')
            .add(dailyHours[1].slice(0, 2), 'hours')
            .add(dailyHours[1].slice(2, 4), 'minutes');

          tempAvailabilityList.push(...getAllAvailableTimeSlot(start, end, availability, userReservation, partyAmount, outletTables,))
        }
      } else if (availability.repeatShiftType === RESERVATIONS_SHIFT_TYPE.DAILY) {
        // DAILY

        dailyHours = availability.weekdaysTimeRepeat.split('-');
        start = moment(dateSelectedTimestamp).startOf('day')
          .add(dailyHours[0].slice(0, 2), 'hours')
          .add(dailyHours[0].slice(2, 4), 'minutes');
        end = moment(dateSelectedTimestamp).startOf('day')
          .add(dailyHours[1].slice(0, 2), 'hours')
          .add(dailyHours[1].slice(2, 4), 'minutes');

        tempAvailabilityList.push(...getAllAvailableTimeSlot(start, end, availability, userReservation, partyAmount, outletTables,))
      } else if (availability.repeatShiftType === RESERVATIONS_SHIFT_TYPE.WEEKLY) {
        // WEEKLY

        let timeLapse = 0;
        // repeatEveryShiftPeriod to check if it intersects the calendar week
        while (true) {
          const repeatedWeekDate = moment(availability.dateStartSingle)
            .isoWeekday(1)
            .add(availability.repeatEveryShiftPeriod * timeLapse, 'weeks')

          // if past calendar week range then break
          if (moment(repeatedWeekDate).isAfter(moment(dateSelectedTimestamp).endOf('isoweek'))) {
            break;
          }

          // check if it is in the calendar week range
          if (moment(repeatedWeekDate).isBetween(
            moment(dateSelectedTimestamp).startOf('isoweek'),
            moment(dateSelectedTimestamp).endOf('isoweek'))) {

            // specify the start day of weekday (Mon is 0)
            const day = moment(dateSelectedTimestamp).isoWeekday() - 1;

            // check which day is true to repeat [mon,tue,wed,thu,fri,sat,sun]
            if (availability.repeatOnWeekdays[day] === true) {
              if (availability.isWeekdaysTimeSame) {
                // repeating day is same time (weekdaysTimeRepeat)
                dailyHours = availability.weekdaysTimeRepeat.split('-');
                start = moment(dateSelectedTimestamp)
                  .add(dailyHours[0].slice(0, 2), 'hours')
                  .add(dailyHours[0].slice(2, 4), 'minutes');
                end = moment(dateSelectedTimestamp)
                  .add(dailyHours[1].slice(0, 2), 'hours')
                  .add(dailyHours[1].slice(2, 4), 'minutes');

                tempAvailabilityList.push(...getAllAvailableTimeSlot(start, end, availability, userReservation, partyAmount, outletTables,))
              } else {
                // repeating day is different time
                // use weekdaysTime and find the time for current day
                dailyHours = availability.weekdaysTime[day].split('-');
                start = moment(dateSelectedTimestamp)
                  .add(dailyHours[0].slice(0, 2), 'hours')
                  .add(dailyHours[0].slice(2, 4), 'minutes');
                end = moment(dateSelectedTimestamp)
                  .add(dailyHours[1].slice(0, 2), 'hours')
                  .add(dailyHours[1].slice(2, 4), 'minutes');

                tempAvailabilityList.push(...getAllAvailableTimeSlot(start, end, availability, userReservation, partyAmount, outletTables,))
              }
            }
            return;
          }

          timeLapse++;
          if (timeLapse > 100) {
            // console.log('%cdangerous INFINITE LOOP return ########################',
            // "color: blue; font-family:monospace; font-size: 20px")
            break;
          }
          if (availability.repeatEveryShiftPeriod === 0) {
            break;
          }
        }
      }
    });

    // sort the list by start time
    tempAvailabilityList = tempAvailabilityList.sort((a, b) => a.startTimeMilli - b.startTimeMilli);
    setReservationAvailabilityToday(tempAvailabilityList);

    // filter the reservation names that has time slot
    tempAvailabilityNames = tempAvailabilityNames.filter((name) => {
      let isAvailable = false;
      tempAvailabilityList.forEach((availability) => {
        if (isAvailable) return;
        if (availability.reservationName === name) {
          isAvailable = true;
        }
      });
      return isAvailable;
    });

    // sort according to the starting time
    tempAvailabilityNames = tempAvailabilityNames.sort((a, b) => {
      let aStartTime = 0;
      let bStartTime = 0;
      tempAvailabilityList.forEach((availability) => {
        if (availability.reservationName === a) {
          // get the smallest start time
          if (aStartTime === 0) {
            aStartTime = availability.startTimeMilli;
          } else if (availability.startTimeMilli < aStartTime) {
            aStartTime = availability.startTimeMilli;
          }
        }
        if (availability.reservationName === b) {
          // get the smallest start time
          if (bStartTime === 0) {
            bStartTime = availability.startTimeMilli;
          } else if (availability.startTimeMilli < bStartTime) {
            bStartTime = availability.startTimeMilli;
          }
        }
      });
      return aStartTime - bStartTime;
    });
    setReservationAvailabilityTodayNames(tempAvailabilityNames);

    // display all the reservation
    setSelectedReservationName('All Day');

    // // console.log('tempList', tempAvailabilityList);
    // console.table(tempAvailabilityNames);
  }, [dateSelectedTimestamp, allReservationAvailabilityList, partyAmount, userReservation, outletTables]);

  // when selected reservation name changed, filter the time slot available to display
  useEffect(() => {
    if (selectedReservationName === 'All Day') {
      setReservationAvailabilityFiltered(reservationAvailabilityToday);
    } else {
      let tempAvailabilityList = [];

      reservationAvailabilityToday.forEach((item, index) => {
        if (item.reservationName === selectedReservationName) {
          tempAvailabilityList.push(item);
        }
      });

      setReservationAvailabilityFiltered(tempAvailabilityList);
    }
  }, [reservationAvailabilityToday, selectedReservationName]);

  // when selected time slot changed or party amount changed, chose table
  useEffect(() => {
    let seatSizeDiff = 9999;
    let bestTable = null;
    let isBestAlreadyFound = false;

    // get the table list
    outletTables.forEach((table) => {
      // check if the table is already reserve at that time
      let isReserved = false;

      // if already found a best fit (0 seat difference) then skip
      if (isBestAlreadyFound) {
        return;
      }

      userReservation.forEach((userReservation) => {
        if (userReservation.tableId &&
          userReservation.status === USER_RESERVATION_STATUS.ACCEPTED &&
          userReservation.tableId === table.uniqueId) {

          // check if there reservation at that time
          if (moment(userReservation.reservationTime).isSame(
            moment(timeSelectedMilli))) {
            isReserved = true;
          }

          // check if there is a table turn time clashed
          if (moment(timeSelectedMilli).isBetween(
            moment(userReservation.reservationTime),
            moment(userReservation.reservationTime).add(table.turnTime || 60, 'minutes'),
            'minutes', '[]')) {
            isReserved = true;
          }
        }
      });

      // not reserved, get the best suited table
      if (!isReserved) {
        let maxPax = table.capacity || 9999;
        let minPax = table.paxMin || 1;

        // able to fit and more than minimum pax
        if (table.capacity >= (isNaN(+partyAmount) ? 10 : +partyAmount) &&
          maxPax >= (isNaN(+partyAmount) ? 10 : +partyAmount) &&
          minPax <= (isNaN(+partyAmount) ? 10 : +partyAmount)) {

          // found better table
          if (table.capacity - partyAmount < seatSizeDiff) {
            seatSizeDiff = table.capacity - partyAmount;
            bestTable = table;

            // check for the best fit
            if (seatSizeDiff === 0) {
              isBestAlreadyFound = true;
            }
          }
        }
      }
    });

    if (bestTable == null) return;

    CommonStore.update((s) => {
      s.selectedReservationTableId = bestTable.uniqueId;
      s.selectedReservationTableCode = bestTable.code;
    });
  }, [reservationAvailabilityToday, timeSelectedMilli, partyAmount, userReservation, outletTables]);


  const sortCRMUsers = (dataList, userSortFieldType) => {
    var dataListTemp = [...dataList];

    const userSortFieldTypeValue =
      USER_SORT_FIELD_TYPE_VALUE[userSortFieldType];

    const userSortFieldTypeCompare =
      REPORT_SORT_FIELD_TYPE_COMPARE[userSortFieldType];

    //NAME
    if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.NAME_ASC) {
        dataListTemp.sort((a, b) =>
          (a[userSortFieldTypeValue]
            ? a[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
        );
      }
    } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.NAME_DESC) {
        dataListTemp.sort((a, b) =>
          (b[userSortFieldTypeValue]
            ? b[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
        );
      }
    }

    //NUMBER
    if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.NUMBER_ASC) {
        dataListTemp.sort((a, b) =>
          (a[userSortFieldTypeValue]
            ? a[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '') -
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : ''),
        );
      }
    } else if (userSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
      if (userSortFieldType === USER_SORT_FIELD_TYPE.NUMBER_DESC) {
        dataListTemp.sort((a, b) =>
          (b[userSortFieldTypeValue]
            ? b[userSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            (b[userSortFieldTypeValue] ? b[userSortFieldTypeValue] : '') -
            (a[userSortFieldTypeValue] ? a[userSortFieldTypeValue] : ''),
        );
      }
    }
    return dataListTemp;
  };

  const renderItem = ({ item, index }) => {
    return (
      <View
        style={{
          backgroundColor: '#FFFFFF',
          paddingVertical: 10,
          borderColor: '#BDBDBD',
          borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
          borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
        }}>
        <TouchableOpacity
          onPress={() => {
            setSelectedGuestObject(item);
            setSelectedGuestName(item.name);
            setReservationSection(RESERVATION_SECTION.TABLE);
          }}>
          <View
            style={{
              flexDirection: 'row',
              marginTop: 10,
              marginBottom: 10,
              alignItems: 'center',
            }}>
            <View
              style={{
                flexDirection: 'row',
                marginLeft: 0,
                width: '7%',
                alignItems: 'center',
                paddingLeft: 20,
              }}>
              {item.avatar ? (
                <AsyncImage
                  style={{
                    width: switchMerchant ? 35 : 50,
                    height: switchMerchant ? 35 : 50,
                    alignSelf: 'center',
                    borderRadius: 100,
                    marginLeft: 5,
                  }}
                  source={{
                    uri: item.avatar,
                  }}
                  item={item}
                  hideLoading
                />
              ) : (
                <Image
                  style={{
                    width: switchMerchant ? 35 : 50,
                    height: switchMerchant ? 35 : 50,
                    alignSelf: 'center',
                    borderRadius: 100,
                    marginLeft: 5,
                  }}
                  source={require('../assets/image/profile-pic.jpg')}
                  hideLoading
                />
              )}
            </View>
            <View style={{ flexDirection: 'row', marginLeft: 0, width: '20%' }}>
              <View
                style={{
                  flexDirection: 'column',
                  marginLeft: 5,
                  paddingLeft: 20,
                }}>
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 10 : 14,
                    fontWeight: '500',
                    textAlign: 'left',
                    fontFamily: 'NunitoSans-Bold',
                    marginBottom: 5,
                  }}>
                  {item.name}
                </Text>
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 9 : 12,
                    fontFamily: 'NunitoSans-Regular',
                    fontWeight: '500',
                    textAlign: 'left',
                    color: Colors.fieldtTxtColor,
                  }}>
                  {item.uniqueName}
                </Text>
              </View>
            </View>

            <View style={{ flexDirection: 'row', marginLeft: 0, width: '23%' }}>
              <View style={{ flexDirection: 'column' }}>
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    fontWeight: '500',
                    textAlign: 'left',
                    marginBottom: 5,
                  }}>
                  {item.number}
                </Text>
                <Text
                  style={{
                    flex: 1,
                    fontSize: switchMerchant ? 10 : 14,
                    fontFamily: 'NunitoSans-Regular',
                    fontWeight: '500',
                    textAlign: 'left',
                  }}>
                  {item.email}
                </Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  const renderList = (item, index) => {
    // console.log(item, 'here');
    return (
      <View
        style={{
          marginVertical: 5,
          flexDirection: 'row',
          width: windowWidth * 0.4,
        }}>
        <Text style={{}}>Name: {item.item.userName}</Text>
        <Text>Table Code: {item.item.tableCode}</Text>
        <Text>Pax: {item.item.pax}</Text>
      </View>
    );
  };

  // const handleChoosePhoto = () => {
  //   const imagePickerOptions = {
  //     mediaType: 'photo',
  //     quality: 0.5,
  //     includeBase64: false,
  //     maxWidth: 512,
  //     maxHeight: 512,
  //   };

  //   launchImageLibrary(imagePickerOptions, (response) => {
  //     if (response.didCancel) {
  //     } else if (response.error) {
  //       Alert.alert(response.error.toString());
  //     } else {
  //       // setState({ image: response.uri });
  //       setGuestAvatar(response.uri);
  //       setImage(response.uri);
  //       setImageType(response.uri.slice(response.uri.lastIndexOf('.')));

  //       setIsImageChanged(true);
  //     }
  //   });
  // };

  // create new user or guest
  const createCRMUser = async (isAutoPush = false) => {
    if (
      // !customerAvatar ||
      // !customerName ||
      !guestPhone ||
      !guestName
      // !customerEmail
      // !customerAddress
    ) {
      Alert.alert(
        'Error',
        'Please fill in all required information:\nName\nContact number',
        [{ text: 'OK', onPress: () => { } }],
        { cancelable: false },
      );

    } else {
      ///////////////////////////////////

      var parsedPhone = guestPhone;
      if (parsedPhone.length > 0 && !parsedPhone.startsWith('6')) {
        parsedPhone = `6${parsedPhone}`;
      }

      var sameUser = crmUsers.find(crmUser => {
        if (guestEmail && crmUser.email === guestEmail) {
          return true;
        }

        if (parsedPhone && crmUser.number === parsedPhone) {
          return true;
        }
      });

      if (sameUser) {
        Alert.alert(
          'Info',
          'Existing email and/or phone number found, please try another one.',
          [{ text: 'OK', onPress: () => { } }],
          { cancelable: false },
        );
        return;
      }

      ///////////////////////////////////
      // upload image

      var profileImageImagePath = '';
      var profileImageCommonIdLocal = uuidv4();

      if (image && imageType) {
        // promotionCommonIdLocal = uuidv4();

        profileImageImagePath = await uploadImageToFirebaseStorage(
          {
            uri: image,
            type: imageType,
          },
          `/merchant/${merchantId}/crm/user/${profileImageCommonIdLocal}/image${imageType}`,
        );
      }

      // means new item

      var body = {
        merchantId,
        merchantName,
        outletId: currOutletId,

        avatar: profileImageImagePath,
        isImageChanged,
        dob: '',
        email: guestEmail || '',
        gender: guestGender,
        name: `${guestName} ${guestLastName}`,
        number: guestPhone,
        uniqueName: guestName || '',

        address: '',
        lat: '',
        lng: '',

        emailSecond: '',
        numberSecond: '',
        addressSecond: '',
        latSecond: '',
        lngSecond: '',

        timeline: {},

        commonId: profileImageCommonIdLocal,
      };

      // console.log('body ', body);

      CommonStore.update((s) => {
        s.isLoading = true;
      });

      ApiClient.POST(API.createCRMUser, body, false).then((result) => {
        if (result && result.status === 'success') {
          Alert.alert(
            'Success',
            'Customer has been added',
            [
              {
                text: 'OK',
                onPress: () => {
                  setReservationSection(RESERVATION_SECTION.GUEST);
                },
              },
            ],
            { cancelable: false },
          );
        }

        CommonStore.update((s) => {
          s.isLoading = false;
        });
      });

    }
  };

  // validate and create reservation
  const handleCreateReservation = async () => {
    if (selectedReservationTableId === '') {
      Alert.alert('Please select a table');
      return;
    }

    let combinedRemarks = '';
    if (dietaryRestrictions) {
      combinedRemarks += `\n\nDietary Restrictions: \n${dietaryRestrictions}`;
    }
    if (specialOccasions) {
      combinedRemarks += `\n\nSpecial occasions: \n${specialOccasions}`;
    }

    let body = {};

    if (selectedGuestName === 'Temporary guest') {
      body = {
        pax: partyAmount,
        userId: '',
        reservationTime: timeSelectedMilli, // unix
        reservationAvailabilityId: selectedReservationId,

        merchantId,
        outletId: currOutletId,

        outletCover: currOutlet.cover ? currOutlet.cover : '',
        merchantLogo: merchantLogo ? merchantLogo : '',
        outletName: currOutlet.name ? currOutlet.name : '',
        merchantName: merchantName ? merchantName : '',

        userName: '',
        userPhone: '',
        userEmail: '',

        crmUserId: '',

        tableId: selectedReservationTableId,
        tableCode: selectedReservationTableCode,

        remarks: combinedRemarks,
      }
    } else {
      body = {
        pax: partyAmount,
        userId: selectedGuestObject.userId,
        reservationTime: timeSelectedMilli, // unix
        reservationAvailabilityId: selectedReservationId,

        merchantId,
        outletId: currOutletId,

        outletCover: currOutlet.cover ? currOutlet.cover : '',
        merchantLogo: merchantLogo ? merchantLogo : '',
        outletName: currOutlet.name ? currOutlet.name : '',
        merchantName: merchantName ? merchantName : '',

        userName: selectedGuestObject.name,
        userPhone: selectedGuestObject.number,
        userEmail: selectedGuestObject.email,

        crmUserId: selectedGuestObject.uniqueId,

        tableId: selectedReservationTableId,
        tableCode: selectedReservationTableCode,

        remarks: combinedRemarks,
      }
    }

    // console.log('body', body);

    if (netInfo.isInternetReachable && netInfo.isConnected) {
      ApiClient.POST(API.createUserReservationByMerchant, body);

      Alert.alert('Success', 'Reservation has been created');
    }
    else {
      await APILocal.createUserReservationByMerchant({
        body,
        uid: firebaseUid,  // merchant id
      })
        .then((result) => {
          if (result.status) {
            Alert.alert('Success', 'Reservation has been created');
          }
        })
        .catch((err) => {
          // console.log(err);
        });

    }

    // clear all states
    setSelectedReservationId('');
    setTimeSelected('');
    setTimeSelectedMilli('');
    setPartyAmount('2');

    setSearch('');
    setSelectedGuestObject({});
    setSelectedGuestName('');

    CommonStore.update((s) => {
      s.selectedReservationTableId = '';
      s.selectedReservationTableCode = '';
    });

    setReservationSection(RESERVATION_SECTION.DATETIME);
    setAddReservationModal1(false);
  }

  ///////footer//////////

  return (
    <View>
      {/* ///////////////////////   FOOTER START  ///////////////////////////////// */}
      <DateTimePickerModal
        isVisible={showDateTimePicker}
        mode={'date'}
        onConfirm={(text) => {
          setRev_date(moment(text).startOf('day'));
          // console.log('time0 + ' + moment(text).format('DD MM YYYY'));
          setShowDateTimePicker(false);
          CommonStore.update((s) => {
            s.selectedCalendarData = moment(text).format('YYYY-MM-DD');
          });
        }}
        onCancel={() => {
          setShowDateTimePicker(false);
        }}
        style={{ zIndex: 1000 }}
      // maximumDate={moment(rev_date1).toDate()}
      />

      <DateTimePickerModal
        isVisible={showDateTimePicker1}
        mode={'date'}
        onConfirm={(text) => {
          setRev_date1(moment(text).endOf('day'));
          setShowDateTimePicker1(false);
          // console.log('rev' + rev_date1);
        }}
        onCancel={() => {
          setShowDateTimePicker1(false);
          // console.log('rev' + rev_date);
        }}
        style={{ zIndex: 1000 }}
        minimumDate={moment(rev_date).toDate()}
      />
      {currPage !== 'Reservation Analytic - KooDoo Manager' ?
        <View
          style={{
            position: 'absolute', //bottom: switchMerchant ? windowHeight * 0.002 : windowHeight * 0.002,
            left: windowWidth * Styles.sideBarWidth,
            height: switchMerchant
              ? windowHeight * 0.1
              : windowHeight * 0.1,
            backgroundColor: 'white',
            flex: 1,
            flexDirection: 'row',
            width:
              windowWidth -
              windowWidth * Styles.sideBarWidth,
            alignItems: 'center',
            paddingHorizontal: 20,
            zIndex: 100,
          }}>
          <View
            style={{ flexDirection: 'row', justifyContent: 'flex-start', flex: 1 }}>
            {/* <TouchableOpacity
            onPress={() => {
              setAddReservationModal1(true);
            }}>
            <View style={{ borderWidth: 1, borderColor: 'black', padding: 10, borderRadius: 15, }}>
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 14,
                  fontFamily: 'NunitoSans-Regular',
                }}>
                Add Reservation
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              setAlldayModal(true);
            }}>
            <View
              style={{
                borderWidth: 1,
                borderColor: 'black',
                padding: 10,
                marginLeft: 10,
                borderRadius: 15,
              }}>
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 14,
                  fontFamily: 'NunitoSans-Regular',
                }}>
                All Day
              </Text>
            </View>
          </TouchableOpacity> */}
          </View>

          <View style={{ flexDirection: 'row', justifyContent: 'center', }}>
            <TouchableOpacity
              style={{
                width: switchMerchant ? 30 : 45,
                height: switchMerchant ? 35 : 41,
                backgroundColor: Colors.primaryColor,
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: 15,
              }}
              onPress={() => {
                //setRev_date(moment(rev_date).add(-1, 'days').toDate());
                // Go back to previous date within the current month
                //  const prevDate = moment(rev_date).subtract(1, 'day');
                //  if (prevDate.date() === 1) {
                //    setSelectedResDay(prevDate.daysInMonth().toString());
                //    CommonStore.update((s) => {
                //      s.selectedCalendarData = prevDate.endOf('month').format('YYYY-MM-DD');
                //    });
                //  } else {
                //    // If the value is 1 on setSelectedResDay during pressing back
                //    // Go to the previous day in the same month
                //    setSelectedResDay(prevDate.date().toString());
                //    CommonStore.update((s) => {
                //      s.selectedCalendarData = prevDate.format('YYYY-MM-DD');
                //    });


                // let toMoveDateObj = moment(rev_date).add(-1, 'days');
                // if (moment(toMoveDateObj).format('MM') !== moment().format('MM')) {
                //   // means is another month already

                //   // set the new month, and new day

                //   let selectedResDayStr = parseInt(moment(toMoveDateObj).format('DD'));
                //   let selectedResMonthStr = parseInt(moment(toMoveDateObj).format('MM'));
                //   let selectedResYearStr = parseInt(moment(toMoveDateObj).format('YYYY'));

                //   CommonStore.update(s => {
                //     s.selectedCalendarData = moment(`${selectedResYearStr}-${selectedResMonth}-${selectedResDayStr}`, `YYYY-MM-DD`).format('YYYY-MM-DD');
                //   });
                // }
                // else {
                //   // just set the new day

                //   CommonStore.update((s) => {
                //     s.selectedCalendarData = moment(rev_date).add(-1, 'days').format('YYYY-MM-DD');
                //   });
                // }

                let toMoveDateObj = moment(rev_date).add(-1, 'days');

                let selectedResDayStr = parseInt(moment(toMoveDateObj).format('DD'));
                let selectedResMonthStr = parseInt(moment(toMoveDateObj).format('MM'));
                let selectedResYearStr = parseInt(moment(toMoveDateObj).format('YYYY'));

                setSelectedResDay(selectedResDayStr);
                setSelectedResMonth(selectedResMonthStr);
                setSelectedResYear(selectedResYearStr);

                CommonStore.update((s) => {
                  s.selectedCalendarData = moment(toMoveDateObj).format('YYYY-MM-DD');
                });
              }}>
              <ArrowLeft color={Colors.whiteColor} width={30} height={30} />
            </TouchableOpacity>
            {/* <TouchableOpacity
            onPress={() => {
              setShowDateTimePicker(true);
              setShowDateTimePicker1(false);
            }}>
            <View
              style={{
                borderWidth: 1,
                borderColor: 'black',
                padding: 10,
                marginHorizontal: 10,
                borderRadius: 15,
              }}>
              <Text
                style={[
                  { fontFamily: 'NunitoSans-Regular' },
                  switchMerchant
                    ? {
                      fontSize: 10,
                    }
                    : {},
                ]}>
                {moment(rev_date).format('DD MMM yyyy')}
              </Text>
            </View>
          </TouchableOpacity> */}

            {/* added Day Month Year RNPickerSelect */}
            <View style={{
              width: 90,
              height: 40,
              backgroundColor: '#fafafa',
              borderRadius: 10,
              marginRight: 10,
              marginLeft: 10,
            }}>
              <DropDownPicker
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: 80,
                  height: 40,
                  borderRadius: 10,
                  borderWidth: 1,
                  borderColor: "#E5E5E5",
                  flexDirection: "row",
                }}
                dropDownContainerStyle={{
                  width: 80,
                  backgroundColor: Colors.fieldtBgColor,
                  borderColor: "#E5E5E5",
                }}
                labelStyle={{
                  marginLeft: 5,
                  flexDirection: "row",
                }}
                textStyle={{
                  fontSize: 14,
                  fontFamily: 'NunitoSans-Regular',

                  marginLeft: 5,
                  paddingVertical: 10,
                  flexDirection: "row",
                }}
                selectedItemContainerStyle={{
                  flexDirection: "row",
                }}

                showArrowIcon={true}
                ArrowDownIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-down-outline"
                  />
                )}
                ArrowUpIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-up-outline"
                  />
                )}

                showTickIcon={true}
                TickIconComponent={({ press }) => (
                  <Ionicon
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    color={
                      press ? Colors.fieldtBgColor : Colors.primaryColor
                    }
                    name={'md-checkbox'}
                    size={25}
                  />
                )}
                placeholderStyle={{
                  // color: Colors.fieldtTxtColor,
                  // marginTop: 15,
                }}
                dropDownDirection="TOP"
                placeholder={selectedResDay}
                items={Object.entries(resDays).map(([key, value]) => {
                  return { label: value.toString(), value: value.toString().padStart(2, '0') }
                })}
                value={selectedResDay}
                onSelectItem={(item) => {
                  setSelectedResDay(item.value);
                  //// console.log(selectedResDay)

                  CommonStore.update(s => {
                    s.selectedCalendarData = moment(`${selectedResYear}-${selectedResMonth}-${item.value}`, `YYYY-MM-DD`).format('YYYY-MM-DD');
                  });
                }}
                open={openDate}
                setOpen={setOpenDate}
              />
            </View>

            <View style={{
              width: 90,
              height: 40,
              backgroundColor: '#fafafa',
              borderRadius: 10,
              marginRight: 10,
            }}>
              <DropDownPicker
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: 80,
                  height: 40,
                  borderRadius: 10,
                  borderWidth: 1,
                  borderColor: "#E5E5E5",
                  flexDirection: "row",
                }}
                dropDownContainerStyle={{
                  width: 80,
                  backgroundColor: Colors.fieldtBgColor,
                  borderColor: "#E5E5E5",
                }}
                labelStyle={{
                  marginLeft: 5,
                  flexDirection: "row",
                }}
                textStyle={{
                  fontSize: 14,
                  fontFamily: 'NunitoSans-Regular',

                  marginLeft: 5,
                  paddingVertical: 10,
                  flexDirection: "row",
                }}
                selectedItemContainerStyle={{
                  flexDirection: "row",
                }}

                showArrowIcon={true}
                ArrowDownIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-down-outline"
                  />
                )}
                ArrowUpIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-up-outline"
                  />
                )}

                showTickIcon={true}
                TickIconComponent={({ press }) => (
                  <Ionicon
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    color={
                      press ? Colors.fieldtBgColor : Colors.primaryColor
                    }
                    name={'md-checkbox'}
                    size={25}
                  />
                )}
                placeholderStyle={{
                  // color: Colors.fieldtTxtColor,
                  // marginTop: 15,
                }}
                dropDownDirection="TOP"
                placeholder={selectedResMonth}
                items={Object.entries(resMonths).map(([key, value]) => {
                  return { label: value.toString(), value: value.toString().padStart(2, '0') }
                })}
                value={selectedResMonth}
                onSelectItem={(item) => {
                  var monthParsed = item.value;

                  if (item.value.length === 3) {
                    monthParsed = moment(item.value, 'MMM').format('MM');
                  }

                  setSelectedResMonth(monthParsed);
                  //// console.log(selectedResMonth)

                  console.log('selectedResYear');
                  console.log(selectedResYear);
                  console.log('selectedResDay');
                  console.log(selectedResDay);
                  console.log('set rev_date monthParsed');
                  console.log(monthParsed);

                  let selectedResDayParsed = parseInt(selectedResDay);
                  let maxDaysForThePeriod = moment(`${selectedResYear}-${monthParsed}-01`, `YYYY-MM-DD`).daysInMonth();
                  if (selectedResDayParsed > maxDaysForThePeriod) {
                    // ex: 31 > 30 max days

                    selectedResDayParsed = maxDaysForThePeriod.toString().padStart(2, '0');
                  }
                  else {
                    selectedResDayParsed = selectedResDay;
                  }

                  CommonStore.update(s => {
                    s.selectedCalendarData = moment(`${selectedResYear}-${monthParsed}-${selectedResDayParsed}`, `YYYY-MM-DD`).format('YYYY-MM-DD');
                  });
                }}
                open={openMonth}
                setOpen={setOpenMonth}
              />
            </View>
            <View style={{
              width: 90,
              height: 40,
              backgroundColor: '#fafafa',
              borderRadius: 10,
              marginRight: 10,
            }}>
              <DropDownPicker
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: 80,
                  height: 40,
                  borderRadius: 10,
                  borderWidth: 1,
                  borderColor: "#E5E5E5",
                  flexDirection: "row",
                }}
                dropDownContainerStyle={{
                  width: 80,
                  backgroundColor: Colors.fieldtBgColor,
                  borderColor: "#E5E5E5",
                }}
                labelStyle={{
                  marginLeft: 5,
                  flexDirection: "row",
                }}
                textStyle={{
                  fontSize: 14,
                  fontFamily: 'NunitoSans-Regular',

                  marginLeft: 5,
                  paddingVertical: 10,
                  flexDirection: "row",
                }}
                selectedItemContainerStyle={{
                  flexDirection: "row",
                }}

                showArrowIcon={true}
                ArrowDownIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-down-outline"
                  />
                )}
                ArrowUpIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-up-outline"
                  />
                )}

                showTickIcon={true}
                TickIconComponent={({ press }) => (
                  <Ionicon
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    color={
                      press ? Colors.fieldtBgColor : Colors.primaryColor
                    }
                    name={'md-checkbox'}
                    size={25}
                  />
                )}
                placeholderStyle={{
                  color: Colors.fieldtTxtColor,
                  // marginTop: 15,
                }}
                dropDownDirection="TOP"
                placeholder={selectedResYear}
                items={Object.entries(resYears).map(([key, value]) => {
                  return { label: value.toString(), value: value.toString() }
                })}
                value={selectedResYear}
                onSelectItem={(item) => {
                  setSelectedResYear(item.value)
                  //// console.log(selectedResYear)

                  console.log('selectedResMonth');
                  console.log(selectedResMonth);
                  console.log('selectedResDay');
                  console.log(selectedResDay);
                  console.log('set rev_date item');
                  console.log(item);

                  let selectedResDayParsed = parseInt(selectedResDay);
                  let maxDaysForThePeriod = moment(`${item}-${selectedResMonth}-01`, `YYYY-MM-DD`).daysInMonth();
                  if (selectedResDayParsed > maxDaysForThePeriod) {
                    // ex: 31 > 30 max days

                    selectedResDayParsed = maxDaysForThePeriod.toString().padStart(2, '0');
                  }
                  else {
                    selectedResDayParsed = selectedResDay;
                  }

                  CommonStore.update(s => {
                    s.selectedCalendarData = moment(`${item.value}-${selectedResMonth}-${selectedResDay}`, `YYYY-MM-DD`).format('YYYY-MM-DD');
                  });
                }}
                open={openYear}
                setOpen={setOpenYear}
              />
            </View>

            <TouchableOpacity
              style={{
                width: switchMerchant ? 30 : 45,
                height: switchMerchant ? 35 : 41,
                backgroundColor: Colors.primaryColor,
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: 15,
              }}
              onPress={() => {
                // moment(rev_date).format('DD MMM yyyy') ===
                //   moment(rev_date1).format('DD MMM yyyy')
                //   ? ''
                //   : 

                // if (!(parseInt(selectedResDay) + 1 == 32)) {
                //   setSelectedResDay((parseInt(selectedResDay) + 1).toString())
                // }

                // if (!(parseInt(selectedResDay) + 1 == 32)) {
                //   console.log('selectedResDay');
                //   console.log(selectedResDay);
                //   console.log('set rev_date rev_date');
                //   console.log(rev_date);

                //   CommonStore.update((s) => {
                //     s.selectedCalendarData = moment(rev_date).add(1, 'days').format('YYYY-MM-DD');
                //   })
                // }

                let toMoveDateObj = moment(rev_date).add(1, 'days');

                let selectedResDayStr = parseInt(moment(toMoveDateObj).format('DD'));
                let selectedResMonthStr = parseInt(moment(toMoveDateObj).format('MM'));
                let selectedResYearStr = parseInt(moment(toMoveDateObj).format('YYYY'));

                setSelectedResDay(selectedResDayStr);
                setSelectedResMonth(selectedResMonthStr);
                setSelectedResYear(selectedResYearStr);

                CommonStore.update((s) => {
                  s.selectedCalendarData = moment(toMoveDateObj).format('YYYY-MM-DD');
                });

                // Code to switch days on a month
                //     const nextDay = parseInt(selectedResDay) + 1;
                //     const nextDate = moment(rev_date).add(1, 'day');

                //    if (nextDay <= nextDate.daysInMonth()) {
                //     setSelectedResDay(nextDay.toString());
                //     CommonStore.update((s) => {
                //     s.selectedCalendarData = nextDate.format('YYYY-MM-DD');
                //     console.log('selectedResDay');
                //     console.log(selectedResDay);
                //     console.log('set rev_date rev_date');
                //     console.log(rev_date);
                //   });
                //   } else {
                //   // If the last day is current selectedResDay when we press next:
                //   // Move to the 1st day of the next month, without incrementing month.
                //   setSelectedResDay('1');
                //   CommonStore.update((s) => {
                //   s.selectedCalendarData = nextDate.add(1, 'day').startOf('month').format('YYYY-MM-DD');
                //  });

                //setRev_date(moment(rev_date).add(1, 'days').toDate());
              }}>
              <ArrowRight color={Colors.whiteColor} width={30} height={30} />
            </TouchableOpacity>
          </View>

          <View
            style={{ flexDirection: 'row', justifyContent: 'flex-end', flex: 1 }}>
            {/* <TouchableOpacity
            onPress={() => {
              //setPaxrevModal(true);
            }}> */}
            <View style={{ borderWidth: 1, borderColor: 'black', padding: 10, borderRadius: 15, }}>
              <Text
                style={{
                  fontSize: switchMerchant ? 10 : 14,
                  fontFamily: 'NunitoSans-Regular',
                }}>
                {`${paxNum ? paxNum : '0'} Pax / ${revNum ? revNum : '0'} Rev`}
              </Text>
            </View>
            {/* </TouchableOpacity> */}
          </View>
        </View>
        :
        <View
          style={{
            position: 'absolute',
            left: windowWidth * Styles.sideBarWidth,
            height: switchMerchant
              ? windowHeight * 0.1
              : windowHeight * 0.1,
            backgroundColor: 'white',
            flex: 1,
            flexDirection: 'row',
            width:
              windowWidth -
              windowWidth * Styles.sideBarWidth,
            alignItems: 'center',
            paddingHorizontal: 20,
            zIndex: 100,
            justifyContent: 'center',
          }}>
          <View style={{ flexDirection: 'row', justifyContent: 'center', }}>
            {/* added Day Month Year RNPickerSelect */}
            <View style={{
              width: 90,
              height: 40,
              backgroundColor: '#fafafa',
              borderRadius: 10,
              marginRight: 10,
            }}>
              <DropDownPicker
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: 80,
                  height: 40,
                  borderRadius: 10,
                  borderWidth: 1,
                  borderColor: "#E5E5E5",
                  flexDirection: "row",
                }}
                dropDownContainerStyle={{
                  width: 80,
                  backgroundColor: Colors.fieldtBgColor,
                  borderColor: "#E5E5E5",
                }}
                labelStyle={{
                  marginLeft: 5,
                  flexDirection: "row",
                }}
                textStyle={{
                  fontSize: 14,
                  fontFamily: 'NunitoSans-Regular',

                  marginLeft: 5,
                  paddingVertical: 10,
                  flexDirection: "row",
                }}
                selectedItemContainerStyle={{
                  flexDirection: "row",
                }}

                showArrowIcon={true}
                ArrowDownIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-down-outline"
                  />
                )}
                ArrowUpIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-up-outline"
                  />
                )}

                showTickIcon={true}
                TickIconComponent={({ press }) => (
                  <Ionicon
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    color={
                      press ? Colors.fieldtBgColor : Colors.primaryColor
                    }
                    name={'md-checkbox'}
                    size={25}
                  />
                )}
                placeholderStyle={{
                  // color: Colors.fieldtTxtColor,
                  // marginTop: 15,
                }}
                dropDownDirection="TOP"
                placeholder={selectedResMonth}
                items={Object.entries(resMonths).map(([key, value]) => {
                  return { label: value.toString(), value: value.toString().padStart(2, '0') }
                })}
                value={selectedResMonth}
                onSelectItem={(item) => {
                  var monthParsed = item.value;

                  if (item.value.length === 3) {
                    monthParsed = moment(item.value, 'MMM').format('MM');
                  }

                  setSelectedResMonth(monthParsed);
                  //// console.log(selectedResMonth)

                  console.log('selectedResYear');
                  console.log(selectedResYear);
                  console.log('selectedResDay');
                  console.log(selectedResDay);
                  console.log('set rev_date monthParsed');
                  console.log(monthParsed);

                  let selectedResDayParsed = parseInt(selectedResDay);
                  let maxDaysForThePeriod = moment(`${selectedResYear}-${monthParsed}-01`, `YYYY-MM-DD`).daysInMonth();
                  if (selectedResDayParsed > maxDaysForThePeriod) {
                    // ex: 31 > 30 max days

                    selectedResDayParsed = maxDaysForThePeriod.toString().padStart(2, '0');
                  }
                  else {
                    selectedResDayParsed = selectedResDay;
                  }

                  CommonStore.update(s => {
                    s.selectedCalendarData = moment(`${selectedResYear}-${monthParsed}-${selectedResDayParsed}`, `YYYY-MM-DD`).format('YYYY-MM-DD');
                  });
                }}
                open={openMonth}
                setOpen={setOpenMonth}
              />
            </View>
            <View style={{
              width: 90,
              height: 40,
              backgroundColor: '#fafafa',
              borderRadius: 10,
              marginRight: 10,
            }}>
              <DropDownPicker
                style={{
                  backgroundColor: Colors.fieldtBgColor,
                  width: 80,
                  height: 40,
                  borderRadius: 10,
                  borderWidth: 1,
                  borderColor: "#E5E5E5",
                  flexDirection: "row",
                }}
                dropDownContainerStyle={{
                  width: 80,
                  backgroundColor: Colors.fieldtBgColor,
                  borderColor: "#E5E5E5",
                }}
                labelStyle={{
                  marginLeft: 5,
                  flexDirection: "row",
                }}
                textStyle={{
                  fontSize: 14,
                  fontFamily: 'NunitoSans-Regular',

                  marginLeft: 5,
                  paddingVertical: 10,
                  flexDirection: "row",
                }}
                selectedItemContainerStyle={{
                  flexDirection: "row",
                }}

                showArrowIcon={true}
                ArrowDownIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-down-outline"
                  />
                )}
                ArrowUpIconComponent={({ style }) => (
                  <Ionicon
                    size={25}
                    color={Colors.fieldtTxtColor}
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    name="chevron-up-outline"
                  />
                )}

                showTickIcon={true}
                TickIconComponent={({ press }) => (
                  <Ionicon
                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                    color={
                      press ? Colors.fieldtBgColor : Colors.primaryColor
                    }
                    name={'md-checkbox'}
                    size={25}
                  />
                )}
                placeholderStyle={{
                  color: Colors.fieldtTxtColor,
                  // marginTop: 15,
                }}
                dropDownDirection="TOP"
                placeholder={selectedResYear}
                items={Object.entries(resYears).map(([key, value]) => {
                  return { label: value.toString(), value: value.toString() }
                })}
                value={selectedResYear}
                onSelectItem={(item) => {
                  setSelectedResYear(item.value)
                  //// console.log(selectedResYear)

                  console.log('selectedResMonth');
                  console.log(selectedResMonth);
                  console.log('selectedResDay');
                  console.log(selectedResDay);
                  console.log('set rev_date item');
                  console.log(item);

                  let selectedResDayParsed = parseInt(selectedResDay);
                  let maxDaysForThePeriod = moment(`${item}-${selectedResMonth}-01`, `YYYY-MM-DD`).daysInMonth();
                  if (selectedResDayParsed > maxDaysForThePeriod) {
                    // ex: 31 > 30 max days

                    selectedResDayParsed = maxDaysForThePeriod.toString().padStart(2, '0');
                  }
                  else {
                    selectedResDayParsed = selectedResDay;
                  }

                  CommonStore.update(s => {
                    s.selectedCalendarData = moment(`${item.value}-${selectedResMonth}-${selectedResDay}`, `YYYY-MM-DD`).format('YYYY-MM-DD');
                  });
                }}
                open={openYear}
                setOpen={setOpenYear}
              />
            </View>
          </View>

        </View>
      }

      {/* ///////////////////////   All day modal start  ///////////////////////////////// */}
      <Modal
        style={{ flex: 1 }}
        visible={alldayModal}
        supportedOrientations={['portrait', 'landscape']}
        transparent
        animationType={'fade'}>
        <View
          style={{
            flex: 1,
            backgroundColor: Colors.modalBgColor,
            alignItems: 'center',
          }}>
          <View
            style={{
              height: windowWidth * 0.3,
              width: windowWidth * 0.3,
              backgroundColor: Colors.whiteColor,
              borderRadius: 12,
              padding: windowWidth * 0.03,
              alignItems: 'center',
              position: 'absolute',
              left: windowWidth * 0.09,
              bottom: windowHeight * 0.065,
            }}>
            <TouchableOpacity
              style={{
                position: 'absolute',
                right: windowWidth * 0.02,
                top: windowWidth * 0.02,

                elevation: 1000,
                zIndex: 1000,
              }}
              onPress={() => {
                setAlldayModal(false);
              }}>
              <AntDesign
                name="closecircle"
                size={switchMerchant ? 15 : 25}
                color={Colors.fieldtTxtColor}
              />
            </TouchableOpacity>
            <View
              style={{
                alignItems: 'center',
              }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  textAlign: 'center',
                  fontSize: switchMerchant ? 16 : 24,
                }}>
                Reservation Availability
              </Text>
            </View>
          </View>
        </View>
      </Modal>
      {/* ///////////////////////   All day modal end  ///////////////////////////////// */}

      {/* ///////////////////////   pax/rev modal start  ///////////////////////////////// */}

      <Modal
        style={{ flex: 1 }}
        visible={paxrevModal}
        supportedOrientations={['portrait', 'landscape']}
        transparent
        animationType={'fade'}>
        <View
          style={{
            flex: 1,
            backgroundColor: Colors.modalBgColor,
            alignItems: 'center',
          }}>
          <View
            style={{
              height: windowWidth * 0.3,
              width: windowWidth * 0.45,
              backgroundColor: Colors.whiteColor,
              borderRadius: 12,
              padding: windowWidth * 0.03,
              alignItems: 'center',
              position: 'absolute',
              right: windowWidth * 0.01,
              bottom: windowHeight * 0.065,
            }}>
            <TouchableOpacity
              style={{
                position: 'absolute',
                right: windowWidth * 0.02,
                top: windowWidth * 0.02,

                elevation: 1000,
                zIndex: 1000,
              }}
              onPress={() => {
                setPaxrevModal(false);
              }}>
              <AntDesign
                name="closecircle"
                size={switchMerchant ? 15 : 25}
                color={Colors.fieldtTxtColor}
              />
            </TouchableOpacity>
            <View
              style={{
                alignItems: 'center',
              }}>
              <Text
                style={{
                  fontFamily: 'NunitoSans-Bold',
                  textAlign: 'center',
                  fontSize: switchMerchant ? 16 : 24,
                }}>
                Availability Overview
              </Text>
            </View>
            <View style={{ flex: 1, justifyContent: 'center' }}>
              {selectedCalendarArray.length > 0 ? (
                <FlatList
                  showsVerticalScrollIndicator={false}
                  // data={list}
                  data={selectedCalendarArray}
                  renderItem={renderList}
                  keyExtractor={(item, index) => String(index)}
                />
              ) : (
                <Text>-No data-</Text>
              )}
            </View>
          </View>
        </View>
      </Modal>
      {/* ///////////////////////   pax/rev modal end  ///////////////////////////////// */}

      {/* ///////////////////////   add reservation modal start  ///////////////////////////////// */}
      <DateTimePickerModal
        isVisible={waitlistStart}
        mode={'time'}
        onConfirm={(text) => {
          setWaitlistStartTime(moment(text));

          setWaitlistStart(false);
        }}
        onCancel={() => {
          setWaitlistStart(false);
        }}
      />
      <DateTimePickerModal
        isVisible={waitlistEnd}
        mode={'time'}
        onConfirm={(text) => {
          setWaitlistEndTime(moment(text));

          setWaitlistEnd(false);
        }}
        onCancel={() => {
          setWaitlistEnd(false);
        }}
      />
      {/* ///////////////////////   pax/rev modal end  ///////////////////////////////// */}

      {/* ///////////////////////   FOOTER END  ///////////////////////////////// */}
    </View>
  );
};

var styles = StyleSheet.create({
  timebtn: {
    backgroundColor: Colors.whiteColor,
    borderRadius: 5,
    paddingVertical: 10,
    justifyContent: 'center',
    alignContent: 'center',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    zIndex: -1,
  },
  timebtn2: {
    backgroundColor: Colors.primaryColor,
    borderRadius: 5,
    paddingVertical: 10,
    justifyContent: 'center',
    alignContent: 'center',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    zIndex: -1,
  },
  closeButton: {
    position: 'absolute',
    right: Dimensions.get('window').width * 0.02,
    top: Dimensions.get('window').width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
});

const pickerStyle = StyleSheet.create({
  // RNPickerSelect Icon Style
  iconContainer: {
    top: 2,
    left: '75%',
    //flexDirection: 'row',
    elevation: 1,
    justifyContent: 'center',
    borderWidth: 0
  },

  placeholder: {
    color: Colors.whiteColor,
  },
  // RNPickerSelect Style with 
  inputAndroid: {
    color: Colors.whiteColor,
    fontFamily: 'NunitoSans-Regular',
    fontSize: 18,
    //opacity: 0
    //width: 120,
    backgroundColor: Colors.tabGrey,
    borderRadius: 4,
    height: 40,
    //justifyContent: 'center',
    textAlign: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    paddingVertical: 5,
  },
  inputIOS: {
    color: Colors.whiteColor,
    fontFamily: 'NunitoSans-Regular',
    fontSize: 18,
    //opacity: 0
    //width: 120,
    backgroundColor: Colors.tabGrey,
    borderRadius: 4,
    height: 40,
    //justifyContent: 'center',
    textAlign: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    paddingVertical: 5,
  },
});
const pickerStyle1 = StyleSheet.create({

  // RNPickerSelect Icon Style
  iconContainer: {
    top: 2,
    left: '75%',
    //flexDirection: 'row',
    elevation: 1,
    justifyContent: 'center',
    borderWidth: 0
  },

  // RNPickerSelect Style with 
  inputAndroid: {
    //backgroundColor: Colors.primaryColor,
    color: Colors.blackColor,
    fontFamily: 'NunitoSans-Regular',
    fontSize: 16,
    //opacity: 0
    //width: 120,
    backgroundColor: Colors.tabCyan,
    borderRadius: 4,
    height: 40,
    justifyContent: 'center',

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  inputIOS: {
    backgroundColor: '#fafafa',
    color: 'black',
    fontFamily: 'NunitoSans-Regular',
    fontSize: 10,

    paddingLeft: 12,
  },
});

export default Footer;
