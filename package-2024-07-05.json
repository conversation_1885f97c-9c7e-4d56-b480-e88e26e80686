{"name": "koodoo-merchant-web", "version": "0.1.0", "private": true, "dependencies": {"@babel/plugin-transform-modules-commonjs": "^7.19.6", "@react-native-async-storage/async-storage": "^1.15.5", "@react-native-community/datetimepicker": "^5.0.1", "@react-native-community/geolocation": "^2.0.2", "@react-native-community/netinfo": "^7.1.6", "@react-navigation/bottom-tabs": "^5.11.11", "@react-navigation/native": "^5.9.4", "@react-navigation/stack": "^5.14.5", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "aws-sdk": "^2.1051.0", "axios": "^0.21.1", "base64-arraybuffer": "^1.0.1", "bignumber.js": "^9.1.1", "bluebird": "^3.7.2", "file-saver": "^2.0.5", "firebase": "^8.8.0", "fusioncharts": "^3.18.0", "geofire-common": "^5.2.0", "hashids": "^2.3.0", "hashish": "^0.0.4", "idb": "^7.1.0", "jetifier": "^2.0.0", "jszip": "^3.7.1", "moment": "^2.29.1", "multiselect-react-dropdown": "^2.0.17", "nanoid": "^4.0.0", "pdfh5": "^1.4.2", "pullstate": "^1.22.1", "react": "^17.0.2", "react-beautiful-dnd": "^13.1.0", "react-csv": "^2.1.9", "react-data-table-component": "^7.5.3", "react-datepicker": "^4.5.0", "react-dom": "^17.0.2", "react-fusioncharts": "^3.1.2", "react-google-autocomplete": "^2.6.1", "react-horizontal-scrolling-menu": "^2.7.0", "react-multiple-select-dropdown-lite": "^2.0.6", "react-native-awesome-alerts": "^1.5.2", "react-native-base64": "^0.2.1", "react-native-calendars": "^1.1298.0", "react-native-canvas": "^0.1.38", "react-native-datepicker": "^1.7.2", "react-native-draggable": "^3.3.0", "react-native-draggable-flatlist": "^3.0.4", "react-native-dropdown-picker": "^5.4.6", "react-native-gesture-handler": "^1.10.3", "react-native-get-random-values": "^1.7.0", "react-native-modal-datetime-picker": "^13.0.0", "react-native-ping": "^1.2.6", "react-native-qrcode-scanner": "^1.5.4", "react-native-reanimated": "^2.3.1", "react-native-safe-area-context": "^3.2.0", "react-native-screens": "^3.4.0", "react-native-svg": "^12.1.1", "react-native-swipe-gestures": "^1.0.5", "react-native-table-component": "^1.2.2", "react-native-thermal-receipt-printer-image-qr": "^0.1.6", "react-native-vector-icons": "^8.1.0", "react-native-web": "^0.17.1", "react-native-webview": "^11.15.0", "react-places-autocomplete": "^7.3.0", "react-scripts": "4.0.3", "react-select": "^5.2.1", "react-switch": "^6.0.0", "react-timekeeper": "^2.1.3", "rn-fetch-blob": "^0.12.0", "signature_pad": "^4.1.3", "use-file-picker": "^1.4.1", "uuid": "^8.3.2", "w-json-stream": "^1.0.16", "web-vitals": "^1.0.1", "webpack": "^4.44.2", "xlsx": "^0.17.4"}, "scripts": {"start": "shx cp src/config/firebase-web.dev.json src/config/firebase-web.json && shx cp src/constant/dev.env.js src/constant/env.js && shx cp .dev.env .env && react-app-rewired start", "start-prod": "shx cp src/config/firebase-web.prod.json src/config/firebase-web.json && shx cp src/constant/prod.env.js src/constant/env.js && shx cp .prod.env .env && react-app-rewired start", "start-uat": "shx cp src/config/firebase-web.prod.json src/config/firebase-web.json && shx cp src/constant/uat.env.js src/constant/env.js && shx cp .uat.env .env && react-app-rewired start", "build": "shx cp src/config/firebase-web.dev.json src/config/firebase-web.json && shx cp src/constant/dev.env.js src/constant/env.js && shx cp .dev.env .env && npx env-cmd .env && react-app-rewired --max_old_space_size=4096 build", "build-prod": "shx cp src/config/firebase-web.prod.json src/config/firebase-web.json && shx cp src/constant/prod.env.js src/constant/env.js && shx cp .prod.env .env && npx env-cmd .env && react-app-rewired --max_old_space_size=4096 build", "build-uat": "shx cp src/config/firebase-web.prod.json src/config/firebase-web.json && shx cp src/constant/uat.env.js src/constant/env.js && shx cp .uat.env .env && npx env-cmd .env && react-app-rewired --max_old_space_size=4096 build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": [">0.2%", "not dead", "not op_mini all"]}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.16.5", "@babel/preset-react": "^7.18.6", "customize-cra": "^1.0.0", "html-loader": "^3.0.1", "react-app-rewired": "^2.2.1", "shx": "^0.3.3"}}