import React, { Component, useState, useEffect } from 'react'
import { StyleSheet, View, Text, ScrollView, Alert } from 'react-native'
import * as User from '../util/User';
import firestore from '@react-native-firebase/firestore';
import AppNavigator from '../navigation/AppNavigator';
import LoginScreen from './LoginScreen.js';
import HomeScreen from '../screen/HomeScreen';
// import { cos } from 'react-native-reanimated';
import { UserStore } from '../store/userStore';
import {
    listenToUserChanges,
    listenToLocationChanges, listenToSelectedOutletChanges, listenToSelectedOutletItemChanges, listenToCommonChangesMerchant, listenToSelectedOutletTagChanges, listenToSearchOutletTextChanges, listenToSearchOutletMerchantIdChanges, listenToSelectedOutletTableIdChanges, requestNotificationsPermission, getOutletById
} from '../util/common';
import * as geofire from 'geofire-common';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { CommonStore } from '../store/commonStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Draggable from 'react-native-draggable';
import Colors from '../constant/Colors';
import { useNavigation } from '@react-navigation/native'; import { DataStore } from '../store/dataStore';
// import messaging from '@react-native-firebase/messaging';
import { Collections } from '../constant/firebase';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import { parseMessages } from '../util/notifications';
import { NotificationStore } from '../store/notificationStore';
import { PROMOTION_TYPE, PROMOTION_TYPE_VARIATION } from '../constant/promotions';

var hide = true;

const MainScreen = props => {
    // const {
    //     navigation,
    // } = props;

    // useEffect(() => {
    //     setInterval(() => { checkLogin() }, 1000);

    //     checkLogin();
    // }, []);

    /////////////////////////////////////////////////////

    // const [showApp, setShowApp] = useState(true);
    const showApp = CommonStore.useState(s => s.showApp);

    const [cartIcon, setCartIcon] = useState(false);

    const firebaseUid = UserStore.useState(s => s.firebaseUid);

    const lat = CommonStore.useState(s => s.lat);
    const lng = CommonStore.useState(s => s.lng);

    const selectedOutlet = CommonStore.useState(s => s.selectedOutlet);
    const selectedOutletItem = CommonStore.useState(s => s.selectedOutletItem);

    const selectedOutletTag = CommonStore.useState(s => s.selectedOutletTag);

    const searchOutletText = CommonStore.useState(s => s.searchOutletText);
    const searchOutletMerchantId = CommonStore.useState(s => s.searchOutletMerchantId);

    const cartItems = CommonStore.useState(s => s.cartItems);

    const debugText = CommonStore.useState(s => s.debugText);

    const selectedOutletTableId = CommonStore.useState(s => s.selectedOutletTableId);

    const navigationObj = DataStore.useState(s => s.navigationObj);

    const beerDockets = CommonStore.useState(s => s.beerDockets);
    const selectedUserBeerDocket = CommonStore.useState(s => s.selectedUserBeerDocket);
    const beerDocketsRedemptions = CommonStore.useState(s => s.beerDocketsRedemptions);
    const beerDocketsRedemptionsBDDict = CommonStore.useState(s => s.beerDocketsRedemptionsBDDict);

    const currPage = CommonStore.useState(s => s.currPage);

    const nPromotionNotificationManual = NotificationStore.useState(s => s.nPromotionNotificationManual);
    const nPromotionNotificationAuto = NotificationStore.useState(s => s.nPromotionNotificationAuto);
    const nUserOrderCourierAction = NotificationStore.useState(s => s.nUserOrderCourierAction);

    const userGroups = UserStore.useState(s => s.userGroups);
    const email = UserStore.useState(s => s.email);

    const selectedOutletPromotions = CommonStore.useState(s => s.selectedOutletPromotions);
    const availablePromotions = CommonStore.useState(s => s.availablePromotions);

    const selectedOutletPointsRedeemPackages = CommonStore.useState(s => s.selectedOutletPointsRedeemPackages);
    const availablePointsRedeemPackages = CommonStore.useState(s => s.availablePointsRedeemPackages);

    const selectedOutletCRMTagsDict = CommonStore.useState(s => s.selectedOutletCRMTagsDict);
    const selectedOutletCRMUser = CommonStore.useState(s => s.selectedOutletCRMUser);

    /////////////////////////////////////////////////////

    const loadAsyncStorage = async () => {
        // await AsyncStorage.removeItem(`${firebaseUid}.cartItems`);

        const cartItemsRaw = await AsyncStorage.getItem(`${firebaseUid}.cartItems`);
        const cartOutletId = await AsyncStorage.getItem(`${firebaseUid}.cartOutletId`);

        if (cartItemsRaw) {
            DataStore.update(s => {
                s.cartItems = [
                    // ...s.cartItems,
                    ...JSON.parse(cartItemsRaw),
                ];
            });
        }

        if (cartOutletId) {
            CommonStore.update(s => {
                s.cartOutletId = cartOutletId;
            });
        }
    };

    useEffect(() => {
        if (firebaseUid !== '') {
            listenToUserChanges(firebaseUid);

            // loadAsyncStorage();
        }
    }, [firebaseUid]);

    useEffect(() => {
        if (lat !== null && lng !== null) {
            listenToLocationChanges(lat, lng);
        }
    }, [lat, lng]);

    useEffect(() => {
        if (selectedOutlet !== null &&
            email) {
            listenToSelectedOutletChanges(selectedOutlet, email);
        }
    }, [selectedOutlet, email]);

    useEffect(() => {
        if (selectedOutletItem !== null &&
            selectedOutletItem !== undefined &&
            selectedOutletItem.uniqueId) {
            listenToSelectedOutletItemChanges(selectedOutletItem);
        }
    }, [selectedOutletItem]);

    useEffect(() => {
        listenToCommonChangesMerchant();

        requestNotificationsPermission();

        // messaging().onMessage(async msg => {
        //     console.log('message from foreground!');
        //     console.log(msg);

        //     parseMessages(msg);
        // });
    }, []);

    useEffect(() => {
        if (selectedOutletTag !== null &&
            selectedOutletTag !== undefined &&
            selectedOutletTag.uniqueId) {
            listenToSelectedOutletTagChanges(selectedOutletTag);
        }
    }, [selectedOutletTag]);

    useEffect(() => {
        if (searchOutletText && searchOutletText.length > 0) {
            listenToSearchOutletTextChanges(searchOutletText);
        }
    }, [searchOutletText]);

    useEffect(() => {
        if (searchOutletMerchantId && searchOutletMerchantId.length > 0) {
            listenToSearchOutletMerchantIdChanges(searchOutletMerchantId);
        }
    }, [searchOutletMerchantId]);

    useEffect(() => {
        if (selectedOutletTableId && selectedOutletTableId.length > 0 && selectedOutlet && selectedOutlet.uniqueId && firebaseUid.length > 0) {
            listenToSelectedOutletTableIdChanges(firebaseUid, selectedOutletTableId, selectedOutlet.uniqueId);
        }
    }, [firebaseUid, selectedOutletTableId, selectedOutlet]);

    useEffect(() => {
        // combine merchant's beer docket with user's redeemed beer docket record, to merge user's action changes (redeemed mug, extended days, etc)

        var userBeerDocketsTemp = [];
        var selectedUserBeerDocketTemp = {};

        for (var i = 0; i < beerDockets.length; i++) {
            var record = {
                ...beerDockets[i],
                beerDocketId: beerDockets[i].uniqueId,
            };

            if (beerDocketsRedemptionsBDDict[record.beerDocketId]) {
                record = {
                    ...record,
                    ...beerDocketsRedemptionsBDDict[record.uniqueId], // extend the docket default data with user's own data
                    userBeerDocketId: beerDocketsRedemptionsBDDict[record.uniqueId].uniqueId,
                };
            }

            userBeerDocketsTemp.push(record);

            if (record.beerDocketId === selectedUserBeerDocket.beerDocketId) {
                selectedUserBeerDocketTemp = record;
            }
        };

        console.log('changed userBeerDockets!');
        console.log(userBeerDocketsTemp);

        CommonStore.update(s => {
            s.userBeerDockets = userBeerDocketsTemp;

            if (selectedUserBeerDocket && selectedUserBeerDocket.beerDocketId) {
                s.selectedUserBeerDocket = selectedUserBeerDocketTemp;
            }
        });
    }, [beerDockets, beerDocketsRedemptionsBDDict]);

    /////////////////////////////////////////////////////

    useEffect(() => {
        if (cartItems.length > 0) {
            setCartIcon(true);
        }
        else {
            setCartIcon(false);
        }
    }, [cartItems.length]);

    // useEffect(() => {
    //     if (firebaseUid.length > 0) {
    //         messaging().getToken()
    //             .then(async tokenFcm => {
    //                 await AsyncStorage.setItem('tokenFcm', tokenFcm);

    //                 await updateTokenFcm();
    //             });

    //         messaging().onTokenRefresh(async tokenFcm => {
    //             await AsyncStorage.setItem('tokenFcm', tokenFcm);

    //             await updateTokenFcm();
    //         });
    //     }
    // }, [firebaseUid]);

    ////////////////////////////////////////////////////////

    // from notifications

    useEffect(() => {
        var promotionOutletId = '';

        if (nPromotionNotificationManual && nPromotionNotificationManual.type) {
            // if (nUserOrder.orderType === ORDER_TYPE.DINEIN) {
            //     navigation.navigate('Order');
            // }
            // else {
            //     navigation.navigate('Takeaway');
            // }

            promotionOutletId = nPromotionNotificationManual.outletId;
        }
        else if (nPromotionNotificationAuto && nPromotionNotificationAuto.type) {
            promotionOutletId = nPromotionNotificationAuto.outletId;
        }

        if (promotionOutletId) {
            redirectToPromotionOutlet(promotionOutletId);
        }
    }, [nPromotionNotificationManual, nPromotionNotificationAuto]);

    const redirectToPromotionOutlet = async (outletId) => {
        const outletSnapshot = await firestore()
            .collection(Collections.Outlet)
            .where('uniqueId', '==', outletId)
            .limit(1)
            .get();

        var outlet = null;
        if (!outletSnapshot.empty) {
            outlet = outletSnapshot.docs[0].data();
        }

        CommonStore.update(s => {
            s.selectedOutlet = outlet;
        }, () => {
            navigationObj.navigate('Outlet', { outletData: outlet });
        });
    };

    useEffect(() => {
        if (nUserOrderCourierAction && nUserOrderCourierAction.type) {
            redirectToOrderHistoryDetails(nUserOrderCourierAction.orderId);
        }
    }, [nUserOrderCourierAction]);

    const redirectToOrderHistoryDetails = async (orderId) => {
        const userOrderSnapshot = await firestore()
            .collection(Collections.UserOrder)
            .where('uniqueId', '==', orderId)
            .limit(1)
            .get();

        var userOrder = null;
        if (!userOrderSnapshot.empty) {
            userOrder = userOrderSnapshot.docs[0].data();
        }

        CommonStore.update(s => {
            s.selectedUserOrder = userOrder;
        });

        navigationObj.navigate("OrderHistoryDetail", { orderId: userOrder.uniqueId });
    };

    ////////////////////////////////////////////////////////

    const updateTokenFcm = async () => {
        const tokenFcm = await AsyncStorage.getItem('tokenFcm');

        if (tokenFcm) {
            const body = {
                tokenFcm: tokenFcm,
                userId: firebaseUid,
            };

            ApiClient.POST(API.updateTokenFcm, body).then((result) => {
                console.log('updated token fcm');
            });
        }
    };

    /////////////////////////////////////////////////////////

    useEffect(() => {
        var availablePromotionsTemp = [];
        var availablePointsRedeemPackagesTemp = [];

        for (var i = 0; i < selectedOutletPromotions.length; i++) {
            var isValid = false;

            if (userGroups.includes(selectedOutletPromotions[i].targetUserGroup)) {
                isValid = true;
            }

            if (selectedOutletCRMTagsDict[selectedOutletPromotions[i].targetUserGroup]) {
                const currCrmUserTag = selectedOutletCRMTagsDict[selectedOutletPromotions[i].targetUserGroup];

                if (currCrmUserTag.emailList.includes(email)) {
                    // means got

                    isValid = true;
                }
            }

            if (isValid) {
                availablePromotionsTemp.push(selectedOutletPromotions[i]);
            }
        }

        for (var i = 0; i < selectedOutletPointsRedeemPackages.length; i++) {
            var isValid = false;

            if (userGroups.includes(selectedOutletPointsRedeemPackages[i].targetUserGroup)) {
                isValid = true;
            }

            if (selectedOutletCRMTagsDict[selectedOutletPointsRedeemPackages[i].targetUserGroup]) {
                const currCrmUserTag = selectedOutletCRMTagsDict[selectedOutletPointsRedeemPackages[i].targetUserGroup];

                if (currCrmUserTag.emailList.includes(email)) {
                    // means got

                    isValid = true;
                }
            }

            //////////////////////////////////

            if (selectedOutletCRMUser && selectedOutletCRMUser.pointsRedeemPackageDisableDict) {
                if (selectedOutletCRMUser.pointsRedeemPackageDisableDict[selectedOutletPointsRedeemPackages[i].uniqueId]) {
                    isValid = false;
                }
            }

            //////////////////////////////////

            if (isValid) {
                availablePointsRedeemPackagesTemp.push(selectedOutletPointsRedeemPackages[i]);
            }
        }

        CommonStore.update(s => {
            s.availablePromotions = availablePromotionsTemp;
            s.availablePointsRedeemPackages = availablePointsRedeemPackagesTemp;
        });
    }, [
        selectedOutletPromotions,
        selectedOutletPointsRedeemPackages,
        userGroups,
        email,

        selectedOutletCRMTagsDict,
        selectedOutletCRMUser,
    ]);

    useEffect(() => {
        var overrideItemPriceSkuDictTemp = {};
        var amountOffItemSkuDictTemp = {};
        var percentageOffItemSkuDictTemp = {};

        var overrideCategoryPriceNameDictTemp = {};
        var amountOffCategoryNameDictTemp = {};
        var percentageOffCategoryNameDictTemp = {};

        for (var i = 0; i < availablePromotions.length; i++) {
            if (availablePromotions[i].promotionType === PROMOTION_TYPE.OVERRIDE_EXISTING_PRICE) {
                for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
                    const criteria = availablePromotions[i].criteriaList[j];

                    if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
                        for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                            overrideItemPriceSkuDictTemp[criteria.variationItemsSku[k]] = criteria.priceBeforeTax;
                        }
                    }
                    else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
                        for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                            overrideCategoryPriceNameDictTemp[criteria.variationItemsSku[k]] = criteria.priceBeforeTax;
                        }
                    }
                }
            }
            else if (availablePromotions[i].promotionType === PROMOTION_TYPE.TAKE_AMOUNT_OFF) {
                for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
                    const criteria = availablePromotions[i].criteriaList[j];

                    if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
                        for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                            amountOffItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                                amountOff: criteria.amountOff,
                                maxQuantity: criteria.maxQuantity,
                                minQuantity: criteria.minQuantity,

                                quantityMin: criteria.quantityMin,
                                quantityMax: criteria.quantityMax,
                                priceMin: criteria.priceMin,
                                priceMax: criteria.priceMax,
                            };
                        }
                    }
                    else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
                        for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                            amountOffCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
                                amountOff: criteria.amountOff,
                                maxQuantity: criteria.maxQuantity,
                                minQuantity: criteria.minQuantity,

                                quantityMin: criteria.quantityMin,
                                quantityMax: criteria.quantityMax,
                                priceMin: criteria.priceMin,
                                priceMax: criteria.priceMax,
                            };
                        }
                    }
                }
            }
            else if (availablePromotions[i].promotionType === PROMOTION_TYPE.TAKE_PERCENTAGE_OFF) {
                for (var j = 0; j < availablePromotions[i].criteriaList.length; j++) {
                    const criteria = availablePromotions[i].criteriaList[j];

                    if (criteria.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
                        for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                            percentageOffItemSkuDictTemp[criteria.variationItemsSku[k]] = {
                                percentageOff: criteria.percentageOff,
                                maxQuantity: criteria.maxQuantity,
                                minQuantity: criteria.minQuantity,

                                quantityMin: criteria.quantityMin,
                                quantityMax: criteria.quantityMax,
                                priceMin: criteria.priceMin,
                                priceMax: criteria.priceMax,
                            };
                        }
                    }
                    else if (criteria.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
                        for (var k = 0; k < criteria.variationItemsSku.length; k++) {
                            percentageOffCategoryNameDictTemp[criteria.variationItemsSku[k]] = {
                                percentageOff: criteria.percentageOff,
                                maxQuantity: criteria.maxQuantity,
                                minQuantity: criteria.minQuantity,

                                quantityMin: criteria.quantityMin,
                                quantityMax: criteria.quantityMax,
                                priceMin: criteria.priceMin,
                                priceMax: criteria.priceMax,
                            };
                        }
                    }
                }
            }
        }

        CommonStore.update(s => {
            s.overrideItemPriceSkuDict = overrideItemPriceSkuDictTemp;
            s.amountOffItemSkuDict = amountOffItemSkuDictTemp;
            s.percentageOffItemSkuDict = percentageOffItemSkuDictTemp;

            s.overrideCategoryPriceNameDict = overrideCategoryPriceNameDictTemp;
            s.amountOffCategoryNameDict = amountOffCategoryNameDictTemp;
            s.percentageOffCategoryNameDict = percentageOffCategoryNameDictTemp;
        });
    }, [availablePromotions]);

    useEffect(() => {
        var pointsRedeemItemSkuDict = {};
        var pointsRedeemCategoryNameDict = {};

        for (var i = 0; i < availablePointsRedeemPackages.length; i++) {
            const pointsRedeemPackage = availablePointsRedeemPackages[i];

            if (pointsRedeemPackage.variation === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
                for (var k = 0; k < pointsRedeemPackage.variationItemsSku.length; k++) {
                    pointsRedeemItemSkuDict[pointsRedeemPackage.variationItemsSku[k]] = {
                        packageId: pointsRedeemPackage.uniqueId,
                        limitRedemptionPerUser: pointsRedeemPackage.limitRedemptionPerUser,
                        conversionCurrency: pointsRedeemPackage.conversionCurrency,
                        conversionCurrencyTo: pointsRedeemPackage.conversionCurrencyTo,
                        conversionPointsFrom: pointsRedeemPackage.conversionPointsFrom,
                    };
                }
            }
            else if (pointsRedeemPackage.variation === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
                for (var k = 0; k < pointsRedeemPackage.variationItemsSku.length; k++) {
                    pointsRedeemCategoryNameDict[pointsRedeemPackage.variationItemsSku[k]] = {
                        packageId: pointsRedeemPackage.uniqueId,
                        limitRedemptionPerUser: pointsRedeemPackage.limitRedemptionPerUser,
                        conversionCurrency: pointsRedeemPackage.conversionCurrency,
                        conversionCurrencyTo: pointsRedeemPackage.conversionCurrencyTo,
                        conversionPointsFrom: pointsRedeemPackage.conversionPointsFrom,
                    };
                }
            }
        }

        CommonStore.update(s => {
            s.pointsRedeemItemSkuDict = pointsRedeemItemSkuDict;
            s.pointsRedeemCategoryNameDict = pointsRedeemCategoryNameDict;
        });
    }, [availablePointsRedeemPackages]);

    /////////////////////////////////////////////////////////

    // function here

    // const checkLogin = () => {
    //     setShowApp(User.islogined());
    // }
    const checkLogin = () => {
        CommonStore.update(s => {
            s.showApp = User.islogined();
        })
        console.log("CHECK LOGIN")
        linkTo && linkTo(`${prefix}/dashboard`);
    }

    const onCartClicked = () => {
        if (cartItems.length > 0) {
            navigationObj && navigationObj.navigate("Cart", { test: null, outletData: selectedOutlet });
        } else {
            window.confirm("Info", "No item in your cart at the moment", [
                { text: "OK", onPress: () => { } }
            ],
                { cancelable: false })
        }
    };

    // function end

    CommonStore.update(s => {
        s.checkLogin = checkLogin;
        console.log(s.checkLogin)
    })

    return (
        <>
            {
                !showApp ? <LoginScreen /> :
                    <AppNavigator />
            }

            {(showApp && cartIcon && currPage !== 'Cart') ?
                <Draggable
                    shouldReverse={false}
                    renderSize={100}
                    // renderColor={Colors.secondaryColor}
                    isCircle
                    x={270}
                    y={470}
                    // onShortPressRelease={() => { goToCart(), cartCount() }}
                    onShortPressRelease={onCartClicked}
                >
                    <View style={{
                        width: 60,
                        height: 60,
                        backgroundColor: Colors.secondaryColor,
                        borderRadius: 30,
                        justifyContent: "center",
                        // backgroundColor: 'red',
                        shadowColor: '#000',
                        shadowOffset: {
                            width: 0,
                            height: 1,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 2.22,
                        elevation: 5,
                    }}>
                        <View style={{ alignSelf: "center" }}>
                            <Ionicons name="cart-outline" size={42} color={Colors.mainTxtColor} />
                        </View>
                        <View style={styles.cartCount}>
                            <Text style={{
                                color: Colors.whiteColor,
                                fontSize: 14,
                                fontFamily: "NunitoSans-Bold"
                            }}>{cartItems.length}</Text>
                        </View>
                    </View>
                </Draggable>
                : <></>
            }

            {/* <ScrollView style={{
                width: '100%',
                height: '20%',
                position: 'absolute',
                top: 0,
                backgroundColor: 'black',
            }}>
                <Text style={{
                    color: 'yellow',
                }}>
                    {debugText}
                </Text>
            </ScrollView> */}

        </>

        // <AppNavigator />
        // <LoginScreen />
    )
}

const styles = StyleSheet.create({
    cartCount: {
        position: 'absolute',
        top: -12,
        right: -10,
        backgroundColor: Colors.primaryColor,
        width: 30,
        height: 30,
        borderRadius: 30 / 2,
        alignContent: 'center',
        alignItems: 'center',
        justifyContent: 'center',
    },
});

export default MainScreen