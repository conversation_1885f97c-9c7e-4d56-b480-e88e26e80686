import { openDB } from 'idb';

const dbPromise = openDB('offline-db', 1, {
    upgrade(db) {
        db.createObjectStore('offline');
        // db.createObjectStore('@commonStore');
        // db.createObjectStore('@dataStore');
        // db.createObjectStore('@userStore');
        // db.createObjectStore('@merchantStore');
        // db.createObjectStore('@outletStore');
    },
});

export async function idbGet(key) {
    return (await dbPromise).get('offline', key);
};

export async function idbSet(key, val) {
    return (await dbPromise).put('offline', val, key);
};

export async function idbDel(key) {
    return (await dbPromise).delete('offline', key);
};

export async function idbClear() {
    return (await dbPromise).clear('offline');
};

export async function idbKeys() {
    return (await dbPromise).getAllKeys('offline');
};