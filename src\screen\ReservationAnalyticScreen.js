import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    FlatList,
    TouchableOpacity,
    Dimensions,
    Alert,
    Button,
    Modal,
    TextInput,
    KeyboardAvoidingView,
    ActivityIndicator,
    useWindowDimensions,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Ionicons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Plus from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Feather from 'react-native-vector-icons/Feather';
import PlusSvg from '../assets/svg/Plus.svg';
import DropDownPicker from 'react-native-dropdown-picker';
import Close from 'react-native-vector-icons/AntDesign';
import ApiClient from '../util/ApiClient';
import AntDesign from 'react-native-vector-icons/AntDesign';
import API from '../constant/API';
import * as User from '../util/User';
import * as Cart from '../util/Cart';
// import Dash from 'react-native-dash';
import moment from 'moment';
import Styles from '../constant/Styles';
// import QRCode from 'react-native-qrcode-svg';
import EIcon from 'react-native-vector-icons/Entypo';
// import CurrencyInput, { formatValue } from 'react-currency-input-field';
import AIcon from 'react-native-vector-icons/AntDesign';
import OrderModal from './components/OrderModal';
// import Barcode from 'react-native-barcode-builder';
// import molpay from 'molpay-mobile-xdk-reactnative-beta';
// import {
//     getTransformForScreenInsideNavigation,
//     isTablet
// } from '../util/common';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { CommonStore } from '../store/commonStore';
import { UserStore } from '../store/userStore';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
// import Swipeable from 'react-native-gesture-handler/Swipeable';
import {
    DELAY_LONG_PRESS_TIME,
    MODE_ADD_CART,
    OFFLINE_BILL_TYPE,
    OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
    OFFLINE_PAYMENT_METHOD_TYPE,
    ORDER_TYPE,
    USER_ORDER_PRIORITY,
    USER_ORDER_STATUS,
    EXPAND_TAB_TYPE,
    USER_RESERVATION_STATUS,
} from '../constant/common';
// import { useKeyboard } from '../hooks';
// import RNPickerSelect from 'react-native-picker-select';
import {
    getObjectDiff,
    isObjectEqual,
    listenToCurrOutletIdChangesWaiter,
    naturalCompare,
} from '../util/common';
import { qrUrl } from '../constant/env';
import { printUserOrder } from '../util/printer';
import { Collections } from '../constant/firebase';
// import firestore from '@react-native-firebase/firestore';
// import CheckBox from 'react-native-check-box';
import AsyncImage from '../components/asyncImage';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { customAlphabet } from 'nanoid';
// const alphabet = '**********';
// const nanoid = customAlphabet(alphabet, 12);
// import CheckBox from '@react-native-community/checkbox';
// import { DIMENTIONS } from 'react-native-numeric-input';
import { color } from 'react-native-reanimated';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { ReactComponent as GCalendar } from "../assets/svg/GCalendar.svg";
import FusionCharts from "react-fusioncharts";
import {
    CHART_DATA,
    CHART_TYPE,
    FS_LIBRARY_PATH,
    CHART_Y_AXIS_DROPDOWN_LIST,
    CHART_FIELD_COMPARE_DROPDOWN_LIST,
    CHART_FIELD_NAME_DROPDOWN_LIST,
    CHART_FIELD_TYPE,
    CHART_FIELD_COMPARE_DICT,
    CHART_PERIOD,
} from '../constant/chart';
import {
    filterChartItems,
    getDataForChartDashboardTodaySales,
    getDataForReservationGuestStats,
    getDataForSalesLineChart,
} from '../util/chart';
import Footer from './Footer';
import { useFocusEffect } from '@react-navigation/native';
// import UserIdleWrapper from '../components/userIdleWrapper';
import { ReactComponent as Inventory } from "../assets/svg/InventoryB.svg";
import { ReactComponent as Accountcancel } from "../assets/svg/Accountcancel.svg";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import { sliceUnicodeStringV2WithDots } from "../util/common";
import MultiSelect from "react-multiple-select-dropdown-lite";
import "../constant/styles.css";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "../constant/datePicker.css";

const ReservationAnalyticScreen = (props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    const [switchMerchant, setSwitchMerchant] = useState(false);

    // Reservation or Loyalty page useState
    const [isReservationPage, setIsReservationPage] = useState(true);

    // traffic drop down
    const [
        selectedChartDropdownValueLineChart,
        setSelectedChartDropdownValueLineChart,
    ] = useState(
        CHART_Y_AXIS_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][0].value,
    );

    // Reservation page useState
    const [guestNum, setGuestNum] = useState('');
    const [estimatedRevenue, setEstimatedRevenue] = useState('');
    const [reservationsNum, setReservationsNum] = useState('');
    const [walksInNum, setWalksInNum] = useState('');
    const [cancellationsNum, setCancellationsNum] = useState('');
    const [noShowsNum, setNoShowsNum] = useState('');

    // traffic table data
    const [salesLineChart, setSalesLineChart] = useState({});

    const eventsChart = {
        dataPlotClick: (e, item) => {
            // console.log('test data plot');
        },
    };

    // search bar useState
    const [search, setSearch] = useState('');

    // Date time picker useState
    const [showDateTimeStartPicker, setShowDateTimeStartPicker] = useState(false);
    const [showDateTimeEndPicker, setShowDateTimeEndPicker] = useState(false);

    // Date time useState
    const [revStartDate, setRevStartDate] = useState(moment().startOf('month').startOf('day'));
    const [revEndDate, setRevEndDate] = useState(moment().endOf('month').endOf('day'));

    const [reservationGuestStatsChart, setReservationGuestStatsChart] = useState({});

    const historyStartDate = CommonStore.useState(s => s.historyStartDate);
    const historyEndDate = CommonStore.useState(s => s.historyEndDate);

    ////////////////////////////////////////////////////////////////////

    // Navigation bar
    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );
    const userName = UserStore.useState((s) => s.name);
    const Loyaltycampaign = OutletStore.useState(s => s.loyaltyCampaigns)

    const userReservations = OutletStore.useState(s => s.userReservations);
    const crmUsers = OutletStore.useState(s => s.crmUsers);

    const allOutlets = MerchantStore.useState((s) => s.allOutlets);
    const allOutletsUserOrdersDone = OutletStore.useState(
        (s) => s.allOutletsUserOrdersDone,
    );
    const [
        appliedChartFilterQueriesLineChart,
        setAppliedChartFilterQueriesLineChart,
    ] = useState([]);
    const [salesLineChartPeriod, setSalesLineChartPeriod] = useState(
        CHART_PERIOD.THIS_MONTH,
    );
    const [expandLineSelection, setExpandLineSelection] = useState(false);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    const currOutletId = MerchantStore.useState((s) => s.currOutletId);
    ////////////////////////////////////////////////////////////////////

    useEffect(() => {
        const allOutletsUserOrdersDoneFilteredLineChart =
            allOutletsUserOrdersDone.filter((item) =>
                filterChartItems(item, appliedChartFilterQueriesLineChart),
            );

        const lineresult = getDataForSalesLineChart(
            allOutlets,
            allOutletsUserOrdersDoneFilteredLineChart,
            salesLineChartPeriod,
            selectedChartDropdownValueLineChart,
            // revStartDate,
            // revEndDate,

            historyStartDate,
            historyEndDate,
        );

        if (lineresult) {
            setSalesLineChart(lineresult.chartData);
            setExpandLineSelection(false);
        }
        else {
            setSalesLineChart({});
            setExpandLineSelection(false);
        }
        const result = getDataForReservationGuestStats(
            userReservations,
            crmUsers,

            // allOutlets,
            // allOutletsUserOrdersDoneFilteredLineChart,
            // salesLineChartPeriod,
            // selectedChartDropdownValueLineChart,

            // revStartDate,
            // revEndDate,

            historyStartDate,
            historyEndDate,
        );

        if (result) {
            // setSalesLineChart(result.chartData);
            // setExpandLineSelection(false);

            // var testResult = result.data.reduce((accum, row) => accum + parseInt(row.value), 0) > 0;

            setReservationGuestStatsChart(result);
        }
        else {
            setReservationGuestStatsChart({});
        }

        /////////////////////////////////////////////////////

        var guestNumTemp = 0;
        var estimatedRevenueTemp = 0;
        var reservationsNumTemp = 0;
        var walksInNumTemp = 0;
        var cancellationsNumTemp = 0;
        var noShowsNumTemp = 0;
        for (var i = 0; i < userReservations.length; i++) {
            reservationsNumTemp++;

            guestNumTemp += userReservations[i].pax ? userReservations[i].pax : 0;
            cancellationsNumTemp += userReservations[i].status === USER_RESERVATION_STATUS.CANCELED ? 1 : 0;
            noShowsNumTemp += userReservations[i].status === USER_RESERVATION_STATUS.NO_SHOW ? 1 : 0;
        };

        setReservationsNum(reservationsNumTemp);
        setGuestNum(guestNumTemp);
        setCancellationsNum(cancellationsNumTemp);
        setNoShowsNum(noShowsNumTemp);

        /////////////////////////////////////////////////////
    }, [
        allOutlets,
        allOutletsUserOrdersDone,
        // salesLineChartPeriod,
        // salesBarChartPeriod,
        // selectedChartDropdownValueBarChart,
        selectedChartDropdownValueLineChart,
        // appliedChartFilterQueriesBarChart,
        // appliedChartFilterQueriesLineChart,

        userReservations,

        // revStartDate,
        // revEndDate,

        historyStartDate,
        historyEndDate,
    ]);

    const isLoading = CommonStore.useState((s) => s.isLoading);

    const currOutletShiftStatus = OutletStore.useState(
        (s) => s.currOutletShiftStatus
    );

    var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
        label: sliceUnicodeStringV2WithDots(outlet.name, 20),
        value: outlet.uniqueId,
    }));

    // useEffect(() => {
    //     CommonStore.update((s) => {
    //         s.outletSelectDropdownView = () => {
    //             return (
    //                 <View
    //                     style={{
    //                         flexDirection: "row",
    //                         alignItems: "center",
    //                         borderRadius: 8,
    //                         width: 200,
    //                         backgroundColor: "white",
    //                     }}
    //                 >
    //                     {currOutletId.length > 0 &&
    //                         allOutlets.find((item) => item.uniqueId === currOutletId) ? (
    //                         <MultiSelect
    //                             clearable={false}
    //                             singleSelect={true}
    //                             defaultValue={currOutletId}
    //                             placeholder={"Choose Outlet"}
    //                             onChange={(value) => {
    //                                 if (value) { // if choose the same option again, value = ""
    //                                     MerchantStore.update((s) => {
    //                                         s.currOutletId = value;
    //                                         s.currOutlet =
    //                                             allOutlets.find(
    //                                                 (outlet) => outlet.uniqueId === value
    //                                             ) || {};
    //                                     });
    //                                 }

    //                                 CommonStore.update((s) => {
    //                                     s.shiftClosedModal = false;
    //                                 });
    //                             }}
    //                             options={targetOutletDropdownListTemp}
    //                             className="msl-varsHEADER"
    //                         />
    //                     ) : (
    //                         <ActivityIndicator size={"small"} color={Colors.whiteColor} />
    //                     )}

    //                     {/* <Select

    //           placeholder={"Choose Outlet"}
    //           onChange={(items) => {
    //             setSelectedOutletList(items);
    //           }}
    //           options={outletDropdownList}
    //           isMulti
    //         /> */}
    //                 </View>
    //             );
    //         };
    //     });
    // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

    //Header
    navigation.setOptions({
        headerLeft: () => (
            <View
                style={[
                    styles.headerLeftStyle,
                    {
                        width: windowWidth * 0.17,
                    },
                ]}
            >
                <img src={headerLogo} width={124} height={26} />
                {/* <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require("../assets/image/logo.png")}
        /> */}
            </View>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        justifyContent: "center",
                        alignItems: "center",
                        // marginRight: Platform.OS === "ios" ? "27%" : 0,
                        // bottom: switchMerchant ? "2%" : 0,
                        //width:  "55%",
                    },
                    Dimensions.get("screen").width <= 768
                        ? { right: Dimensions.get("screen").width * 0.12 }
                        : {},
                ]}
            >
                <Text
                    style={{
                        fontSize: 24,
                        // lineHeight: 25,
                        textAlign: "center",
                        fontFamily: "NunitoSans-Bold",
                        color: Colors.whiteColor,
                        opacity: 1,
                    }}
                >
                    Analytics
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                {/* {console.log("edward test")} */}
                {/* {console.log(outletSelectDropdownView)} */}
                {outletSelectDropdownView && outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: "white",
                        width: 0.5,
                        height: Dimensions.get("screen").height * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                        // borderWidth: 1
                    }}
                ></View>
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === "admin") {
                            navigation.navigate("General Settings - KooDoo Manager")
                        }
                    }}
                    style={{ flexDirection: "row", alignItems: "center" }}
                >
                    <Text
                        style={{
                            fontFamily: "NunitoSans-SemiBold",
                            fontSize: 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            //backgroundColor: "red",
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: "white",
                        }}
                    >
                        <img
                            src={personicon}
                            width={windowHeight * 0.035}
                            height={windowHeight * 0.035}
                        />
                        {/* <Image
              style={{
                width: windowHeight * 0.05,
              height: windowHeight * 0.05,
                alignSelf: "center",
              }}
              source={require("../assets/image/profile-pic.jpg")}
            /> */}
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    const renderloyalty = ({ item }) => {
        return (
            <View style={styles.tableContentView}>
                <View>
                    <Text style={styles.tableContentTextFirst}>
                        Active
                    </Text>
                    <Text style={styles.tableContentTextSecond}>
                        {item.campaignName}
                    </Text>
                    <Text style={styles.tableContentTextThird}>
                        {item.campaignDescription}
                    </Text>
                </View>

                <View style={{ justifyContent: 'center' }}>
                    <Text style={styles.tableContentTextBig}>
                        0
                    </Text>
                    <Text style={styles.tableContentTextSmall}>
                        Total Delivered
                    </Text>
                </View>
                <View style={{ justifyContent: 'center' }}>
                    <Text style={styles.tableContentTextBig}>
                        0
                    </Text>
                    <Text style={styles.tableContentTextSmall}>
                        Total Visits
                    </Text>
                </View>
                <View style={{ justifyContent: 'center' }}>
                    <Text style={styles.tableContentTextBig}>
                        0%
                    </Text>
                    <Text style={styles.tableContentTextSmall}>
                        Visit Rate
                    </Text>
                </View>
                <View style={{ justifyContent: 'center' }}>
                    <Text style={styles.tableContentTextBig}>
                        0
                    </Text>
                    <Text style={styles.tableContentTextSmall}>
                        MYR Revenue
                    </Text>
                </View>
            </View>
        )
    }

    return (
        // <UserIdleWrapper disabled={!isMounted}>
        <View
            style={[
                styles.container,
                // !isTablet()
                //     ? {
                //         transform: [{ scaleX: 1 }, { scaleY: 1 }],
                //     }
                //     : {},
                // {
                //     ...getTransformForScreenInsideNavigation(),
                // }
            ]}>

            {/* Sidebar */}
            <View
                style={[
                    styles.sidebar,
                    // !isTablet()
                    //     ? {
                    //         width: windowWidth * 0.08,
                    //     }
                    //     : {},
                    // switchMerchant
                    //     ? {
                    //         // width: '10%'
                    //     }
                    //     : {},
                    // {
                    //     width: windowWidth * 0.08,
                    // }
                ]}>
                <SideBar
                    navigation={props.navigation}
                    selectedTab={1}
                    expandOperation
                />
            </View>

            <ScrollView
                scrollEnabled={switchMerchant}
                showsVerticalScrollIndicator={false}
                style={{}}
                contentContainerStyle={{
                    paddingBottom: windowHeight * 0.1,
                    backgroundColor: Colors.highlightColor,
                }}>
                {/* Modal start */}
                <DateTimePickerModal
                    isVisible={showDateTimeStartPicker}
                    mode={'date'}
                    onConfirm={(text) => {
                        // setRevStartDate(moment(text).startOf('day'));

                        CommonStore.update(s => {
                            s.historyStartDate = moment(text).startOf('day').valueOf();
                        });

                        setShowDateTimeStartPicker(false);
                        setSalesLineChartPeriod(CHART_PERIOD.NONE);
                    }}
                    onCancel={() => {
                        setShowDateTimeStartPicker(false);
                    }}
                    maximumDate={moment(historyEndDate).toDate()}
                />

                <DateTimePickerModal
                    isVisible={showDateTimeEndPicker}
                    mode={'date'}
                    onConfirm={(text) => {
                        // setRevEndDate(moment(text).endOf('day'));

                        CommonStore.update(s => {
                            s.historyEndDate = moment(text).endOf('day').valueOf();
                        });

                        setShowDateTimeEndPicker(false);
                        setSalesLineChartPeriod(CHART_PERIOD.NONE);
                    }}
                    onCancel={() => {
                        setShowDateTimeEndPicker(false);
                    }}
                    minimumDate={moment(historyStartDate).toDate()}
                />
                {/* Modal End */}

                <View>
                    {/* Top view */}
                    <View style={[
                        styles.topBar,
                        {
                            height: windowHeight * 0.07,
                            width: windowWidth * 0.9,
                        },
                        { paddingHorizontal: 25, paddingVertical: 5, marginVertical: 10, flexDirection: 'row', justifyContent: 'space-between' }
                    ]}>
                        {/* <TouchableOpacity
            style={[
              styles.topBarButton,
              isReservationPage ? {} : { backgroundColor: Colors.whiteColor },
            ]}
            onPress={() => setIsReservationPage(true)}
          > */}
                        <View style={{
                            //backgroundColor: Colors.whiteColor,
                            //width: windowWidth * 0.24,
                            justifyContent: 'center',
                        }}>
                            <Text
                                style={{
                                    //fontSize: 30,
                                    fontSize: switchMerchant ? 20 : 26,
                                    fontFamily: 'NunitoSans-Bold',
                                }}>
                                {/* {orderList.length} */}
                                Reservation Analytics
                            </Text>
                        </View>
                        {/* </TouchableOpacity> */}

                        {/* <TouchableOpacity
            style={[
              styles.topBarButton,
              isReservationPage ? { backgroundColor: Colors.whiteColor } : {},
            ]}
            onPress={() => setIsReservationPage(false)}
          >
            <Text
              style={{
                textAlign: 'center',
              }}>
              Loyalty
            </Text>
          </TouchableOpacity> */}

                        {/* Search Bar */}
                        {/* Show or hide between the Page */}
                        {!isReservationPage ?
                            <View
                                style={{
                                    margin: 3,
                                    backgroundColor: 'white',
                                    borderRadius: 7,
                                    flexDirection: 'row',
                                    alignContent: 'center',
                                    alignItems: 'center',
                                    shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 3,
                                    borderWidth: 1,
                                    borderColor: '#E5E5E5',
                                }}>
                                <Icon
                                    name="search"
                                    size={13}
                                    color={Colors.primaryColor}
                                    style={{ marginLeft: 15 }}
                                />
                                <TextInput
                                    style={[{
                                        fontSize: 13,
                                        fontFamily: 'NunitoSans-Regular',
                                        paddingLeft: 5,
                                        height: windowHeight * 0.18,
                                    },
                                    switchMerchant ? { width: 220 } : { width: (!switchMerchant && windowWidth < 1000 && windowHeight <= 610) ? windowWidth * 0.4 : windowWidth * 0.45 },
                                    ]}
                                    clearButtonMode="while-editing"
                                    placeholder=" Search"
                                    placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                    onChangeText={(text) => {
                                        // setSearch(text.trim());
                                        setSearch(text);
                                    }}
                                    value={search}
                                />
                            </View>
                            : null}

                        {/* Date time picker */}
                        <View
                            style={{
                                flexDirection: 'row',
                                marginVertical: switchMerchant ? 3 : 5,
                                //marginLeft: 'auto',
                                display: 'none'
                            }}>
                            <View
                                style={{
                                    paddingHorizontal: 15,
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    borderRadius: 10,
                                    paddingVertical: 5,
                                    justifyContent: 'center',
                                    backgroundColor: Colors.whiteColor,
                                    shadowOpacity: 0,
                                    shadowColor: '#000',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: 1,
                                }}>
                                <View
                                    style={{ alignSelf: 'center', marginRight: 5 }}
                                    onPress={() => {
                                        // setState({
                                        //     pickerMode: 'date',
                                        //     showDateTimePicker: true,
                                        // });
                                    }}>
                                    {/* <EvilIcons name="calendar" size={25} color={Colors.primaryColor} /> */}
                                    <GCalendar
                                        width={switchMerchant ? 10 : 20}
                                        height={switchMerchant ? 10 : 20}
                                    />
                                </View>
                                <DatePicker
                                    selected={moment(historyStartDate).toDate()}
                                    onChange={(date) => {
                                        // setRev_date(moment(text).startOf('day'));
                                        CommonStore.update(s => {
                                            s.historyStartDate = moment(date).startOf('day');
                                        });

                                        setSalesLineChartPeriod(CHART_PERIOD.NONE);
                                    }}
                                    maxDate={moment(historyEndDate).toDate()}
                                />

                                <Text
                                    style={
                                        switchMerchant
                                            ? {
                                                fontSize: 10,
                                                fontFamily: "NunitoSans-Regular",
                                            }
                                            : { fontFamily: "NunitoSans-Regular" }
                                    }
                                >
                                    -
                                </Text>

                                <DatePicker
                                    selected={moment(historyEndDate).toDate()}
                                    onChange={(date) => {
                                        // setRev_date1(moment(text).endOf('day'));
                                        CommonStore.update(s => {
                                            s.historyEndDate = moment(date).endOf('day');
                                        });

                                        setSalesLineChartPeriod(CHART_PERIOD.NONE);
                                    }}
                                    minDate={moment(historyStartDate).toDate()}
                                />
                            </View>
                        </View>
                    </View>

                    {/* Switch between reservation and loyalty page ternery */}
                    {isReservationPage ?
                        //  Reservation Page
                        <View style={{
                            paddingHorizontal: 15,
                            paddingVertical: 5,
                            width: windowWidth * 0.92,
                            zIndex: -1,
                        }}>
                            <ScrollView scrollEnabled={false}
                                style={{ height: Platform.OS == 'ios' ? windowHeight * 0.75 : windowHeight * 0.75 }}>
                                {/* Top 2 view */}
                                <View
                                    style={{
                                        flexDirection: 'row',
                                    }}>
                                    <View style={{ flexDirection: 'column', }}>
                                        {/* Traffic table */}

                                        {/* <View
                        style={{
                          backgroundColor: Colors.whiteColor,
                          width: windowWidth * 0.608,
                          height: Platform.OS == 'ios' ? windowHeight * 0.43 : windowHeight * 0.4,
                          margin: 10,
                          justifyContent: 'space-around',
                          textAlign: 'center',
                          alignContent: 'center',
                          borderRadius: 5,
                          paddingVertical: 10,
                          paddingHorizontal: 15,
                        }}>
                        <View
                          style={{
                            //paddingLeft: 10,
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: 14,
                              color: 'black',
                            }}>Traffic over time</Text>
                        </View>

                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'flex-start',
                            alignContent: 'center',
                          }}>
                          <Text
                            style={{
                              //paddingLeft: 10,
                              paddingTop: 4,
                              fontFamily: 'NunitoSans-SemiBold',
                              fontSize: switchMerchant ? 10 : 14,
                              color: 'gray',
                              textAlign: 'center',
                            }}>Guest</Text>
                          <Text
                            style={{
                              paddingLeft: 10,
                              paddingTop: 4,
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Regular',
                              textAlign: 'center',
                            }}>{guestNum || 0}</Text>
                          <Text
                            style={{
                              paddingLeft: 20,
                              paddingTop: 4,
                              color: 'gray',
                              fontFamily: 'NunitoSans-SemiBold',
                              fontSize: switchMerchant ? 10 : 14,
                              textAlign: 'center',
                            }}>Estimated Revenue</Text>
                          <Text
                            style={{
                              paddingLeft: 10,
                              paddingTop: 4,
                              fontSize: switchMerchant ? 10 : 14,
                              fontFamily: 'NunitoSans-Regular',
                              textAlign: 'center',
                            }}>MYR {estimatedRevenue || 0}</Text>

                          <View
                            style={{
                              marginLeft: 'auto',
                              //paddingRight: 10,
                              flexDirection: 'row',
                              height: 30,
                            }}>
                            <DropDownPicker
                              arrowColor={'black'}
                              arrowSize={switchMerchant ? 8 : 13}
                              arrowStyle={{ fontWeight: 'bold' }}
                              style={{
                                width: switchMerchant ? 100 : 180,
                                paddingVertical: 0,
                                borderRadius: 10,
                                bottom: 10
                              }}
                              placeholderStyle={{ color: Colors.fieldtTxtColor }}
                              items={
                                CHART_Y_AXIS_DROPDOWN_LIST[
                                CHART_TYPE.DASHBOARD_LINE_CHART_SALES
                                ]
                              }
                              itemStyle={{ justifyContent: 'flex-start', zIndex: 2 }}
                              placeholder={'Select'}
                              onChangeItem={(item) => {
                                setSelectedChartDropdownValueLineChart(item.value);
                              }}
                              defaultValue={selectedChartDropdownValueLineChart}
                              dropDownMaxHeight={150}
                              dropDownStyle={{
                                width: switchMerchant ? 100 : 180,
                                height: switchMerchant ? 100 : 120,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                                borderWidth: 1,
                                textAlign: 'left',
                                zIndex: 2,
                              }}
                              globalTextStyle={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: switchMerchant ? 10 : 14,
                                color: Colors.fontDark,
                                marginLeft: 5,
                              }}
                            />
                          </View>
                        </View>

                        <View
                          style={{
                            // backgroundColor: 'red',
                            zIndex: -1,
                            //paddingLeft: 5,
                          }}>
                          <FusionCharts
                            type={CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].type}
                            width={Platform.OS == 'ios' ? windowWidth * 0.58 :
                              windowWidth <= 1133
                                ? switchMerchant
                                  ? windowWidth * 0.57
                                  : windowWidth *
                                  (0.9 - Styles.sideBarWidth)
                                : windowWidth *
                                (0.66 - Styles.sideBarWidth)
                            }
                            height={
                              switchMerchant
                                ? windowHeight * 0.3
                                : windowHeight * 0.3
                            }
                            dataFormat={salesLineChart.dataFormat}
                            dataSource={salesLineChart.dataSource}
                            libraryPath={FS_LIBRARY_PATH}
                            events={eventsChart}
                          />
                        </View>

                      </View> */}

                                        {/* Bottom 3 view */}
                                        <View
                                            style={{
                                                flexDirection: 'row',
                                            }}>

                                            {/* Guest stat chart */}
                                            <View
                                                style={{
                                                    backgroundColor: Colors.whiteColor,
                                                    // width: windowWidth * 0.296,
                                                    width: windowWidth * 0.55,
                                                    // height: windowHeight * 0.30,
                                                    height: windowHeight * 0.50,
                                                    margin: 10,
                                                    justifyContent: 'space-around',
                                                    textAlign: 'center',
                                                    alignContent: 'center',
                                                    borderRadius: 5,
                                                    paddingVertical: 15,
                                                    paddingHorizontal: 15,
                                                }}>

                                                {/* <View
                    style={{
                      paddingLeft: 10,
                    }}>
                    <Text>Repeat guest stats</Text>
                  </View>

                  <View
                    style={{
                      zIndex: -1,
                      paddingLeft: 5,
                    }}>
                    <FusionCharts
                      type={CHART_DATA[CHART_TYPE.RESERVATION_GUEST_STATS].type}
                      width={
                        windowWidth <= 1133
                          ? switchMerchant
                            ? windowWidth * 0.8
                            : windowWidth *
                            (0.9 - Styles.sideBarWidth)
                          : windowWidth *
                          (0.365 - Styles.sideBarWidth)
                      }
                      height={
                        switchMerchant
                          ? windowHeight * 0.9
                          : windowHeight * 0.25
                      }
                      // width={
                      //   windowHeight * 0.25
                      // }
                      // height={
                      //   windowHeight * 0.25
                      // }
                      dataFormat={reservationGuestStatsChart.dataFormat}
                      dataSource={reservationGuestStatsChart.dataSource}
                      libraryPath={FS_LIBRARY_PATH}
                      events={eventsChart}
                    />
                  </View> */}

                                                {
                                                    (
                                                        reservationGuestStatsChart &&
                                                        reservationGuestStatsChart.dataSource &&
                                                        reservationGuestStatsChart.dataSource.data &&
                                                        reservationGuestStatsChart.dataSource.data.reduce((accum, row) => accum + parseInt(row.value), 0) > 0
                                                    )
                                                        ?
                                                        <>
                                                            <View
                                                                style={{
                                                                    //paddingLeft: 10,
                                                                }}>
                                                                <Text style={{
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                }}>Repeat guest stats</Text>
                                                            </View>

                                                            <View
                                                                style={{
                                                                    zIndex: -1,
                                                                    //paddingLeft: 5,
                                                                }}>
                                                                <FusionCharts
                                                                    {...{
                                                                        width: windowWidth * 0.5,
                                                                        height: windowHeight * 0.45,
                                                                        type: CHART_DATA[CHART_TYPE.RESERVATION_GUEST_STATS].type,
                                                                        dataFormat: reservationGuestStatsChart.dataFormat,
                                                                        dataSource: reservationGuestStatsChart.dataSource,
                                                                    }}
                                                                />
                                                            </View>
                                                        </>
                                                        :
                                                        <View style={[switchMerchant ? {
                                                            fontSize: 12,
                                                            justifyContent: 'center',
                                                            alignItems: 'center',
                                                        } : {
                                                            justifyContent: 'center',
                                                            alignItems: 'center',
                                                        }]}>
                                                            <Text>No data for this period</Text>
                                                        </View>
                                                }
                                            </View>

                                            {/* Turn time chart */}
                                            {/* <View
                          style={{
                            backgroundColor: Colors.whiteColor,
                            width: windowWidth * 0.296,
                            height: windowHeight * 0.30,
                            margin: 10,
                            justifyContent: 'space-around',
                            textAlign: 'center',
                            alignContent: 'center',
                            borderRadius: 5,
                            paddingVertical: 15,
                            paddingHorizontal: 15,
                          }}>
                          <View
                            style={{
                              //paddingLeft: 10,
                            }}>
                            <Text style={{
                              fontFamily: 'NunitoSans-Bold',
                              fontSize: switchMerchant ? 10 : 14,
                              color: 'black'
                            }}>Turn time by Party size</Text>
                          </View>

                          <View
                            style={{
                              zIndex: -1,
                              //paddingRight: 10,
                            }}>
                            <FusionCharts
                              type={CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].type}
                              width={
                                Platform.OS == 'ios' ? windowHeight * 0.39 :
                                  windowWidth <= 1133
                                    ? switchMerchant
                                      ? windowWidth * 0.275
                                      : windowWidth *
                                      (0.9 - Styles.sideBarWidth)
                                    : windowWidth *
                                    (0.345 - Styles.sideBarWidth)
                              }
                              height={
                                switchMerchant
                                  ? windowHeight * 0.25
                                  : windowHeight * 0.25
                              }
                              dataFormat={salesLineChart.dataFormat}
                              dataSource={salesLineChart.dataSource}
                              libraryPath={FS_LIBRARY_PATH}
                              events={eventsChart}
                            />
                          </View>
                        </View> */}

                                            {/* Guest feedback */}
                                            {/* <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                    width: windowWidth * 0.3,
                    height: windowHeight * 0.30,
                    margin: 5,
                    justifyContent: 'space-around',
                    textAlign: 'center',
                    alignContent: 'center',
                    borderRadius: 5,
                  }}>
                  <View
                    style={{
                      paddingLeft: 10,
                    }}>
                    <Text>Overall Guest Feedback</Text>
                  </View>

                  <View
                    style={{
                      zIndex: -1,
                      paddingLeft: 5,
                    }}>
                    <FusionCharts
                      type={CHART_DATA[CHART_TYPE.DASHBOARD_LINE_CHART_SALES].type}
                      width={
                        windowWidth <= 1133
                          ? switchMerchant
                            ? windowWidth * 0.8
                            : windowWidth *
                            (0.9 - Styles.sideBarWidth)
                          : windowWidth *
                          (0.37 - Styles.sideBarWidth)
                      }
                      height={
                        switchMerchant
                          ? windowHeight * 0.9
                          : windowHeight * 0.25
                      }
                      dataFormat={salesLineChart.dataFormat}
                      dataSource={salesLineChart.dataSource}
                      libraryPath={FS_LIBRARY_PATH}
                      events={eventsChart}
                    />
                  </View>
                </View> */}
                                        </View>
                                    </View>
                                    {/* Summary list */}
                                    <View
                                        style={{
                                            backgroundColor: Colors.whiteColor,
                                            width: windowWidth * 0.315,
                                            // height: Platform.OS == 'ios' ? windowHeight * 0.751 : windowHeight * 0.721,
                                            height: windowHeight * 0.5,
                                            margin: 10,
                                            justifyContent: 'space-between',
                                            textAlign: 'center',
                                            alignContent: 'center',
                                            borderRadius: 5,
                                            zIndex: -2,
                                        }}>
                                        {/* <View
                        style={[styles.summaryListView,
                        { borderBottomWidth: 1, borderBottomColor: Colors.tabGrey }]}>
                        <Text
                          style={[styles.summaryListText, switchMerchant ? { padding: 10, fontSize: 12 } : {}]}>
                          Estimated Revenue
                        </Text>
                        <Text
                          style={[styles.summaryListText, switchMerchant ? { padding: 10, paddingRight: 15, fontSize: 12 } : {}]}>
                          MYR {estimatedRevenue || 0}
                        </Text>
                      </View> */}
                                        <View
                                            style={[styles.summaryListView,
                                            { borderBottomWidth: 1, borderBottomColor: Colors.tabGrey, height: '25%', alignItems: 'center' }]}>
                                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                <Text
                                                    style={[styles.summaryListText, switchMerchant ? { padding: 10, fontSize: 12 } : {}]}>
                                                    Reservations
                                                </Text>
                                                <Inventory width={20} height={20} color={Colors.blackColor} />
                                            </View>
                                            <Text
                                                style={[styles.summaryListText, switchMerchant ? { padding: 10, paddingRight: 15, fontSize: 12 } : {}]}>
                                                {reservationsNum || 0}
                                            </Text>
                                        </View>
                                        {/* <View
                        style={[styles.summaryListView,
                        { borderBottomWidth: 1, borderBottomColor: Colors.tabGrey }]}>
                        <Text
                          style={[styles.summaryListText, switchMerchant ? { padding: 10, fontSize: 12 } : {}]}>
                          Walk-ins
                        </Text>
                        <Text
                          style={[styles.summaryListText, switchMerchant ? { padding: 10, paddingRight: 15, fontSize: 12 } : {}]}>
                          {walksInNum || 0}
                        </Text>
                      </View> */}
                                        <View
                                            style={[styles.summaryListView,
                                            { borderBottomWidth: 1, borderBottomColor: Colors.tabGrey, height: '25%', alignItems: 'center' }]}>
                                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                <Text
                                                    style={[styles.summaryListText, switchMerchant ? { padding: 10, fontSize: 12 } : {}]}>
                                                    Guest Nums
                                                </Text>
                                                <Feather name="users" size={20} />
                                            </View>
                                            <Text
                                                style={[styles.summaryListText, switchMerchant ? { padding: 10, paddingRight: 15, fontSize: 12 } : {}]}>
                                                {guestNum || 0}
                                            </Text>
                                        </View>
                                        <View
                                            style={[styles.summaryListView,
                                            { borderBottomWidth: 1, borderBottomColor: Colors.tabGrey, height: '25%', alignItems: 'center' }]}>
                                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                <Text
                                                    style={[styles.summaryListText, switchMerchant ? { padding: 10, fontSize: 12 } : {}]}>
                                                    Cancellations
                                                </Text>
                                                <Accountcancel width={25} height={25} />
                                            </View>
                                            <Text
                                                style={[styles.summaryListText, switchMerchant ? { padding: 10, paddingRight: 15, fontSize: 12 } : {}]}>
                                                {cancellationsNum || 0}
                                            </Text>
                                        </View>
                                        <View
                                            style={{
                                                justifyContent: 'space-between',
                                                flexDirection: 'row',
                                                height: '25%',
                                                alignItems: 'center'
                                            }}>
                                            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                                <Text
                                                    style={[styles.summaryListText, switchMerchant ? { padding: 10, fontSize: 12 } : {}]}>
                                                    No-shows
                                                </Text>
                                                <Feather name='eye-off' size={20} />
                                            </View>
                                            <Text
                                                style={[styles.summaryListText, switchMerchant ? { padding: 10, paddingRight: 15, fontSize: 12 } : {}]}>
                                                {noShowsNum || 0}
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                            </ScrollView>
                        </View>
                        :
                        //  Loyalty Page
                        <View style={{}}>
                            {/* Bottom table */}
                            <ScrollView>
                                {/* Active campaign table*/}
                                <View
                                    style={[{
                                        margin: 5,
                                        flexDirection: 'column',
                                    },
                                    switchMerchant ? { width: windowWidth * 0.77 } : { width: windowWidth * 0.91 },
                                    ]}>
                                    {/* Table title */}
                                    <View style={
                                        [styles.tableTitleView,
                                        switchMerchant
                                            ? {}
                                            : {
                                                height: windowHeight * 0.05,
                                            }
                                        ]}>
                                        <Text style={styles.tableTitleTextFirst}>Active Campaign</Text>
                                        <Text style={styles.tableTitleText}>Delivered</Text>
                                        <Text style={styles.tableTitleText}>Guest visits</Text>
                                        <Text style={styles.tableTitleText}>Visit rate</Text>
                                        <Text style={styles.tableTitleText}>Revenue</Text>
                                    </View>

                                    {/* Table Summary */}
                                    <View style={styles.tableSummaryView}>

                                        <View>
                                            <Text style={styles.tableSummaryTextFirstBig}>
                                                Active Campaign Summary
                                            </Text>
                                            <Text style={styles.tableSummaryTextFirstSmall}>
                                                The total performance of your current Autopilot Campaign
                                            </Text>
                                        </View>

                                        <View style={{ justifyContent: 'center' }}>
                                            <Text style={styles.tableSummaryTextBig}>
                                                0
                                            </Text>
                                            <Text style={styles.tableSummaryTextSmall}>
                                                Total Delivered
                                            </Text>
                                        </View>
                                        <View style={{ justifyContent: 'center' }}>
                                            <Text style={styles.tableSummaryTextBig}>
                                                0
                                            </Text>
                                            <Text style={styles.tableSummaryTextSmall}>
                                                Total Visits
                                            </Text>
                                        </View>
                                        <View style={{ justifyContent: 'center' }}>
                                            <Text style={styles.tableSummaryTextBig}>
                                                0%
                                            </Text>
                                            <Text style={styles.tableSummaryTextSmall}>
                                                Visit Rate
                                            </Text>
                                        </View>
                                        <View style={{ justifyContent: 'center' }}>
                                            <Text style={styles.tableSummaryTextBig}>
                                                0
                                            </Text>
                                            <Text style={styles.tableSummaryTextSmall}>
                                                MYR Revenue
                                            </Text>
                                        </View>
                                    </View>

                                    {/* Table content */}
                                    <View>
                                        <FlatList
                                            data={Loyaltycampaign}
                                            renderItem={renderloyalty}
                                        />
                                    </View>
                                    {/* <View style={styles.tableContentView}>

                  <View>
                    <Text style={styles.tableContentTextFirst}>
                      Active
                    </Text>
                    <Text style={styles.tableContentTextSecond}>
                      Birthday
                    </Text>
                    <Text style={styles.tableContentTextThird}>
                      7 days before birthday - 14 day expiration
                    </Text>
                  </View>

                  <View style={{ justifyContent: 'center' }}>
                    <Text style={styles.tableContentTextBig}>
                      2
                    </Text>
                    <Text style={styles.tableContentTextSmall}>
                      Total Delivered
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center' }}>
                    <Text style={styles.tableContentTextBig}>
                      0
                    </Text>
                    <Text style={styles.tableContentTextSmall}>
                      Total Visits
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center' }}>
                    <Text style={styles.tableContentTextBig}>
                      0%
                    </Text>
                    <Text style={styles.tableContentTextSmall}>
                      Visit Rate
                    </Text>
                  </View>
                  <View style={{ justifyContent: 'center' }}>
                    <Text style={styles.tableContentTextBig}>
                      0
                    </Text>
                    <Text style={styles.tableContentTextSmall}>
                      MYR Revenue
                    </Text>
                  </View>
                </View> */}
                                </View>

                                {/* Inactive campaign table*/}
                                <View
                                    style={[{
                                        margin: 5,
                                        flexDirection: 'column',
                                    },
                                    switchMerchant ? { width: windowWidth * 0.77 } : { width: windowWidth * 0.91 },
                                    ]}>
                                    {/* Table title */}
                                    <View style={
                                        [styles.tableTitleView,
                                        switchMerchant
                                            ? {}
                                            : {
                                                height: windowHeight * 0.05,
                                            }
                                        ]}>
                                        <Text style={styles.tableTitleTextFirst}>Inactive Campaign</Text>
                                        <Text style={styles.tableTitleText}>Delivered</Text>
                                        <Text style={styles.tableTitleText}>Guest Visits</Text>
                                        <Text style={styles.tableTitleText}>Visit Rate</Text>
                                        <Text style={styles.tableTitleText}>Revenue</Text>
                                    </View>

                                    {/* Table Summary */}
                                    <View style={styles.tableSummaryView}>

                                        <View>
                                            <Text style={styles.tableSummaryTextFirstBig2}>
                                                Inactive Campaign Summary
                                            </Text>
                                            <Text style={styles.tableSummaryTextFirstSmall}>
                                                The total performance of your current Autopilot Campaign
                                            </Text>
                                        </View>

                                        <View style={{ justifyContent: 'center' }}>
                                            <Text style={styles.tableSummaryTextBig2}>
                                                0
                                            </Text>
                                            <Text style={styles.tableSummaryTextSmall}>
                                                Total Delivered
                                            </Text>
                                        </View>
                                        <View style={{ justifyContent: 'center' }}>
                                            <Text style={styles.tableSummaryTextBig2}>
                                                0
                                            </Text>
                                            <Text style={styles.tableSummaryTextSmall}>
                                                Total Visits
                                            </Text>
                                        </View>
                                        <View style={{ justifyContent: 'center' }}>
                                            <Text style={styles.tableSummaryTextBig2}>
                                                0%
                                            </Text>
                                            <Text style={styles.tableSummaryTextSmall}>
                                                Visit Rate
                                            </Text>
                                        </View>
                                        <View style={{ justifyContent: 'center' }}>
                                            <Text style={styles.tableSummaryTextBig2}>
                                                0
                                            </Text>
                                            <Text style={styles.tableSummaryTextSmall}>
                                                MYR Revenue
                                            </Text>
                                        </View>
                                    </View>

                                    {/* Table content */}
                                    <View style={styles.tableContentView}>

                                        <View>
                                            <Text style={styles.tableContentTextFirst2}>
                                                Inactive
                                            </Text>
                                            <Text style={styles.tableContentTextSecond}>
                                                Birthday
                                            </Text>
                                            <Text style={styles.tableContentTextThird}>
                                                7 days before birthday - 14 day expiration
                                            </Text>
                                        </View>

                                        <View style={{ justifyContent: 'center' }}>
                                            <Text style={styles.tableContentTextBig}>
                                                0
                                            </Text>
                                            <Text style={styles.tableContentTextSmall}>
                                                Total Delivered
                                            </Text>
                                        </View>
                                        <View style={{ justifyContent: 'center' }}>
                                            <Text style={styles.tableContentTextBig}>
                                                0
                                            </Text>
                                            <Text style={styles.tableContentTextSmall}>
                                                Total Visits
                                            </Text>
                                        </View>
                                        <View style={{ justifyContent: 'center' }}>
                                            <Text style={styles.tableContentTextBig}>
                                                0%
                                            </Text>
                                            <Text style={styles.tableContentTextSmall}>
                                                Visit Rate
                                            </Text>
                                        </View>
                                        <View style={{ justifyContent: 'center' }}>
                                            <Text style={styles.tableContentTextBig}>
                                                0
                                            </Text>
                                            <Text style={styles.tableContentTextSmall}>
                                                MYR Revenue
                                            </Text>
                                        </View>
                                    </View>
                                </View>
                            </ScrollView>
                        </View>
                    }
                </View>
            </ScrollView >
            <View
                style={{
                    flex: 1,
                    position: 'absolute',
                    bottom: windowHeight * 0.18,
                }}>
                <Footer />
            </View>
        </View >
        // </UserIdleWrapper >
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: 'row',
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    sidebar: {
        width: Dimensions.get('window').width * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //   width: 0,
        //   height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    headerLeftStyle: {
        width: Dimensions.get('window').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },

    topBar: {
        flexDirection: 'row',
        height: Dimensions.get('window').height * 0.11,
        width: Dimensions.get('window').width * 0.785,
        //backgroundColor: Colors.lightGrey,
        justifyContent: 'flex-start',
    },
    topBarButton: {
        padding: 5,
        backgroundColor: Colors.lightGrey,
        width: Dimensions.get('window').width * 0.12,
        justifyContent: 'center',
    },
    tableTitleView: {
        backgroundColor: Colors.lightGrey,
        flexDirection: 'row',
        justifyContent: 'center',
        height: Dimensions.get('window').height * 0.08,
        alignContent: 'center',
        textAlign: 'center',
    },
    tableTitleTextFirst: {
        textAlign: 'left',
        paddingTop: 5,
        marginLeft: 5,
        width: Dimensions.get('window').width * 0.3,
    },
    tableTitleText: {
        paddingTop: 5,
        textAlign: 'center',
        width: Dimensions.get('window').width * 0.11,
    },
    tableSummaryView: {
        paddingTop: 5,
        paddingBottom: 10,
        backgroundColor: Colors.whiteColor,
        flexDirection: 'row',
        justifyContent: 'center',
        alignContent: 'center',
        textAlign: 'center',
        borderBottomWidth: 1,
        borderBottomColor: 'grey',
    },
    tableSummaryTextFirstBig: {
        textAlign: 'left',
        paddingTop: 5,
        marginLeft: 5,
        width: Dimensions.get('window').width * 0.3,
        color: 'lightseagreen',
        fontSize: 17,
    },
    tableSummaryTextFirstBig2: {
        textAlign: 'left',
        paddingTop: 5,
        marginLeft: 5,
        width: Dimensions.get('window').width * 0.3,
        color: 'red',
        fontSize: 17,
    },
    tableSummaryTextFirstSmall: {
        textAlign: 'left',
        paddingTop: 5,
        marginLeft: 5,
        width: Dimensions.get('window').width * 0.3,
        color: 'gray',
        fontSize: 10,
    },
    tableSummaryTextBig: {
        textAlign: 'center',
        color: 'lightseagreen',
        fontSize: 17,
        width: Dimensions.get('window').width * 0.11,
    },
    tableSummaryTextBig2: {
        textAlign: 'center',
        color: 'red',
        fontSize: 17,
        width: Dimensions.get('window').width * 0.11,
    },
    tableSummaryTextSmall: {
        textAlign: 'center',
        paddingTop: 5,
        color: 'gray',
        fontSize: 10,
        width: Dimensions.get('window').width * 0.11,
    },
    tableContentView: {
        paddingTop: 5,
        paddingBottom: 10,
        backgroundColor: Colors.whiteColor,
        flexDirection: 'row',
        justifyContent: 'center',
        alignContent: 'center',
        textAlign: 'center',
        borderBottomWidth: 1,
        borderBottomColor: 'grey',
    },
    tableContentTextFirst: {
        textAlign: 'left',
        paddingTop: 5,
        marginLeft: 5,
        width: Dimensions.get('window').width * 0.3,
        color: 'lightseagreen',
        fontSize: 10,
    },
    tableContentTextFirst2: {
        textAlign: 'left',
        paddingTop: 5,
        marginLeft: 5,
        width: Dimensions.get('window').width * 0.3,
        color: 'red',
        fontSize: 10,
    },
    tableContentTextSecond: {
        textAlign: 'left',
        paddingTop: 5,
        marginLeft: 5,
        width: Dimensions.get('window').width * 0.3,
        color: 'black',
        fontSize: 15,
    },
    tableContentTextThird: {
        textAlign: 'left',
        paddingTop: 5,
        marginLeft: 5,
        width: Dimensions.get('window').width * 0.3,
        color: 'gray',
        fontSize: 10,
    },
    tableContentTextBig: {
        textAlign: 'center',
        fontSize: 17,
        color: 'black',
        width: Dimensions.get('window').width * 0.11,
    },
    tableContentTextSmall: {
        textAlign: 'center',
        paddingTop: 5,
        color: 'gray',
        fontSize: 10,
        width: Dimensions.get('window').width * 0.11,
    },
    summaryListView: {
        justifyContent: 'space-between',
        flexDirection: 'row',
    },
    summaryListText: {
        padding: 15,
        margin: 10,
        fontFamily: 'NunitoSans-Bold',
        //fontSize: switchMerchant ? 10 : 14,
        fontSize: 14,
        color: Colors.fontDark,
    }


});

export default ReservationAnalyticScreen;
