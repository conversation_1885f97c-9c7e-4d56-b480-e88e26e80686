import React, { useState, Component, useEffect } from "react";
import {
  StyleSheet,
  ScrollView,
  Image,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
  Modal,
  KeyboardAvoidingView,
  Dimensions,
  ActivityIndicator,
  useWindowDimensions,
} from "react-native";
import firebase from "firebase";
import AsyncStorage from "@react-native-async-storage/async-storage";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import Colors from "../constant/Colors";
import Styles from "../constant/Styles";
import API from "../constant/API";
import * as Token from "../util/Token";
import * as User from "../util/User";
import { useNavigation } from '@react-navigation/native';
import { idbGet, idbSet, idbClear } from '../util/db';
import { CommonStore } from "../store/commonStore.js";
import { UserStore } from "../store/userStore";
import { MerchantStore } from "../store/merchantStore";
import { OutletStore } from '../store/outletStore';
import ApiClient from "../util/ApiClient";
import AwesomeAlert from "react-native-awesome-alerts";
import { useLinkTo, useRoute } from "@react-navigation/native";
import { APP_TYPE, OFFLINE_BILL_TYPE, OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST, RESERVATION_PRIORITY, ROLE_TYPE } from "../constant/common";
import NetInfo from "@react-native-community/netinfo";

import imgLogo from "../asset/image/logo.png";
import { isMobile } from "../util/common";
import { DataStore } from "../store/dataStore";
import { prefix } from "../constant/env";

//import auth from '@react-native-firebase/auth';
import { Collections } from '../constant/firebase';
//import firestore from '@react-native-firebase/firestore';
import moment from 'moment';
import { TableStore } from "../store/tableStore";

const LoginScreen = (props) => {
  const {
    checkLogin,
    switchLogin,
    changeToPinLogin,
    route
  } = props;

  //   const checkLogin = CommonStore.useState((s) => s.checkLogin);

  console.log(route);

  // this.goToLoginState = this.goToLoginState.bind(this);

  const linkTo = useLinkTo();
  const windowDimensions = useWindowDimensions();
  const navigation = useNavigation();
  const [showAlertLogin, setShowAlertLogin] = useState(false);

  const email = UserStore.useState((s) => s.email);
  const name = UserStore.useState((s) => s.name);
  const googleId = UserStore.useState((s) => s.googleId);
  const imageUrl = UserStore.useState((s) => s.imageUrl);
  const tokenId = UserStore.useState((s) => s.tokenId);

  const [password, setPassword] = useState("");
  const [reset, setReset] = useState(false);
  const [loadingModal, setLoadingModal] = useState(false);

  /////////////////////////////////////////////////

  // 13-10-2022 - Ported support code login (Greg)

  const [showSupportCodeLogin, setShowSupportCodeLogin] = useState(false);
  const [supportCode, setSupportCode] = useState("");

  /////////////////////////////////////////////////

  const isAuthenticating = CommonStore.useState((s) => s.isAuthenticating);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const [loading, setLoading] = useState(true);
  // function here

  useEffect(() => {
    console.log("isAuthenticating");
    console.log(isAuthenticating);
  });

  useEffect(() => {
    const autoLogin = async () => {
      const email = await AsyncStorage.getItem('email');
      const password = await AsyncStorage.getItem('password');

      if (email && password) {
        try {
          let res = {};
          try {
            res = await firebase.auth()
              .signInWithEmailAndPassword(email, password);
          } catch (error) {
            console.log("existing email and password not found");
          }

          if (res && res.user) {
            ////////////////////////////////////

            // 2023-08-09 - Clear everything first

            await new Promise(async resolve => {
              await AsyncStorage.getAllKeys()
                .then(keys => AsyncStorage.multiRemove(keys))
                .then(() => {
                  console.log('done removed');

                  resolve();
                });
            });

            await idbClear();

            ////////////////////////////////////

            let user = res.user;
            let firebaseToken = await user.getIdToken();
            ApiClient.GET(
              API.getToken + firebaseToken + "&app=" + APP_TYPE.MERCHANT
            ).then(async (result) => {
              // setLoading(false);

              if (result && result.merchantId) {
                if (result.token) {
                  Token.setToken(result.token);
                  Token.setRefreshToken(result.refreshToken);

                  await AsyncStorage.setItem("accessToken", result.token);

                  CommonStore.update(s => {
                    s.isUserActive = true;
                  });

                  ApiClient.GET(API.userAdmin).then(async (userData) => {
                    User.setUserData(userData);
                    User.setName(userData.name);
                    User.setRefreshToken(userData.refreshToken);
                    User.setUserId(userData.firebaseUid);
                    User.setlogin(true);
                    User.setMerchantId(userData.merchantId);
                    User.setOutletId(userData.outletId);

                    global.currUserRole = userData.role;

                    await AsyncStorage.setItem("email", email);
                    await AsyncStorage.setItem("password", password);
                    await AsyncStorage.setItem("loggedIn", "true");

                    await AsyncStorage.setItem(
                      "userData",
                      JSON.stringify(userData)
                    );

                    ////////////////////////////////////

                    await AsyncStorage.setItem(
                      "merchantId",
                      userData.merchantId
                    );

                    await AsyncStorage.setItem(
                      "refreshToken",
                      userData.refreshToken
                    );

                    await AsyncStorage.setItem("role", userData.role);

                    await AsyncStorage.setItem(
                      "firebaseUid",
                      userData.firebaseUid
                    );

                    AsyncStorage.setItem(
                      'privileges',
                      JSON.stringify(userData.privileges || [])
                    );

                    await AsyncStorage.setItem(
                      'screensToBlock',
                      JSON.stringify(userData.screensToBlock || [])
                    );

                    AsyncStorage.setItem(
                      'currOutletId',
                      userData.outletId,
                    );

                    console.log('@@userData@@');
                    console.log(userData);

                    UserStore.update((s) => {
                      s.firebaseUid = userData.firebaseUid;
                      s.merchantId = userData.merchantId;

                      s.role = userData.role;
                      s.name = userData.name;

                      s.isAlphaUser = userData.isAlphaUser ? true : false;

                      s.isBetaUser = userData.isBetaUser ? true : false;

                      s.privileges = userData.privileges;
                      s.screensToBlock = userData.screensToBlock ? userData.screensToBlock : [];

                      s.pinNo = userData.pinNo || '';

                      s.isMasterAccount = userData.isMasterAccount !== undefined ? userData.isMasterAccount : (userData.role === ROLE_TYPE.ADMIN ? true : false);
                    });

                    global.privileges_state = userData.privileges;

                    if (userData.role === ROLE_TYPE.ADMIN) {
                      global.privileges = [
                        "EMPLOYEES",
                        "OPERATION",
                        "PRODUCT",
                        "INVENTORY",
                        "INVENTORY_COMPOSITE",
                        "DOCKET",
                        "VOUCHER",
                        "PROMOTION",
                        "CRM",
                        "LOYALTY",
                        "TRANSACTIONS",
                        "REPORT",
                        "RESERVATIONS",

                        // for action
                        'REFUND_ORDER',

                        'SETTINGS',

                        'QUEUE',

                        'OPEN_CASH_DRAWER',

                        'KDS',

                        'UPSELLING',

                        // for Kitchen

                        'REJECT_ITEM',
                        'CANCEL_ORDER',
                        //'REFUND_tORDER',
                      ];
                    } else {
                      global.privileges = global.privileges_state || [];
                    }

                    MerchantStore.update((s) => {
                      s.currOutletId = userData.outletId;
                    });

                    ////////////////////////////////////

                    // ApiClient.GET(API.outlet2 + User.getOutletId()).then((result) => {
                    //   User.setName(result.name);
                    //   User.setQueueStatus(result.queueStatus);
                    //   User.setRefreshCurrentAction(result.reservationStatus);
                    //   checkLogin(true)
                    // });

                    // checkLogin(true);
                    linkTo && linkTo(`${prefix}/home`);

                    setLoadingModal(false);

                    CommonStore.update((s) => {
                      s.isAuthenticating = false;

                      s.paymentDetailsBoCreditSMS = null;
                      s.isPayingCreditSMS = false;

                      s.paymentDetailsBoCreditWhatsapp = null;
                      s.isPayingCreditWhatsapp = false;
                    });
                  });
                } else {
                  alert("Login failed: Invalid merchant account");

                  setLoadingModal(false);
                  CommonStore.update((s) => {
                    s.isAuthenticating = false;
                  });
                }
              } else {
                alert("Login failed: Invalid merchant account");

                setLoadingModal(false);
                CommonStore.update((s) => {
                  s.isAuthenticating = false;
                });
              }
            });
          }

          // const result = await ApiClient.POST(API.loginEndpoint, { email, password });
          // if (result && result.accessToken) {
          //   // Store tokens and user data securely
          //   await AsyncStorage.setItem('accessToken', result.accessToken);
          //   await AsyncStorage.setItem('refreshToken', result.refreshToken);

          //   // Optionally store other user data if needed
          //   await AsyncStorage.setItem('userData', JSON.stringify(result.userData));

          //   // Navigate to dashboard or home screen
          //   navigation.navigate('Dashboard');
          // } else {
          //   // Handle login failure or invalid credentials
          //   Alert.alert('Login Failed', 'Invalid credentials. Please try again.');
          // }

        } catch (error) {
          console.error('Auto-login error:', error);
          Alert.alert('Auto-login Error', 'Failed to auto-login. Please try again.');
        }
      } else {
        // Handle case where no stored credentials are found
        Alert.alert('No stored credentials', 'Please login again');
        // Navigate to your login screen if needed
        navigation.navigate('LoginScreen');
      }
    };
    autoLogin(); // Call autoLogin function on component mount

    // Optionally, you can clean up any subscriptions or timers here
    return () => {
      // Cleanup logic if needed
    };
  }, []);
  // useEffect(async () => {
  //   console.log('clear cached data');

  //   await idbClear();

  //   await AsyncStorage.clear();

  //   global.ptBatchPairs = [];
  //   global.pteBatchPairs = [];

  //   global.ptStartDatePrev = null;
  //   global.ptEndDatePrev = null;

  //   global.ptDateTimestamp = Date.now();

  //   global.payoutTransactions = [];
  //   global.payoutTransactionsExtend = [];
  // }, []);

  const goReset = () => {
    const body = {
      email: email,
    };
    ApiClient.POST(API.userResetPassword, body).then((result) => {
      if (result == true) {
        alert(
          "Success: Temporary password reset link has been sent to your email"
          // [
          //     {
          //         text: 'OK',
          //         onPress: () => {
          //             setReset(!reset);
          //         },
          //     },
          // ],
          // { cancelable: false },
        );
      } else {
        alert(
          "Error: Unable to send the reset password email"
          // [
          //     {
          //         text: 'OK',
          //         onPress: () => {
          //             setReset(!reset);
          //         },
          //     },
          // ],
          // { cancelable: false },
        );
      }
    });
  };

  const clearGlobalStates = () => {
    CommonStore.replace({
      isLoading: false,
      isLoadingApplicableVoucher: false,

      isOrdering: false,

      orderTables: [],
      switchMerchant: false,

      simulateTabletMode: false,
      windowWidthSimulate: 2160,
      windowHeightSimulate: 1620,
      fontScaleSimulate: 2,

      windowWidthForModal: 2160,
      windowHeightForModal: 1620,

      accumulator: {},

      isOnMenu: true,
      isOnCategory: true,

      ///////////////////////////////

      isAuthenticating: false,

      selectedOutletSection: {},

      selectedOutletTable: {},

      /////////////////////////////////

      // scope_storage_access: false,
      // scope_storage_dir: '',

      currOutletTaxes: [],

      // Shared from user app

      outletsTaxDict: {},

      selectedOutletItems: [], // [ outlet_item, ... ]
      selectedOutletItemCategories: [], // [ outlet_item_category, ... ]

      selectedOutletItemCategory: {}, // outlet_item_category
      selectedOutletItem: {}, // outlet_item

      outletsItemAddOnDict: {}, // { outlet_item_id -> outlet_item_addon, ... }
      outletsItemAddOnChoiceDict: {}, // { outlet_item_addon_id -> outlet_item_addon_choice, ... }

      selectedOutletItemAddOn: {}, // { outlet_item_addon_id -> { outlet_item_addon_choice_id = true/false, ... }, ... }
      selectedOutletItemAddOnChoice: {}, // { outlet_item_addon_choice_id -> true/false, ... }

      onUpdatingCartItem: null,

      onUpdatingCurrPendingOrder: null,
      modeAddCart: 'NORMAL',

      cartItems: [], // [ { itemId: outlet_item_id, choices: { outlet_item_addon_choice_id: true/false, ... } }, ... ]
      cartItemsMo: [],
      cartItemChoices: {}, // { outlet_item_id -> { outlet_item_addon_choice_id = true, ... }, ... }

      // Calendar data
      calendarDataArray: [],
      selectedCalendarData: moment().format('YYYY-MM-DD'),
      selectedCalendarArray: [],

      orderType: 'DELIVERY',
      orderTypeSub: 'NORMAL',

      cartOutletItemsDict: {},
      cartOutletItemAddOnDict: {},
      cartOutletItemAddOnChoiceDict: {},

      cartItemsProcessed: [],
      cartItemsMoProcessed: [],

      /////////////////////////////////

      segments: [],
      // segmentsDict: {},

      merchantVouchers: [],
      merchantVouchersDict: {},

      merchantVoucherReportsVoucherIdDict: {},

      outletSupplyItems: [],
      outletSupplyItemsDict: {},
      outletSupplyItemsSkuDict: {},

      supplyItems: [],
      supplyItemsSkuDict: {},
      supplyItemsDict: {},
      suppliers: [],
      suppliersDict: {},

      purchaseOrders: [],
      stockTransfers: [],
      stockTakes: [],

      suppliersProduct: [],
      suppliersProductDict: {},

      purchaseOrdersProduct: [],
      stockTransfersProduct: [],
      stockTakesProduct: [],
      stockReturnsProduct: [],

      allOutletsSupplyItems: [],
      allOutletsSupplyItemsDict: {},
      allOutletsSupplyItemsSkuDict: {},

      allOutletsItemAddOn: [],
      allOutletsItemAddOnDict: {}, // pass outletitem uniqueid to get the addon obj
      allOutletsItemAddOnChoiceDict: {}, // pass addon uniqueid to get the addonchoice obj

      allOutletsItemAddOnIdDict: {}, // pass outletitem uniqueid to get the addon obj
      allOutletsItemAddOnChoiceIdDict: {}, // pass addon uniqueid to get the addonchoice obj

      selectedProductEdit: null,
      selectedOutletCategoryEdit: null,
      selectedSupplierEdit: null,
      selectedPurchaseOrderEdit: null,
      selectedStockTransferEdit: null,
      selectedStockTakeEdit: null,
      selectedStockReturnEdit: null,
      selectedVoucherEdit: null,
      selectedOutletEmployeeEdit: null,

      selectedPromotionEdit: null,

      selectedCustomerEdit: null,

      selectedGuestEdit: null, // pass selected guest

      selectedPreorderPackageEdit: null,

      selectedPointsRedeemPackageEdit: null,

      selectedSegmentEdit: null,

      selectedBeerDocketEdit: null,

      selectedPurchaseOrderEditFromSupplier: null,

      selectedLoyaltyStampEdit: null,

      selectedLoyaltyCampaignEdit: null,

      selectedTaggableVoucherEdit: null,

      selectedStampTypeEdit: null,

      selectedLoyaltySignUpCampaignEdit: null,

      selectedTopupCreditTypeEdit: null,

      selectedUpsellingCampaignEdit: null,

      userCart: {},

      currPage: 'Dashboard',
      currPageStack: ['Dashboard'],
      pageStack: ['Dashboard'],
      expandTab: 'DASHBOARD',

      timestamp: Date.now(),

      routeParams: {},

      outletSelectDropdownView: () => { },

      selectedOrderToPayUserId: '',
      selectedOrderToPayUserIdVoucherIdRedemptionList: [],
      selectedOrderToPayUserIdVoucherIdValidList: [],

      summaryCheckDict: {},
      summaryCancelledCheckDict: {},
      summaryDeliveredCheckDict: {},

      chatbotModalVisibility: false,
      chatbotMessages: [
        // {
        //     _id: 1,
        //     text: 'Hello developer',
        //     createdAt: new Date(),
        //     user: {
        //         _id: 2,
        //         name: 'React Native',
        //         avatar: 'https://placeimg.com/140/140/any',
        //     },
        // },
        // {
        //     _id: 1,
        //     text: 'Hello developer',
        //     createdAt: new Date(),
        //     user: {
        //         _id: 2,
        //         name: 'React Native',
        //         avatar: 'https://placeimg.com/140/140/any',
        //     },
        // },
      ],


      // Navigation param
      venueSettingPage: '',

      // selectedOutletPromotions: [],

      availablePromotions: [],
      availablePromoCodePromotions: [],
      selectedOutletPromotion: {},
      allPromotions: {},

      overrideItemPriceSkuDict: {},
      amountOffItemSkuDict: {},
      percentageOffItemSkuDict: {},
      buy1Free1ItemSkuDict: {},
      deliveryItemSkuDict: {},
      takeawayItemSkuDict: {},

      overrideCategoryPriceNameDict: {},
      amountOffCategoryNameDict: {},
      percentageOffCategoryNameDict: {},
      buy1Free1CategoryNameDict: {},
      deliveryCategoryNameDict: {},
      takeawayCategoryNameDict: {},

      overrideItemPriceSkuDictLC: {},
      fiItemSkuDictLC: {},
      amountOffItemSkuDictLC: {},
      percentageOffItemSkuDictLC: {},
      buy1Free1ItemSkuDictLC: {},
      deliveryItemSkuDictLC: {},
      takeawayItemSkuDictLC: {},

      overrideCategoryPriceNameDictLC: {},
      fiCategoryNameDictLC: {},
      amountOffCategoryNameDictLC: {},
      percentageOffCategoryNameDictLC: {},
      buy1Free1CategoryNameDictLC: {},
      deliveryCategoryNameDictLC: {},
      takeawayCategoryNameDictLC: {},

      currOutletOrderNumber: {},

      isCheckingOutTakeaway: false,
      checkingOutTakeawayOrder: {},
      checkingOutTakeawayTimestamp: Date.now(),

      isCounterOrdering: false, // 2022-06-10 Fixes

      selectedLoyaltyCampaign: {},
      selectedGuestDetails: {},

      shiftClosedModal: false,

      selectedReservationTableId: '',
      selectedReservationTableCode: '',

      selectedTaggableVoucher: {},

      selectedPromoCodePromotion: {},

      isNewTaggableVoucherScreenMounted: false,
      isNewCampaignScreenMounted: false,
      isNewLoyaltyCampaignScreenMounted: false,
      isSupplierProductScreenMounted: false,

      isOfflineReady: false,

      // clearDashboardDataFunc: null,

      moPax: '1',
      moMethod: 'dinein',

      enteredPinNo: '',

      isUserActive: true,

      supportCodeData: null,

      topupCreditTypeOrder: {},

      timestampPromotion: Date.now(),

      cartItemsTableIdDict: {},

      /////////////////////////////////

      // Mo-screens specific

      selectedOutletTableMo: {},
      orderTypeMo: 'DELIVERY',
      orderTypeSubMo: 'NORMAL',

      /////////////////////////////////

      timestampOutletCategory: Date.now(),

      /////////////////////////////////

      // 11/10/2022 For Available On function (Time) - Greg

      timeCheckItem: Date.now(),

      /////////////////////////////////

      // 2022-11-14 - Printer checking modal

      printerCheckingModalVisibility: false,

      /////////////////////////////////

      loyaltyStampGetItemSkuDict: {},
      loyaltyStampGetCategoryNameDict: {},
      loyaltyStampBuyItemSkuDict: {},
      loyaltyStampBuyCategoryNameDict: {},

      userLoyaltyStampGetLsItemDict: {},

      /////////////////////////////////

      historyStartDate: moment().startOf('month').startOf('day').valueOf(),
      historyEndDate: moment().endOf('month').endOf('day').valueOf(),

      /////////////////////////////////

      newSelectedOutletItemAddOnDetails: {},

      /////////////////////////////////

      merchantsOnboarded: [],
      outletsOnboarded: [],
      employeeClocks: [],
      userActions: [],

      gmvOrders: [],
      gmvStartDate: Date.now(),
      gmvEndDate: Date.now(),

      payoutTransactions: [],
      ptStartDate: Date.now(),
      ptEndDate: Date.now(),

      gmvOrdersRecent: [],

      /////////////////////////////////

      deviceInfo: {
        uid: '', // unique id
        dt: Date.now(),
        dtf: Date.now(), // date time format
        dn: '', // device name
        did: '', // device id
        dm: '', // device model
        os: '',
        ver: '',
      },

      /////////////////////////////////

      selectedOrder: {},

      payoutTransactionsOriginal: [],

      /////////////////////////////////

      sideBarScrollX: 0,
      sideBarScrollY: 0,

      /////////////////////////////////

      networkNotStable: false,
    });

    MerchantStore.replace({
      description: '',
      name: '',
      shortcode: '',
      logo: '',

      merchantLastUpdated: Date.now(),

      allOutlets: [],
      allOutletsDict: {},

      currOutletId: '',
      currOutlet: {
        uniqueId: '',
        privileges: [],

        toggleOpenOrder: false,
      },

      poNumber: 0,
      poNumberUpdatedAt: null,
      poNumberProduct: 0,
      poNumberProductUpdatedAt: null,

      isMasterAccount: undefined,
    });

    OutletStore.replace({
      sortedOutletItems: [],

      outletItems: [],
      outletItemsDict: {},
      outletItemsSkuDict: {},
      outletCategories: [],
      outletCategoriesDict: {},

      allOutletsItems: [],
      allOutletsItemsSkuDict: {},
      allOutletsCategories: [],
      allOutletsCategoriesNameDict: {},
      allOutletsCategoriesDict: {},

      allOutletsCategoriesUnique: [],

      outletSections: [],
      outletSectionsDict: {},

      outletTables: [],
      outletTablesDict: {},
      outletTablesCodeDict: {},

      outletTableCombinations: [],
      outletTableCombinationsDict: {},

      userOrders: [],
      userOrdersDict: {}, // { user_order_id -> user_order, ... }
      userOrdersTableDict: {}, // { outlet_table_id -> [ user_order, .... ], ... }

      userOrdersAllStatus: [],

      userReservations: [],
      userReservationsDict: {},
      userReservationsUserIdDict: {},

      userReservationsWaitList: [],
      userReservationsWaitListDict: {},
      userReservationsWaitListUserIdDict: {},

      userQueues: [],
      userQueuesDict: {},
      userRings: [],
      userRingsDict: {},

      allOutletsUserOrdersDoneProcessed: [],
      allOutletsUserOrdersDone: [],
      allOutletsUserOrders: [],

      allOutletsUserOrdersDoneRealTime: [],
      allOutletsUserOrdersRealTime: [],
      allOutletsUserOrdersDoneCache: [],
      allOutletsUserOrdersCache: [],

      allOutletsUserOrdersLoyaltyDone: [],
      allOutletsUserOrdersLoyalty: [],

      allOutletsUserOrdersLoyaltyDoneRealTime: [],
      allOutletsUserOrdersLoyaltyRealTime: [],
      allOutletsUserOrdersLoyaltyDoneCache: [],
      allOutletsUserOrdersLoyaltyCache: [],

      userOrdersExpandedDict: {}, // { user_order_id -> true, ... } | used for KitchenOrder
      // userORdersExpandedFooterDict: {}, // { user_order_id -> true, ... } | used for KitchenScreen, footer

      currOutletShift: {},
      currOutletShiftStatus: 'CLOSED',

      allOutletShifts: [],
      reportOutletShifts: [],

      allOutletsEmployees: [],
      allOutletsEmployeesUserActionsDict: {},

      outletsOpeningDict: {},

      beerDocketCategories: [],
      beerDocketCategoriesDict: {},

      beerDockets: [],
      beerDocketsDict: {},

      userBeerDockets: [],

      userOrderBeerDocketUBDIdDict: {},

      promotions: [],
      promotionsDict: {},

      linkedMerchantIdUsers: [],
      linkedOutletFavoriteUsers: [],
      favoriteMerchantIdUsers: [],
      crmUsersRaw: [],
      crmUsers: [],
      crmUsersDict: {},
      crmUserTags: [],
      crmUserTagsDict: {},
      crmSegments: [],

      selectedCustomerOrdersUserId: [],
      selectedCustomerDineInOrdersUserId: [],
      selectedCustomerOrdersPhone: [],
      selectedCustomerDineInOrdersPhone: [],

      selectedCustomerOrders: [],
      selectedCustomerDineInOrders: [],

      selectedCustomerAddresses: [],
      // selectedCustomerPointsTransactions: [],
      // selectedCustomerPointsBalance: 0,
      selectedCustomerReservations: [],
      //selectedCustomerVouchers: [],
      selectedCustomerVoucherRedemptions: [],
      selectedCustomerUserBeerDockets: [],

      selectedCustomerUserBeerDocketsEmail: [],
      selectedCustomerUserBeerDocketsUserId: [],

      selectedCustomerCashbackAmount: 0,

      selectedCustomerLCCTransactions: [], // loyalty campaign credit transactions
      selectedCustomerLCCBalance: 0, // loyalty campaign credit balance

      selectedCustomerLCCTransactionsEmail: [], // loyalty campaign credit transactions
      selectedCustomerLCCBalanceEmail: 0, // loyalty campaign credit balance
      selectedCustomerLCCTransactionsPhone: [], // loyalty campaign credit transactions
      selectedCustomerLCCBalancePhone: 0, // loyalty campaign credit balance
      // selectedCustomerLCCTransactionsUserId: [], // loyalty campaign credit transactions
      // selectedCustomerLCCBalanceUserId: 0, // loyalty campaign credit balance

      selectedCustomerPointsTransactions: [],
      selectedCustomerPointsBalance: 0,
      selectedCustomerPointsTransactionsEmail: [],
      selectedCustomerPointsBalanceEmail: 0,
      selectedCustomerPointsTransactionsPhone: [],
      selectedCustomerPointsBalancePhone: 0,

      selectedCustomerUserLoyaltyCampaigns: [],

      selectedCustomerUserTaggableVouchers: [],
      selectedCustomerUserTaggableVouchersView: [],

      selectedCustomerUserLoyaltyStamps: [],

      preorderPackages: [],

      pointsRedeemPackages: [],
      pointsRedeemPackagesDict: {},

      outletPrinters: [],

      outletPaymentMethods: [],

      loyaltyStamps: [],
      loyaltyStampsType: [],

      loyaltyCampaigns: [],
      availableLoyaltyCampaigns: [],

      loyaltyTier: {},

      currTableQRUrl: '',
      currOrderRegisterUrl: '',

      taggableVouchers: [],
      availableTaggableVouchers: [],

      employeeClockDict: {},

      userTaggableVouchers: [],

      userOrdersPrinting: [],

      /////////////////////////////////////////

      selectedCustomerApplicableVoucherIdList: [],

      /////////////////////////////////////////

      topupCreditTypes: [],

      /////////////////////////////////////////

      // currMerchantPayment: {
      //     uniqueId: '',

      //     outletId: '',
      //     merchantId: '',

      //     merchantName: '',
      //     merchantEmail: '',
      //     companyName: '',
      //     companyRegistrationNo: '',
      //     companyAddress: '',
      //     directorName: '',
      //     startDate: Date.now(),

      //     email: '',
      //     name: '',
      //     userId: '',

      //     agreementId: '',
      //     attachmentList: [],

      //     appliedVoucherCode: '',

      //     invoiceAmount: '',
      //     sstAmount: '',
      //     invoiceAndSSTAmount: '',
      //     invoiceAmountRaw: 100,
      //     sstAmountRaw: 10,
      //     invoiceAndSSTAmountRaw: 110,
      //     processingFee: 6,

      //     ///////////////////////////////////////////////////

      //     invoiceCount: 1,

      //     ///////////////////////////////////////////////////

      //     // 2022-10-26 - new fields

      //     accountEmail: '',
      //     accountPhone: '',
      //     processingFeeType: 'QR_SALES',

      //     invoiceAmountOriginal: '',
      //     sstAmountOriginal: '',
      //     invoiceAndSSTAmountOriginal: 'invoiceAndSSTAmountOriginal',
      //     invoiceAmountRawOriginal: 200,
      //     sstAmountRawOriginal: 20,
      //     invoiceAndSSTAmountRawOriginal: 220,
      //     processingFeeOriginal: 10,

      //     toGenerateVoucherCode: true,
      //     voucherCode: 'A',

      //     ///////////////////////////////////////////////////

      //     type: 'REGISTRATION',

      //     status: 'PENDING_SIGN',

      //     ///////////////////////////////////////////////////

      //     isVoucherCodeApplied: true, // if user applied

      //     agreementLink: 'https://koodooprod.s3.ap-southeast-1.amazonaws.com/pdf/test-sign.pdf',
      //     agreementSignedLink: '',

      //     invoiceLink: '',
      //     receiptLink: '',

      //     ////////////////////////////////////////////////////////

      //     bankType: '',
      //     bankCode: '',
      //     bankAccountName: '',
      //     bankAccountNumber: '',
      //     contactEmail: '',
      //     contactMobile: '',

      //     picFullName: '',
      //     picNRICPassport: '',
      //     picIdType: '',
      //     companyName: '',

      //     country: '',

      //     outletId: '',

      //     companyRobRoc: '',

      //     acceptedPrivacyPolicy: true,
      //     acceptedTermsConditions: true,

      //     ////////////////////////////////////////////////////////

      //     paymentDate: null,
      //     paymentDetails: {},

      //     isPaidOnline: false,
      //     isPaidOffline: false,

      // }, // default is null

      currMerchantPayment: null,

      smsCreditBalance: 0,

      upsellingCampaigns: [],

      ptTimestamp: Date.now(),
      pteTimestamp: Date.now(),

      payoutTransactions: [],
      payoutTransactionsExtend: [],

      reportDisplayType: 'DAY',
    });

    TableStore.replace({
      showDiscountModal: false,
      selectedDiscountItem: {},
      discountModalAmount: 0,
      discountModalQuantity: 0,
      isDiscountPercentage: false,
      isItemDiscount: false,

      discountCartItemDict: {},
      discountOrder: {
        value: '0',
        text: '0',
      },

      discountAccum: 0,
      discountAccumOrder: 0,

      discountText: '',

      showAddCustomer: false,

      isCustomer: false,
      showCustomerList: false,

      searchCustomer: '',

      customerAvatar: '',
      customerName: '',
      customerPhone: '+60',
      customerUsername: '',

      customerNextVisitDate: '',
      customerNextVisitTime: '',
      customerNextVisitDateIsChanged: false,
      customerNextVisitTimeIsChanged: false,

      customerIsActiveMember: true,
      customerTags: [],
      customerGender: 'Others',
      customerDob: Date.now(),
      customerEmail: '',
      customerRace: '',

      customerAddress: '',
      customerAddressLat: '',
      customerAddressLng: '',

      customerState: '',
      customerPostcode: '',
      customerFirstVisitDate: '',
      customerLastVisitDate: '',

      customerPhoneSecond: '',
      customerEmailSecond: '',
      customerAddressSecond: '',
      customerAddressLatSecond: '',
      customerAddressLngSecond: '',

      image: '',
      imageType: '',
      isImageChanged: false,

      selectedVoucherUserIdList: [],

      selectedVoucherUserId: [],
      selectedVoucherUserList: [],
      selectedVoucherUserItem: '',

      floorSection: [],
      refresh: false,
      tableSection: [],
      modal: false,
      outletId: '',
      visible: false,
      selectedTableId: false,
      customerAdd: false,
      orderDetail: false,
      selectedTableCode: null,
      orderCode: [],
      displayQr: false,
      qrData: [],
      qrTableId: null,
      tableSum: null,
      paymentOrder: null,
      total: null,
      paymentMethods: OFFLINE_PAYMENT_METHOD_DROPDOWN_LIST,
      isCancelled: false,
      isDelivered: false,
      promoCode: null,
      points: null,
      selectedPay: 'Credit Card',

      selectedPaymentMethodType: '',
      selectedPaymentMethodRemarks: '',
      selectedPaymentMethodBankCode: 'MBBEMYKL',
      selectedPaymentMethodLast4CardDigit: '',

      cancelUser: [],
      deliveredUser: [],
      cancelledUser: [],
      summaryTotal: 0,
      orderId: null,
      receiptInfo: null,
      selectedImage: null,
      capturedReceipt: null,
      search: '',
      cashInsert: false,
      cashAmount: '0',
      checkOutTime: null,
      tableCheckoutState: 0,
      displayQrModal: false,
      displayQModal: false,
      tableList: '',
      totalTables: 0,
      changeTable: false,
      changeTableId: '',
      joinTableId: '',
      onRemove: '',
      modalOrder: false,
      deleteTableModal: false,
      renderPaymentSummary: false,
      renderReceipt: false,
      loading: false,
      section: '',
      tableCode: '',
      capacity: 0,
      seat: 0,
      isCheckout: false,
      isPay: false,

      isAddSection: false,

      inputSectionName: '',

      inputTableCode: '',
      inputTablePax: '0',
      inputTablePaxMin: '0',
      inputTableTurnTime: 60,
      inputTableBookingPriority: RESERVATION_PRIORITY.MEDIUM,
      inputTableIsOnline: true,

      turnTimeOption: [
        { label: '0h 15m', value: 15 },
        { label: '0h 30m', value: 30 },
        { label: '0h 45m', value: 45 },
        { label: '1h 00m', value: 60 },
        { label: '1h 15m', value: 75 },
        { label: '1h 30m', value: 90 },
        { label: '1h 45m', value: 105 },
        { label: '2h 00m', value: 120 },
        { label: '2h 15m', value: 135 },
        { label: '2h 30m', value: 150 },
        { label: '2h 45m', value: 165 },
        { label: '3h 00m', value: 180 },
        { label: '3h 15m', value: 195 },
        { label: '3h 30m', value: 210 },
        { label: '3h 45m', value: 225 },
        { label: '4h 00m', value: 240 },
        { label: '4h 15m', value: 255 },
        { label: '4h 30m', value: 270 },
        { label: '4h 45m', value: 285 },
        { label: '5h 00m', value: 300 },
      ],
      priorityOption: [
        { label: 'Low', value: 'LOW' },
        { label: 'Medium', value: 'MEDIUM' },
        { label: 'High', value: 'HIGH' },
      ],

      seatingPax: 0,
      viewTableOrderModal: false,
      orderDisplaySummary: true,
      orderDisplayIndividual: false,
      orderDisplayProduct: false,
      updateTableModal: false,
      joinTableModal: false,
      sectionArea: [],
      currentSectionArea: null,
      addSectionAreaModel: false,
      addTableModal: false,
      preventDeleteTableModal: false,
      seatingModal: false,
      numPad: false,
      amount: '0.00',
      diffAmount: 0,
      paidAmount: 0,

      qrDateTimeEncrypted: '',

      joinTableItems: [],

      filteredOutletTables: [],
      filteredOutletTablesForRendered: [],

      showGenericQRCode: false,

      splitAmount: 0,

      selectedVoucher: {},

      selectedOrderToPayUserList: [],
      selectedOrderToPayUserIdDict: [],
      selectedOrderToPayList: [],
      selectedOrdersCartItems: [],
      currPendingOrder: {},
      currPendingOrderTotalPrice: 0,
      currPendingOrderDiscount: 0,
      currPendingOrderTax: 0,
      currPendingOrderSc: 0,
      currPendingOrderFinalPrice: 0,
      currBillType: OFFLINE_BILL_TYPE.SUMMARY,

      selectedIndividualOrdersDict: {},
      selectedProductOrdersDict: {},
      selectedSummaryOrdersDict: {},
      selectedCancelledOrdersDict: {},
      selectedDeliveredOrdersDict: {},
      count: 0,
      deliveryCount: 0,
      cancelledCount: 0,
      userOrdersActive: [],

      voucherList: [],
      selectedVoucherId: '',

      phoneNumberChecking: false,
      currCRMUser: null,

      phoneNumber: '+60',
      loyaltyName: '',

      showLoyaltyModal: false,
      showAddLoyaltyModal: false,
      selectedRegisteredLoyalty: '',
      searchLoyalty: '',
      loyaltyAmount: '0.00',

      discountAmount: {},

      isRedeem: true,

      registeredCRMUsersDropdownList: [],
      selectedRegisteredCRMUserId: '',

      cashbackAmount: '0.00',

      showVoucher: false,
      useCashback: false,
      useLoyaltyVoucher: false,

      cashbackModal: false,

      discountPromotionsTotalLCC: 0,
      qrTakeaway: false,

      promoCodePromotionDropdownList: [],
      selectedPromoCodePromotionId: '',

      outletPaymentMethodsDropdownList: [],

      promotionIdAppliedList: [],

      selectedOrderItemList: [],
      selectedOrderItemCancelledList: [],
      selectedOrderItemDeliveredList: [],

      selectedOrderItemCheckDict: {},
      selectedOrderItemCancelledCheckDict: {},
      selectedOrderItemDeliveredCheckDict: {},

      taggableVoucherDropdownList: [],
      selectedTaggableVoucherId: '',

      appliedDiscountTotal: 0,

      discountPromotionsTotal: 0,

      qrCodeTableRef: null,
      qrCodeTakeawayRef: null,

      isSavingQRCode: false,

      splitQuantity: 1,

      //switch table layout
      switchTableLayout: false,

      forceCloseShiftBeforeSignOut: false,

      receiptEmailToSent: '',
    });

    UserStore.replace({
      avatar: '',
      dob: null,
      email: '',
      gender: '',
      name: '',
      number: '',
      outletId: '',
      race: '',
      state: '',
      uniqueName: '',
      updatedAt: null,

      merchantId: '',
      role: '', // legacy
      refreshToken: '',
      firebaseUid: '',

      ///////////////////////////////////
      // Share from user app

      userAddresses: [],
      selectedUserAddress: null,

      userGroups: ['EVERYONE'],

      isAlphaUser: false, // show all hidden modules if true

      isBetaUser: false, // show all hidden modules if true, for beta one

      privileges: [],
      screensToBlock: [],

      pinNo: '',

      isMasterAccount: undefined,

      googleEmail: '',
      googleName: '',
      googleId: '',
      imageUrl: '',
      tokenId: '',
      userId: '',
      token: '',
    });
  };

  const goLogin = async () => {
    ///////////////////////////////////////////

    // 2024-02-06 - reset previous account data upon login

    console.log('clear cached data');

    await idbClear();

    await AsyncStorage.clear();

    global.ptBatchPairs = [];
    global.pteBatchPairs = [];

    global.ptStartDatePrev = null;
    global.ptEndDatePrev = null;

    global.ptDateTimestamp = Date.now();

    global.payoutTransactions = [];
    global.payoutTransactionsExtend = [];

    global.ptDict = {};
    global.pteDict = {};

    clearGlobalStates();

    ///////////////////////////////////////////

    if (!showSupportCodeLogin) {
      if (!email || !password) {
        alert("Login failed: Empty email and password");

        setLoadingModal(false);
        CommonStore.update((s) => {
          s.isAuthenticating = false;
        });

        return;
      }

      NetInfo.fetch().then(async (state) => {
        console.log("Connection type", state.type);
        console.log("Is connected?", state.isInternetReachable);
        console.log(state);

        if (state.isConnected) {
          try {
            let res = {};
            try {
              res = await firebase.auth()
                .signInWithEmailAndPassword(email, password);
            } catch (error) {
              console.log("existing email and password not found");

              // alert('Login failed', "Invalid email and password", [
              //   { text: "OK", onPress: () => { } }
              // ],
              //   { cancelable: false });

              // CommonStore.update(s => {
              //   s.isAuthenticating = false;
              // });
            }

            if (res.user === undefined) {
              // fail to find merchant in firebase, try to login using legacy user id/password

              ApiClient.POST(API.legacyLogin, {
                username: email,
                password: password,
              })
                .then(async (result) => {
                  if (result && result.idToken) {
                    let firebaseToken = result.idToken;

                    try {
                      res = await firebase
                        .auth()
                        .signInWithCustomToken(result.customToken);
                    } catch (error) {
                      console.log("failed to login with custom token");
                    }

                    await AsyncStorage.setItem(
                      "customToken",
                      result.customToken
                    );
                    await AsyncStorage.setItem("firebaseToken", firebaseToken);

                    // ApiClient.GET(API.getToken + firebaseToken).then(async (result) => {
                    ApiClient.GET(
                      API.getToken + firebaseToken + "&app=" + APP_TYPE.MERCHANT
                    ).then(async (result) => {
                      // setLoading(false);

                      if (result.merchantId) {
                        if (result.token) {
                          Token.setToken(result.token);
                          Token.setRefreshToken(result.refreshToken);
                          await AsyncStorage.setItem(
                            "accessToken",
                            result.token
                          );
                          ApiClient.GET(API.userAdmin).then(
                            async (userData) => {
                              User.setUserData(userData);
                              User.setName(userData.name);
                              User.setRefreshToken(userData.refreshToken);
                              User.setUserId(userData.firebaseUid);
                              User.setlogin(true);
                              User.setMerchantId(userData.merchantId);
                              User.setOutletId(userData.outletId);

                              await AsyncStorage.setItem("email", email);
                              await AsyncStorage.setItem("password", password);
                              await AsyncStorage.setItem("loggedIn", "true");
                              await AsyncStorage.setItem(
                                "userData",
                                JSON.stringify(userData)
                              );

                              ////////////////////////////////////

                              await AsyncStorage.setItem(
                                "merchantId",
                                userData.merchantId
                              );

                              await AsyncStorage.setItem(
                                "refreshToken",
                                userData.refreshToken
                              );

                              await AsyncStorage.setItem("role", userData.role);

                              await AsyncStorage.setItem(
                                "firebaseUid",
                                userData.firebaseUid
                              );

                              UserStore.update((s) => {
                                s.firebaseUid = userData.firebaseUid;
                                s.merchantId = userData.merchantId;

                                s.role = userData.role;
                                s.name = userData.name;
                              });

                              MerchantStore.update((s) => {
                                s.currOutletId = userData.outletId;
                              });

                              ////////////////////////////////////

                              // ApiClient.GET(API.outlet2 + User.getOutletId()).then((result) => {
                              //   User.setName(result.name);
                              //   User.setQueueStatus(result.queueStatus);
                              //   User.setRefreshCurrentAction(result.reservationStatus);
                              //   checkLogin(true)
                              // });

                              // checkLogin(true);

                              ////////////////////////////////////

                              // 2023-05-29 - Changes from pin login screen

                              // this._pinLogin();

                              // await connectToPrinter('192.168.1.2'); // try to let the bluetooth popup appeared

                              ////////////////////////////////////

                              linkTo && linkTo(`${prefix}/home`);

                              setLoadingModal(false);

                              CommonStore.update((s) => {
                                s.isAuthenticating = false;

                                s.paymentDetailsBoCreditSMS = null;
                                s.isPayingCreditSMS = false;

                                s.paymentDetailsBoCreditWhatsapp = null;
                                s.isPayingCreditWhatsapp = false;
                              });
                            }
                          );
                        } else {
                          alert("Login failed: Invalid merchant account");

                          setLoadingModal(false);
                          CommonStore.update((s) => {
                            s.isAuthenticating = false;
                          });
                        }
                      } else {
                        alert("Login failed: Invalid merchant account");

                        setLoadingModal(false);
                        CommonStore.update((s) => {
                          s.isAuthenticating = false;
                        });
                      }
                    });
                  }

                  if (result && result.status === "error") {
                    alert("Error: Invalid email or password");

                    setLoadingModal(false);
                    CommonStore.update((s) => {
                      s.isAuthenticating = false;
                    });
                  }
                })
                .catch((ex) => {
                  console.error("legacyLogin error");
                  console.error(ex);

                  alert("Login failed: Invalid merchant account");

                  setLoadingModal(false);
                  CommonStore.update((s) => {
                    s.isAuthenticating = false;
                  });
                });
            } else {
              // means firebase email & password auth success

              ////////////////////////////////////

              // 2023-08-09 - Clear everything first

              await new Promise(async resolve => {
                await AsyncStorage.getAllKeys()
                  .then(keys => AsyncStorage.multiRemove(keys))
                  .then(() => {
                    console.log('done removed');

                    resolve();
                  });
              });

              await idbClear();

              ////////////////////////////////////

              let user = res.user;
              let firebaseToken = await user.getIdToken();
              ApiClient.GET(
                API.getToken + firebaseToken + "&app=" + APP_TYPE.MERCHANT
              ).then(async (result) => {
                // setLoading(false);

                if (result && result.merchantId) {
                  if (result.token) {
                    Token.setToken(result.token);
                    Token.setRefreshToken(result.refreshToken);

                    await AsyncStorage.setItem("accessToken", result.token);

                    CommonStore.update(s => {
                      s.isUserActive = true;
                    });

                    ApiClient.GET(API.userAdmin).then(async (userData) => {
                      User.setUserData(userData);
                      User.setName(userData.name);
                      User.setRefreshToken(userData.refreshToken);
                      User.setUserId(userData.firebaseUid);
                      User.setlogin(true);
                      User.setMerchantId(userData.merchantId);
                      User.setOutletId(userData.outletId);

                      global.currUserRole = userData.role;

                      await AsyncStorage.setItem("email", email);
                      await AsyncStorage.setItem("password", password);
                      await AsyncStorage.setItem("loggedIn", "true");

                      await AsyncStorage.setItem(
                        "userData",
                        JSON.stringify(userData)
                      );

                      ////////////////////////////////////

                      await AsyncStorage.setItem(
                        "merchantId",
                        userData.merchantId
                      );

                      await AsyncStorage.setItem(
                        "refreshToken",
                        userData.refreshToken
                      );

                      await AsyncStorage.setItem("role", userData.role);

                      await AsyncStorage.setItem(
                        "firebaseUid",
                        userData.firebaseUid
                      );

                      AsyncStorage.setItem(
                        'privileges',
                        JSON.stringify(userData.privileges || [])
                      );

                      await AsyncStorage.setItem(
                        'screensToBlock',
                        JSON.stringify(userData.screensToBlock || [])
                      );

                      AsyncStorage.setItem(
                        'currOutletId',
                        userData.outletId,
                      );

                      console.log('@@userData@@');
                      console.log(userData);

                      UserStore.update((s) => {
                        s.firebaseUid = userData.firebaseUid;
                        s.merchantId = userData.merchantId;

                        s.role = userData.role;
                        s.name = userData.name;

                        s.isAlphaUser = userData.isAlphaUser ? true : false;

                        s.isBetaUser = userData.isBetaUser ? true : false;

                        s.privileges = userData.privileges;
                        s.screensToBlock = userData.screensToBlock ? userData.screensToBlock : [];

                        s.pinNo = userData.pinNo || '';

                        s.isMasterAccount = userData.isMasterAccount !== undefined ? userData.isMasterAccount : (userData.role === ROLE_TYPE.ADMIN ? true : false);
                      });

                      global.privileges_state = userData.privileges;

                      if (userData.role === ROLE_TYPE.ADMIN) {
                        global.privileges = [
                          "EMPLOYEES",
                          "OPERATION",
                          "PRODUCT",
                          "INVENTORY",
                          "INVENTORY_COMPOSITE",
                          "DOCKET",
                          "VOUCHER",
                          "PROMOTION",
                          "CRM",
                          "LOYALTY",
                          "TRANSACTIONS",
                          "REPORT",
                          "RESERVATIONS",

                          // for action
                          'REFUND_ORDER',

                          'SETTINGS',

                          'QUEUE',

                          'OPEN_CASH_DRAWER',

                          'KDS',

                          'UPSELLING',

                          // for Kitchen

                          'REJECT_ITEM',
                          'CANCEL_ORDER',
                          //'REFUND_tORDER',
                        ];
                      } else {
                        global.privileges = global.privileges_state || [];
                      }

                      MerchantStore.update((s) => {
                        s.currOutletId = userData.outletId;
                      });

                      ////////////////////////////////////

                      // ApiClient.GET(API.outlet2 + User.getOutletId()).then((result) => {
                      //   User.setName(result.name);
                      //   User.setQueueStatus(result.queueStatus);
                      //   User.setRefreshCurrentAction(result.reservationStatus);
                      //   checkLogin(true)
                      // });

                      // checkLogin(true);
                      linkTo && linkTo(`${prefix}/home`);

                      setLoadingModal(false);

                      CommonStore.update((s) => {
                        s.isAuthenticating = false;

                        s.paymentDetailsBoCreditSMS = null;
                        s.isPayingCreditSMS = false;

                        s.paymentDetailsBoCreditWhatsapp = null;
                        s.isPayingCreditWhatsapp = false;
                      });
                    });
                  } else {
                    alert("Login failed: Invalid merchant account");

                    setLoadingModal(false);
                    CommonStore.update((s) => {
                      s.isAuthenticating = false;
                    });
                  }
                } else {
                  alert("Login failed: Invalid merchant account");

                  setLoadingModal(false);
                  CommonStore.update((s) => {
                    s.isAuthenticating = false;
                  });
                }
              });
            }
          } catch (error) {
            alert("Login failed: Invalid email and password");
            // setState({ loading: false }); test herks

            setLoadingModal(false);
            CommonStore.update((s) => {
              s.isAuthenticating = false;
            });
          }
        }
        else {
          // 2022-07-04 changes, disable first

          // offline mode

          // if user login once, and have @userStore and @merchantStore cached locally, can just log them in

          const userStoreRaw = await AsyncStorage.getItem('@userStore');
          const merchantStoreRaw = await AsyncStorage.getItem('@merchantStore');
          const lastAccessToken = await AsyncStorage.getItem('last.accessToken');
          const lastUserData = await AsyncStorage.getItem('last.userData');
          const lastRefreshToken = await AsyncStorage.getItem('last.refreshToken');

          // console.log('check store');
          // console.log(userStoreRaw);
          // console.log(merchantStoreRaw);
          // console.log(lastAccessToken);
          // console.log(lastUserData);
          // console.log(lastRefreshToken);

          if (userStoreRaw && merchantStoreRaw && lastAccessToken && lastUserData && lastRefreshToken) {
            // console.log('last login - pass');

            const storedEmail = await AsyncStorage.getItem('email');
            const storedPassword = await AsyncStorage.getItem('password');

            if (email === storedEmail && password === storedPassword) {
              // console.log('same credentials - pass');

              const userStoreData = JSON.parse(userStoreRaw);
              const merchantStoreData = JSON.parse(merchantStoreRaw);

              UserStore.replace(userStoreData);
              MerchantStore.replace(merchantStoreData);

              await AsyncStorage.setItem('accessToken', lastAccessToken);
              await AsyncStorage.setItem('userData', lastUserData);
              await AsyncStorage.setItem('refreshToken', lastRefreshToken);

              const userData = JSON.parse(lastUserData);

              Token.setToken(lastAccessToken);
              Token.setRefreshToken(lastRefreshToken);

              User.setUserData(userData);
              User.setName(userData.name);
              User.setRefreshToken(userData.refreshToken);
              User.setUserId(userData.firebaseUid);
              User.setlogin(true);
              User.setMerchantId(userData.merchantId);
              User.setOutletId(userData.outletId);

              checkLogin(true);
            }
            else {
              window.confirm('Info, Your device is currently offline, please sign in with the last used account');
              setLoadingModal(false);
            }
          }
          else {
            window.confirm('Info, Connect to the internet and sign in to enable offline mode');
            setLoadingModal(false);
          }

          CommonStore.update(s => {
            s.isAuthenticating = false;
          });
        }
      });

      // setLoading(true);
      //  CommonStore.update(s => {
      //  s.isAuthenticating = true;
      //});
    } else {
      // support code login

      if (
        !supportCode ||
        supportCode.length !== 12 ||
        !new RegExp("[A-Z0-9]+$", "g").test(supportCode)
      ) {
        window.confirm(
          "Login failed",
          "Invalid or empty support code.",
          [
            {
              text: "OK",
              onPress: () => {
                setLoadingModal(false);
              },
            },
          ],
          { cancelable: false }
        );

        return;
      }

      NetInfo.fetch().then(async (state) => {
        // console.log("Connection type", state.type);
        // console.log("Is connected?", state.isInternetReachable);
        // console.log(state);

        if (
          // state.isInternetReachable
          true
        ) {
          try {
            let res = {};
            try {
              res = await firebase
                .auth()
                .signInAnonymously();
            } catch (error) {
              // console.log('existing email and password not found');

              setLoadingModal(false);

              // window.confirm('Login failed', "Invalid email and password", [
              //   { text: "OK", onPress: () => { } }
              // ],
              //   { cancelable: false });

              // CommonStore.update(s => {
              //   s.isAuthenticating = false;
              // });
            }

            const supportCodeSnapshot = await firebase
              .firestore()
              .collection(Collections.SupportCode)
              .where("code", "==", supportCode)
              .where("deletedAt", "==", null)
              .where("endDateTime", ">", moment().valueOf())
              .limit(1)
              .get();

            if (supportCodeSnapshot && !supportCodeSnapshot.empty) {
              const supportCodeObj = supportCodeSnapshot.docs[0].data();

              // valid support code found

              if (moment().isBefore(supportCodeObj.startDateTime)) {
                window.confirm(
                  "Login failed",
                  "This support code still haven't unlocked yet.",
                  [
                    {
                      text: "OK",
                      onPress: () => {
                        setLoadingModal(false);
                      },
                    },
                  ],
                  { cancelable: false }
                );

                CommonStore.update((s) => {
                  s.isAuthenticating = false;
                });

                return;
              }

              ////////////////////////////////////

              // 2023-08-09 - Clear everything first

              await new Promise(async resolve => {
                await AsyncStorage.getAllKeys()
                  .then(keys => AsyncStorage.multiRemove(keys))
                  .then(() => {
                    console.log('done removed');

                    resolve();
                  });
              });

              await idbClear();

              ////////////////////////////////////

              let userAnonymous = res.user;
              let firebaseToken = await userAnonymous.getIdToken();
              ApiClient.GET(
                API.getTokenForSupport +
                firebaseToken +
                "&app=" +
                APP_TYPE.MERCHANT +
                "&code=" +
                supportCode
              ).then(async (result) => {
                // setLoading(false);

                if (result && result.merchantId) {
                  if (result && result.token) {
                    Token.setToken(result.token);
                    Token.setRefreshToken(result.refreshToken);

                    await AsyncStorage.setItem("accessToken", result.token);

                    CommonStore.update(s => {
                      s.isUserActive = true;
                    });

                    ApiClient.GET(API.userAdminForSupport + supportCode).then(
                      async (userData) => {
                        CommonStore.update((s) => {
                          s.supportCodeData =
                            userData && userData.supportCode
                              ? userData.supportCode
                              : null;

                          s.paymentDetailsBoCreditSMS = null;
                          s.isPayingCreditSMS = false;

                          s.paymentDetailsBoCreditWhatsapp = null;
                          s.isPayingCreditWhatsapp = false;
                        });

                        await AsyncStorage.setItem(
                          "supportCodeData",
                          userData && userData.supportCode
                            ? JSON.stringify(userData.supportCode)
                            : ""
                        );

                        User.setUserData(userData);
                        User.setName(userData.name);
                        User.setRefreshToken(userData.refreshToken);
                        User.setUserId(userData.firebaseUid);
                        User.setlogin(true);
                        User.setMerchantId(userData.merchantId);
                        User.setOutletId(userData.outletId);

                        global.currUserRole = userData.role;

                        if (userData.role === ROLE_TYPE.ADMIN) {
                          await AsyncStorage.setItem(
                            "email",
                            userData &&
                              userData.supportCode &&
                              userData.supportCode.clientEmail
                              ? userData.supportCode.clientEmail
                              : ""
                          );
                          // await AsyncStorage.setItem(
                          //   'password',
                          //   password
                          // );

                          // await AsyncStorage.setItem('last.accessToken', result.token);

                          // await AsyncStorage.setItem(
                          //   'last.userData',
                          //   JSON.stringify(userData)
                          // );

                          // await AsyncStorage.setItem(
                          //   'last.refreshToken',
                          //   userData.refreshToken
                          // );
                        }

                        await AsyncStorage.setItem("loggedIn", "true");

                        await AsyncStorage.setItem(
                          "userData",
                          JSON.stringify(userData)
                        );

                        ////////////////////////////////////

                        await AsyncStorage.setItem(
                          "merchantId",
                          userData.merchantId
                        );

                        await AsyncStorage.setItem(
                          "refreshToken",
                          userData.refreshToken
                        );

                        await AsyncStorage.setItem("role", userData.role);

                        await AsyncStorage.setItem(
                          "firebaseUid",
                          userData.firebaseUid
                        );

                        AsyncStorage.setItem(
                          'privileges',
                          JSON.stringify(userData.privileges || [])
                        );

                        await AsyncStorage.setItem(
                          'screensToBlock',
                          JSON.stringify(userData.screensToBlock || [])
                        );

                        AsyncStorage.setItem(
                          'currOutletId',
                          userData.outletId,
                        );

                        if (userData.isAlphaUser) {
                          await AsyncStorage.setItem("isAlphaUser", "1");
                        } else {
                          await AsyncStorage.setItem("isAlphaUser", "0");
                        }

                        if (userData.isBetaUser) {
                          await AsyncStorage.setItem("isBetaUser", "1");
                        } else {
                          await AsyncStorage.setItem("isBetaUser", "0");
                        }

                        await AsyncStorage.setItem(
                          'isMasterAccount',
                          userData.isMasterAccount !== undefined ?
                            (userData.isMasterAccount ? '1' : '0') :
                            '1',
                        );

                        console.log('@@userData@@');
                        console.log(userData);

                        global.currUserRole = userData.role;

                        UserStore.update((s) => {
                          s.firebaseUid = userData.firebaseUid;
                          s.merchantId = userData.merchantId;

                          s.role = userData.role;
                          s.name = userData.name;

                          s.isAlphaUser = userData.isAlphaUser ? true : false;

                          s.isBetaUser = userData.isBetaUser ? true : false;

                          // Email login no need first
                          // s.privileges = userData.privileges;

                          s.privileges = userData.privileges;
                          s.screensToBlock = userData.screensToBlock ? userData.screensToBlock : [];

                          s.pinNo = userData.pinNo || '';

                          s.isMasterAccount = userData.isMasterAccount !== undefined ? userData.isMasterAccount : (userData.role === ROLE_TYPE.ADMIN ? true : false);
                        });

                        global.privileges_state = userData.privileges;

                        if (userData.role === ROLE_TYPE.ADMIN) {
                          global.privileges = [
                            "EMPLOYEES",
                            "OPERATION",
                            "PRODUCT",
                            "INVENTORY",
                            "INVENTORY_COMPOSITE",
                            "DOCKET",
                            "VOUCHER",
                            "PROMOTION",
                            "CRM",
                            "LOYALTY",
                            "TRANSACTIONS",
                            "REPORT",
                            "RESERVATIONS",

                            // for action
                            'REFUND_ORDER',

                            'SETTINGS',

                            'QUEUE',

                            'OPEN_CASH_DRAWER',

                            'KDS',

                            'UPSELLING',

                            // for Kitchen

                            'REJECT_ITEM',
                            'CANCEL_ORDER',
                            //'REFUND_tORDER',
                          ];
                        } else {
                          global.privileges = global.privileges_state || [];
                        }

                        MerchantStore.update((s) => {
                          s.currOutletId = userData.outletId;
                        });

                        ////////////////////////////////////

                        // ApiClient.GET(API.outlet2 + User.getOutletId()).then((result) => {
                        //   User.setName(result.name);
                        //   User.setQueueStatus(result.queueStatus);
                        //   User.setRefreshCurrentAction(result.reservationStatus);
                        //   checkLogin(true)
                        // });

                        linkTo && linkTo(`${prefix}/home`);

                        setLoadingModal(false);

                        CommonStore.update((s) => {
                          s.isAuthenticating = false;

                          s.paymentDetailsBoCreditSMS = null;
                          s.isPayingCreditSMS = false;

                          s.paymentDetailsBoCreditWhatsapp = null;
                          s.isPayingCreditWhatsapp = false;
                        });

                        // checkLogin(true);

                        // clock in
                        // let dateTime = Date.now();

                        // let body = {
                        //   employeeId: userData.firebaseUid,
                        //   loginTime: dateTime,

                        //   merchantId: userData.merchantId,
                        //   outletId: userData.outletId,
                        // }

                        // ApiClient.POST(API.updateUserClockInOut, body).then((result) => {
                        //   // console.log('updateUserClockIn', result);
                        // });

                        // switchLogin();
                      }
                    );
                  } else {
                    alert('Login failed: Invalid merchant account');

                    // alert(
                    //   "Login failed",
                    //   "Invalid merchant account",
                    //   [
                    //     {
                    //       text: "OK",
                    //       onPress: () => {
                    //         setLoadingModal(false);
                    //       },
                    //     },
                    //   ],
                    //   { cancelable: false }
                    // );

                    setLoadingModal(false);

                    CommonStore.update((s) => {
                      s.isAuthenticating = false;
                    });
                  }
                } else {
                  alert('Login failed: Invalid merchant account');

                  // alert(
                  //   "Login failed",
                  //   "Invalid merchant account",
                  //   [
                  //     {
                  //       text: "OK",
                  //       onPress: () => {
                  //         setLoadingModal(false);
                  //       },
                  //     },
                  //   ],
                  //   { cancelable: false }
                  // );

                  setLoadingModal(false);

                  CommonStore.update((s) => {
                    s.isAuthenticating = false;
                  });
                }
              });
            } else {
              alert('Login failed: No matched support code found, please try another one.');

              // alert(
              //   "Login failed",
              //   "No matched support code found, please try another one.",
              //   [
              //     {
              //       text: "OK",
              //       onPress: () => {
              //         setLoadingModal(false);
              //       },
              //     },
              //   ],
              //   { cancelable: false }
              // );

              setLoadingModal(false);

              CommonStore.update((s) => {
                s.isAuthenticating = false;
              });
            }
          } catch (error) {
            alert("Login failed: Unable to commnunicate with the server, please try again.");

            setLoadingModal(false);

            CommonStore.update((s) => {
              s.isAuthenticating = false;
            });
          }
        } else {
          alert(
            "Info, Device is offline now, please connect to the internet first."
          );

          setLoadingModal(false);

          CommonStore.update((s) => {
            s.isAuthenticating = false;
          });
        }
      });
    }
  };

  useEffect(() => {
    if (linkTo) {
      DataStore.update((s) => {
        s.linkToFunc = linkTo;
      });
    }
  }, [linkTo]);

  useEffect(() => {
    CommonStore.update((s) => {
      s.routeName = route.name;
    });
  }, [route]);

  useEffect(() => {
    const parent = props.navigation.dangerouslyGetParent();
    parent.setOptions({
      tabBarVisible: false,
    });
    return () =>
      parent.setOptions({
        tabBarVisible: true,
      });
  }, []);

  // useEffect(() => {
  //     firebase.auth().onAuthStateChanged((user) => {
  //         if (user) {
  //             // User is signed in, see docs for a list of available properties
  //             // https://firebase.google.com/docs/reference/js/firebase.User

  //             console.log('auth changed!');
  //             console.log(user);

  //             var uid = user.uid;
  //             // ...
  //         } else {
  //             // User is signed out
  //             // ...
  //         }
  //     });
  // }, []);

  // function end

  return (
    <View
      style={{
        width: isMobile() ? windowDimensions.width : windowDimensions.width,
        height: windowDimensions.height,
        ...isMobile() && {
          //marginVertical: -20,
        },
      }}
    >
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",

          width: isMobile() ? windowDimensions.width : windowDimensions.width,
          height: windowDimensions.height * 0.9,
        }}
      >
        {reset == false ? (
          <View>
            <Image
              style={{
                width: 300,
                height: 67,
                alignSelf: "center",
                marginBottom: 50,

                ...isMobile() && {
                  marginBottom: 20,
                },
              }}
              resizeMode="contain"
              source={imgLogo}
            />
            <Text
              style={[
                styles.logoTxt,
                isMobile()
                  ? {
                    transform: [{ scaleX: 0.6 }, { scaleY: 0.6 }],
                    width: windowDimensions.width,
                    textAlign: "center",
                    marginTop: 0,
                    marginTop: 20,
                    marginBottom: 30,
                    fontSize: 30,
                  }
                  : {},
              ]}
            >
              Unlimited Perks
            </Text>
            <Text
              style={[
                styles.loginTxt,
                isMobile()
                  ? {
                    fontSize: 20,
                    marginBottom: 5,
                    marginLeft: 25,
                  }
                  : {},
              ]}
            >
              Login
            </Text>
            <Text
              style={[
                styles.description,
                isMobile()
                  ? {
                    paddingVertical: 5,
                    fontSize: 12,
                    marginBottom: 15,
                    marginLeft: 25,
                  }
                  : {},
              ]}
            >
              Please login to continue
            </Text>
            {showSupportCodeLogin ? (
              <>
                <TextInput
                  editable={!isAuthenticating}
                  underlineColorAndroid={Colors.fieldtBgColor}
                  autoCapitalize="none"
                  clearButtonMode="while-editing"
                  style={[
                    styles.textInput,
                    isMobile()
                      ? {
                        marginLeft: 25,
                        marginRight: 25,
                        marginTop: 10,
                      }
                      : {},
                  ]}
                  placeholder="Support Code"
                  placeholderTextColor={"#8e8e8e"}
                  secureTextEntry={true}
                  onChangeText={(text) => {
                    setSupportCode(text.trim());
                  }}
                  value={supportCode}
                />
              </>
            ) : (
              <>
                <TextInput
                  //editable={!isAuthenticating}
                  underlineColorAndroid={Colors.fieldtBgColor}
                  clearButtonMode="while-editing"
                  autoCapitalize="none"
                  style={[
                    styles.textInput,
                    isMobile()
                      ? {
                        height: 40,
                        marginTop: 10,
                        marginLeft: 25,
                        marginRight: 25,
                      }
                      : {},
                  ]}
                  placeholder="Email"
                  placeholderTextColor={"#8e8e8e"}
                  keyboardType="email-address"
                  onChangeText={(text) => {
                    UserStore.update((s) => {
                      s.email = text.trim();
                    });
                  }}
                  value={email}
                />
                <TextInput
                  //editable={!isAuthenticating}
                  underlineColorAndroid={Colors.fieldtBgColor}
                  autoCapitalize="none"
                  clearButtonMode="while-editing"
                  style={[
                    styles.textInput,
                    isMobile()
                      ? {
                        height: 40,
                        marginTop: 20,
                        marginLeft: 25,
                        marginRight: 25,
                      }
                      : {},
                  ]}
                  placeholder="Password"
                  placeholderTextColor={"#8e8e8e"}
                  secureTextEntry={true}
                  onChangeText={(text) => {
                    setPassword(text.trim());
                  }}
                  value={password}
                />
              </>
            )}
            <TouchableOpacity
              //disabled={isAuthenticating}
              onPress={() => {
                goLogin();
                setLoadingModal(true);
                CommonStore.update((s) => {
                  s.isAuthenticating = true;
                });
              }}
            >
              <View
                style={[
                  Styles.button,
                  { marginTop: 25 },
                  isMobile()
                    ? {
                      marginTop: 27,
                      marginVertical: 10,
                      marginLeft: 25,
                      marginRight: 25,
                      justifyContent: 'center',
                      height: 55,
                    }
                    : {},
                ]}
              >
                {isAuthenticating ? (
                  <Text
                    style={[
                      { color: "#ffffff", fontSize: 18, },
                      isMobile()
                        ? {
                          fontSize: 14,
                        }
                        : {},
                    ]}
                  >
                    LOADING...
                  </Text>
                ) : (
                  <Text
                    style={[
                      { color: "#ffffff", fontSize: 18 },
                      isMobile()
                        ? {
                          fontSize: 14,
                        }
                        : {},
                    ]}
                  >
                    {loadingModal ? "LOADING..." : "LOGIN"}
                  </Text>
                )}
              </View>
            </TouchableOpacity>

            <View style={[styles.resetContainer, { marginBottom: 10 }, isMobile()
              ? {
                marginTop: 20,
              }
              : {},]}>
              <Text
                style={[
                  { color: Colors.fieldtTxtColor },
                ]}
              >
                {showSupportCodeLogin ? "Business Account" : "Supporting"}?{" "}
              </Text>
              <TouchableOpacity
                onPress={() => {
                  // switchLogin();
                  // changeToPinLogin();
                  setShowSupportCodeLogin(!showSupportCodeLogin);
                }}
              >
                <Text
                  style={[
                    { color: Colors.primaryColor },
                  ]}
                >
                  {showSupportCodeLogin ? "Email" : "Code"}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={[styles.resetContainer,
            isMobile()
              ? {
                marginTop: 7,
              }
              : {},
            ]}>
              <Text
                style={[
                  { color: Colors.fieldtTxtColor },
                ]}
              >
                Forgot Password?{" "}
              </Text>
              <TouchableOpacity
                onPress={() => {
                  setReset(!reset);
                }}
              >
                <Text
                  style={[
                    { color: Colors.primaryColor },
                  ]}
                >
                  Reset it
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <View style={{
            //alignSelf: "center", 
            //width: '65%',
            ...(isMobile() ? {
              width: '100%',
            } : {}),
          }}>
            <Image
              style={{
                width: 300,
                height: 67,
                alignSelf: "center",
                marginBottom: 50,

                ...isMobile() && {
                  marginBottom: 20,
                },
              }}
              resizeMode="contain"
              source={imgLogo}
            />
            <Text style={[
              styles.logoTxt,
              isMobile()
                ? {
                  transform: [{ scaleX: 0.6 }, { scaleY: 0.6 }],
                  width: windowDimensions.width,
                  textAlign: "center",
                  marginTop: 0,
                  marginTop: 20,
                  marginBottom: 30,
                  fontSize: 30,
                }
                : {},
            ]}>
              Unlimited Perks
            </Text>
            <Text style={[
              styles.loginTxt,
              isMobile()
                ? {
                  fontSize: 20,
                  marginLeft: 25,
                }
                : {},
            ]}>
              Reset Password
            </Text>
            <Text style={[
              styles.description,
              isMobile()
                ? {
                  fontSize: 12,
                  marginLeft: 25,
                }
                : {},
            ]}>
              The temporary password will be sent to your email
            </Text>
            <TextInput
              editable={!isAuthenticating}
              underlineColorAndroid={Colors.fieldtBgColor}
              clearButtonMode="while-editing"
              style={[styles.textInput,
              isMobile() && {
                marginLeft: 25,
                marginRight: 25,
                marginTop: 10,
              }
              ]}
              placeholder="Email"
              placeholderTextColor={"#8e8e8e"}
              keyboardType="email-address"
              onChangeText={(text) => {
                UserStore.update((s) => {
                  s.email = text.trim();
                });
              }}
              value={email}
            />
            <TouchableOpacity
              disabled={isAuthenticating}
              onPress={() => {
                goReset();
              }}
            >
              <View style={[Styles.button, { marginTop: 50 },
              isMobile() && {
                marginTop: 27,
                marginLeft: 25,
                marginRight: 25,
              }
              ]}>
                <Text style={[{ color: "#ffffff" }, { fontSize: 18 },
                isMobile() && {
                  fontSize: 14,
                }
                ]}>
                  {isAuthenticating ? "LOADING..." : "RESET"}
                </Text>
              </View>
            </TouchableOpacity>
            <View style={styles.resetContainer}>
              <Text style={{ color: Colors.fieldtTxtColor }}>
                Already have account?{" "}
              </Text>
              <TouchableOpacity
                onPress={() => {
                  setReset(!reset);
                }}
              >
                <Text style={{ color: Colors.primaryColor }}>Login</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: Dimensions.get("window").width,
    height: Dimensions.get("window").height,
  },
  headerLogo: {
    width: 112,
    height: 25,
  },
  logo: {
    width: 300,
    height: 67,
    alignSelf: "center",
    marginTop: 10,
  },
  logoTxt: {
    color: Colors.descriptionColor,
    fontSize: 20,
    letterSpacing: 7,
    marginTop: 10,
    alignSelf: "center",
    marginBottom: 40,
    fontFamily: "NunitoSans-Regular",
  },
  loginTxt: {
    color: Colors.mainTxtColor,
    // fontWeight: "500",
    fontSize: 26,
    fontFamily: "NunitoSans-Bold",
  },
  description: {
    color: Colors.descriptionColor,
    paddingVertical: 10,
    fontFamily: "NunitoSans-Regular",
    fontSize: 16,
    marginBottom: 5,
    marginTop: -5,
  },
  textInput: {
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 8,
    marginTop: 20,
    fontFamily: "NunitoSans-Regular",
    fontSize: 16,
  },
  checkBox: {
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: Colors.descriptionColor,

    width: 30,
    height: 10,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",

    // marginRight: 5,
  },
  floatbtn: {
    zIndex: 1,
    position: "absolute",
    bottom: 30,
    right: 30,
    width: 60,
    height: 60,
    borderRadius: 60 / 2,
    backgroundColor: Colors.primaryColor,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  loginImg: {
    width: undefined,
    height: "100%",
    resizeMode: "cover",
  },
  resetContainer: {
    alignItems: "center",
    flexDirection: "row",
    marginTop: 0,
    alignSelf: "center",
  },

  switchContainer: {
    position: "absolute",
    bottom: 0,
    display: "flex",
    alignItems: "center",
    width: "100%",
  },

  centerText: {
    padding: 10,
    fontSize: 18,
    color: Colors.descriptionColor,
    textAlign: "center",
    fontFamily: "NunitoSans-Regular",
  },
  scanText: {
    fontSize: 20,
    color: "#000000",
    textAlign: "center",
    backgroundColor: "#008000",
  },
});

export default LoginScreen;
