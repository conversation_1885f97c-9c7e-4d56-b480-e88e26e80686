import React, { Component, useState, useEffect } from 'react';
import {
    View,
    Text,
    Platform,
    Dimensions,
    DevSettings,
    Alert,
    PermissionsAndroid,
    NativeEventEmitter,
    DeviceEventEmitter,
} from 'react-native';
import firebase from 'firebase/app';
// import firestore from '@react-native-firebase/firestore';
// import database from '@react-native-firebase/database';
// import storage from '@react-native-firebase/storage';
// import auth from '@react-native-firebase/auth';
import { Collections } from '../constant/firebase';
import { UserStore } from '../store/userStore';
import * as User from "./User";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import {
    OUTLET_SHIFT_STATUS,
    ROLE_TYPE,
    USER_ORDER_STATUS,
    USER_QUEUE_STATUS,
    USER_RESERVATION_STATUS,
    USER_RING_STATUS,
    REPORT_SORT_FIELD_TYPE_COMPARE,
    REPORT_SORT_FIELD_TYPE_VALUE,
    REPORT_SORT_COMPARE_OPERATOR,
    REPORT_SORT_FIELD_TYPE,
    ORDER_TYPE,
    ACCUMULATOR_ID,
    EMAIL_REPORT_TYPE,
    PAYMENT_SORT_FIELD_TYPE,
    PAYMENT_SORT_FIELD_TYPE_VALUE,
    PAYMENT_SORT_COMPARE_OPERATOR,
    PAYMENT_SORT_FIELD_TYPE_COMPARE,
    TIMEZONE,
    KD_PRINT_EVENT_TYPE,
    KD_PRINT_VARIATION,
    KD_FONT_SIZE,
    PRODUCT_PRICE_TYPE,
    UNIT_TYPE,
    DEFAULT_CRM_TAG,
    ORDER_TYPE_DETAILS,
    UNIT_TYPE_SHORT,
    APP_TYPE,
    PAYMENT_CHANNEL_NAME_PARSED,
    PRINTER_USER_PRIORITY,
    ORDER_TYPE_SUB,
    CHARGES_TYPE,
    REPORT_DISPLAY_TYPE,
    DATE_COMPARE_TYPE,
    KD_ITEM_STATUS,
    ORDER_TYPE_PARSED,
} from '../constant/common';
import { APPLY_DISCOUNT_PER, APPLY_DISCOUNT_TYPE, PROMOTION_TYPE_VARIATION } from '../constant/promotions';
import { CommonStore } from '../store/commonStore';
import API from '../constant/API';
import moment from 'moment';
// import messaging from '@react-native-firebase/messaging';
// import RNFetchBlob from 'rn-fetch-blob';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
// const RNFS = require('react-native-fs');
import XLSX from 'xlsx';
import ApiClient from './ApiClient';
// const { nanoid } = require('nanoid');
// const pixelWidth = require('string-pixel-width');
import { decode } from 'base64-arraybuffer';
import AWS from 'aws-sdk';
import { awsBucket, awsId, awsSecret } from '../constant/env';
import { connectToPrinter, printDocketForKD, printKDSummaryCategoryWrapper, printUserOrder } from './printer';
import { ESCPOS_CMD, PRINTER_TASK_TYPE_PARSED, PRINTER_USAGE_TYPE } from '../constant/printer';
// import * as ScopedStorage from "react-native-scoped-storage"
// import analytics from '@react-native-firebase/analytics';
// import { isTablet as isTabletOriginal } from 'react-native-device-detection';

import { customAlphabet } from 'nanoid';
// import { storageMMKV } from './storageMMKV';
import { KD_OPTIONS_DELIVER_REJECT, PRINTER_PAPER_WIDTH } from '../constant/printer';
import BigNumber from 'bignumber.js';
// import { FileLogger } from "react-native-file-logger";
// import { Q } from '@nozbe/watermelondb';
import { EI_ERROR_CODE, EI_STATUS } from '../constant/einvoice';
import { getOrderDiscountInfo, getOrderDiscountInfoInclOrderBased, sendOrderReceiptEmail } from './common';

const alphabetEmailAddress = 'abcdefghijklmnopqrstuvwxyz0123456789';
const randomEmailAddress = customAlphabet(alphabetEmailAddress, 10);

const randomUserIdHuman = customAlphabet(alphabetEmailAddress, 7);

export const eInvoiceSubmitDocumentByUserOrder = async (params) => {
    let {
        userOrder,
        userOrderId,

        rptId,

        currOutlet,
    } = params;

    if (userOrder && userOrder.uniqueId) {
        // no need retrieve the order data
    }
    else {
        const userOrderSnapshot = await firebase.firestore()
            .collection(Collections.UserOrder)
            .where('uniqueId', '==', userOrderId)
            .limit(1)
            .get();

        if (!userOrderSnapshot.empty) {
            userOrder = userOrderSnapshot.docs[0].data();
        }
    }

    //////////////////////////////////////////////

    // inner functions

    const eInvoiceSubmitDocumentByUserOrderInnerFunc = async () => {
        if (userOrder && (
            userOrder.epStateTo &&
            userOrder.epNameTo &&
            userOrder.epPhoneTo &&
            userOrder.epAddr1To &&
            userOrder.epCityTo &&
            userOrder.epCodeTo &&
            userOrder.userEmailSecond &&
            userOrder.userTin &&
            userOrder.userEiIdType &&
            userOrder.userEiId &&
            currOutlet.eiTin &&
            currOutlet.eiId &&
            currOutlet.eiIdType &&
            currOutlet.eiRegName &&
            currOutlet.eiPhone &&
            currOutlet.eiEmail &&
            currOutlet.epAddr1From &&
            currOutlet.epCityFrom &&
            currOutlet.epCodeFrom &&
            currOutlet.epStateFrom
        )) {
            // valid, can continue
        }
        else {
            Alert.alert('Info', `Insufficient data required for e-Invoice submission, please choose the 'Paid By User' field, to have the following info available:\n\nAddress\nEmail\nTIN\nIC/Passport/MyTentera\n\nMerchant TIN\nMerchant ID\nMerchant ID Type\nMerchant's Company Registration Name\nMerchant Phone\nMerchant Email\nMerchant Address\nMerchant City\nMerchant Postcode\nMerchant State`);

            CommonStore.update((s) => {
                s.isLoading = false;
            });

            return;
        }

        let body = {
            tin: userOrder.userTin,
            idType: userOrder.userEiIdType,
            idValue: userOrder.userEiId,

            eiTin: currOutlet.eiTin,
            eiId: currOutlet.eiId,
            eiIdType: currOutlet.eiIdType,

            accessToken: currOutlet.eiAccessToken,
            expiryDate: currOutlet.eiExpiryDate,

            outletId: currOutlet.uniqueId,
        };

        ApiClient.POST(API.eInvoiceValidateTaxpayer, body, false).then(
            (result) => {
                console.log('result');
                console.log(result);

                if (result && result.status === 'success') {
                    // valid, can proceed

                    let body = {
                        userOrder: userOrder,

                        eiTin: currOutlet.eiTin,
                        eiId: currOutlet.eiId,
                        eiIdType: currOutlet.eiIdType,

                        accessToken: currOutlet.eiAccessToken,
                        expiryDate: currOutlet.eiExpiryDate,

                        rptId: rptId ? rptId : '',

                        outletId: currOutlet.uniqueId,
                        // merchantId: currOutlet.merchantId,
                    };

                    ApiClient.POST(API.eInvoiceSubmitDocument, body, false).then(
                        (result) => {
                            console.log('result');
                            console.log(result);

                            let error = null;
                            let errorMessage2 = '';
                            if (result && result.data && result.data.rejectedDocuments &&
                                result.data.rejectedDocuments[0] &&
                                result.data.rejectedDocuments[0].error) {
                                error = result.data.rejectedDocuments[0].error;

                                if (result.data.rejectedDocuments[0].error.details &&
                                    result.data.rejectedDocuments[0].error.details.length > 0) {
                                    errorMessage2 = result.data.rejectedDocuments[0].error.details.map(detailObj => detailObj.message ? detailObj.message : '').join(', ');
                                }
                            }

                            let errorMessage = '';
                            if (result && result.data && result.data.error &&
                                result.data.error.message) {
                                errorMessage = result.data.error.message;
                            }

                            let errorMessageCombined = '';
                            if (errorMessage) {
                                errorMessageCombined += `\n\n${errorMessage}`;
                            }
                            if (errorMessage2) {
                                errorMessageCombined += `\n${errorMessage2}`;
                            }

                            if (result && result.status === 'success') {
                                // valid, can proceed

                                if (result.resStatus === 200) {
                                    Alert.alert('Info', `Order has been submitted successfully.${errorMessageCombined}`);
                                }
                                else if (result.resStatus === 202) {
                                    Alert.alert('Info', `Order has been submitted successfully. (There might be a short delay for the validation)${errorMessageCombined}`);
                                }

                                ////////////////////////////////////////

                                // get document and send email together

                                if (result && result.data && result.data.acceptedDocuments &&
                                    result.data.acceptedDocuments[0] && result.data.acceptedDocuments[0].uuid) {
                                    console.log('submitted, now wait for 5 seconds to get details');

                                    let eiDocId = result.data.acceptedDocuments[0].uuid;

                                    setTimeout(() => {
                                        let body = {
                                            // userOrder: userOrder,
                                            userOrderId: userOrder.uniqueId,

                                            eiDocId: eiDocId,

                                            eiTin: currOutlet.eiTin,
                                            eiId: currOutlet.eiId,
                                            eiIdType: currOutlet.eiIdType,

                                            accessToken: currOutlet.eiAccessToken,
                                            expiryDate: currOutlet.eiExpiryDate,

                                            rptId: rptId ? rptId : '',

                                            outletId: currOutlet.uniqueId,
                                            merchantId: currOutlet.merchantId,
                                        };

                                        ApiClient.POST(API.eInvoiceGetDocumentDetails, body, false).then(
                                            (result) => {
                                                console.log('result');
                                                console.log(result);

                                                let errorMessageCombined = '';
                                                if (result && result.data && result.data.validationResults &&
                                                    result.data.validationResults.status === 'Invalid') {
                                                    errorMessageCombined += `\n\n${result.data.validationResults.status}`;
                                                    if (result.data.validationResults.validationSteps && result.data.validationResults.validationSteps.length > 0) {
                                                        errorMessageCombined += '\n';
                                                        errorMessageCombined += result.data.validationResults.validationSteps
                                                            .filter(step => step.status !== 'Valid' && step.error)
                                                            .map(step => {
                                                                let stepError = '';
                                                                if (step.error.error) {
                                                                    stepError += `${step.error.error}, `;
                                                                }
                                                                if (step.error.errorMs) {
                                                                    stepError += `${step.error.errorMs}, `;
                                                                }
                                                                if (step.error.innerError && step.error.innerError[0] &&
                                                                    step.error.innerError[0].error) {
                                                                    stepError += `${step.error.innerError[0].error}`;
                                                                }

                                                                return stepError;
                                                            })
                                                            .join('\n');
                                                    }
                                                }

                                                if (result && result.status === 'success') {
                                                    // valid, can proceed

                                                    if (result.resStatus === 200) {
                                                        // Alert.alert('Info', 'Invoice has been synced successfully.');
                                                    }

                                                    if (errorMessageCombined.length > 0) {
                                                        Alert.alert('Info', `Failed to sync the invoice from LHDN server, please try again later.${errorMessageCombined}`);
                                                    }

                                                    ///////////////////////////////////////////

                                                    // can proceed

                                                    setTimeout(async () => {
                                                        const toSentEmail = userOrder.userEmailSecond;

                                                        const crmUserSnapshot = await firebase.firestore()
                                                            .collection(Collections.CRMUser)
                                                            .where('number', '==', userOrder.userPhone)
                                                            .where('outletId', '==', userOrder.outletId)
                                                            .limit(1)
                                                            .get();

                                                        var crmUser = null;
                                                        if (!crmUserSnapshot.empty) {
                                                            crmUser = crmUserSnapshot.docs[0].data();
                                                        }

                                                        if (crmUser) {
                                                            if (toSentEmail && toSentEmail.length > 0 && toSentEmail.includes('@')) {
                                                                sendOrderReceiptEmail(userOrder.uniqueId, toSentEmail, crmUser);
                                                            }
                                                        }
                                                    }, currOutlet.eiSrWt ? currOutlet.eiSrWt : 3000); // wait time to send receipt, default is 3 seconds

                                                    ///////////////////////////////////////////
                                                } else {
                                                    Alert.alert('Info', `Failed to sync the invoice from LHDN server, please try again later.${errorMessageCombined}`);
                                                }

                                                CommonStore.update((s) => {
                                                    s.isLoading = false;
                                                });

                                                return;
                                            },
                                        );
                                    }, currOutlet.eiWt ? currOutlet.eiWt : 8000); // wait time to get document details after submission (default is 5 seconds)
                                }

                                ////////////////////////////////////////
                            } else {
                                if (result) {
                                    console.log(result);

                                    if (error && error.code) {
                                        if (result.resStatus === 400 && error.code === EI_ERROR_CODE.BAD_STRUCTURE) {
                                            Alert.alert('Info', `Invalid document structure.${errorMessageCombined}`);
                                        }
                                        else if (result.resStatus === 400 && error.code === EI_ERROR_CODE.MAXIMUM_SIZE_EXCEEDED) {
                                            Alert.alert('Info', `Maximum document size is exceeded.${errorMessageCombined}`);
                                        }
                                        else if (result.resStatus === 403 && error.code === EI_ERROR_CODE.INCORRECT_SUBMITTER) {
                                            Alert.alert('Info', `Incorrect submitter.${errorMessageCombined}`);
                                        }
                                        else if (result.resStatus === 422 && error.code === EI_ERROR_CODE.DUPLICATE_SUBMISSION) {
                                            Alert.alert('Info', `Duplicated submission.${errorMessageCombined}`);
                                        }
                                        else {
                                            Alert.alert('Info', `An error occurred during the e-Invoice submission.\n\nHTTP Status Code: ${result.resStatus}\nError Code: ${error.code}\nError Message: ${errorMessage ? errorMessage : 'N/A'}\n${errorMessage2 ? errorMessage2 : ''}`);
                                        }
                                    }
                                    else {
                                        if (result && result.data && typeof result.data.error === 'string') {
                                            Alert.alert('Info', `An error occurred during the e-Invoice submission.\n\n${result.data.error}${errorMessageCombined}`);
                                        }
                                        else if (result && result.data && result.data.error && typeof result.data.error.code === 'string') {
                                            Alert.alert('Info', `An error occurred during the e-Invoice submission.\n\n${result.data.error.code}${result.data.error.message ? `\n${result.data.error.message}` : ``}`);
                                        }
                                        else if (result.resStatus && result.resStatusText) {
                                            Alert.alert('Info', `An error occurred during the e-Invoice submission.\n\nHTTP Status Code: ${result.resStatus}\nMessage: ${result.resStatusText}${errorMessageCombined}`);
                                        }
                                        else {
                                            Alert.alert('Info', `An error occurred during the e-Invoice submission.${errorMessageCombined}`);
                                        }
                                    }
                                }
                                else {
                                    Alert.alert('Info', `An error occurred during the e-Invoice submission.`);
                                }
                            }

                            CommonStore.update((s) => {
                                s.isLoading = false;
                            });

                            return;
                        },
                    );
                } else {
                    if (result.resStatus === 400) {
                        Alert.alert('Info', 'Invalid Tax Identification Number.');
                    }
                    else if (result.resStatus === 404) {
                        Alert.alert('Info', 'Unmatched Tax Identification Number with ID.');
                    }
                    else if (result.resStatus === 429) {
                        let errorMsg = result.message ? result.message : 'Rate limit is exceeded, please try again later.';
                        if (result.resStatusText) {
                            errorMsg.push(`\n${result.resStatusText}`);
                        }
                        Alert.alert('Info', errorMsg);
                    }
                    else {
                        Alert.alert('Info', 'An error occurred during the Tax Identification Number validation process.');
                    }
                }

                CommonStore.update((s) => {
                    s.isLoading = false;
                });

                return;
            },
        );
    };

    //////////////////////////////////////////////

    if (userOrder && userOrder.eiDocId) {
        // means this order is already submitted

        Alert.alert('Info', `This order is already submitted to the e-Invoice system, do you want to submit as invoice again?`,
            [
                {
                    text: 'Yes',
                    onPress: async () => {
                        // proceed normally

                        eInvoiceSubmitDocumentByUserOrderInnerFunc();
                    },
                },
                {
                    text: 'No',
                    onPress: () => {
                        CommonStore.update((s) => {
                            s.isLoading = false;
                        });

                        return;
                    },
                },
            ],
            { cancelable: true },
        );
    }
    else {
        // can proceed

        eInvoiceSubmitDocumentByUserOrderInnerFunc();
    }
};

export const eInvoiceGetDocumentDetailsByUserOrder = async (params) => {
    let {
        userOrder,
        userOrderId,

        rptId,

        currOutlet,
    } = params;

    if (userOrder && userOrder.uniqueId) {
        // no need retrieve the order data
    }
    else {
        const userOrderSnapshot = await firebase.firestore()
            .collection(Collections.UserOrder)
            .where('uniqueId', '==', userOrderId)
            .limit(1)
            .get();

        if (!userOrderSnapshot.empty) {
            userOrder = userOrderSnapshot.docs[0].data();
        }
    }

    if (userOrder && userOrder.eiDocId) {
        // can proceed
    }
    else {
        // means this order haven't submitted

        Alert.alert('Info', `Please submit as e-Invoice first.`);

        CommonStore.update((s) => {
            s.isLoading = false;
        });

        return;
    }

    if (userOrder && (
        userOrder.epStateTo &&
        userOrder.epNameTo &&
        userOrder.epPhoneTo &&
        userOrder.epAddr1To &&
        userOrder.epCityTo &&
        userOrder.epCodeTo &&
        userOrder.userEmailSecond &&
        userOrder.userTin &&
        userOrder.userEiIdType &&
        userOrder.userEiId &&
        currOutlet.eiTin &&
        currOutlet.eiId &&
        currOutlet.eiIdType &&
        currOutlet.eiRegName &&
        currOutlet.eiPhone &&
        currOutlet.eiEmail &&
        currOutlet.epAddr1From &&
        currOutlet.epCityFrom &&
        currOutlet.epCodeFrom &&
        currOutlet.epStateFrom
    )) {
        // valid, can continue
    }
    else {
        Alert.alert('Info', `Insufficient data required for e-Invoice submission, please choose the 'Paid By User' field, to have the following info available:\n\nAddress\nEmail\nTIN\nIC/Passport/MyTentera\n\nMerchant TIN\nMerchant ID\nMerchant ID Type\nMerchant's Company Registration Name\nMerchant Phone\nMerchant Email\nMerchant Address\nMerchant City\nMerchant Postcode\nMerchant State`);

        CommonStore.update((s) => {
            s.isLoading = false;
        });

        return;
    }

    let body = {
        tin: userOrder.userTin,
        idType: userOrder.userEiIdType,
        idValue: userOrder.userEiId,

        eiTin: currOutlet.eiTin,
        eiId: currOutlet.eiId,
        eiIdType: currOutlet.eiIdType,

        accessToken: currOutlet.eiAccessToken,
        expiryDate: currOutlet.eiExpiryDate,

        outletId: currOutlet.uniqueId,
    };

    ApiClient.POST(API.eInvoiceValidateTaxpayer, body, false).then(
        (result) => {
            console.log('result');
            console.log(result);

            if (result && result.status === 'success') {
                // valid, can proceed

                let body = {
                    userOrder: userOrder,
                    userOrderId: userOrder.uniqueId,

                    eiDocId: userOrder.eiDocId,

                    eiTin: currOutlet.eiTin,
                    eiId: currOutlet.eiId,
                    eiIdType: currOutlet.eiIdType,

                    accessToken: currOutlet.eiAccessToken,
                    expiryDate: currOutlet.eiExpiryDate,

                    rptId: rptId ? rptId : '',

                    outletId: currOutlet.uniqueId,
                    merchantId: currOutlet.merchantId,
                };

                ApiClient.POST(API.eInvoiceGetDocumentDetails, body, false).then(
                    (result) => {
                        console.log('result');
                        console.log(result);

                        let errorMessageCombined = '';
                        if (result && result.data && result.data.validationResults &&
                            result.data.validationResults.status === 'Invalid') {
                            errorMessageCombined += `\n\n${result.data.validationResults.status}`;
                            if (result.data.validationResults.validationSteps && result.data.validationResults.validationSteps.length > 0) {
                                errorMessageCombined += '\n';
                                errorMessageCombined += result.data.validationResults.validationSteps
                                    .filter(step => step.status !== 'Valid' && step.error)
                                    .map(step => {
                                        let stepError = '';
                                        if (step.error.error) {
                                            stepError += `${step.error.error}, `;
                                        }
                                        if (step.error.errorMs) {
                                            stepError += `${step.error.errorMs}, `;
                                        }
                                        if (step.error.innerError && step.error.innerError[0] &&
                                            step.error.innerError[0].error) {
                                            stepError += `${step.error.innerError[0].error}`;
                                        }

                                        return stepError;
                                    })
                                    .join('\n');
                            }
                        }

                        if (result && result.status === 'success') {
                            // valid, can proceed

                            if (result.resStatus === 200) {
                                if (result.userOrderData &&
                                    result.userOrderData.eiStatusV === EI_STATUS.VALID) {
                                    Alert.alert('Info', 'Invoice has been synced successfully.');
                                }
                                else {
                                    Alert.alert('Info', 'Invoice has been synced, but the document is still under validation process, the QR URL will be available at later time.');
                                }

                                if (errorMessageCombined.length > 0) {
                                    Alert.alert('Info', `Failed to sync the invoice from LHDN server, please try again later.${errorMessageCombined}`);
                                }
                            }
                        } else {
                            Alert.alert('Info', `Failed to sync the invoice from LHDN server, please try again later.${errorMessageCombined}`);
                        }

                        CommonStore.update((s) => {
                            s.isLoading = false;
                        });

                        return;
                    },
                );
            } else {
                Alert.alert('Info', 'Failed to sync the invoice from LHDN server, please try again later.');
            }

            CommonStore.update((s) => {
                s.isLoading = false;
            });

            return;
        },
    );
};

export const eInvoiceCancelDocumentDetailsByUserOrder = async (params) => {
    let {
        userOrder,
        userOrderId,

        eiCancelReason,

        rptId,

        currOutlet,
    } = params;

    if (userOrder && userOrder.uniqueId) {
        // no need retrieve the order data
    }
    else {
        const userOrderSnapshot = await firebase.firestore()
            .collection(Collections.UserOrder)
            .where('uniqueId', '==', userOrderId)
            .limit(1)
            .get();

        if (!userOrderSnapshot.empty) {
            userOrder = userOrderSnapshot.docs[0].data();
        }
    }

    if (userOrder && userOrder.eiDocId) {
        // can proceed
    }
    else {
        // means this order haven't submitted

        Alert.alert('Info', `Please submit as e-Invoice first.`);

        CommonStore.update((s) => {
            s.isLoading = false;
        });

        return;
    }

    if (userOrder && (
        userOrder.epStateTo &&
        userOrder.epNameTo &&
        userOrder.epPhoneTo &&
        userOrder.epAddr1To &&
        userOrder.epCityTo &&
        userOrder.epCodeTo &&
        userOrder.userEmailSecond &&
        userOrder.userTin &&
        userOrder.userEiIdType &&
        userOrder.userEiId &&
        currOutlet.eiTin &&
        currOutlet.eiId &&
        currOutlet.eiIdType &&
        currOutlet.eiRegName &&
        currOutlet.eiPhone &&
        currOutlet.eiEmail &&
        currOutlet.epAddr1From &&
        currOutlet.epCityFrom &&
        currOutlet.epCodeFrom &&
        currOutlet.epStateFrom
    )) {
        // valid, can continue
    }
    else {
        Alert.alert('Info', `Insufficient data required for e-Invoice submission, please choose the 'Paid By User' field, to have the following info available:\n\nAddress\nEmail\nTIN\nIC/Passport/MyTentera\n\nMerchant TIN\nMerchant ID\nMerchant ID Type\nMerchant's Company Registration Name\nMerchant Phone\nMerchant Email\nMerchant Address\nMerchant City\nMerchant Postcode\nMerchant State`);

        CommonStore.update((s) => {
            s.isLoading = false;
        });

        return;
    }

    let body = {
        tin: userOrder.userTin,
        idType: userOrder.userEiIdType,
        idValue: userOrder.userEiId,

        eiTin: currOutlet.eiTin,
        eiId: currOutlet.eiId,
        eiIdType: currOutlet.eiIdType,

        accessToken: currOutlet.eiAccessToken,
        expiryDate: currOutlet.eiExpiryDate,

        outletId: currOutlet.uniqueId,
    };

    ApiClient.POST(API.eInvoiceValidateTaxpayer, body, false).then(
        (result) => {
            console.log('result');
            console.log(result);

            if (result && result.status === 'success') {
                // valid, can proceed

                let body = {
                    userOrder: userOrder,
                    userOrderId: userOrder.uniqueId,

                    eiDocId: userOrder.eiDocId,
                    eiCancelReason: eiCancelReason ? eiCancelReason : '',

                    eiTin: currOutlet.eiTin,
                    eiId: currOutlet.eiId,
                    eiIdType: currOutlet.eiIdType,

                    accessToken: currOutlet.eiAccessToken,
                    expiryDate: currOutlet.eiExpiryDate,

                    rptId: rptId ? rptId : '',

                    outletId: currOutlet.uniqueId,
                    merchantId: currOutlet.merchantId,
                };

                ApiClient.POST(API.eInvoiceCancelDocument, body, false).then(
                    (result) => {
                        console.log('result');
                        console.log(result);

                        if (result && result.status === 'success') {
                            // valid, can proceed

                            if (result.resStatus === 200) {
                                Alert.alert('Info', 'Invoice has been cancelled successfully.');
                            }
                        } else {
                            let errorMessage = '';
                            let errorMessage2 = '';
                            if (result && result.data && result.data.error &&
                                result.data.error.message) {
                                errorMessage = result.data.error.message;

                                if (result.data.error.details &&
                                    result.data.error.details[0] &&
                                    result.data.error.details[0].message) {
                                    errorMessage2 = result.data.error.details[0].message;
                                }
                            }

                            Alert.alert('Info', `Failed to cancel the invoice, please try again later.${errorMessage ? `\n\n${errorMessage}` : ''}\n${errorMessage2 ? errorMessage2 : ''}`);
                        }

                        CommonStore.update((s) => {
                            s.isLoading = false;
                        });

                        return;
                    },
                );
            } else {
                Alert.alert('Info', 'Failed to cancel the invoice, please try again later.');
            }

            CommonStore.update((s) => {
                s.isLoading = false;
            });

            return;
        },
    );
};

export const eInvoiceProceedAfterPaidUserOrder = async (params) => {
    let {
        userOrder,
        userOrderId,
        currOutlet,

        toSentEmail,
        currToPaidUser,
    } = params;

    if (userOrder && userOrder.uniqueId) {
        // no need retrieve the order data
    }
    else {
        const userOrderSnapshot = await firebase.firestore()
            .collection(Collections.UserOrder)
            .where('uniqueId', '==', userOrderId)
            .limit(1)
            .get();

        if (!userOrderSnapshot.empty) {
            userOrder = userOrderSnapshot.docs[0].data();
        }
    }

    if (userOrder && userOrder.eiDocId) {
        // means this order is already submitted

        Alert.alert('Info', `This order is already submitted to the e-Invoice system.`);

        CommonStore.update((s) => {
            s.isLoading = false;
        });

        return;
    }
    else {
        // can proceed
    }

    if (userOrder && (
        userOrder.epStateTo &&
        userOrder.epNameTo &&
        userOrder.epPhoneTo &&
        userOrder.epAddr1To &&
        userOrder.epCityTo &&
        userOrder.epCodeTo &&
        userOrder.userEmailSecond &&
        userOrder.userTin &&
        userOrder.userEiIdType &&
        userOrder.userEiId &&
        currOutlet.eiTin &&
        currOutlet.eiId &&
        currOutlet.eiIdType &&
        currOutlet.eiRegName &&
        currOutlet.eiPhone &&
        currOutlet.eiEmail &&
        currOutlet.epAddr1From &&
        currOutlet.epCityFrom &&
        currOutlet.epCodeFrom &&
        currOutlet.epStateFrom
    )) {
        // valid, can continue
    }
    else {
        Alert.alert('Info', `Insufficient data required for e-Invoice submission, please choose the 'Paid By User' field, to have the following info available:\n\nAddress\nEmail\nTIN\nIC/Passport/MyTentera\n\nMerchant TIN\nMerchant ID\nMerchant ID Type\nMerchant's Company Registration Name\nMerchant Phone\nMerchant Email\nMerchant Address\nMerchant City\nMerchant Postcode\nMerchant State`);

        CommonStore.update((s) => {
            s.isLoading = false;
        });

        return;
    }

    let body = {
        tin: userOrder.userTin,
        idType: userOrder.userEiIdType,
        idValue: userOrder.userEiId,

        eiTin: currOutlet.eiTin,
        eiId: currOutlet.eiId,
        eiIdType: currOutlet.eiIdType,

        accessToken: currOutlet.eiAccessToken,
        expiryDate: currOutlet.eiExpiryDate,

        outletId: currOutlet.uniqueId,
    };

    ApiClient.POST(API.eInvoiceValidateTaxpayer, body, false).then(
        (result) => {
            console.log('result');
            console.log(result);

            if (result && result.status === 'success') {
                // valid, can proceed

                let body = {
                    userOrder: userOrder,

                    eiTin: currOutlet.eiTin,
                    eiId: currOutlet.eiId,
                    eiIdType: currOutlet.eiIdType,

                    accessToken: currOutlet.eiAccessToken,
                    expiryDate: currOutlet.eiExpiryDate,

                    outletId: currOutlet.uniqueId,
                    // merchantId: currOutlet.merchantId,
                };

                ApiClient.POST(API.eInvoiceSubmitDocument, body, false).then(
                    (result) => {
                        console.log('result');
                        console.log(result);

                        let error = null;
                        let errorMessage2 = '';
                        if (result && result.data && result.data.rejectedDocuments &&
                            result.data.rejectedDocuments[0] &&
                            result.data.rejectedDocuments[0].error) {
                            error = result.data.rejectedDocuments[0].error;

                            if (result.data.rejectedDocuments[0].error.details &&
                                result.data.rejectedDocuments[0].error.details.length > 0) {
                                errorMessage2 = result.data.rejectedDocuments[0].error.details.map(detailObj => detailObj.message ? detailObj.message : '').join(', ');
                            }
                        }

                        let errorMessage = '';
                        if (result && result.data && result.data.error &&
                            result.data.error.message) {
                            errorMessage = result.data.error.message;
                        }

                        let errorMessageCombined = '';
                        if (errorMessage) {
                            errorMessageCombined += `\n\n${errorMessage}`;
                        }
                        if (errorMessage2) {
                            errorMessageCombined += `\n${errorMessage2}`;
                        }

                        if (result && result.status === 'success') {
                            // valid, can proceed

                            if (result.resStatus === 200) {
                                // Alert.alert('Info', 'Order has been submitted successfully.');
                            }
                            else if (result.resStatus === 202) {
                                // Alert.alert('Info', 'Order has been submitted successfully. (There might be a short delay for the validation)');
                            }

                            ////////////////////////////////////////

                            // continue

                            if (result && result.data && result.data.acceptedDocuments &&
                                result.data.acceptedDocuments[0] && result.data.acceptedDocuments[0].uuid) {
                                console.log('submitted, now wait for 5 seconds to get details');

                                let eiDocId = result.data.acceptedDocuments[0].uuid;

                                setTimeout(() => {
                                    let body = {
                                        // userOrder: userOrder,
                                        userOrderId: userOrder.uniqueId,

                                        eiDocId: eiDocId,

                                        eiTin: currOutlet.eiTin,
                                        eiId: currOutlet.eiId,
                                        eiIdType: currOutlet.eiIdType,

                                        accessToken: currOutlet.eiAccessToken,
                                        expiryDate: currOutlet.eiExpiryDate,

                                        outletId: currOutlet.uniqueId,
                                        merchantId: currOutlet.merchantId,
                                    };

                                    ApiClient.POST(API.eInvoiceGetDocumentDetails, body, false).then(
                                        (result) => {
                                            console.log('result');
                                            console.log(result);

                                            let errorMessageCombined = '';
                                            if (result && result.data && result.data.validationResults &&
                                                result.data.validationResults.status === 'Invalid') {
                                                errorMessageCombined += `\n\n${result.data.validationResults.status}`;
                                                if (result.data.validationResults.validationSteps && result.data.validationResults.validationSteps.length > 0) {
                                                    errorMessageCombined += '\n';
                                                    errorMessageCombined += result.data.validationResults.validationSteps
                                                        .filter(step => step.status !== 'Valid' && step.error)
                                                        .map(step => {
                                                            let stepError = '';
                                                            if (step.error.error) {
                                                                stepError += `${step.error.error}, `;
                                                            }
                                                            if (step.error.errorMs) {
                                                                stepError += `${step.error.errorMs}, `;
                                                            }
                                                            if (step.error.innerError && step.error.innerError[0] &&
                                                                step.error.innerError[0].error) {
                                                                stepError += `${step.error.innerError[0].error}`;
                                                            }

                                                            return stepError;
                                                        })
                                                        .join('\n');
                                                }
                                            }

                                            if (result && result.status === 'success') {
                                                // valid, can proceed

                                                if (result.resStatus === 200) {
                                                    // Alert.alert('Info', 'Invoice has been synced successfully.');
                                                }

                                                if (errorMessageCombined.length > 0) {
                                                    Alert.alert('Info', `Failed to sync the invoice from LHDN server, please try again later.${errorMessageCombined}`);
                                                }

                                                ///////////////////////////////////////////

                                                // can proceed

                                                setTimeout(() => {
                                                    if (toSentEmail.length > 0 && toSentEmail.includes('@')) {
                                                        sendOrderReceiptEmail(userOrder.uniqueId, toSentEmail, currToPaidUser);
                                                    }
                                                }, currOutlet.eiSrWt ? currOutlet.eiSrWt : 3000); // wait time to send receipt, default is 3 seconds

                                                ///////////////////////////////////////////
                                            } else {
                                                let errorMessage = '';
                                                let errorMessage2 = '';
                                                if (result && result.data && result.data.error &&
                                                    result.data.error.message) {
                                                    errorMessage = result.data.error.message;

                                                    if (result.data.error.details &&
                                                        result.data.error.details[0] &&
                                                        result.data.error.details[0].message) {
                                                        errorMessage2 = result.data.error.details[0].message;
                                                    }
                                                }

                                                Alert.alert('Info', `Failed to sync the invoice from LHDN server, please try again later.\n\nError Message: ${errorMessage ? errorMessage : 'N/A'}\n${errorMessage2 ? errorMessage2 : ''}${errorMessageCombined}`);
                                            }

                                            CommonStore.update((s) => {
                                                s.isLoading = false;
                                            });

                                            return;
                                        },
                                    );
                                }, currOutlet.eiWt ? currOutlet.eiWt : 8000); // wait time to get document details after submission (default is 5 seconds)
                            }
                            else {
                                Alert.alert('Info', 'Failed to submit the e-Invoice;');
                            }

                            ////////////////////////////////////////
                        } else {
                            if (result) {
                                console.log(result);

                                if (error && error.code) {
                                    if (result.resStatus === 400 && error.code === EI_ERROR_CODE.BAD_STRUCTURE) {
                                        Alert.alert('Info', `Invalid document structure.${errorMessageCombined}`);
                                    }
                                    else if (result.resStatus === 400 && error.code === EI_ERROR_CODE.MAXIMUM_SIZE_EXCEEDED) {
                                        Alert.alert('Info', `Maximum document size is exceeded.${errorMessageCombined}`);
                                    }
                                    else if (result.resStatus === 403 && error.code === EI_ERROR_CODE.INCORRECT_SUBMITTER) {
                                        Alert.alert('Info', `Incorrect submitter.${errorMessageCombined}`);
                                    }
                                    else if (result.resStatus === 422 && error.code === EI_ERROR_CODE.DUPLICATE_SUBMISSION) {
                                        Alert.alert('Info', `Duplicated submission.${errorMessageCombined}`);
                                    }
                                    else {
                                        Alert.alert('Info', `An error occurred during the e-Invoice submission.\n\nHTTP Status Code: ${result.resStatus}\nError Code: ${error.code}\nError Message: ${errorMessage ? errorMessage : 'N/A'}\n${errorMessage2 ? errorMessage2 : ''}`);
                                    }
                                }
                                else {
                                    if (result && result.data && typeof result.data.error === 'string') {
                                        Alert.alert('Info', `An error occurred during the e-Invoice submission.\n\n${result.data.error}${errorMessageCombined}`);
                                    }
                                    else if (result && result.data && result.data.error && typeof result.data.error.code === 'string') {
                                        Alert.alert('Info', `An error occurred during the e-Invoice submission.\n\n${result.data.error.code}${result.data.error.message ? `\n${result.data.error.message}` : ``}`);
                                    }
                                    else if (result.resStatus && result.resStatusText) {
                                        Alert.alert('Info', `An error occurred during the e-Invoice submission.\n\nHTTP Status Code: ${result.resStatus}\nMessage: ${result.resStatusText}${errorMessageCombined}`);
                                    }
                                    else {
                                        Alert.alert('Info', `An error occurred during the e-Invoice submission.${errorMessageCombined}`);
                                    }
                                }
                            }
                            else {
                                Alert.alert('Info', `An error occurred during the e-Invoice submission.`);
                            }
                        }

                        CommonStore.update((s) => {
                            s.isLoading = false;
                        });

                        return;
                    },
                );
            } else {
                if (result.resStatus === 400) {
                    Alert.alert('Info', 'Invalid Tax Identification Number.');
                }
                else if (result.resStatus === 404) {
                    Alert.alert('Info', 'Unmatched Tax Identification Number with ID.');
                }
                else if (result.resStatus === 429) {
                    let errorMsg = result.message ? result.message : 'Rate limit is exceeded, please try again later.';
                    if (result.resStatusText) {
                        errorMsg.push(`\n${result.resStatusText}`);
                    }
                    Alert.alert('Info', errorMsg);
                }
                else {
                    Alert.alert('Info', 'An error occurred during the Tax Identification Number validation process.');
                }
            }

            CommonStore.update((s) => {
                s.isLoading = false;
            });

            return;
        },
    );
};

export const updateRptInfo = async (idData, userOrder, customData = {}) => {
    const {
        rptId,
        rpteId,
    } = idData;

    const toUsedId = rptId ? rptId : rpteId;
    const toUsedCollectionName = rptId ? Collections.RazerPayoutTransaction : Collections.RazerPayoutTransactionExtend;

    if (toUsedId && toUsedCollectionName) {
        const rptSnapshot = await firebase.firestore()
            .collection(toUsedCollectionName)
            .where('uniqueId', '==', toUsedId)
            .limit(1)
            .get();

        var rpt = null;
        if (!rptSnapshot.empty) {
            rpt = rptSnapshot.docs[0].data();
        }

        if (rpt) {
            const userOrdersFiguresUpdated = rpt.userOrdersFigures.map(userOrderFigure => {
                if (userOrderFigure.orderId === userOrder.uniqueId) {
                    return {
                        ...userOrderFigure,

                        ...(userOrder.userPhone && !userOrderFigure.userPhone) && {
                            userPhone: userOrder.userPhone ? userOrder.userPhone : '',
                        },

                        ...(customData.eiDocId) && {
                            eiDId: customData.eiDocId ? customData.eiDocId : '',
                        },
                        ...(customData.eiLongId) && {
                            eiLId: customData.eiLongId ? customData.eiLongId : '',
                        },
                        ...(customData.eiStatus) && {
                            eiStatus: customData.eiStatus ? customData.eiStatus : '',
                        },
                    };
                }
                else {
                    return userOrderFigure;
                }
            });

            firebase.firestore().collection(toUsedCollectionName).doc(toUsedId).update({
                userOrdersFigures: userOrdersFiguresUpdated,

                updatedAt: Date.now(),
            });

            const rptUpdated = {
                ...rpt,

                userOrdersFigures: userOrdersFiguresUpdated,

                updatedAt: Date.now(),
            };

            const rptPos = (rptId ? global.payoutTransactions : global.payoutTransactionsExtend)
                .indexOf(rptFind => {
                    if (rptFind.uniqueId === toUsedId) {
                        return true;
                    }
                    else {
                        return false;
                    }
                });

            if (rptPos >= 0) {
                (rptId ? global.payoutTransactions : global.payoutTransactionsExtend)[rptPos] = rptUpdated;

                if (rptId) {
                    OutletStore.update(s => {
                        s.ptTimestamp = Date.now();
                    });
                }
                else {
                    OutletStore.update(s => {
                        s.pteTimestamp = Date.now();
                    });
                }
            }
        }
    }
};

export const batchUpdateOrderEiStatus = async (orderList, eiStatus) => {
    const userOrderBatch = firebase.firestore().batch();

    for (var index = 0; index < orderList.length; index++) {
        const order = orderList[index];

        if (!order.eiStatus ||
            order.eiStatus === EI_STATUS.EXPORTED) {
            userOrderBatch.update(
                firebase.firestore().collection(Collections.UserOrder).doc(order.uniqueId),
                {
                    eiStatus: eiStatus,

                    updatedAt: Date.now(),
                },
            );
        }
    }

    userOrderBatch.commit();
};

export const convertOrdersToEiBatchTemplate = (orderList) => {
    var excelData = [];

    for (var i = 0; i < orderList.length; i++) {
        const record = orderList[i];

        const calculatedDiscount = getOrderDiscountInfoInclOrderBased(record);

        const paymentMethodParsed = record.paymentDetails && record.paymentDetails.channel
            ?
            PAYMENT_CHANNEL_NAME_PARSED[
                record.paymentDetails.channel
            ]
                ?
                PAYMENT_CHANNEL_NAME_PARSED[
                record.paymentDetails.channel
                ]
                : record.paymentDetails.channel
            :
            'N/A';

        var excelRow = {
            'Order ID': `#${(record.orderType === ORDER_TYPE.DINEIN ? '' : 'T') + record.orderId}`,
            'Transaction Category':
                ORDER_TYPE_PARSED[record.orderType],
            'Sales (RM)': +parseFloat(
                record.totalPrice,
            ).toFixed(2),
            'Transaction Date': moment(
                record.createdAt,
            ).format('DD MMM hh:mm A'),
            'Total Discount (RM)': +parseFloat(
                calculatedDiscount,
            ).toFixed(2),
            'Discount (%)': +parseFloat(
                isFinite(
                    calculatedDiscount /
                    record.finalPrice + record.discount,
                )
                    ? (calculatedDiscount /
                        record.finalPrice + record.discount) *
                    100
                    : 0,
            ).toFixed(2),
            'Tax (RM)': +parseFloat(record.tax).toFixed(2),
            'Service Charge (RM)': +parseFloat(
                record.sc || 0,
            ).toFixed(2),
            // 'GP (%)': +parseFloat(0).toFixed(2),
            'Net Sales (RM)': +parseFloat(
                record.finalPrice,
            ).toFixed(2),
            'Payment Method': paymentMethodParsed,
            'e-Invoice Status': record.eiStatus ? record.eiStatus : 'N/A',
            'e-Invoice QR': record.eiQrUrl ? record.eiQrUrl : 'N/A',
            'e-Invoice URL': record.eiUrl ? record.eiUrl : 'N/A',
            'e-Invoice Submission Date/Time': typeof record.eiSubDt === 'number' ? moment(
                record.eiSubDt,
            ).format('DD MMM hh:mm A') : 'N/A',
        };

        excelData.push(excelRow);
    }

    return excelData;
};
