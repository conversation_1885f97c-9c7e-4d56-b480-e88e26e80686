import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { CHATBOT_USER } from '../constant/chatbot';

export const toChatMessage = (pointer = null, text = '', user = {
    firebaseUid: '',
    name: '',
    avatar: '',
}) => {
    if (pointer) {
        // means system message

        return [
            {
                _id: uuidv4(),
                text: text || pointer.title,
                createdAt: new Date(),
                user: CHATBOT_USER,
                options: pointer.options,
                isFeedbackRequired: pointer.isFeedbackRequired || false, 
                isFirstNode: pointer.isFirstNode || false, 
            },
        ];
    }
    else {
        return [
            {
                _id: uuidv4(),
                text: text,
                createdAt: new Date(),
                user: CHATBOT_USER,
                options: [],
                // isFeedbackRequired: pointer.isFeedbackRequired || false, 
                // isFirstNode: pointer.isFirstNode || false, 
            },
        ];
    }
};

export const toChatMessageUserDelegate = (pointer = null, text = '', user = {
    firebaseUid: '',
    name: '',
    avatar: '',
}) => {
    if (pointer) {
        // means system message

        return [
            {
                _id: uuidv4(),
                text: pointer.title,
                createdAt: new Date(),
                user: user,
                options: [],
                // isFeedbackRequired: pointer.isFeedbackRequired || false, 
                // isFirstNode: pointer.isFirstNode || false, 
            },
        ];
    }
    else {

    }
};

export const findChatbotTemplateParentNode = (pointer) => {
    
};
