import { useEffect, useState } from 'react';
import { Keyboard, KeyboardEvent } from 'react-native';
import { CommonStore } from '../store/commonStore';


export const useSwitchMerchant = () => {
  const switchMerchant = CommonStore.useState(s => s.switchMerchant);

  return switchMerchant;
};

export const useKeyboard = () => {
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  const onKeyboardDidShow = e => {
    setKeyboardHeight(e.endCoordinates.height);
  }

  const onKeyboardDidHide = () => {
    setKeyboardHeight(0);
  }

  useEffect(() => {
    Keyboard.addListener('keyboardDidShow', onKeyboardDidShow);
    Keyboard.addListener('keyboardDidHide', onKeyboardDidHide);
    return () => {
      Keyboard.removeListener('keyboardDidShow', onKeyboardDidShow);
      Keyboard.removeListener('keyboardDidHide', onKeyboardDidHide);
    };
  }, []);

  return [keyboardHeight];
};
