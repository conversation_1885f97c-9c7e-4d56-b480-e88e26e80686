import React, {
    Component,
    useReducer,
    useState,
    useEffect,
    useMemo,
    useLayoutEffect,
    useRef,
    useCallback,
} from 'react';
import {
    StyleSheet,
    ScrollView,
    Image,
    View,
    Text,
    Alert,
    Dimensions,
    TouchableOpacity,
    Switch,
    FlatList,
    Modal,
    TextInput,
    KeyboardAvoidingView,
    PermissionsAndroid,
    Platform,
    ActivityIndicator,
    PlatformColor,
    Picker,
    useWindowDimensions,
    InteractionManager,
    Animated,
} from 'react-native';
// import Animated, { Easing, mix } from 'react-native-reanimated';
import Colors from '../constant/Colors';
import firebase from "firebase";
import SideBar from './SideBar';
import Styles from '../constant/Styles';
import Feather from 'react-native-vector-icons/Feather';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Icon from 'react-native-vector-icons/FontAwesome5';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import ApiClient from '../util/ApiClient';
import API from '../constant/API';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as User from '../util/User';
import DropDownPicker from 'react-native-dropdown-picker';
//import RNPickerSelect from 'react-native-picker-select';
import Entypo from 'react-native-vector-icons/Entypo';
import moment, { now } from 'moment';
//import moment from "moment-timezone";
import Ionicon from 'react-native-vector-icons/Ionicons';

import Icon1 from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import Octicons from 'react-native-vector-icons/Octicons';
import { CSVLink } from "react-csv";
import { CommonStore } from '../store/commonStore';
import { getCartItemDiscount, isMobile } from "../util/common";
import { ReactComponent as Report } from "../assets/svg/Report.svg";
// import { ReactComponent as ReportG } from "../assets/svg/ReportG.svg";
// import { ReactComponent as InventoryG } from "../assets/svg/InventoryG.svg";
import { ReactComponent as Inventory } from "../assets/svg/Inventory.svg";

import {
    requestStoragePermission,
    getAddOnChoicePrice,
    getAddOnChoiceQuantity,
    getCartItemPriceWithoutAddOn,
    getOrderDiscountInfo,
    getOrderDiscountInfoInclOrderBased,
    getTransformForModalInsideNavigation,
    getTransformForScreenInsideNavigation,
    /*listenToCurrOutletIdReservationChanges,*/

    listenToCurrOutletIdReservationChanges,

    convertArrayToCSV,

    requestNotificationsPermission,
    sortReportDataList,

    generateEmailReport,

    getCachedUrlContent,
    sliceUnicodeStringV2WithDots,

    isObjectEqual,
    naturalCompare,

    waitForSeconds,
    compareOrderDateByDisplayType,
} from '../util/common';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
import {
    NOTIFICATIONS_CHANNEL,
    TIMEZONE,
    NOTIFICATIONS_ID,
    CHART_HOURLY_LABEL_LIST,
    ROLE_TYPE,
    ORDER_TYPE,
    ORDER_TYPE_PARSED,
    NOTIFICATIONS_TYPE,
    REPORT_SORT_FIELD_TYPE,
    TABLE_PAGE_SIZE_DROPDOWN_LIST,
    EMAIL_REPORT_TYPE,
    USER_RESERVATION_STATUS,
    OUTLET_SHIFT_STATUS,
    USER_ORDER_STATUS,
    EXPAND_TAB_TYPE,
    PRIVILEGES_NAME,
    KD_PRINT_EVENT_TYPE,
    KD_PRINT_VARIATION,
    PRODUCT_PRICE_TYPE,
    UNIT_TYPE_SHORT,
    APP_TYPE,
    DATE_COMPARE_TYPE,
    CHANNEL_TYPE,
    PAYMENT_CHANNEL_NAME_PARSED
} from '../constant/common';
import { OutletStore } from '../store/outletStore';

import FusionCharts from "react-fusioncharts";
import FC from "fusioncharts";
import Column2D from "fusioncharts/fusioncharts.charts";
import FusionTheme from "fusioncharts/themes/fusioncharts.theme.fusion";
import {
    CHART_DATA,
    CHART_TYPE,
    FS_LIBRARY_PATH,
    CHART_Y_AXIS_DROPDOWN_LIST,
    CHART_FIELD_COMPARE_DROPDOWN_LIST,
    CHART_FIELD_NAME_DROPDOWN_LIST,
    CHART_FIELD_TYPE,
    CHART_FIELD_COMPARE_DICT,
    CHART_PERIOD,
    CHART_X_AXIS_DROPDOWN_LIST,
    CHART_X_AXIS_TYPE,
} from '../constant/chart';
import {
    filterChartItems,
    getDataForChartDashboardTodaySales,
    getDataForSalesLineChart,
} from '../util/chart';
//import messaging from '@react-native-firebase/messaging';
import "react-datepicker/dist/react-datepicker.css";
import "../constant/datePicker.css";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";
import "../constant/styles.css";

import APILocal from '../util/apiLocalReplacers';

import TopBar from './TopBar';

import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
import BigNumber from 'bignumber.js';
import { useLinkTo } from '@react-navigation/native';
import { linkManagerComposite, linkManagerReports, prefix } from "../constant/env";

const { nanoid } = require('nanoid');

FusionCharts.fcRoot(FC, Column2D, FusionTheme);


global.printedOrderListLocal = [];
global.printedOrderPaidOnlineListLocal = [];

global.printedOrderAutoApproveListLocal = [];


global.timerAutoApproveOrders = 0;
global.timerAutoApproveOrdersTiming = 0;

global.isCheckingPrintKdOsNow = false;

setInterval(() => {
    const elements = document.querySelectorAll('[fill="#b1b2b7"]');


    if (elements && elements.length > 0) {
        for (var i = 0; i < elements.length; i++) {
            elements[i].style.display = 'none';
        }
    }
}, 1000);

const HomePage = (props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    ///////////////////////////////////////////////////////////

    // const netInfo = useNetInfo();
    const linkTo = useLinkTo();

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();
    const reportOutletIdList = CommonStore.useState(s => s.reportOutletIdList);

    const [totalTransactions, setTotalTransactions] = useState(0);
    const [totalSales, setTotalSales] = useState(0);
    const [currentOutletTotalSales, setCurrentOutletTotalSales] = useState(0);
    const [allDayTotalTransaction, setAllDayTotalTransaction] = useState(0);
    const [totalSold, setTotalSold] = useState(0);
    const [outletDropdownList, setOutletDropdownList] = useState([]);

    const [switchMerchant, setSwitchMerchant] = useState(false);

    const selectedCalendarData = CommonStore.useState(s => s.selectedCalendarData);

    const historyStartDate = CommonStore.useState(s => s.historyStartDate);
    const historyEndDate = CommonStore.useState(s => s.historyEndDate);

    const reportOutletShifts = OutletStore.useState((s) => s.reportOutletShifts);
    const reportDisplayType = OutletStore.useState((s) => s.reportDisplayType);

    const [perPage, setPerPage] = useState(10);
    const [pageCount, setPageCount] = useState(0);
    // const [detailsPageCount, setDetailsPageCount] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    // const [currentPageText, setCurrentPageText] = useState('1');
    const [currentDetailsPage, setCurrentDetailsPage] = useState(1);
    const [pageReturn, setPageReturn] = useState(1);

    //////////////////////////////////////////////////////////////

    const [salesLineChart, setSalesLineChart] = useState({});

    const [salesLineChartPeriod, setSalesLineChartPeriod] = useState(
        CHART_PERIOD.THIS_MONTH,
    );
    const [salesBarChartPeriod, setSalesBarChartPeriod] = useState(
        CHART_PERIOD.THIS_MONTH,
    );

    const [dailySalesDetailsList, setDailySalesDetailsList] = useState([]);

    const [selectedItemSummary, setSelectedItemSummary] = useState({});

    const [selectedChartDropdownValueX, setSelectedChartDropdownValueX] =
        useState(
            CHART_X_AXIS_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][0].value,
        );

    const [chartDropdownValueXList, setChartDropdownValueXList] = useState(CHART_X_AXIS_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_LINE_CHART_SALES]);

    // const allOutletsItems = OutletStore.useState((s) => s.allOutletsItems);
    const allOutletsCategories = OutletStore.useState((s) => s.allOutletsCategories);
    // const allOutletsUserOrders = OutletStore.useState((s) => s.allOutletsUserOrders);

    /////////////////////////////////////////////////////////////////////////////////////

    // 2022-08-17 - Topup credit type report

    const allOutletsUserOrdersLoyaltyDoneRealTime = OutletStore.useState((s) => s.allOutletsUserOrdersLoyaltyDoneRealTime);
    const allOutletsUserOrdersLoyaltyRealTime = OutletStore.useState((s) => s.allOutletsUserOrdersLoyaltyRealTime);
    const allOutletsUserOrdersLoyaltyDoneCache = OutletStore.useState((s) => s.allOutletsUserOrdersLoyaltyDoneCache);
    const allOutletsUserOrdersLoyaltyCache = OutletStore.useState((s) => s.allOutletsUserOrdersLoyaltyCache);

    /////////////////////////////////////////////////////////////////////////////////////

    const userOrders = OutletStore.useState((s) => s.userOrders);

    const currPageStack = CommonStore.useState((s) => s.currPageStack);

    const userName = UserStore.useState((s) => s.name);
    // const merchantName = MerchantStore.useState(s => s.name);

    const merchantId = UserStore.useState((s) => s.merchantId);
    const role = UserStore.useState((s) => s.role);
    const firebaseUid = UserStore.useState((s) => s.firebaseUid);

    const currOutletId = MerchantStore.useState((s) => s.currOutletId);
    const currOutlet = MerchantStore.useState(s => s.currOutlet);
    const allOutletsRaw = MerchantStore.useState((s) => s.allOutlets);

    const isMasterAccount = UserStore.useState((s) => s.isMasterAccount);

    const crmUsers = OutletStore.useState((s) => s.crmUsers);
    const crmUsersRaw = OutletStore.useState((s) => s.crmUsersRaw);
    const linkedMerchantIdUsers = OutletStore.useState(
        (s) => s.linkedMerchantIdUsers,
    );
    const favoriteMerchantIdUsers = OutletStore.useState(
        (s) => s.favoriteMerchantIdUsers,
    );
    const simpliFiedLayout = CommonStore.useState(
        (s) => s.simpliFiedLayout,
    );
    // const selectedCustomerDineInOrders = CommonStore.useState(s => s.selectedCustomerDineInOrders);

    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );

    const [showDetails, setShowDetails] = useState(false);
    const [transactionTypeSalesDetails, setTransactionTypeSalesDetails] =
        useState([]);

    const [expandLineSelection, setExpandLineSelection] = useState(false);

    const [
        selectedChartDropdownValueLineChart,
        setSelectedChartDropdownValueLineChart,
    ] = useState(
        CHART_Y_AXIS_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][0].value,
    );
    const [
        selectedChartFilterQueriesLineChart,
        setSelectedChartFilterQueriesLineChart,
    ] = useState([
        {
            fieldNameKey:
                CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][0]
                    .value,
            fieldNameType:
                CHART_FIELD_NAME_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_LINE_CHART_SALES][0]
                    .fieldType,
            fieldCompare:
                CHART_FIELD_COMPARE_DROPDOWN_LIST[
                    CHART_TYPE.DASHBOARD_LINE_CHART_SALES
                ][0].value,
            fieldDataValue: null,
        },
    ]);
    const [
        appliedChartFilterQueriesLineChart,
        setAppliedChartFilterQueriesLineChart,
    ] = useState([]);

    const [
        selectedChartDropdownValueBarChart,
        setSelectedChartDropdownValueBarChart,
    ] = useState(
        CHART_Y_AXIS_DROPDOWN_LIST[CHART_TYPE.DASHBOARD_TODAY_SALES][0].value,
    );

    const [
        appliedChartFilterQueriesBarChart,
        setAppliedChartFilterQueriesBarChart,
    ] = useState([]);

    const merchantLogo = MerchantStore.useState((s) => s.logo);

    const privileges_state = UserStore.useState((s) => s.privileges);

    const [privileges, setPrivileges] = useState([]);

    /////////////////////////////////////////////////////////////////////////////////////

    const [filterAppType, setFilterAppType] = useState([APP_TYPE.WEB_ORDER, APP_TYPE.MERCHANT, APP_TYPE.USER, APP_TYPE.WAITER]);
    // const [filterAppType, setFilterAppType] = useState([]);

    const [allOutlets, setAllOutlets] = useState([]);

    const ptTimestamp = OutletStore.useState(s => s.ptTimestamp);
    const pteTimestamp = OutletStore.useState(s => s.pteTimestamp);

    const allOutletsUserOrdersDoneRaw = OutletStore.useState((s) => s.allOutletsUserOrdersDone);
    const [allOutletsUserOrdersDone, setAllOutletsUserOrdersDone] = useState([]);

    const [selectedOutletId, setSelectedOutletId] = useState("");

    useEffect(() => {
        setAllOutlets(allOutletsRaw.filter(outlet => {
            if (outlet.uniqueId === currOutletId || isMasterAccount) {
                return true;
            }
            else {
                return false;
            }
        }));
    }, [allOutletsRaw, currOutletId, isMasterAccount]);

    /////////////////////////////////////////////////////////////////////////////////////

    const iconWifiOpacity = useRef(new Animated.Value(0)).current;

    const startAnimation = () => {
        Animated.sequence([
            Animated.timing(iconWifiOpacity, {
                toValue: 1,
                duration: 1000, // Adjust the duration as needed
                useNativeDriver: true,
            }),
            Animated.timing(iconWifiOpacity, {
                toValue: 0,
                duration: 1000, // Adjust the duration as needed
                useNativeDriver: true,
            }),
        ]).start(() => {
            startAnimation();
        });
    };

    // Interpolate the node from 0 to 1 without clamping
    // const opacityIconWifi = mix(animationIconWifi, 0.1, 1);

    useEffect(() => {
        startAnimation();
    }, []);

    useEffect(() => {
        setOutletDropdownList(
            allOutlets.map((item) => ({
                label: item.name,
                value: item.uniqueId,
            })),
        );
        if (selectedOutletId === "" && allOutlets.length > 0 && currOutletId) {
            setSelectedOutletId(currOutletId);

            // setSelectedOutletList([currOutletId]);
            CommonStore.update((s) => {
                s.reportOutletIdList = [currOutletId];
            })
        }
    }, [allOutlets, currOutletId]);

    /////////////////////////////////////////////////////////////////////////////////////

    const pinNo = UserStore.useState(s => s.pinNo);

    useEffect(async () => {
        // admin full access

        // const enteredPinNo = await AsyncStorage.getItem('enteredPinNo');
        //const enteredPinNo = storageMMKV.getString('enteredPinNo');

        //if (role === ROLE_TYPE.ADMIN && pinNo === enteredPinNo) {
        if (role === ROLE_TYPE.ADMIN) {
            setPrivileges([
                "EMPLOYEES",
                "OPERATION",
                "PRODUCT",
                "INVENTORY",
                "INVENTORY_COMPOSITE",
                "DOCKET",
                "VOUCHER",
                "PROMOTION",
                "CRM",
                "LOYALTY",
                "TRANSACTIONS",
                "REPORT",
                "RESERVATIONS",

                // for action
                'REFUND_ORDER',

                'SETTINGS',

                'QUEUE',

                'OPEN_CASH_DRAWER',

                'KDS',

                'UPSELLING',

                // for Kitchen

                'REJECT_ITEM',
                'CANCEL_ORDER',
                //'REFUND_tORDER',
            ]);
        } else {
            setPrivileges(privileges_state || []);
        }
    }, [role, privileges_state, pinNo]);

    /////////////////////////////////////////////////////////////////////////////////////

    // useEffect(() => {
    //     InteractionManager.runAfterInteractions(() => {
    //         setChartDropdownValueXList(CHART_X_AXIS_DROPDOWN_LIST[
    //             CHART_TYPE.DASHBOARD_LINE_CHART_SALES
    //         ].filter(xAxisType => {
    //             if (salesLineChartPeriod === CHART_PERIOD.THIS_WEEK) {
    //                 if (xAxisType.value === CHART_X_AXIS_TYPE.WEEK || xAxisType.value === CHART_X_AXIS_TYPE.MONTH) {
    //                     return false;
    //                 }
    //                 else {
    //                     return true;
    //                 }
    //             }
    //             else if (salesLineChartPeriod === CHART_PERIOD.THIS_MONTH) {
    //                 if (xAxisType.value === CHART_X_AXIS_TYPE.MONTH) {
    //                     return false;
    //                 }
    //                 else {
    //                     return true;
    //                 }
    //             }
    //             else if (salesLineChartPeriod === CHART_PERIOD.THREE_MONTHS) {
    //                 return true;
    //             }
    //             else if (salesLineChartPeriod === CHART_PERIOD.SIX_MONTHS) {
    //                 if (xAxisType.value === CHART_X_AXIS_TYPE.DAY) {
    //                     return false;
    //                 }
    //                 else {
    //                     return true;
    //                 }
    //             }
    //             else if (salesLineChartPeriod === CHART_PERIOD.THIS_YEAR) {
    //                 if (xAxisType.value === CHART_X_AXIS_TYPE.DAY) {
    //                     return false;
    //                 }
    //                 else {
    //                     return true;
    //                 }
    //             }
    //             else if (salesLineChartPeriod === CHART_PERIOD.YTD) {
    //                 if (xAxisType.value === CHART_X_AXIS_TYPE.DAY) {
    //                     return false;
    //                 }
    //                 else {
    //                     return true;
    //                 }
    //             }
    //         }));
    //     });
    // }, [salesLineChartPeriod]);

    /////////////////////////////////////////////////////////////////////////////////////

    useEffect(() => {
        // to prevent the app 'hang' if request failed or error, will change to apiClient/apiReplacer callback trigger in future

        setTimeout(() => {
            CommonStore.update(s => {
                s.isLoading = false;
            });
        }, 5000);
    }, []);

    /////////////////////////////////////////////////////////////////////////////////////

    // 2025-04-30 - no need first
    useEffect(() => {
        InteractionManager.runAfterInteractions(() => {
            var outletCategories = [];
            var outletCategoriesDict = {};

            var allOutletsCategoriesCurrOutlet = allOutletsCategories.filter(item => item.outletId === currOutletId);

            for (var i = 0; i < allOutletsCategoriesCurrOutlet.length; i++) {
                outletCategories.push(allOutletsCategoriesCurrOutlet[i]);
                outletCategoriesDict[allOutletsCategoriesCurrOutlet[i].uniqueId] = allOutletsCategoriesCurrOutlet[i];

                global.outletCategoriesDict[allOutletsCategoriesCurrOutlet[i].uniqueId] = allOutletsCategoriesCurrOutlet[i];
            }

            outletCategories.sort((a, b) => {
                return naturalCompare(a.name || '', b.name || '');
            })

            OutletStore.update((s) => {
                s.outletCategories = outletCategories;
                s.outletCategoriesDict = outletCategoriesDict;
            });

            CommonStore.update((s) => {
                s.selectedOutletItemCategory = outletCategories[0];
            });
        });
    }, [currOutletId, allOutletsCategories]);

    /////////////////////////////////////////////////////////////////

    const updateTokenFcm = async () => {
        const tokenFcm = await AsyncStorage.getItem('tokenFcm');

        const supportCodeDataRaw = await AsyncStorage.getItem('supportCodeData');
        // const supportCodeData = JSON.parse(supportCodeDataRaw);

        if (tokenFcm) {
            const body = {
                tokenFcm,
                userId: firebaseUid,
                outletId: currOutletId,
                merchantId,
                role,

                isSupportAccount: supportCodeDataRaw ? true : false,
            };

            ApiClient.POST(API.updateTokenFcm, body).then((result) => {
                // console.log('updated token fcm');
            });
        }
    };

    ////////////////////////////////////////////////////////

    // from crm

    useEffect(() => {
        InteractionManager.runAfterInteractions(() => {

            global.crmUsersDt = Date.now();

            OutletStore.update((s) => {
                s.crmUsers = crmUsersRaw;
            });
        });
    }, [crmUsersRaw, linkedMerchantIdUsers, favoriteMerchantIdUsers]);

    const timeCheckItem = CommonStore.useState(s => s.timeCheckItem)
    useEffect(() => {
        setInterval(() => {
            CommonStore.update(s => {
                s.timeCheckItem = Date.now();
            });
        }, 30000);
    }, [])

    useEffect(() => {
        requestStoragePermission();
    }, [merchantLogo]);

    //////////////////////////////////////////////////////////////////////////////////

    // useEffect(() => {
    //     InteractionManager.runAfterInteractions(() => {
    //         if (allOutlets.length > 0 &&
    //             allOutlets[0].merchantId === merchantId) {
    //             const result = getDataForSalesLineChart(
    //                 allOutlets,
    //                 // allOutletsUserOrdersDoneFilteredLineChart,
    //                 allOutletsUserOrdersDone,
    //                 salesLineChartPeriod,
    //                 selectedChartDropdownValueLineChart,
    //                 selectedChartDropdownValueX,
    //                 historyStartDate,
    //                 historyEndDate,

    //                 reportDisplayType,
    //                 reportOutletShifts,
    //             );

    //             if (result) {
    //                 setSalesLineChart(result.chartData);
    //                 setExpandLineSelection(false);
    //             }
    //             else {
    //                 setSalesLineChart({});
    //                 setExpandLineSelection(false);
    //             }
    //         }
    //         else {
    //             setSalesLineChart({});
    //             setExpandLineSelection(false);
    //         }
    //     });
    // }, [
    //     allOutlets,
    //     allOutletsUserOrdersDone,
    //     salesLineChartPeriod,
    //     salesBarChartPeriod,
    //     selectedChartDropdownValueBarChart,
    //     selectedChartDropdownValueLineChart,
    //     appliedChartFilterQueriesBarChart,
    //     appliedChartFilterQueriesLineChart,
    //     historyStartDate,
    //     historyEndDate,

    //     selectedChartDropdownValueX,

    //     merchantId,
    //     // filterAppType,
    // ]);

    // useEffect(() => {
    //     //////////////////////////////////////////////////////////////////////////////////////

    //     InteractionManager.runAfterInteractions(() => {
    //         var dailySalesDetailsListTemp = [];
    //         const processedOrders = new Set();

    //         let allDayTotalTransactionTemp = 0;
    //         let currentOutletTotalSalesTemp = 0;

    //         var totalSalesTemp = 0;
    //         var totalTransactionsTemp = 0;
    //         var totalSoldTemp = 0;
    //         var currDate = Date.now();

    //         if (currOutletId && currOutletId.length > 0) {
    //             for (
    //                 var i = 0;
    //                 i <= moment(historyEndDate).diff(moment(historyStartDate), 'day');
    //                 i++
    //             ) {
    //                 var currDateTime = moment(historyStartDate).add(i, 'day');

    //                 var record = {
    //                     summaryId: nanoid(),
    //                     dateTime: moment(currDateTime).format('D MMM YYYY'),
    //                     dateTimeRaw: currDateTime,
    //                     totalSales: 0,
    //                     totalTransactions: 0,
    //                     totalDiscount: 0,
    //                     discount: 0,
    //                     tax: 0,
    //                     serviceCharge: 0,
    //                     gp: 0,
    //                     totalSalesReturn: 0,
    //                     netSales: 0,
    //                     averageNetSales: 0,
    //                     detailsList: [],
    //                     itemCostPrice: 0,
    //                 };

    //                 dailySalesDetailsListTemp.push(record);
    //             }

    //             ///////////////////////////////////////////////////////

    //             let lastCheckingRecord = null;
    //             let lastCheckingRecordIndex = -1;

    //             var record = null;

    //             for (var j = 0; j < allOutletsUserOrdersDone.length; j++) {
    //                 // console.log(`check order id: ${allOutletsUserOrdersDone[j].orderId}`);
    //                 // console.log(`check order sales: ${allOutletsUserOrdersDone[j].finalPrice}`);
    //                 if (processedOrders.has(allOutletsUserOrdersDone[j].uniqueId)) {
    //                     continue;
    //                 }
    //                 if (
    //                     // 2024-01-09 - For master account, just show all data for this report screen
    //                     (
    //                         isMasterAccount && (reportOutletIdList && reportOutletIdList.includes(allOutletsUserOrdersDone[j].outletId))
    //                         ||
    //                         (!isMasterAccount && allOutletsUserOrdersDone[j].outletId === currOutletId)
    //                     )
    //                     // &&
    //                     // compareOrderDateByDisplayType(
    //                     //   currDateTime,
    //                     //   allOutletsUserOrdersDone[j].createdAt,
    //                     //   DATE_COMPARE_TYPE.IS_SAME,
    //                     //   'day',
    //                     //   reportDisplayType,
    //                     //   reportOutletShifts
    //                     // )
    //                     // &&

    //                     // (filterAppType && filterAppType.includes(allOutletsUserOrdersDone[j].appType) || filterAppType.length === 0)
    //                 ) {
    //                     let toProceed = false;

    //                     if (lastCheckingRecord &&
    //                         compareOrderDateByDisplayType(
    //                             lastCheckingRecord.dateTimeRaw,
    //                             allOutletsUserOrdersDone[j].createdAt,
    //                             DATE_COMPARE_TYPE.IS_SAME,
    //                             'day',
    //                             reportDisplayType,
    //                             reportOutletShifts
    //                         )
    //                     ) {
    //                         toProceed = true;
    //                     }
    //                     else {
    //                         for (let findCRI = 0; findCRI < dailySalesDetailsListTemp.length; findCRI++) {
    //                             const currCheckingRecord = dailySalesDetailsListTemp[findCRI];

    //                             if (currCheckingRecord &&
    //                                 compareOrderDateByDisplayType(
    //                                     currCheckingRecord.dateTimeRaw,
    //                                     allOutletsUserOrdersDone[j].createdAt,
    //                                     DATE_COMPARE_TYPE.IS_SAME,
    //                                     'day',
    //                                     reportDisplayType,
    //                                     reportOutletShifts
    //                                 )
    //                             ) {
    //                                 lastCheckingRecord = currCheckingRecord;
    //                                 lastCheckingRecordIndex = findCRI;

    //                                 toProceed = true;
    //                                 break;
    //                             }
    //                         }
    //                     }

    //                     if (
    //                         toProceed
    //                     ) {
    //                         // console.log(`approved order id: ${allOutletsUserOrdersDone[j].orderId}`);
    //                         // console.log(`approved order sales: ${allOutletsUserOrdersDone[j].finalPrice}`);
    //                         // console.log('-----------------------------------');

    //                         /////////////////////////////////////////////////////////////////

    //                         // for today summary
    //                         processedOrders.add(allOutletsUserOrdersDone[j].uniqueId);
    //                         record = lastCheckingRecord;

    //                         if (moment(record.dateTimeRaw).isSame(currDate, 'day')) {
    //                             for (var totalIndex = 0; totalIndex < allOutletsUserOrdersDone[j].cartItems.length; totalIndex++) {
    //                                 const cartItem = allOutletsUserOrdersDone[j].cartItems[totalIndex];

    //                                 totalSoldTemp += cartItem.quantity !== undefined ? cartItem.quantity : cartItem.qty;
    //                             }

    //                             totalSalesTemp += allOutletsUserOrdersDone[j].finalPrice;
    //                             totalTransactionsTemp += 1;
    //                         }

    //                         /////////////////////////////////////////////////////////////////

    //                         allDayTotalTransactionTemp += 1;
    //                         currentOutletTotalSalesTemp += allOutletsUserOrdersDone[j].finalPrice;

    //                         record.totalSales += (allOutletsUserOrdersDone[j].finalPriceBefore ? allOutletsUserOrdersDone[j].finalPriceBefore : allOutletsUserOrdersDone[j].finalPrice);
    //                         record.totalTransactions += 1;

    //                         const discountCalculated = getOrderDiscountInfoInclOrderBased(allOutletsUserOrdersDone[j]);

    //                         record.totalDiscount += discountCalculated;
    //                         record.discount += !isNaN(
    //                             (discountCalculated /
    //                                 allOutletsUserOrdersDone[j].finalPrice) *
    //                             100,
    //                         )
    //                             ? (discountCalculated /
    //                                 allOutletsUserOrdersDone[j].finalPrice) *
    //                             100
    //                             : 0;
    //                         record.tax += !isNaN(
    //                             (allOutletsUserOrdersDone[j].tax /
    //                                 allOutletsUserOrdersDone[j].finalPrice) *
    //                             100,
    //                         )
    //                             ? allOutletsUserOrdersDone[j].tax
    //                             : 0;
    //                         record.serviceCharge += allOutletsUserOrdersDone[j].sc || 0;
    //                         // record.totalSalesReturn += 0;

    //                         var orderSalesReturn = 0;
    //                         if (allOutletsUserOrdersDone[j].cartItemsCancelled) {
    //                             for (
    //                                 var k = 0;
    //                                 k < allOutletsUserOrdersDone[j].cartItemsCancelled.length;
    //                                 k++
    //                             ) {
    //                                 orderSalesReturn +=
    //                                     allOutletsUserOrdersDone[j].cartItemsCancelled[k].price;
    //                             }
    //                         }

    //                         var orderCostPrice = allOutletsUserOrdersDone[j].finalPrice;
    //                         if (allOutletsUserOrdersDone[j].cartItems) {
    //                             for (var k = 0; k < allOutletsUserOrdersDone[j].cartItems.length; k++) {
    //                                 orderCostPrice -= ((allOutletsUserOrdersDone[j].cartItems[k].itemCostPrice !== undefined ? allOutletsUserOrdersDone[j].cartItems[k].itemCostPrice : allOutletsUserOrdersDone[j].cartItems[k].icp)
    //                                     ?
    //                                     ((allOutletsUserOrdersDone[j].cartItems[k].itemCostPrice !== undefined ? allOutletsUserOrdersDone[j].cartItems[k].itemCostPrice : allOutletsUserOrdersDone[j].cartItems[k].icp) * (allOutletsUserOrdersDone[j].cartItems[k].quantity !== undefined ? allOutletsUserOrdersDone[j].cartItems[k].quantity : allOutletsUserOrdersDone[j].cartItems[k].qty))
    //                                     :
    //                                     0);
    //                             }

    //                             // for (var k = 0; k < allOutletsUserOrdersDone[j].cartItems.length; k++) {
    //                             //   orderCostPrice += allOutletsUserOrdersDone[j].cartItems[k].itemCostPrice ?
    //                             //     (allOutletsUserOrdersDone[j].finalPriceBefore != 0 ||
    //                             //       allOutletsUserOrdersDone[j].finalPrice != 0
    //                             //       ? (allOutletsUserOrdersDone[j].finalPriceBefore
    //                             //         ? allOutletsUserOrdersDone[j].finalPriceBefore
    //                             //         : allOutletsUserOrdersDone[j].finalPrice) -
    //                             //       allOutletsUserOrdersDone[j].tax -
    //                             //       (allOutletsUserOrdersDone[j].sc
    //                             //         ? allOutletsUserOrdersDone[j].sc
    //                             //         : 0)
    //                             //       : 0) - allOutletsUserOrdersDone[j].cartItems[k].itemCostPrice : 0
    //                             // }
    //                         }
    //                         record.itemCostPrice += orderCostPrice;

    //                         record.totalSalesReturn += orderSalesReturn;

    //                         record.netSales += (allOutletsUserOrdersDone[j].finalPriceBefore != 0 || allOutletsUserOrdersDone[j].finalPrice != 0 ?
    //                             ((allOutletsUserOrdersDone[j].finalPriceBefore ? allOutletsUserOrdersDone[j].finalPriceBefore : allOutletsUserOrdersDone[j].finalPrice) - allOutletsUserOrdersDone[j].tax - (allOutletsUserOrdersDone[j].sc ? allOutletsUserOrdersDone[j].sc : 0))
    //                             : 0)

    //                         if (typeof record.subTotal === 'undefined') {
    //                             record.subTotal = 0; // Initialize to 0 if not set
    //                         }

    //                         var subTotalTemp = 0;
    //                         var feeCustomTemp = 0;
    //                         // var discountInfo = (allOutletsUserOrdersDone[j].cartItems).length > 0
    //                         //   ? getOrderDiscountInfo(allOutletsUserOrdersDone[j])
    //                         //   : 0;

    //                         // // Calculate the subtotal for the current order
    //                         // subTotalTemp = (allOutletsUserOrdersDone[j].isRefundOrder && allOutletsUserOrdersDone[j].finalPrice <= 0)
    //                         //   ? 0
    //                         //   : ((allOutletsUserOrdersDone[j].totalPrice + discountInfo) - discountCalculated);

    //                         // // Accumulate the subtotal
    //                         // record.subTotal += subTotalTemp; // Add current subtotal to the total
    //                         if (allOutletsUserOrdersDone[j].uniqueId === 'dffa08a2-2c6d-4cc2-829d-b5f91b65e76b') {
    //                             console.log(allOutletsUserOrdersDone[j]);
    //                         }

    //                         const cartItemDiscount = getCartItemDiscount(allOutletsUserOrdersDone[j]);

    //                         if (cartItemDiscount > 0) {
    //                             if (allOutletsUserOrdersDone[j].discount > cartItemDiscount) {
    //                                 const discDiff = BigNumber(allOutletsUserOrdersDone[j].discount).minus(cartItemDiscount).toNumber();
    //                                 feeCustomTemp = BigNumber(allOutletsUserOrdersDone[j].finalPriceBefore)
    //                                     .plus(discDiff) // add back manual discount
    //                                     .minus(allOutletsUserOrdersDone[j].totalPrice)
    //                                     .minus(allOutletsUserOrdersDone[j].tax)
    //                                     .minus(allOutletsUserOrdersDone[j].sc)
    //                                     // .minus(discDiff)
    //                                     .toNumber();
    //                             }
    //                             else {
    //                                 feeCustomTemp = BigNumber(allOutletsUserOrdersDone[j].finalPriceBefore)
    //                                     .minus(allOutletsUserOrdersDone[j].totalPrice)
    //                                     .minus(allOutletsUserOrdersDone[j].tax)
    //                                     .minus(allOutletsUserOrdersDone[j].sc)
    //                                     .toNumber();
    //                             }
    //                         }
    //                         else {
    //                             feeCustomTemp = BigNumber(allOutletsUserOrdersDone[j].finalPriceBefore)
    //                                 .minus(allOutletsUserOrdersDone[j].totalPrice)
    //                                 .minus(allOutletsUserOrdersDone[j].tax)
    //                                 .minus(allOutletsUserOrdersDone[j].sc)
    //                                 .plus(discountCalculated)
    //                                 .toNumber();
    //                         }

    //                         subTotalTemp = BigNumber(allOutletsUserOrdersDone[j].finalPriceBefore)
    //                             .minus(feeCustomTemp ? feeCustomTemp : 0)
    //                             .minus(allOutletsUserOrdersDone[j].tax ? allOutletsUserOrdersDone[j].tax : 0)
    //                             .minus(allOutletsUserOrdersDone[j].sc ? allOutletsUserOrdersDone[j].sc : 0)
    //                             .toNumber();

    //                         record.subTotal += subTotalTemp;

    //                         if (record.totalTransactions > 0) {
    //                             record.averageNetSales = record.netSales / record.totalTransactions;
    //                         }

    //                         console.log('----------------');
    //                         console.log(allOutletsUserOrdersDone[j].uniqueId);
    //                         console.log(allOutletsUserOrdersDone[j].orderId);
    //                         console.log(allOutletsUserOrdersDone[j].finalPrice)
    //                         console.log(subTotalTemp)

    //                         record.detailsList.push({
    //                             ...allOutletsUserOrdersDone[j],
    //                             // cartItems: [],
    //                             // uniqueId: allOutletsUserOrdersDone[j].uniqueId,
    //                             discountPercentage: parseFloat(
    //                                 isFinite(
    //                                     discountCalculated /
    //                                     (allOutletsUserOrdersDone[j].finalPrice + discountCalculated),
    //                                 )
    //                                     ? (discountCalculated /
    //                                         (allOutletsUserOrdersDone[j].finalPrice + discountCalculated)) *
    //                                     100
    //                                     : 0,
    //                             ),
    //                             salesReturn: orderSalesReturn,

    //                             subTotal: subTotalTemp,
    //                         });

    //                         // to update back

    //                         dailySalesDetailsListTemp[lastCheckingRecordIndex] = lastCheckingRecord;

    //                         // can continue, since 1 order will belong to 1 section only

    //                         continue;
    //                     }
    //                 }
    //             }
    //         }

    //         //////////////////////////////////////////////////////////////////////

    //         // for summary

    //         setAllDayTotalTransaction(allDayTotalTransactionTemp);
    //         setCurrentOutletTotalSales(currentOutletTotalSalesTemp);

    //         setTotalSales(totalSalesTemp);
    //         setTotalTransactions(totalTransactionsTemp);
    //         setTotalSold(totalSoldTemp);

    //         //////////////////////////////////////////////////////////////////////

    //         setDailySalesDetailsList(dailySalesDetailsListTemp);

    //         //setCurrentPage(1);
    //         setPageCount(Math.ceil(dailySalesDetailsListTemp.length / perPage));

    //         setShowDetails(false);

    //         setCurrentPage(1);
    //     });
    // }, [
    //     allOutlets,
    //     allOutletsUserOrdersDone,
    //     currOutletId,
    //     historyStartDate,
    //     historyEndDate,
    //     perPage,

    //     //isMounted, // only update this if screen on display
    //     // filterAppType,

    //     reportOutletShifts,
    //     reportDisplayType,

    //     isMasterAccount,

    //     reportOutletIdList,
    // ]);

    // useEffect(() => {
    //     if (showDetails && selectedItemSummary.detailsList) {
    //         setTransactionTypeSalesDetails(selectedItemSummary.detailsList);

    //         setPageReturn(currentPage);
    //         // console.log('currentPage value is');
    //         // console.log(currentPage);
    //         setCurrentDetailsPage(1);
    //         setPageCount(Math.ceil(selectedItemSummary.detailsList.length / perPage));
    //     }
    // }, [
    //     showDetails,
    //     selectedItemSummary,
    //     perPage,
    //     filterAppType,
    // ]);

    // 2025-04-25 - memory optimized version
    // useEffect(() => {
    //     let isActive = true; // For cleanup
    //     const CHUNK_SIZE = 50; // Reduced chunk size for mobile

    //     // Use requestIdleCallback if available, otherwise fallback to setTimeout
    //     const scheduleWork = (callback) => {
    //         if ('requestIdleCallback' in window) {
    //             requestIdleCallback(callback);
    //         } else {
    //             setTimeout(callback, 0);
    //         }
    //     };

    //     const processOrders = async () => {
    //         try {
    //             // Create a weak map to track processed orders
    //             const processedOrders = new WeakMap();
    //             let result = [];

    //             const currDateTime = moment().valueOf();
    //             const startTime = moment().set({ hour: 0, minute: 0, second: 0 });
    //             const endTime = moment().set({ hour: 5, minute: 55, second: 0 });

    //             // Process payout transactions in chunks
    //             const processPayoutChunk = async (transactions, startIdx) => {
    //                 if (!isActive) return null;

    //                 const endIdx = Math.min(startIdx + CHUNK_SIZE, transactions.length);
    //                 const chunk = transactions.slice(startIdx, endIdx);

    //                 for (const transaction of chunk) {
    //                     if (!transaction.userOrdersFigures) continue;

    //                     // Process only required fields to save memory
    //                     const validOrders = transaction.userOrdersFigures
    //                         .filter(order =>
    //                             filterAppType.includes(order.appType) &&
    //                             !processedOrders.has(order)
    //                         )
    //                         // .map(order => ({
    //                         //   uniqueId: order.uniqueId,
    //                         //   appType: order.appType,
    //                         //   createdAt: order.createdAt,
    //                         //   // Add only other essential fields you need
    //                         // }))
    //                         ;

    //                     result.push(...validOrders);

    //                     // Mark orders as processed
    //                     validOrders.forEach(order => processedOrders.set(order, true));
    //                 }

    //                 if (endIdx < transactions.length && isActive) {
    //                     return new Promise(resolve =>
    //                         scheduleWork(() => resolve(processPayoutChunk(transactions, endIdx)))
    //                     );
    //                 }
    //             };

    //             // Process raw orders in chunks
    //             const processRawChunk = async (startIdx) => {
    //                 if (!isActive) return null;

    //                 const endIdx = Math.min(startIdx + CHUNK_SIZE, allOutletsUserOrdersDoneRaw.length);

    //                 for (let i = startIdx; i < endIdx; i++) {
    //                     const order = allOutletsUserOrdersDoneRaw[i];

    //                     // Skip if already processed
    //                     if (processedOrders.has(order)) continue;

    //                     const orderDate = moment(order.createdAt);
    //                     const isValidTimeframe =
    //                         orderDate.isSame(currDateTime, 'day') ||
    //                         (moment(currDateTime).isBetween(startTime, endTime) &&
    //                             moment(currDateTime).add(-1, 'day').isSame(orderDate, 'day'));

    //                     if (isValidTimeframe && filterAppType.includes(order.appType)) {
    //                         // result.push({
    //                         //   uniqueId: order.uniqueId,
    //                         //   appType: order.appType,
    //                         //   createdAt: order.createdAt,
    //                         //   // Add only other essential fields you need
    //                         // });
    //                         result.push(order);
    //                         processedOrders.set(order, true);
    //                     }
    //                 }

    //                 if (endIdx < allOutletsUserOrdersDoneRaw.length && isActive) {
    //                     return new Promise(resolve =>
    //                         scheduleWork(() => resolve(processRawChunk(endIdx)))
    //                     );
    //                 }
    //             };

    //             // Process in sequence
    //             await processPayoutChunk(global.payoutTransactions, 0);
    //             await processPayoutChunk(global.payoutTransactionsExtend, 0);
    //             await processRawChunk(0);

    //             // Update state only if component is still mounted
    //             if (isActive) {
    //                 setAllOutletsUserOrdersDone(result);
    //             }

    //         } catch (error) {
    //             console.error('Error processing orders:', error);
    //         }
    //     };

    //     // Start processing after animations
    //     InteractionManager.runAfterInteractions(() => {
    //         processOrders();
    //     });

    //     // Cleanup
    //     return () => {
    //         isActive = false;
    //     };
    // }, [
    //     allOutletsUserOrdersDoneRaw,
    //     ptTimestamp,
    //     pteTimestamp,
    //     reportOutletShifts,
    //     reportDisplayType,
    //     filterAppType,
    // ]);

    let maxCharacters;

    if (windowWidth >= 350 && windowWidth < 370) {
        maxCharacters = 11;
    } else if (windowWidth >= 370 && windowWidth < 385) {
        maxCharacters = 13;
    } else if (windowWidth >= 385 && windowWidth < 400) {
        maxCharacters = 15;
    } else if (windowWidth >= 400 && windowWidth < 418) {
        maxCharacters = 17;
    } else if (windowWidth >= 418 && windowWidth < 435) {
        maxCharacters = 19;
    } else {
        maxCharacters = 50;
    }

    let truncatedUserName;

    if (userName.length > maxCharacters) {
        let truncatedString = userName.substring(0, maxCharacters); // Get substring up to maxCharacters

        // Remove trailing spaces from the substring
        while (truncatedString.charAt(truncatedString.length - 1) === ' ') {
            truncatedString = truncatedString.slice(0, -1);
        }

        truncatedUserName = truncatedString + '...'; // Add ellipsis
    } else {
        truncatedUserName = userName;
    }


    navigation.setOptions({
        headerLeft: () => (
            <View
                // onPress={() => {
                //   if (isAlphaUser || true) {
                //     navigation.navigate('MenuOrderingScreen');

                //     CommonStore.update((s) => {
                //       s.currPage = 'MenuOrderingScreen';
                //       s.currPageStack = [...currPageStack, 'MenuOrderingScreen'];
                //     });
                //   }
                //   else {
                //     navigation.navigate('Table');

                //     CommonStore.update((s) => {
                //       s.currPage = 'Table';
                //       s.currPageStack = [...currPageStack, 'Table'];
                //     });
                //   }
                //   if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                //     CommonStore.update((s) => {
                //       s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                //     });
                //   }
                // }}
                style={[styles.headerLeftStyle, {
                    width: windowWidth * 0.17,
                    ...isMobile() && {
                        display: 'none',
                    },
                }]}>
                <img src={headerLogo} width={124} height={26} />
            </View>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        justifyContent: 'center',
                        alignItems: 'center',
                        //marginRight: switchMerchant ? "27%" : 0,
                        //bottom: switchMerchant ? '2%' : 0,
                        //width: switchMerchant ? '100%' : switchMerchant ? "96%" : "55%",
                        ...isMobile() && {
                            display: 'none',
                        },
                    },
                    windowWidth >= 768 && switchMerchant
                        ? { right: windowWidth * 0.1 }
                        : {},
                    windowWidth <= 768
                        ? { right: 20 }
                        : {},
                ]}>
                <Text
                    style={{
                        fontSize: switchMerchant ? 20 : 24,
                        // lineHeight: 25,
                        textAlign: "center",
                        fontFamily: "NunitoSans-Bold",
                        color: Colors.whiteColor,
                        opacity: 1,
                    }}>
                    Dashboard
                </Text>
            </View>
        ),
        headerRight: () => (

            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: isMobile() ? '' : 'space-between',
                    ...isMobile() && {
                        //windowWidth: Dimensions.get('window').width,
                        marginLeft: 5,
                        width: windowWidth * 0.97,
                        //position: 'absolute',
                        //alignSelf: 'auto',
                        //marginLeft: 0,
                        //left: 0,
                        //left: 0,
                        //marginLeft: -30,
                        //marginRight: 'auto',
                        //marginLeft: 'auto',
                    },
                }}>
                {outletSelectDropdownView && outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: 'white',
                        width: 0.5,
                        height: windowHeight * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                    }} />
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate('General Settings - KooDoo BackOffice');
                        }
                    }}
                    style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <Text
                        style={[{
                            fontFamily: 'NunitoSans-SemiBold',
                            fontSize: switchMerchant ? 10 : 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }, switchMerchant ? { width: windowWidth / 8 } : {}]}
                        numberOfLines={switchMerchant ? 1 : 1}
                        ellipsizeMode="tail"
                    >
                        {isMobile() ? truncatedUserName : userName}
                        {/*truncatedUserName*/}
                        {/*userName1.length > 18 ? `${userName1.substring(0, 18)}...` : userName1*/}
                    </Text>
                    <View
                        style={{
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'white',
                            ...isMobile() && {
                                display: 'none',
                            },
                        }}>
                        <img
                            src={personicon}
                            width={windowHeight * 0.035}
                            height={windowHeight * 0.035}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        ),
    });

    // useEffect(() => {
    //     InteractionManager.runAfterInteractions(() => {
    //         OutletStore.update(s => {
    //             s.allOutletsUserOrdersLoyaltyDone = allOutletsUserOrdersLoyaltyDoneCache.concat(allOutletsUserOrdersLoyaltyDoneRealTime);
    //             s.allOutletsUserOrdersLoyalty = allOutletsUserOrdersLoyaltyCache.concat(allOutletsUserOrdersLoyaltyRealTime);
    //         });
    //     });
    // }, [
    //     allOutletsUserOrdersLoyaltyDoneCache,
    //     allOutletsUserOrdersLoyaltyCache,
    //     allOutletsUserOrdersLoyaltyDoneRealTime,
    //     allOutletsUserOrdersLoyaltyRealTime,
    // ]);

    useEffect(() => {
        if (currOutlet && currOutlet.uniqueId) {
            setCurrOutletData();
        }
    }, [currOutlet]);

    const setCurrOutletData = async () => {
        // await AsyncStorage.setItem('isQRPrintReceipt', currOutlet.isQRPrintReceipt ? '1' : '0');

        global.isQRPrintReceipt = currOutlet.isQRPrintReceipt ? '1' : '0';

        // await AsyncStorage.setItem('isQRNotPrintLogo', currOutlet.isQRNotPrintLogo ? '1' : '0');

        global.isQRNotPrintLogo = currOutlet.isQRNotPrintLogo ? '1' : '0';
    };

    const flatListRef = useRef();

    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [dimensions, setDimensions] = useState({
        window: Dimensions.get('window')
    });

    // ScreenSize Listener
    useEffect(() => {
        const subscription = Dimensions.addEventListener('change', ({ window }) => {
            setDimensions({ window });

            if (!isSidebarOpen) {
                const newSidebarWidth = calculateSidebarWidth(window.width);
                sidebarXValue.setValue(-newSidebarWidth * 1.2);
            }
        });

        return () => subscription.remove();
    }, [isSidebarOpen]);

    const calculateSidebarWidth = (width) => {
        const isLandscapeMode = width > Dimensions.get('window').height;

        if (isLandscapeMode) {
            return isMobile() ? width * 0.23 : width * 0.08;
        } else {
            return isMobile() ? width * 0.23 : width * 0.08;
        }
    };

    const sidebarClose = () => {
        setIsSidebarOpen(false);
        Animated.timing(sidebarXValue, {
            toValue: -sidebarWidth,
            duration: 200,
            useNativeDriver: true,
        }).start();

        Animated.timing(contentXValue, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
        }).start();
    };

    const sidebarOpen = () => {
        setIsSidebarOpen(true);
        Animated.timing(sidebarXValue, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
        }).start();

        Animated.timing(contentXValue, {
            toValue: sidebarWidth * 0.55,
            duration: 200,
            useNativeDriver: true,
        }).start();
    };

    const sidebarWidth = useMemo(() => {
        return calculateSidebarWidth(dimensions.window.width);
    }, [dimensions.window.width]);

    const sidebarXValue = useRef(new Animated.Value(
        isSidebarOpen ? 0 : -calculateSidebarWidth(Dimensions.get('window').width) * 1.2
    )).current;
    const contentXValue = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        CommonStore.update((s) => {
            s.simpliFiedLayout = true;
        })
    }, [])

    return (
        <View>
            {isMobile() && <TopBar navigation={navigation} />}

            <View
                style={[
                    styles.container,
                    {
                        height: windowHeight,
                        width: windowWidth,
                    }
                ]}>
                {/* {isSidebarOpen && ( */}
                <Animated.View
                    style={[
                        styles.sidebar,
                        {
                            transform: [{ translateX: sidebarXValue }],
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            zIndex: 2,
                            height: '100%',
                            width: sidebarWidth,
                            marginTop: 50,
                        }
                    ]}
                >
                    <SideBar navigation={navigation} selectedTab={0} isSidebarOpen={isSidebarOpen} />
                </Animated.View>
                {/* )} */}

                <View style={[{ height: windowHeight, flex: 9, },
                {
                    ...isMobile() && {
                        flex: 3,
                        //backgroundColor:'yellow',
                    }
                },
                ]}>{/* flex: 9 or 3 ,width: windowWidth*/}
                    <View
                        style={{
                            width: isMobile() ? windowWidth * 0.23 : 123,
                            height: 70,

                            //backgroundColor: 'red',

                            zIndex: 3,

                            marginLeft: isSidebarOpen ? 0 : 0,
                        }}
                    >
                        {isSidebarOpen ?
                            <Animated.View style=
                                {{
                                    transform: [{ translateX: sidebarXValue }],
                                    backgroundColor: Colors.whiteColor,
                                    height: '100%',
                                    width: sidebarWidth,
                                }}>
                                <TouchableOpacity
                                    style={{
                                        marginTop: 5,
                                    }}
                                    onPress={sidebarClose}
                                >
                                    <MaterialIcons name='keyboard-capslock' color={Colors.primaryColor} size={isMobile() ? 35 : 30} style={{ alignSelf: 'center', transform: [{ rotate: '270deg' }], marginTop: 7, marginLeft: -3 }} />
                                </TouchableOpacity>
                            </Animated.View>
                            :
                            <View style={{}}>
                                <TouchableOpacity
                                    style={{ marginTop: 5, }}
                                    onPress={sidebarOpen}
                                >
                                    <View style={{
                                        flexDirection: 'row',
                                        marginLeft: 10,
                                        marginVertical: 10,
                                        justifyContent: 'center',
                                        flexDirection: 'row',
                                        borderWidth: 1,
                                        borderColor: Colors.primaryColor,
                                        backgroundColor: '#4E9F7D',
                                        borderRadius: 5,
                                        //width: 160,
                                        height: switchMerchant ? 35 : 40,
                                        width: windowWidth * 0.4,
                                        alignItems: 'center',
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 1,
                                        zIndex: -1,

                                    }}>
                                        <MaterialIcons name='keyboard-capslock' color={Colors.whiteColor} size={isMobile() ? 25 : 30} style={{ alignSelf: 'flex-start', transform: [{ rotate: '90deg' }], marginTop: 5 }} />
                                        <Text style={{
                                            color: Colors.whiteColor,
                                            marginLeft: 5,
                                            fontSize: switchMerchant ? 10 : 16,
                                            fontFamily: 'NunitoSans-Bold',
                                        }}
                                        >
                                            MORE PAGES
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </View>
                        }

                    </View>

                    <Animated.ScrollView
                        showsVerticalScrollIndicator={false}
                        style={{
                            transform: [{ translateX: contentXValue }],
                        }}
                        contentContainerStyle={{
                            paddingBottom: windowHeight * 0.1,
                        }}>
                        <ScrollView horizontal={simpliFiedLayout ? isSidebarOpen : true} showsHorizontalScrollIndicator={simpliFiedLayout ? isSidebarOpen : true}>
                            <View
                                style={{
                                    padding: 10,

                                    // width: windowWidth - 140,
                                    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
                                    backgroundColor: Colors.highlightColor,
                                    ...isMobile() && {
                                        //backgroundColor: 'red',
                                        width: simpliFiedLayout ? windowWidth : windowWidth * 3,
                                        marginLeft: isSidebarOpen ? 10 : 10,
                                    }

                                }}>
                                <View style={{
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    justifyContent: 'space-evenly',
                                    alignSelf: "center",
                                    borderRadius: 5,
                                    marginTop: 10,
                                    width: windowWidth * 0.8,
                                    height: windowHeight * 0.75,
                                    backgroundColor: Colors.fieldtBgColor,
                                    shadowOffset: {
                                        width: 0,
                                        height: 1,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    right: 10,
                                }}>
                                    <TouchableOpacity
                                        style={{
                                            height: windowHeight * 0.2,
                                            width: windowWidth * 0.5,
                                            backgroundColor: Colors.secondaryColor,
                                            borderRadius: 10,
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            flexDirection: 'column',
                                        }}
                                        onPress={() => {
                                            //put the link here
                                            // linkTo && linkTo(`${prefix}/dashboard`);

                                            window.open(linkManagerReports, '_blank');
                                        }}
                                    >
                                        <View style={{
                                            marginBottom: 15,
                                        }}>
                                            <Report
                                                width={
                                                    isMobile() ? windowWidth * 0.1 : windowWidth * 0.05
                                                }
                                                height={
                                                    isMobile() ? windowWidth * 0.1 : windowWidth * 0.05
                                                }
                                            />
                                        </View>

                                        <Text style={{
                                            fontSize: 24,
                                            color: Colors.darkBgColor,
                                            fontFamily: "NunitoSans-SemiBold",
                                        }}>
                                            Report
                                        </Text>
                                    </TouchableOpacity>

                                    <TouchableOpacity
                                        style={{
                                            height: windowHeight * 0.2,
                                            width: windowWidth * 0.5,
                                            backgroundColor: Colors.secondaryColor,
                                            borderRadius: 10,
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            flexDirection: 'column',
                                        }}
                                        onPress={() => {
                                            //put the link here
                                            // linkTo && linkTo(`${prefix}/dashboard`);

                                            window.open(linkManagerComposite, '_blank');
                                        }}
                                    >
                                        <View style={{
                                            marginBottom: 15,
                                        }}>
                                            <Inventory
                                                width={
                                                    isMobile() ? windowWidth * 0.1 : windowWidth * 0.05

                                                }
                                                height={
                                                    isMobile() ? windowWidth * 0.1 : windowWidth * 0.05

                                                }
                                            />
                                        </View>

                                        <Text style={{
                                            fontSize: 24,
                                            color: Colors.darkBgColor,
                                            fontFamily: "NunitoSans-SemiBold",
                                        }}>
                                            Composite
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </View>

                        </ScrollView>
                    </Animated.ScrollView>
                </View>

            </View>



        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: 'row',
    },
    headerLogo: {
        width: 112,
        height: 25,
        marginLeft: 10,
    },
    sidebar: {
        width: Dimensions.get('screen').width * Styles.sideBarWidth,
    },
    circleIcon: {
        width: 30,
        height: 30,
        // resizeMode: 'contain',
        marginRight: 10,
        alignSelf: 'center',
    },
    confirmBox: {
        width: Dimensions.get('screen').width * 0.4,
        height: Dimensions.get('screen').height * 0.3,
        borderRadius: 12,
        backgroundColor: Colors.whiteColor,
        justifyContent: 'space-between',
    },
    textInput8: {
        fontFamily: 'NunitoSans-Regular',
        width: 75,
        height: 50,
        flex: 1,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
    },

    textInput9: {
        fontFamily: 'NunitoSans-Regular',
        width: 350,
        height: 50,
        //flex: 1,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
    },

    headerLeftStyle: {
        width: Dimensions.get('screen').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        flex: 1,
        backgroundColor: Colors.modalBgColor,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalView: {
        height: Dimensions.get('screen').width * 0.3,
        width: Dimensions.get('screen').width * 0.4,
        backgroundColor: Colors.whiteColor,
        borderRadius: 12,
        padding: Dimensions.get('screen').width * 0.03,
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeButton: {
        position: 'absolute',
        right: Dimensions.get('screen').width * 0.02,
        top: Dimensions.get('screen').width * 0.02,

        elevation: 1000,
        zIndex: 1000,
    },
    modalTitle: {
        alignItems: 'center',
        top: '20%',
        position: 'absolute',
    },
    modalBody: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    modalTitleText: {
        fontFamily: 'NunitoSans-Bold',
        textAlign: 'center',
        fontSize: 24,
    },
    modalDescText: {
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 18,
        color: Colors.fieldtTxtColor,
    },
    modalBodyText: {
        flex: 1,
        fontFamily: 'NunitoSans-SemiBold',
        fontSize: 25,
        width: '20%',
    },
    modalSaveButton: {
        width: 130,
        backgroundColor: Colors.fieldtBgColor,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 8,

        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,

        marginVertical: 10,
    },
    ManageFilterBox: {
        height: Dimensions.get('screen').height * 0.24,
        width: Dimensions.get('screen').width * 0.33,
        borderWidth: 1,
        borderColor: '#E5E5E5',
        position: 'absolute',
        marginTop: '13%',
        marginLeft: '12%',
        shadowColor: '#000',
        shadowOffset: {
            width: 1,
            height: 5,
        },
        shadowOpacity: 0.32,
        shadowRadius: 3.22,
        elevation: 10,
        zIndex: 1000,
        borderRadius: 10,
        backgroundColor: Colors.whiteColor,
    },
});

export default HomePage;
